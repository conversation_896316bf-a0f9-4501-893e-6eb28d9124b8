import type { <PERSON>ada<PERSON>, Viewport } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_<PERSON><PERSON> } from "next/font/google";
import "../globals.css";
import { Providers } from "@/providers/provider";
import { Loading } from "@/components/loader/loader";
import { Suspense } from "react";
import { Header } from "@/components/header/header";
import { OfflineBanner } from "@/components/offline-barner/offline-barner";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

// Viewport configuration
export const viewport: Viewport = {
  themeColor: [
    { media: "(prefers-color-scheme: light)", color: "#ffffff" },
    { media: "(prefers-color-scheme: dark)", color: "#000000" },
  ],
  colorScheme: "light dark",
  width: "device-width",
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
};

// Metadata configuration
export const metadata: Metadata = {
  title: {
    default: "404 | Chatzuri - Custom ChatGPT for Your Business",
    template: "%s | Chatzuri",
  },
  description:
    "Build a custom ChatGPT for your business. Embed it on your website to handle customer support, lead generation, and engage with your users. Create your business Chatbot free trial today!",
  applicationName: "Chatzuri",
  authors: [
    { name: "Kevin Kibebe", url: "https://tevinly.com" },
    { name: "Lawrence Njenga", url: "https://github.com/lawrencekm" },
  ],
  generator: "Next.js",
  keywords: [
    "ChatGPT for business",
    "custom chatbot",
    "AI customer support",
    "website chatbot",
    "lead generation bot",
    "AI assistant",
    "business chatbot",
    "no-code chatbot",
    "chatbot builder",
    "customer engagement",
  ],

  referrer: "origin-when-cross-origin",
  creator: "Kevin Kibebe",
  publisher: "Tevinly",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL("https://chatzuri.com"),
  alternates: {
    canonical: "/",
    languages: {
      "en-US": "/en-US",
    },
  },
  openGraph: {
    title: "PhotoShowcase | Photography Community Platform",
    description:
      "Build a custom ChatGPT for your business. Embed it on your website to handle customer support, lead generation, and engage with your users. Create your business Chatbot free trial today!",
    url: "https://chatzuri.com",
    siteName: "Chatzuri - Custom ChatGPT for Your Business",
    images: [
      {
        url: "/images/og-image.png",
        width: 1200,
        height: 630,
        alt: "404 | Chatzuri - Custom ChatGPT for Your Business",
      },
    ],
    locale: "en_US",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "404 | Chatzuri - Custom ChatGPT for Your Business",
    description:
      "Build a custom ChatGPT for your business. Embed it on your website to handle customer support, lead generation, and engage with your users. Create your business Chatbot free trial today!",
    creator: "@Kevin36285655",
    images: ["/images/og-image.png"],
  },
  robots: {
    index: true,
    follow: true,
    nocache: false,
    googleBot: {
      index: true,
      follow: true,
      noimageindex: false,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  icons: {
    icon: [
      { url: "/site-favicon.ico" },
      new URL("/site-favicon.ico", "https://chatzuri.com"),
      { url: "/favicon-16x16.png", sizes: "16x16", type: "image/png" },
      { url: "/favicon-32x32.png", sizes: "32x32", type: "image/png" },
      { url: "/icon-512.png", sizes: "512x512", type: "image/png" },
    ],
    apple: [{ url: "/apple-touch-icon.png" }],
    other: [
      {
        rel: "mask-icon",
        url: "/safari-pinned-tab.svg",
        color: "#00a67e",
      },
    ],
  },
  manifest: "/site.webmanifest",
  category: "photography",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={`${geistSans.variable} ${geistMono.variable} relative antialiased`}
      >
        <Providers>
          <Suspense fallback={<Loading />}>
            <Header />
            {children}
            <OfflineBanner />
          </Suspense>
        </Providers>
      </body>
    </html>
  );
}
