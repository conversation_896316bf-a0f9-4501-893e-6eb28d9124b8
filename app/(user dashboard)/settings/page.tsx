"use client";
import {
  <PERSON><PERSON><PERSON>,
  ArrowLeft,
  Lock,
  Bell,
  Shield,
  CreditCard,
  Globe,
  History,
  DollarSign,
  HelpCircle,
} from "lucide-react";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { motion, AnimatePresence } from "framer-motion";
import { useState } from "react";

export default function SettingsPage() {
  const [activeTab, setActiveTab] = useState("account");
  const [darkMode, setDarkMode] = useState(false);
  const [emailNotifications, setEmailNotifications] = useState(true);
  const [pushNotifications, setPushNotifications] = useState(true);
  const [soundAlerts, setSoundAlerts] = useState(false);

  const springTransition = {
    type: "spring",
    stiffness: 300,
    damping: 25,
  };

  const tabs = [
    { id: "account", icon: <Settings className="h-4 w-4" />, label: "Account" },
    { id: "privacy", icon: <Lock className="h-4 w-4" />, label: "Privacy" },
    {
      id: "notifications",
      icon: <Bell className="h-4 w-4" />,
      label: "Notifications",
    },
    {
      id: "billing",
      icon: <CreditCard className="h-4 w-4" />,
      label: "Billing",
    },
    {
      id: "appearance",
      icon: <Globe className="h-4 w-4" />,
      label: "Appearance",
    },
  ];

  // Mock payment methods data
  const paymentMethods = [
    { id: 1, type: "Visa", last4: "4242", expiry: "12/25", isDefault: true },
    {
      id: 2,
      type: "Mastercard",
      last4: "5555",
      expiry: "06/24",
      isDefault: false,
    },
  ];

  // Mock subscription data
  const currentPlan = {
    name: "Pro Plan",
    price: "$19.99",
    interval: "monthly",
    features: ["Unlimited projects", "Advanced analytics", "Priority support"],
    nextBilling: "January 25, 2024",
  };

  // Mock billing history
  const billingHistory = [
    {
      id: 1,
      date: "Dec 25, 2023",
      amount: "$19.99",
      status: "Paid",
      invoice: "INV-2023-12-25",
    },
    {
      id: 2,
      date: "Nov 25, 2023",
      amount: "$19.99",
      status: "Paid",
      invoice: "INV-2023-11-25",
    },
    {
      id: 3,
      date: "Oct 25, 2023",
      amount: "$19.99",
      status: "Paid",
      invoice: "INV-2023-10-25",
    },
  ];

  return (
    <div className="min-h-screen  p-4 md:p-8">
      <div className="max-w-5xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ ...springTransition, delay: 0.1 }}
          className="flex items-center mb-8"
        >
          <Link href="/dashboard">
            <Button variant="ghost" size="icon" className="rounded-full">
              <ArrowLeft className="h-5 w-5" />
            </Button>
          </Link>
          <h1 className="text-3xl font-bold ml-2 bg-gradient-to-r from-emerald-300 to-emerald-600 bg-clip-text text-transparent">
            Settings
          </h1>
        </motion.div>

        <div className="flex flex-col md:flex-row gap-6">
          {/* Sidebar Navigation */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ ...springTransition, delay: 0.2 }}
            className="w-full md:w-64"
          >
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-2 h-fit sticky top-6">
              <div className="space-y-1">
                {tabs.map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`cursor-pointer w-full flex items-center gap-3 px-4 py-3 text-sm rounded-lg transition-all ${
                      activeTab === tab.id
                        ? "bg-emerald-50 dark:bg-gray-700 text-emerald-600 dark:text-emerald-400"
                        : "hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300"
                    }`}
                  >
                    {tab.icon}
                    {tab.label}
                  </button>
                ))}
              </div>
            </div>
          </motion.div>

          {/* Main Content */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ ...springTransition, delay: 0.3 }}
            className="flex-1"
          >
            <AnimatePresence mode="wait">
              {/* Account Settings */}
              {activeTab === "account" && (
                <motion.div
                  key="account"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  transition={springTransition}
                  className="bg-white dark:bg-gray-800 rounded-xl shadow-sm overflow-hidden"
                >
                  <div className="p-6 border-b border-gray-100 dark:border-gray-700">
                    <h2 className="text-xl font-semibold flex items-center gap-3 dark:text-white">
                      <Settings className="h-5 w-5 text-emerald-500" />
                      Account Settings
                    </h2>
                  </div>
                  <div className="divide-y divide-gray-100 dark:divide-gray-700">
                    <div className="p-6">
                      <div className="space-y-6">
                        <motion.div
                          whileHover={{ scale: 1.01 }}
                          className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg"
                        >
                          <div>
                            <p className="font-medium dark:text-gray-200">
                              Email Notifications
                            </p>
                            <p className="text-sm text-gray-500 dark:text-gray-400">
                              Receive email updates
                            </p>
                          </div>
                          <Switch
                            checked={emailNotifications}
                            onCheckedChange={setEmailNotifications}
                            className="data-[state=checked]:bg-emerald-500 cursor-pointer"
                          />
                        </motion.div>

                        <motion.div
                          whileHover={{ scale: 1.01 }}
                          className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg"
                        >
                          <div>
                            <p className="font-medium dark:text-gray-200">
                              Account Visibility
                            </p>
                            <p className="text-sm text-gray-500 dark:text-gray-400">
                              Make your profile public
                            </p>
                          </div>
                          <Switch />
                        </motion.div>
                      </div>
                    </div>
                  </div>
                </motion.div>
              )}

              {/* Privacy Settings */}
              {activeTab === "privacy" && (
                <motion.div
                  key="privacy"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  transition={springTransition}
                  className="bg-white dark:bg-gray-800 rounded-xl shadow-sm overflow-hidden"
                >
                  <div className="p-6 border-b border-gray-100 dark:border-gray-700">
                    <h2 className="text-xl font-semibold flex items-center gap-3 dark:text-white">
                      <Lock className="h-5 w-5 text-emerald-500" />
                      Privacy & Security
                    </h2>
                  </div>
                  <div className="divide-y divide-gray-100 dark:divide-gray-700">
                    <div className="p-6">
                      <div className="space-y-6">
                        <motion.div
                          whileHover={{ scale: 1.01 }}
                          className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg"
                        >
                          <div>
                            <p className="font-medium dark:text-gray-200">
                              Two-Factor Authentication
                            </p>
                            <p className="text-sm text-gray-500 dark:text-gray-400">
                              Add an extra layer of security
                            </p>
                          </div>
                          <Button variant="outline" className="rounded-full">
                            Enable
                          </Button>
                        </motion.div>

                        <motion.div
                          whileHover={{ scale: 1.01 }}
                          className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg"
                        >
                          <div>
                            <p className="font-medium dark:text-gray-200">
                              Activity Log
                            </p>
                            <p className="text-sm text-gray-500 dark:text-gray-400">
                              View your account activity
                            </p>
                          </div>
                          <Button variant="outline" className="rounded-full">
                            View
                          </Button>
                        </motion.div>

                        <motion.div
                          whileHover={{ scale: 1.01 }}
                          className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg"
                        >
                          <div>
                            <p className="font-medium dark:text-gray-200">
                              Data Export
                            </p>
                            <p className="text-sm text-gray-500 dark:text-gray-400">
                              Download your personal data
                            </p>
                          </div>
                          <Button variant="outline" className="rounded-full">
                            Request
                          </Button>
                        </motion.div>
                      </div>
                    </div>
                  </div>
                </motion.div>
              )}

              {/* Notification Settings */}
              {activeTab === "notifications" && (
                <motion.div
                  key="notifications"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  transition={springTransition}
                  className="bg-white dark:bg-gray-800 rounded-xl shadow-sm overflow-hidden"
                >
                  <div className="p-6 border-b border-gray-100 dark:border-gray-700">
                    <h2 className="text-xl font-semibold flex items-center gap-3 dark:text-white">
                      <Bell className="h-5 w-5 text-emerald-500" />
                      Notification Settings
                    </h2>
                  </div>
                  <div className="divide-y divide-gray-100 dark:divide-gray-700">
                    <div className="p-6">
                      <div className="space-y-6">
                        <motion.div
                          whileHover={{ scale: 1.01 }}
                          className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg"
                        >
                          <div>
                            <p className="font-medium dark:text-gray-200">
                              Push Notifications
                            </p>
                            <p className="text-sm text-gray-500 dark:text-gray-400">
                              Receive push notifications
                            </p>
                          </div>
                          <Switch
                            checked={pushNotifications}
                            onCheckedChange={setPushNotifications}
                            className="data-[state=checked]:bg-emerald-500 cursor-pointer"
                          />
                        </motion.div>

                        <motion.div
                          whileHover={{ scale: 1.01 }}
                          className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg"
                        >
                          <div>
                            <p className="font-medium dark:text-gray-200">
                              Sound Alerts
                            </p>
                            <p className="text-sm text-gray-500 dark:text-gray-400">
                              Play sound for notifications
                            </p>
                          </div>
                          <Switch
                            checked={soundAlerts}
                            onCheckedChange={setSoundAlerts}
                            className="data-[state=checked]:bg-emerald-500 cursor-pointer"
                          />
                        </motion.div>

                        <motion.div
                          whileHover={{ scale: 1.01 }}
                          className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg"
                        >
                          <div>
                            <p className="font-medium dark:text-gray-200">
                              Email Digest
                            </p>
                            <p className="text-sm text-gray-500 dark:text-gray-400">
                              Weekly summary of notifications
                            </p>
                          </div>
                          <Switch className="data-[state=checked]:bg-emerald-500 cursor-pointer" />
                        </motion.div>
                      </div>
                    </div>
                  </div>
                </motion.div>
              )}

              {/* Appearance Settings */}
              {activeTab === "appearance" && (
                <motion.div
                  key="appearance"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  transition={springTransition}
                  className="bg-white dark:bg-gray-800 rounded-xl shadow-sm overflow-hidden"
                >
                  <div className="p-6 border-b border-gray-100 dark:border-gray-700">
                    <h2 className="text-xl font-semibold flex items-center gap-3 dark:text-white">
                      <Globe className="h-5 w-5 text-emerald-500" />
                      Appearance
                    </h2>
                  </div>
                  <div className="divide-y divide-gray-100 dark:divide-gray-700">
                    <div className="p-6">
                      <div className="space-y-6">
                        <motion.div
                          whileHover={{ scale: 1.01 }}
                          className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg"
                        >
                          <div>
                            <p className="font-medium dark:text-gray-200">
                              Dark Mode
                            </p>
                            <p className="text-sm text-gray-500 dark:text-gray-400">
                              Switch between light and dark theme
                            </p>
                          </div>
                          <Switch
                            checked={darkMode}
                            onCheckedChange={setDarkMode}
                            className="data-[state=checked]:bg-emerald-500 cursor-pointer"
                          />
                        </motion.div>

                        <motion.div
                          whileHover={{ scale: 1.01 }}
                          className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg"
                        >
                          <p className="font-medium dark:text-gray-200 mb-2">
                            Theme Color
                          </p>
                          <div className="flex gap-2">
                            {["emerald", "emerald", "green", "orange"].map(
                              (color) => (
                                <button
                                  key={color}
                                  className={`w-8 h-8 rounded-full bg-${color}-500`}
                                  aria-label={`${color} theme`}
                                />
                              )
                            )}
                          </div>
                        </motion.div>
                      </div>
                    </div>
                  </div>
                </motion.div>
              )}

              {/* Billing Settings */}
              {activeTab === "billing" && (
                <motion.div
                  key="billing"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  transition={springTransition}
                  className="space-y-6"
                >
                  {/* Current Plan */}
                  <motion.div
                    whileHover={{ scale: 1.01 }}
                    className="bg-white dark:bg-gray-800 rounded-xl shadow-sm overflow-hidden"
                  >
                    <div className="p-6 border-b border-gray-100 dark:border-gray-700">
                      <h2 className="text-xl font-semibold flex items-center gap-3 dark:text-white">
                        <DollarSign className="h-5 w-5 text-emerald-500" />
                        Your Plan
                      </h2>
                    </div>
                    <div className="p-6">
                      <div className="flex flex-col md:flex-row justify-between gap-6">
                        <div>
                          <h3 className="text-lg font-bold dark:text-white">
                            {currentPlan.name}
                          </h3>
                          <p className="text-2xl font-bold my-2 dark:text-white">
                            {currentPlan.price}{" "}
                            <span className="text-sm font-normal text-gray-500">
                              /{currentPlan.interval}
                            </span>
                          </p>
                          <p className="text-sm text-gray-500 dark:text-gray-400">
                            Next billing: {currentPlan.nextBilling}
                          </p>
                        </div>
                        <div className="space-y-2">
                          {currentPlan.features.map((feature, index) => (
                            <div
                              key={index}
                              className="flex items-center gap-2"
                            >
                              <div className="w-2 h-2 rounded-full bg-emerald-500"></div>
                              <span className="text-sm dark:text-gray-300">
                                {feature}
                              </span>
                            </div>
                          ))}
                        </div>
                      </div>
                      <div className="mt-6 flex gap-3">
                        <Button variant="outline" className="cursor-pointer rounded-full">
                          Change Plan
                        </Button>
                        <Button variant="destructive" className="cursor-pointer rounded-full">
                          Cancel Subscription
                        </Button>
                      </div>
                    </div>
                  </motion.div>

                  {/* Payment Methods */}
                  <motion.div
                    whileHover={{ scale: 1.01 }}
                    className="bg-white dark:bg-gray-800 rounded-xl shadow-sm overflow-hidden"
                  >
                    <div className="p-6 border-b border-gray-100 dark:border-gray-700">
                      <h2 className="text-xl font-semibold flex items-center gap-3 dark:text-white">
                        <CreditCard className="h-5 w-5 text-emerald-500" />
                        Payment Methods
                      </h2>
                    </div>
                    <div className="p-6">
                      <div className="space-y-4">
                        {paymentMethods.map((method) => (
                          <div
                            key={method.id}
                            className="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg"
                          >
                            <div className="flex items-center gap-4">
                              <div
                                className={`w-12 h-8 rounded flex items-center justify-center ${
                                  method.type === "Visa"
                                    ? "bg-emerald-900"
                                    : "bg-yellow-500"
                                }`}
                              >
                                <span className="text-white font-bold text-xs">
                                  {method.type === "Visa" ? "VISA" : "MC"}
                                </span>
                              </div>
                              <div>
                                <p className="font-medium dark:text-gray-200">
                                  •••• •••• •••• {method.last4}
                                </p>
                                <p className="text-sm text-gray-500 dark:text-gray-400">
                                  Expires {method.expiry}
                                </p>
                              </div>
                            </div>
                            <div className="flex items-center gap-2">
                              {method.isDefault && (
                                <span className="px-2 py-1 text-xs rounded-full bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200">
                                  Default
                                </span>
                              )}
                              <Button
                                variant="ghost"
                                size="sm"
                                className="text-emerald-600 cursor-pointer dark:text-emerald-400"
                              >
                                Edit
                              </Button>
                            </div>
                          </div>
                        ))}
                      </div>
                      <Button className="mt-4 w-full cursor-pointer md:w-auto rounded-full">
                        Add Payment Method
                      </Button>
                    </div>
                  </motion.div>

                  {/* Billing History */}
                  <motion.div
                    whileHover={{ scale: 1.01 }}
                    className="bg-white dark:bg-gray-800 rounded-xl shadow-sm overflow-hidden"
                  >
                    <div className="p-6 border-b border-gray-100 dark:border-gray-700">
                      <h2 className="text-xl font-semibold flex items-center gap-3 dark:text-white">
                        <History className="h-5 w-5 text-emerald-500" />
                        Billing History
                      </h2>
                    </div>
                    <div className="p-6">
                      <div className="overflow-x-auto">
                        <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                          <thead>
                            <tr>
                              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                Date
                              </th>
                              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                Amount
                              </th>
                              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                Status
                              </th>
                              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                Invoice
                              </th>
                              <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                Actions
                              </th>
                            </tr>
                          </thead>
                          <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                            {billingHistory.map((item) => (
                              <tr key={item.id}>
                                <td className="px-4 py-3 whitespace-nowrap text-sm dark:text-gray-300">
                                  {item.date}
                                </td>
                                <td className="px-4 py-3 whitespace-nowrap text-sm font-medium dark:text-white">
                                  {item.amount}
                                </td>
                                <td className="px-4 py-3 whitespace-nowrap text-sm">
                                  <span
                                    className={`px-2 py-1 rounded-full text-xs ${
                                      item.status === "Paid"
                                        ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
                                        : "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"
                                    }`}
                                  >
                                    {item.status}
                                  </span>
                                </td>
                                <td className="px-4 py-3 whitespace-nowrap text-sm dark:text-gray-300">
                                  {item.invoice}
                                </td>
                                <td className="px-4 py-3 whitespace-nowrap text-right text-sm">
                                  <Button
                                    variant="link"
                                    className="text-emerald-600 cursor-pointer dark:text-emerald-400 p-0 h-auto"
                                  >
                                    Download
                                  </Button>
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </div>
                  </motion.div>

                  {/* Billing Support */}
                  <motion.div
                    whileHover={{ scale: 1.01 }}
                    className="bg-white dark:bg-gray-800 rounded-xl shadow-sm overflow-hidden"
                  >
                    <div className="p-6 border-b border-gray-100 dark:border-gray-700">
                      <h2 className="text-xl font-semibold flex items-center gap-3 dark:text-white">
                        <HelpCircle className="h-5 w-5 text-emerald-500" />
                        Billing Support
                      </h2>
                    </div>
                    <div className="p-6">
                      <div className="space-y-4">
                        <p className="dark:text-gray-300">
                          Have questions about your bill? Our support team is
                          here to help.
                        </p>
                        <div className="flex flex-col sm:flex-row gap-3">
                          <Button variant="outline" className="cursor-pointer rounded-full">
                            Contact Support
                          </Button>
                          <Button variant="outline" className=" cursor-pointer rounded-full">
                            FAQ & Help Center
                          </Button>
                        </div>
                      </div>
                    </div>
                  </motion.div>
                </motion.div>
              )}
            </AnimatePresence>

            {/* Danger Zone */}
            {activeTab === "account" && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
                className="mt-6 bg-white dark:bg-gray-800 rounded-xl shadow-sm overflow-hidden"
              >
                <div className="p-6 border-b border-red-100 dark:border-red-900/50 bg-red-50 dark:bg-red-900/10">
                  <h2 className="text-xl font-semibold flex items-center gap-3 text-red-600 dark:text-red-400">
                    <Shield className="h-5 w-5" />
                    Danger Zone
                  </h2>
                </div>
                <div className="p-6">
                  <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                    <div>
                      <p className="font-medium dark:text-gray-200">
                        Delete Account
                      </p>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        Permanently remove your account and all data
                      </p>
                    </div>
                    <Button
                      variant="destructive"
                      className="cursor-pointer w-full sm:w-auto"
                      onClick={() => console.log("Delete account clicked")}
                    >
                      Delete Account
                    </Button>
                  </div>
                </div>
              </motion.div>
            )}
          </motion.div>
        </div>
      </div>
    </div>
  );
}
