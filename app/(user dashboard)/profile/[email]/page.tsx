"use client";

import {
  <PERSON><PERSON>,
  ArrowLeft,
  Mail,
  Phone,
  MapPin,
  Lock,
  Key,
  Eye,
  EyeOff,
} from "lucide-react";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { AnimatePresence, motion } from "framer-motion";
import { FormProvider, useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useState } from "react";
import toast from "react-hot-toast";
import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from "@/components/ui/form";

// Form schemas
const profileFormSchema = z.object({
  firstName: z.string().min(2, {
    message: "First name must be at least 2 characters.",
  }),
  lastName: z.string().min(2, {
    message: "Last name must be at least 2 characters.",
  }),
  email: z.string().email({
    message: "Please enter a valid email address.",
  }),
  phone: z.string().min(10, {
    message: "Phone number must be at least 10 digits.",
  }),
  location: z.string().min(2, {
    message: "Please enter a valid location.",
  }),
});

const passwordFormSchema = z
  .object({
    currentPassword: z.string().min(8, {
      message: "Password must be at least 8 characters.",
    }),
    newPassword: z.string().min(8, {
      message: "Password must be at least 8 characters.",
    }),
    confirmPassword: z.string().min(8, {
      message: "Password must be at least 8 characters.",
    }),
  })
  .refine((data) => data.newPassword === data.confirmPassword, {
    message: "Passwords don't match",
    path: ["confirmPassword"],
  });

type ProfileFormValues = z.infer<typeof profileFormSchema>;
type PasswordFormValues = z.infer<typeof passwordFormSchema>;

// Animation transition for framer-motion
const springTransition = {
  type: "spring",
  stiffness: 500,
  damping: 40,
  mass: 1,
};

export default function ProfilePage() {
  const [activeTab, setActiveTab] = useState("profile");
  const tabs = [
    {
      id: "profile",
      icon: <User className="h-4 w-4 mr-2" />,
      label: "Profile",
    },
    {
      id: "security",
      icon: <Lock className="h-4 w-4 mr-2" />,
      label: "Security",
    },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 p-4 md:p-8">
      <div className="max-w-4xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={springTransition}
          className="flex items-center mb-8"
        >
          <Link href="/dashboard">
            <Button variant="ghost" size="icon" className="cursor-pointer rounded-full">
              <ArrowLeft className="h-5 w-5" />
            </Button>
          </Link>
          <h1 className="text-3xl font-bold ml-2 bg-gradient-to-r from-emerald-600 to-green-600 bg-clip-text text-transparent">
            My Profile
          </h1>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, scale: 0.98 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={springTransition}
          className="bg-white dark:bg-gray-800 rounded-xl shadow-sm overflow-hidden"
        >
          {/* Custom Tab Bar */}
          <div className="relative border-b border-gray-200 dark:border-gray-700">
            <div className="flex">
              {tabs.map((tab) => (
                <motion.button
                  key={tab.id}
                  whileHover={{ scale: 1.03 }}
                  whileTap={{ scale: 0.97 }}
                  onClick={() => setActiveTab(tab.id)}
                  className={`cursor-pointer relative px-6 py-3 text-sm font-medium transition-colors duration-200 ${
                    activeTab === tab.id
                      ? "text-emerald-600 dark:text-emerald-400"
                      : "text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
                  }`}
                >
                  <div className="flex items-center">
                    {tab.icon}
                    {tab.label}
                  </div>
                  {activeTab === tab.id && (
                    <motion.div
                      layoutId="activeTabIndicator"
                      className="absolute bottom-0 left-0 right-0 h-0.5 bg-emerald-500"
                      transition={springTransition}
                    />
                  )}
                </motion.button>
              ))}
            </div>
          </div>

          {/* Tab Content */}
          <div className="relative overflow-hidden">
            <AnimatePresence mode="wait">
              <motion.div
                key={activeTab}
                initial={{ opacity: 0, x: activeTab === "profile" ? 50 : -50 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: activeTab === "profile" ? -50 : 50 }}
                transition={{ duration: 0.2 }}
                className="p-6"
              >
                {activeTab === "profile" ? (
                  <ProfileContent />
                ) : (
                  <SecurityContent />
                )}
              </motion.div>
            </AnimatePresence>
          </div>
        </motion.div>
      </div>
    </div>
  );
}

const ProfileContent = () => {
  const [isLoading, setIsLoading] = useState(false);
  // Profile form
  const profileForm = useForm<ProfileFormValues>({
    resolver: zodResolver(profileFormSchema),
    defaultValues: {
      firstName: "John",
      lastName: "Doe",
      email: "<EMAIL>",
      phone: "+****************",
      location: "San Francisco, CA",
    },
  });

  const onProfileSubmit = async (data: ProfileFormValues) => {
    console.log(data);
    try {
      setIsLoading(true);
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1500));

      toast.success(
        <div>
          <div className="font-semibold">Profile updated successfully!</div>
          <div className="text-sm opacity-90">
            Your changes have been saved.
          </div>
        </div>
      );
    } catch (error) {
      console.log(error);
      toast.error(
        <div>
          <div className="font-semibold">Failed to update profile</div>
          <div className="text-sm opacity-90">Please try again later.</div>
        </div>
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ delay: 0.1 }}
      className="flex flex-col md:flex-row gap-8"
    >
      <FormProvider {...profileForm}>
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.1 }}
          className="flex flex-col md:flex-row gap-8"
        >
          {/* Avatar Section */}
          <motion.div
            whileHover={{ scale: 1.02 }}
            className="flex flex-col items-center gap-4 w-full md:w-64"
          >
            <Avatar className="h-40 w-40 border-4 border-white dark:border-gray-800 shadow-lg">
              <AvatarImage src="/profile-pic.jpg" />
              <AvatarFallback className="bg-gradient-to-br from-emerald-500 to-purple-600">
                <User className="h-20 w-20 text-white" />
              </AvatarFallback>
            </Avatar>
            <div className="flex gap-2 w-full">
              <Button variant="outline" className="cursor-pointer w-full rounded-full">
                Change Photo
              </Button>
            </div>
            <div className="w-full text-center">
              <p className="text-sm text-gray-500 dark:text-gray-400">
                JPG, GIF or PNG. Max size of 5MB
              </p>
            </div>
          </motion.div>

          {/* Profile Form */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.2 }}
            className="flex-1"
          >
            <form
              onSubmit={profileForm.handleSubmit(onProfileSubmit)}
              className="space-y-6"
            >
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* First Name */}
                <FormField
                  control={profileForm.control}
                  name="firstName"
                  render={({ field }) => (
                    <motion.div
                      whileHover={{ scale: 1.01 }}
                      className="space-y-2"
                    >
                      <FormItem>
                        <FormLabel>First Name</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage className="text-xs" />
                      </FormItem>
                    </motion.div>
                  )}
                />

                {/* Last Name */}
                <FormField
                  control={profileForm.control}
                  name="lastName"
                  render={({ field }) => (
                    <motion.div
                      whileHover={{ scale: 1.01 }}
                      className="space-y-2"
                    >
                      <FormItem>
                        <FormLabel>Last Name</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage className="text-xs" />
                      </FormItem>
                    </motion.div>
                  )}
                />
              </div>

              {/* Email */}
              <FormField
                control={profileForm.control}
                name="email"
                render={({ field }) => (
                  <motion.div
                    whileHover={{ scale: 1.01 }}
                    className="space-y-2"
                  >
                    <FormItem>
                      <FormLabel>Email</FormLabel>
                      <div className="relative">
                        <Mail className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                        <FormControl>
                          <Input type="email" className="pl-10" {...field} />
                        </FormControl>
                      </div>
                      <FormMessage className="text-xs" />
                    </FormItem>
                  </motion.div>
                )}
              />

              {/* Phone */}
              <FormField
                control={profileForm.control}
                name="phone"
                render={({ field }) => (
                  <motion.div
                    whileHover={{ scale: 1.01 }}
                    className="space-y-2"
                  >
                    <FormItem>
                      <FormLabel>Phone</FormLabel>
                      <div className="relative">
                        <Phone className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                        <FormControl>
                          <Input className="pl-10" {...field} />
                        </FormControl>
                      </div>
                      <FormMessage className="text-xs" />
                    </FormItem>
                  </motion.div>
                )}
              />

              {/* Location */}
              <FormField
                control={profileForm.control}
                name="location"
                render={({ field }) => (
                  <motion.div
                    whileHover={{ scale: 1.01 }}
                    className="space-y-2"
                  >
                    <FormItem>
                      <FormLabel>Location</FormLabel>
                      <div className="relative">
                        <MapPin className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                        <FormControl>
                          <Input className="pl-10" {...field} />
                        </FormControl>
                      </div>
                      <FormMessage className="text-xs" />
                    </FormItem>
                  </motion.div>
                )}
              />             

              {/* Submit Button */}
              <motion.div whileTap={{ scale: 0.98 }} className="pt-4">
                <Button
                  type="submit"
                  className="cursor-pointer w-full md:w-auto"
                  disabled={isLoading}
                >
                  Save Changes
                </Button>
              </motion.div>
            </form>
          </motion.div>
        </motion.div>
      </FormProvider>
    </motion.div>
  );
};

const SecurityContent = () => {
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Password form
  const passwordForm = useForm<PasswordFormValues>({
    resolver: zodResolver(passwordFormSchema),
    defaultValues: {
      currentPassword: "",
      newPassword: "",
      confirmPassword: "",
    },
  });

  const onPasswordSubmit = async (data: PasswordFormValues) => {
    console.log(data);
    try {
      setIsLoading(true);
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1500));
      passwordForm.reset();

      toast.success(
        <div>
          <div className="font-semibold">Password changed successfully!</div>
          <div className="text-sm opacity-90">
            Your new password has been saved.
          </div>
        </div>
      );
    } catch (error) {
      console.log(error);
      toast.error(
        <div>
          <div className="font-semibold">Failed to change password</div>
          <div className="text-sm opacity-90">
            Please check your current password and try again.
          </div>
        </div>
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ delay: 0.1 }}
      className="max-w-2xl mx-auto"
    >
      <FormProvider {...passwordForm}>
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.1 }}
          className="max-w-2xl mx-auto"
        >
          <form
            onSubmit={passwordForm.handleSubmit(onPasswordSubmit)}
            className="space-y-6"
          >
            <FormField
              control={passwordForm.control}
              name="currentPassword"
              render={({ field }) => (
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.2 }}
                  className="space-y-2"
                >
                  <FormItem>
                    <FormLabel>Current Password</FormLabel>
                    <div className="relative">
                      <Key className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                      <FormControl>
                        <Input
                          type={showPassword ? "text" : "password"}
                          className="pl-10 pr-8"
                          {...field}
                        />
                      </FormControl>
                      <button
                        type="button"
                        className="absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground transition-colors"
                        onClick={() => setShowPassword(!showPassword)}
                        aria-label={
                          showPassword ? "Hide password" : "Show password"
                        }
                      >
                        {showPassword ? (
                          <EyeOff className="h-4 w-4" />
                        ) : (
                          <Eye className="h-4 w-4" />
                        )}
                      </button>
                    </div>
                    <FormMessage className="text-xs" />
                  </FormItem>
                </motion.div>
              )}
            />

            <FormField
              control={passwordForm.control}
              name="newPassword"
              render={({ field }) => (
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.3 }}
                  className="space-y-2"
                >
                  <FormItem>
                    <FormLabel htmlFor="newPassword">New Password</FormLabel>
                    <div className="relative">
                      <Key className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
                      <FormControl>
                        <Input
                          id="newPassword"
                          type={showPassword ? "text" : "password"}
                          className="pl-10"
                          {...field}
                        />
                      </FormControl>
                    </div>
                    <FormMessage />
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      Must be at least 8 characters
                    </p>
                  </FormItem>
                </motion.div>
              )}
            />

            <>
              {/* Confirm Password Field */}
              <FormField
                control={passwordForm.control}
                name="confirmPassword"
                render={({ field }) => (
                  <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.4 }}
                    className="space-y-2"
                  >
                    <FormItem>
                      <div className="relative">
                        <Lock className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                        <FormLabel className="sr-only">
                          Confirm Password
                        </FormLabel>
                        <FormControl>
                          <Input
                            type={showPassword ? "text" : "password"}
                            placeholder="Confirm Password"
                            className="pl-10"
                            {...field}
                          />
                        </FormControl>
                      </div>
                      <FormMessage className="text-xs" />
                    </FormItem>
                  </motion.div>
                )}
              />

              {/* Show Password Toggle */}
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.5 }}
                className="flex items-center gap-2 pt-2 pb-4"
              >
                <Switch
                  id="showPassword"
                  checked={showPassword}
                  onCheckedChange={setShowPassword}
                />
                <Label
                  htmlFor="showPassword"
                  className="text-sm font-normal text-muted-foreground"
                >
                  Show passwords
                </Label>
              </motion.div>
            </>

            <motion.div whileTap={{ scale: 0.98 }} className="pt-4">
              <Button type="submit" className="w-full" disabled={isLoading}>
                Change Password
              </Button>
            </motion.div>
          </form>

          {/* Two-Factor Authentication */}
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 }}
            className="mt-12 pt-6 border-t border-gray-200 dark:border-gray-700"
          >
            <h3 className="text-lg font-semibold flex items-center gap-2 dark:text-white mb-4">
              <Lock className="h-5 w-5 text-emerald-500" />
              Two-Factor Authentication
            </h3>
            <div className="bg-gradient-to-r from-emerald-50 to-purple-50 dark:from-gray-700 dark:to-gray-700 rounded-lg p-4">
              <div className="flex items-start justify-between">
                <div>
                  <h4 className="font-medium dark:text-white">
                    Authenticator App
                  </h4>
                  <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                    Add an extra layer of security to your account
                  </p>
                </div>
                <Button variant="outline" className="cursor-pointer rounded-full">
                  Enable
                </Button>
              </div>
            </div>
          </motion.div>
        </motion.div>
      </FormProvider>
    </motion.div>
  );
};
