"use client";

import React, { use } from "react";
import { usePathname } from "next/navigation";
import Link from "next/link";
import { motion } from "framer-motion";
import { Bot, Workflow, MessageSquare, Settings } from "lucide-react";

interface TeamLayoutProps {
  children: React.ReactNode;
  params: Promise<{ slug: string }>;
}

export default function TeamLayout({ children, params }: TeamLayoutProps) {
  const { slug } = use(params);
  const pathname = usePathname();

  const navigationItems = [
    {
      name: "Chatbots",
      href: `/dashboard/team/${slug}`,
      icon: MessageSquare,
      description: "Traditional chatbots for customer support"
    },
    {
      name: "Workflows",
      href: `/dashboard/team/${slug}/workflows`,
      icon: Workflow,
      description: "Visual workflow builder and automation"
    },
    {
      name: "Settings",
      href: `/dashboard/team/${slug}/settings`,
      icon: Settings,
      description: "Team configuration and preferences"
    }
  ];

  const isActive = (href: string) => {
    if (href === `/dashboard/team/${slug}`) {
      return pathname === href;
    }
    return pathname.startsWith(href);
  };

  return (
    <div className="min-h-screen transition-colors duration-300">
      {/* Navigation Tabs */}
      <div className="bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 sticky top-[73px] z-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <nav className="flex space-x-8 overflow-x-auto">
            {navigationItems.map((item) => {
              const Icon = item.icon;
              const active = isActive(item.href);
              
              return (
                <Link
                  key={item.name}
                  href={item.href}
                  className="relative group"
                >
                  <motion.div
                    whileHover={{ y: -2 }}
                    className={`flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors whitespace-nowrap ${
                      active
                        ? "border-emerald-500 text-emerald-600 dark:text-emerald-400"
                        : "border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600"
                    }`}
                  >
                    <Icon className="h-5 w-5" />
                    <span>{item.name}</span>
                  </motion.div>

                  {/* Tooltip */}
                  <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900 dark:bg-gray-700 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none whitespace-nowrap z-20">
                    {item.description}
                    <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900 dark:border-t-gray-700"></div>
                  </div>
                </Link>
              );
            })}
          </nav>
        </div>
      </div>

      {/* Page Content */}
      <main className="flex-1">
        {children}
      </main>
    </div>
  );
}
