"use client";
import AgentTabs from "@/components/tabs/agent-tabs";
import { AnimatePresence, motion } from "framer-motion";
import {
  Code2,
  Puzzle,
  Share,
  ExternalLink
} from "lucide-react";
import React, { useState } from "react";
import { cn } from "@/lib/utils";
import Link from "next/link";
import { useParams } from "next/navigation";
import { CodeBlock } from "@/components/code-block/code-block";
import { CopyButton } from "@/components/copy-button/copy-button";
import IntegrationCards from "@/components/cards/intergrations-card";

type TabType = "embed" | "share" | "integrations";

const ConnectPage = () => {
  const params = useParams();
  const { id: chatbotId } = params;
  const [activeTab, setActiveTab] = useState<TabType>("embed");

  const sourceTabs = [
    { value: "embed", icon: <Code2 className="h-5 w-5" />, label: "Embed" },
    { value: "share", icon: <Share className="h-5 w-5" />, label: "Share" },
    {
      value: "integrations",
      icon: <Puzzle className="h-5 w-5" />,
      label: "Integrations",
    },
  ];

  

  return (
    <div className="min-h-screen ">
      <div className="container mx-auto px-4 py-6">
        {/* Header */}
        <header className="mb-8">
          <h1 className="text-3xl font-bold text-emerald-500 dark:text-emerald-400 mb-2">
            Agent Connect
          </h1>
          <p className="text-gray-600 dark:text-gray-300">
            Configure and test your AI assistant
          </p>
        </header>

        {/* Navigation Tabs */}
        <AgentTabs />

        {/* Main Content */}
        <div className="flex-1 max-w-8xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex flex-col lg:flex-row gap-8">
            {/* Left Sidebar */}
            <div className="lg:w-1/5">
              <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-4 sticky top-8">
                <nav className="flex flex-col space-y-2">
                  {sourceTabs.map((tab) => (
                    <motion.button
                      key={tab.value}
                      onClick={() => setActiveTab(tab.value as TabType)}
                      whileHover={{ x: 5 }}
                      whileTap={{ scale: 0.98 }}
                      className={cn(
                        "w-full px-4 py-3 cursor-pointer rounded-lg flex items-center space-x-3 transition-colors",
                        activeTab === tab.value
                          ? "bg-emerald-50 dark:bg-emerald-900/30 text-emerald-600 dark:text-emerald-400"
                          : "text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                      )}
                    >
                      <span
                        className={cn(
                          "p-2 rounded-lg",
                          activeTab === tab.value
                            ? "bg-emerald-100 dark:bg-emerald-800/50 text-emerald-600 dark:text-emerald-300"
                            : "bg-gray-100 dark:bg-gray-700 text-gray-500 dark:text-gray-400"
                        )}
                      >
                        {tab.icon}
                      </span>
                      <span className="font-medium">{tab.label}</span>
                    </motion.button>
                  ))}
                </nav>
              </div>
            </div>

            {/* Tab Content */}
            <div className="flex-1">
              <AnimatePresence mode="wait">
                <motion.div
                  key={activeTab}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  transition={{ duration: 0.2 }}
                  className="bg-white dark:bg-gray-800 rounded-xl shadow-sm overflow-hidden"
                >
                  {/* Embed Tab */}
                  {activeTab === "embed" && (
                    <div className="p-6">
                      <div className="card card-flush mb-6 mb-xl-9 h-md-100">
                        <div className="card-header pt-7">
                          <h3 className="card-title align-items-start flex-column">
                            <span className="card-label fw-bold text-gray-800 dark:text-white">
                              Embed iframe
                            </span>
                            <p className="text-gray-400 mt-1 fw-semibold fs-6">
                              To add the chatbot anywhere on your website, add
                              this iframe to your html code where you want the
                              chatbot to appear.
                            </p>
                          </h3>
                        </div>
                        <div className="card-body pt-6">
                          <div className="relative row mb-8">
                            <div className="col-xl-12 fv-row bg-light dark:bg-gray-700 p-4 rounded-md relative">
                              <CodeBlock>
                                {`<iframe
  src="https://www.chatzuri.com/p/iframe/${chatbotId}"
  width="100%"
  style="height: 100%; min-height: 800px"
  frameborder="0"
></iframe>`}
                              </CodeBlock>

                              <CopyButton
                                textToCopy={`<iframe
  src="https://www.chatzuri.com/p/iframe/${chatbotId}"
  width="100%"
  style="height: 100%; min-height: 800px"
  frameborder="0"
></iframe>`}
                                buttonId="iframe-copy-btn"
                                classNames="absolute top-3 right-3"
                              />
                            </div>
                          </div>

                          <div className="row mb-8">
                            <span className="card-label fw-bold text-gray-800 dark:text-white">
                              Embed Chat Bubble
                            </span>
                            <p className="text-gray-400 mt-1 fw-semibold fs-6">
                              To add a chat bubble to the bottom right or left
                              of your website add this script tag to your html
                            </p>

                            <div className="relative col-xl-12 fv-row bg-light dark:bg-gray-700 p-4 rounded mt-2">
                              <CodeBlock>
                                {`<script>
  window.embeddedChatbotConfig = {
    chatbotId: "${chatbotId}",
    domain: "www.chatzuri.com"
  };
</script>
<script
  src="https://www.chatzuri.com/assets/js/bubble_embed.js"
  chatbotId="${chatbotId}"
  domain="www.chatzuri.com"
  defer>
</script>`}
                              </CodeBlock>

                              <CopyButton
                                textToCopy={`<script>
window.embeddedChatbotConfig = {
  chatbotId: "${chatbotId}",
  domain: "www.chatzuri.com"
};
</script>
<script
  src="https://www.chatzuri.com/assets/js/bubble_embed.js"
  chatbotId="${chatbotId}"
  domain="www.chatzuri.com"
  defer>
</script>`}
                                buttonId="iframe-script-copy-btn"
                                classNames="absolute top-3 right-3"
                              />
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Share Tab */}
                  {activeTab === "share" && (
                    <div className="p-6">
                      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm overflow-hidden border border-gray-200 dark:border-gray-700">
                        {/* Header */}
                        <div className="px-6 py-5 border-b border-gray-100 dark:border-gray-700">
                          <div className="flex flex-col">
                            <h3 className="text-xl font-bold text-gray-800 dark:text-white flex items-center">
                              <Share className="h-5 w-5 text-emerald-500 mr-2" />
                              Share Your Chatbot
                            </h3>
                            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                              Share your chatbot via direct link or embed it on
                              your website
                            </p>
                          </div>
                        </div>

                        {/* Content */}
                        <div className="p-6">
                          <div className="space-y-4">
                            {/* URL Section */}
                            <div>
                              <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Direct Link
                              </h4>
                              <div className="flex items-center gap-2">
                                <div className="flex-1 bg-gray-50 dark:bg-gray-700 rounded-lg p-3 overflow-x-auto">
                                  <code className="text-sm font-mono text-gray-800 dark:text-gray-200 break-all">
                                    https://www.chatzuri.com/p/iframe/
                                    {chatbotId}
                                  </code>
                                </div>
                                <CopyButton
                                  textToCopy={`https://www.chatzuri.com/p/iframe/${chatbotId}`}
                                  buttonId="external-wyhui--copy-btn"
                                  classNames="flex-shrink-0 px-4 py-2"
                                />
                              </div>
                            </div>

                            {/* Visit Button */}
                            <div className="pt-2">
                              <Link
                                href={`https://www.chatzuri.com/p/iframe/${chatbotId}`}
                                target="_blank"
                                className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                              >
                                <span>Open in new tab</span>
                                <ExternalLink className="h-4 w-4 ml-2" />
                              </Link>
                            </div>

                            {/* QR Code Section (optional) */}
                            <div className="pt-6 border-t border-gray-100 dark:border-gray-700 rounded-md">
                              <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                                Mobile Sharing
                              </h4>
                              <div className="flex items-start gap-4">
                                <div className="bg-white p-2 rounded border border-gray-200 dark:border-gray-600">
                                  {/* Placeholder for QR code - you can use a QR code generator library */}
                                  <div className="w-24 h-24 bg-gray-100 dark:bg-gray-600 flex items-center justify-center text-xs text-gray-400">
                                    QR Code
                                  </div>
                                </div>
                                <div className="flex-1">
                                  <p className="text-sm text-gray-600 dark:text-gray-400">
                                    Scan this QR code to open the chatbot on
                                    mobile devices or share it with others.
                                  </p>
                                  <button className="mt-2 cursor-pointer text-sm text-emerald-600 dark:text-emerald-400 hover:text-emerald-700 dark:hover:text-emerald-500">
                                    Download QR Code
                                  </button>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Integrations Tab */}
                {activeTab === "integrations" && <IntegrationCards />}
                </motion.div>
              </AnimatePresence>
            </div>
          </div>
        </div>
      </div>

      <style jsx>{`
        .code-box {
          border: 1px solid #ccc;
          padding: 15px;
          background-color: #f4f4f4;
          font-family: monospace;
          white-space: pre-wrap;
          word-wrap: break-word;
          border-radius: 4px;
        }
        .dark .code-box {
          background-color: #2d3748;
          border-color: #4a5568;
        }
        .copy-btn {
          margin-top: 10px;
          padding: 8px 12px;
          background-color: #28a745;
          color: white;
          border: none;
          cursor: pointer;
          border-radius: 4px;
        }
        .copy-btn:hover {
          background-color: #218838;
        }
      `}</style>
    </div>
  );
};

export default ConnectPage;
