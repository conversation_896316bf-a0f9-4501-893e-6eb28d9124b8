"use client";

import { useState } from "react";
import Image from "next/image";
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON>,
  CardHeader,
  CardT<PERSON>le,
  CardContent,
  CardDescription,
} from "@/components/ui/card";
import {
  Table,
  TableHeader,
  TableRow,
  TableHead,
  TableBody,
  TableCell,
} from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { AnimatePresence, motion } from "framer-motion";
import {
  MessageSquare,
  Users,
  BarChart2,
  Settings,
  Globe,
  Upload,
  Trash2,
} from "lucide-react";
import { cn } from "@/lib/utils";
import AgentTabs from "@/components/tabs/agent-tabs";
import { Agent, Lead, Message, Thread } from "@/types/types";




const AgentActivity = () => {
  const agent: Agent = { _id: "", url: "", name: "agent" };
  const messages: Message[] = [];
  const leads: Lead[] = [];
  const threads: Thread[] = [];
  const [activeTab, setActiveTab] = useState("chatLogs");
  const [selectedThread, setSelectedThread] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");

  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString("en-US", {
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const formatDate = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
    });
  };

  const filteredMessages = selectedThread
    ? messages.filter((message) => message.conversationId === selectedThread)
    : [];

  // Activity tabs
  const activityTabs = [
    {
      value: "chatLogs",
      icon: <MessageSquare className="h-5 w-5" />,
      label: "Chat Logs",
    },
    { value: "leads", icon: <Users className="h-5 w-5" />, label: "Leads" },
    {
      value: "analytics",
      icon: <BarChart2 className="h-5 w-5" />,
      label: "Analytics",
    },
  ];

  return (
    <div className="min-h-screen">
      <div className="container mx-auto px-4 py-3 mb-6">
        <header>
          <h1 className="text-3xl font-bold text-emerald-500 dark:text-emerald-400 mb-2">
            Agent Activity
          </h1>
          <p className="text-gray-600 dark:text-gray-300">
            Monitor your agent&apos;s interactions and performance
          </p>
        </header>
      </div>

      {/* Main Content Container - Wider */}
      <div className="container mx-auto">
        {/* Navigation Tabs */}
        <AgentTabs />
        <div className="flex flex-col md:flex-row gap-6 py-6">
          {/* Left Column */}
          <div className="w-full md:w-3/4 bg-white dark:bg-gray-800 rounded-lg p-6 shadow">
            <div className="flex-1">
              <AnimatePresence mode="wait">
                <motion.div
                  key={activeTab}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  transition={{ duration: 0.2 }}
                  className="bg-white dark:bg-gray-800 rounded-xl shadow-sm overflow-hidden"
                >
                  {/* Chat Logs Tab */}
                  {activeTab === "chatLogs" && (
                    <div className="p-6">
                      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        {/* Threads List */}
                        <Card className="border-0 shadow-sm">
                          <CardHeader className="border-b">
                            <CardTitle className="flex items-center gap-2">
                              <MessageSquare className="h-5 w-5 text-muted-foreground" />
                              Recent Conversations
                            </CardTitle>
                          </CardHeader>
                          <CardContent className="p-0">
                            <div className="divide-y">
                              {threads.map((thread) => (
                                <motion.div
                                  key={thread._id}
                                  initial={{ opacity: 0 }}
                                  animate={{ opacity: 1 }}
                                  className={`p-4 cursor-pointer transition-colors hover:bg-muted/50 ${
                                    selectedThread === thread._id
                                      ? "bg-muted/50"
                                      : ""
                                  }`}
                                  onClick={() => setSelectedThread(thread._id)}
                                >
                                  <div className="flex justify-between items-start gap-2">
                                    <div className="flex-1 min-w-0">
                                      <p className="font-medium truncate">
                                        {thread.lastUserMessage}
                                      </p>
                                      <p className="text-sm text-muted-foreground truncate">
                                        {thread.lastAsistantMessage}
                                      </p>
                                    </div>
                                    <div className="flex flex-col items-end">
                                      <span className="text-xs text-muted-foreground whitespace-nowrap">
                                        {formatTime(thread.lastSeen)}
                                      </span>
                                      <Badge
                                        variant="outline"
                                        className="mt-1 text-xs"
                                      >
                                        {thread.source || "Web"}
                                      </Badge>
                                    </div>
                                  </div>
                                </motion.div>
                              ))}
                            </div>
                          </CardContent>
                        </Card>

                        {/* Messages */}
                        <Card className="border-0 shadow-sm lg:col-span-2">
                          <CardHeader className="border-b">
                            <CardTitle>
                              {selectedThread ? (
                                <div className="flex items-center gap-3">
                                  <div className="bg-primary/10 p-2 rounded-full">
                                    <MessageSquare className="h-5 w-5 text-primary" />
                                  </div>
                                  <div>
                                    Conversation #{selectedThread.slice(0, 6)}
                                    <CardDescription className="text-sm">
                                      {formatDate(
                                        threads.find(
                                          (t) => t._id === selectedThread
                                        )?.lastSeen || ""
                                      )}
                                    </CardDescription>
                                  </div>
                                </div>
                              ) : (
                                "Select a conversation"
                              )}
                            </CardTitle>
                          </CardHeader>
                          <CardContent className="p-6 h-[500px] overflow-y-auto bg-muted/10 rounded-b-lg">
                            {filteredMessages.length > 0 ? (
                              <div className="space-6">
                                {filteredMessages.map((message, index) => (
                                  <motion.div
                                    key={index}
                                    initial={{ opacity: 0, y: 10 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    className={`flex mb-6 ${
                                      message.role === "assistant"
                                        ? "justify-start"
                                        : "justify-end"
                                    }`}
                                  >
                                    <div
                                      className={`max-w-[80%] p-4 rounded-xl ${
                                        message.role === "assistant"
                                          ? "bg-background border"
                                          : "bg-primary text-primary-foreground"
                                      }`}
                                    >
                                      <div className="flex items-start gap-3 mb-3">
                                        {message.role === "assistant" ? (
                                          <Image
                                            src={
                                              agent.iconPath ||
                                              "/assets/media/avatars/blank.png"
                                            }
                                            alt="agent"
                                            width={32}
                                            height={32}
                                            className="w-8 h-8 rounded-full mt-1 flex-shrink-0"
                                          />
                                        ) : (
                                          <div className="bg-primary-foreground/20 p-1.5 rounded-full flex-shrink-0">
                                            <Users className="h-5 w-5" />
                                          </div>
                                        )}
                                        <div className="flex-1">
                                          <div className="flex items-center gap-2 mb-1">
                                            <p className="font-medium">
                                              {message.role === "assistant"
                                                ? agent.name
                                                : "You"}
                                            </p>
                                            <span className="text-xs text-muted-foreground">
                                              {formatTime(message.createdAt)}
                                            </span>
                                          </div>
                                          <p className="whitespace-pre-wrap">
                                            {message.content}
                                          </p>
                                        </div>
                                      </div>
                                    </div>
                                  </motion.div>
                                ))}
                              </div>
                            ) : (
                              <div className="flex flex-col items-center justify-center h-full text-muted-foreground">
                                <MessageSquare className="h-12 w-12 mb-4" />
                                {selectedThread
                                  ? "No messages in this conversation"
                                  : "Select a conversation to view messages"}
                              </div>
                            )}
                          </CardContent>
                        </Card>
                      </div>
                    </div>
                  )}

                  {/* Leads Tab */}
                  {activeTab === "leads" && (
                    <div className="p-6">
                      <Card className="border-0 shadow-sm">
                        <CardHeader>
                          <div className="flex flex-col md:flex-row md:justify-between md:items-center gap-4">
                            <div>
                              <CardTitle>Leads Captured</CardTitle>
                              <CardDescription>
                                Contacts generated from your agent
                                interactions
                              </CardDescription>
                            </div>
                            <div className="flex gap-2">
                              <Input
                                placeholder="Search leads..."
                                className="max-w-xs"
                                value={searchTerm}
                                onChange={(e) => setSearchTerm(e.target.value)}
                              />
                              <Button variant="outline">
                                <Upload className="h-4 w-4 mr-2" />
                                Export
                              </Button>
                            </div>
                          </div>
                        </CardHeader>
                        <CardContent>
                          <Table>
                            <TableHeader>
                              <TableRow>
                                <TableHead>Name</TableHead>
                                <TableHead>Contact</TableHead>
                                <TableHead>Source</TableHead>
                                <TableHead>Date</TableHead>
                                <TableHead className="text-right">
                                  Actions
                                </TableHead>
                              </TableRow>
                            </TableHeader>
                            <TableBody>
                              {leads
                                .filter(
                                  (lead) =>
                                    lead.title
                                      .toLowerCase()
                                      .includes(searchTerm.toLowerCase()) ||
                                    lead.name
                                      .toLowerCase()
                                      .includes(searchTerm.toLowerCase()) ||
                                    lead.email
                                      .toLowerCase()
                                      .includes(searchTerm.toLowerCase())
                                )
                                .map((lead, index) => (
                                  <TableRow
                                    key={index}
                                    className="hover:bg-muted/50"
                                  >
                                    <TableCell>
                                      <div className="font-medium">
                                        {lead.name}
                                      </div>
                                      <div className="text-sm text-muted-foreground">
                                        {lead.title}
                                      </div>
                                    </TableCell>
                                    <TableCell>
                                      <div className="font-medium">
                                        {lead.email}
                                      </div>
                                      <div className="text-sm text-muted-foreground">
                                        {lead.phone}
                                      </div>
                                    </TableCell>
                                    <TableCell>
                                      <Badge variant="outline">Website</Badge>
                                    </TableCell>
                                    <TableCell>
                                      {formatDate(lead.createdAt)}
                                    </TableCell>
                                    <TableCell className="text-right">
                                      <Button variant="ghost" size="sm">
                                        <Trash2 className="h-4 w-4" />
                                      </Button>
                                    </TableCell>
                                  </TableRow>
                                ))}
                            </TableBody>
                          </Table>
                        </CardContent>
                      </Card>
                    </div>
                  )}

                  {/* Analytics Tab */}
                  {activeTab === "analytics" && (
                    <div className="p-6">
                      <Card className="border-0 shadow-sm">
                        <CardHeader>
                          <CardTitle>Analytics Dashboard</CardTitle>
                          <CardDescription>
                            Performance metrics and insights
                          </CardDescription>
                        </CardHeader>
                        <CardContent>
                          <div className="text-center py-12">
                            <div className="mx-auto max-w-md">
                              <BarChart2 className="h-16 w-16 mx-auto text-muted-foreground mb-4" />
                              <h3 className="text-xl font-semibold mb-2">
                                Unlock Advanced Analytics
                              </h3>
                              <p className="text-muted-foreground mb-6">
                                Upgrade your plan to access detailed analytics,
                                conversation trends, and performance metrics.
                              </p>
                              <Button className="gap-2">
                                Upgrade Plan
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  className="h-4 w-4"
                                  viewBox="0 0 24 24"
                                  fill="none"
                                  stroke="currentColor"
                                  strokeWidth="2"
                                >
                                  <path d="M5 12h14"></path>
                                  <path d="M12 5l7 7-7 7"></path>
                                </svg>
                              </Button>
                            </div>
                          </div>

                          {/* Placeholder charts */}
                          <div className="space-y-6 opacity-30 pointer-events-none">
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                              <Card className="p-6">
                                <div className="h-40 bg-blue-50 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
                                  <p className="text-muted-foreground">
                                    Conversations Chart
                                  </p>
                                </div>
                              </Card>
                              <Card className="p-6">
                                <div className="h-40 bg-green-50 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
                                  <p className="text-muted-foreground">
                                    Engagement Chart
                                  </p>
                                </div>
                              </Card>
                              <Card className="p-6">
                                <div className="h-40 bg-purple-50 dark:bg-purple-900/20 rounded-lg flex items-center justify-center">
                                  <p className="text-muted-foreground">
                                    Leads Chart
                                  </p>
                                </div>
                              </Card>
                            </div>

                            <Card>
                              <CardHeader>
                                <CardTitle>Geographic Distribution</CardTitle>
                                <CardDescription>
                                  Where your users are coming from
                                </CardDescription>
                              </CardHeader>
                              <CardContent>
                                <div className="h-80 bg-muted/20 rounded-lg flex items-center justify-center">
                                  <p className="text-muted-foreground">
                                    Map Visualization
                                  </p>
                                </div>
                              </CardContent>
                            </Card>
                          </div>
                        </CardContent>
                      </Card>
                    </div>
                  )}
                </motion.div>
              </AnimatePresence>
            </div>
          </div>

          {/* Right Column */}
          <div className="w-full md:w-1/4 rounded-lg p-6 shadow space-y-6">
            {/* Activity Type Tabs */}
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-4 sticky top-8">
              <nav className="flex flex-col space-y-2">
                {activityTabs.map((tab) => (
                  <motion.button
                    key={tab.value}
                    onClick={() => setActiveTab(tab.value)}
                    whileHover={{ x: 5 }}
                    whileTap={{ scale: 0.98 }}
                    className={cn(
                      "w-full px-4 py-3 cursor-pointer rounded-lg flex items-center space-x-3 transition-colors",
                      activeTab === tab.value
                        ? "bg-emerald-50 dark:bg-emerald-900/30 text-emerald-600 dark:text-emerald-400"
                        : "text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                    )}
                  >
                    <span
                      className={cn(
                        "p-2 rounded-lg",
                        activeTab === tab.value
                          ? "bg-emerald-100 dark:bg-emerald-800/50 text-emerald-600 dark:text-emerald-300"
                          : "bg-gray-100 dark:bg-gray-700 text-gray-500 dark:text-gray-400"
                      )}
                    >
                      {tab.icon}
                    </span>
                    <span className="font-medium">{tab.label}</span>
                  </motion.button>
                ))}
              </nav>
            </div>

            {/* Activity Summary - with top border for separation */}
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6 sticky top-12">
              <h3 className="text-lg font-bold text-gray-800 dark:text-white mb-6 text-center">
                Activity Summary
              </h3>

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <MessageSquare className="h-5 w-5 text-emerald-500" />
                    <span className="text-gray-600 dark:text-gray-300">
                      Conversations
                    </span>
                  </div>
                  <span className="font-medium text-gray-800 dark:text-white">
                    {threads.length}
                  </span>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Users className="h-5 w-5 text-emerald-500" />
                    <span className="text-gray-600 dark:text-gray-300">
                      Leads Captured
                    </span>
                  </div>
                  <span className="font-medium text-gray-800 dark:text-white">
                    {leads.length}
                  </span>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Globe className="h-5 w-5 text-emerald-500" />
                    <span className="text-gray-600 dark:text-gray-300">
                      Sources
                    </span>
                  </div>
                  <span className="font-medium text-gray-800 dark:text-white">
                    3
                  </span>
                </div>

                <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <BarChart2 className="h-5 w-5 text-emerald-500" />
                      <span className="text-gray-600 dark:text-gray-300">
                        Avg. Response Time
                      </span>
                    </div>
                    <span className="font-medium text-gray-800 dark:text-white">
                      2.4s
                    </span>
                  </div>
                </div>

                <Button className="cursor-pointer w-full mt-6" variant="outline">
                  <Settings className="h-5 w-5 mr-2" />
                  View Settings
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AgentActivity;
