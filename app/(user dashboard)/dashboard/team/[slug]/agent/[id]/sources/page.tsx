"use client";
import AgentTabs from "@/components/tabs/agent-tabs";
import { AnimatePresence, motion } from "framer-motion";
import {
  FileInput,
  FileOutput,
  FileText,
  Globe,
  Plus,
  Trash2,
  Upload,
} from "lucide-react";
import { cn } from "@/lib/utils";
import Dropzone from "react-dropzone";
import { useParams } from "next/navigation";
import React, { useState } from "react";
import axios from "axios";

type TrainingDataEntry = {
  _id: string;
  sourceType: "file" | "text" | "url" | "qa";
  content: {
    fileName?: string;
    characters: number;
  };
  uploadedForTraining?: boolean;
};

type StatItemProps = {
  label: string;
  value: React.ReactNode;
  icon: React.ReactNode;
};

interface WebsiteItemProps {
  url: TrainingData;
}

interface TrainingData {
  _id: string;
  content: {
    text?: string;
    url?: string;
    question?: string;
    answer?: string;
    fileName?: string;
    characters: number;
  };
  sourceType: "file" | "text" | "url" | "qa";
  uploadedForTraining?: boolean;
}

interface TrainingData {
  _id: string;
  sourceType: "file" | "text" | "url" | "qa";
  uploadedForTraining?: boolean; // Made optional with ?
  content: {
    text?: string;
    url?: string;
    question?: string;
    answer?: string;
    fileName?: string;
    characters: number;
    // Add any other possible content fields here
  };
}

interface TextItemProps {
  text: TrainingData;
}

interface QnAItemProps {
  qa: TrainingData;
}

const SourceManagement = () => {
  const params = useParams();
  const { slug: teamUrl, id: agentId } = params;
  const [activeTab, setActiveTab] = useState("files");

  const [trainingData, setTrainingData] = useState<TrainingDataEntry[]>([]);
  const [isTraining, setIsTraining] = useState<boolean>(false);
  // Source type tabs
  const sourceTabs = [
    { value: "files", icon: <FileInput className="h-5 w-5" />, label: "Files" },
    { value: "text", icon: <FileText className="h-5 w-5" />, label: "Text" },
    { value: "website", icon: <Globe className="h-5 w-5" />, label: "Website" },
    { value: "qna", icon: <FileOutput className="h-5 w-5" />, label: "Q&A" },
  ];

  // Calculate character counts
  const calculateStats = () => {
    const stats = {
      files: 0,
      text: 0,
      website: 0,
      qna: 0,
      total: 0,
      count: trainingData.length,
    };

    trainingData.forEach((item) => {
      const chars = item.content.characters || 0;
      stats.total += chars;

      switch (item.sourceType) {
        case "file":
          stats.files += chars;
          break;
        case "text":
          stats.text += chars;
          break;
        case "url":
          stats.website += chars;
          break;
        case "qa":
          stats.qna += chars;
          break;
      }
    });

    return stats;
  };

  const stats = calculateStats();

  interface UploadResponse {
    trainingDataEntries: TrainingDataEntry[];
  }

  const onDrop = async (acceptedFiles: File[]) => {
    const formData = new FormData();
    acceptedFiles.forEach((file: File) => formData.append("files", file));

    try {
      const response = await axios.post<UploadResponse>(
        `/dashboard/${teamUrl}/${agentId}/upload-file`,
        formData,
        { headers: { "Content-Type": "multipart/form-data" } }
      );
      setTrainingData([...trainingData, ...response.data.trainingDataEntries]);
    } catch (error) {
      console.error("Upload failed:", error);
    }
  };

  // Train agent
  const trainAgent = async () => {
    setIsTraining(true);
    try {
      await axios.post(`/dashboard/${teamUrl}/${agentId}/train`);
      // Handle success
    } catch (error) {
      console.error("Training failed:", error);
    } finally {
      setIsTraining(false);
    }
  };

  return (
    <div className="min-h-screen">
      <div className="container mx-auto px-4 py-6">
        {/* Header */}
        <header className="mb-8">
          <h1 className="text-3xl font-bold text-emerald-500 dark:text-emerald-400 mb-2">
            Agent Sources
          </h1>
          <p className="text-gray-600 dark:text-gray-300">
            Configure and test your AI assistant
          </p>
        </header>

        {/* Navigation Tabs */}
        <AgentTabs />

        {/* Main Content */}
        <div className="flex-1 max-w-8xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex flex-col lg:flex-row gap-8">
            {/* Left Column (75%) */}
            <div className="flex flex-row gap-3 lg:w-3/4 ">
              {/* Source Type Tabs */}
              <div className="lg:w-1/5">
                <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-4 sticky top-8">
                  <nav className="flex flex-col space-y-2">
                    {sourceTabs.map((tab) => (
                      <motion.button
                        key={tab.value}
                        onClick={() => setActiveTab(tab.value)}
                        whileHover={{ x: 5 }}
                        whileTap={{ scale: 0.98 }}
                        className={cn(
                          "w-full px-4 py-3 cursor-pointer rounded-lg flex items-center space-x-3 transition-colors",
                          activeTab === tab.value
                            ? "bg-emerald-50 dark:bg-emerald-900/30 text-emerald-600 dark:text-emerald-400"
                            : "text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                        )}
                      >
                        <span
                          className={cn(
                            "p-2 rounded-lg",
                            activeTab === tab.value
                              ? "bg-emerald-100 dark:bg-emerald-800/50 text-emerald-600 dark:text-emerald-300"
                              : "bg-gray-100 dark:bg-gray-700 text-gray-500 dark:text-gray-400"
                          )}
                        >
                          {tab.icon}
                        </span>
                        <span className="font-medium">{tab.label}</span>
                      </motion.button>
                    ))}
                  </nav>
                </div>
              </div>

              {/* Tab Content */}
              <div className="flex-1">
                <AnimatePresence mode="wait">
                  <motion.div
                    key={activeTab}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    transition={{ duration: 0.2 }}
                    className="bg-white dark:bg-gray-800 rounded-xl shadow-sm overflow-hidden"
                  >
                    {/* Files Tab */}
                    {activeTab === "files" && (
                      <div className="p-6">
                        <h3 className="text-lg font-bold text-gray-800 dark:text-white mb-4">
                          Upload Files
                        </h3>
                        <Dropzone onDrop={onDrop}>
                          {({ getRootProps, getInputProps }) => (
                            <div
                              {...getRootProps()}
                              className="border-2 border-dashed border-emerald-300 dark:border-emerald-700 rounded-lg p-8 text-center cursor-pointer hover:bg-emerald-50 dark:hover:bg-emerald-900/20 transition-colors"
                            >
                              <input {...getInputProps()} />
                              <Upload className="h-10 w-10 mx-auto text-emerald-500 mb-3" />
                              <p className="text-gray-700 dark:text-gray-300 font-medium">
                                Drag & drop files here, or click to select files
                              </p>
                              <p className="text-sm text-gray-500 dark:text-gray-400 mt-2">
                                Supported formats: PDF, DOCX, TXT
                              </p>
                            </div>
                          )}
                        </Dropzone>

                        <div className="mt-8">
                          <h4 className="text-md font-semibold text-gray-700 dark:text-gray-300 mb-4">
                            Included Files ({stats.files} chars)
                          </h4>
                          {trainingData.filter((d) => d.sourceType === "file")
                            .length > 0 ? (
                            <div className="space-y-3">
                              {trainingData
                                .filter((d) => d.sourceType === "file")
                                .map((file) => (
                                  <FileItem key={file._id} file={file} />
                                ))}
                            </div>
                          ) : (
                            <p className="text-gray-500 dark:text-gray-400">
                              No files uploaded yet
                            </p>
                          )}
                        </div>
                      </div>
                    )}
                    {/* Text Tab */}
                    {activeTab === "text" && (
                      <div className="p-6">
                        <h3 className="text-lg font-bold text-gray-800 dark:text-white mb-4">
                          Add Text Content
                        </h3>
                        <div className="space-y-4">
                          <textarea
                            className="w-full h-64 p-4 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-800 dark:text-gray-200"
                            placeholder="Paste your text content here..."
                          />
                          <div className="flex justify-end">
                            <motion.button
                              whileHover={{ scale: 1.02 }}
                              whileTap={{ scale: 0.98 }}
                              className="px-6 py-2 bg-emerald-600 cursor-pointer hover:bg-emerald-700 text-white font-medium rounded-lg"
                            >
                              Save Text
                            </motion.button>
                          </div>
                        </div>

                        <div className="mt-8">
                          <h4 className="text-md font-semibold text-gray-700 dark:text-gray-300 mb-4">
                            Included Text ({stats.text} chars)
                          </h4>
                          {trainingData.filter((d) => d.sourceType === "text")
                            .length > 0 ? (
                            <div className="space-y-3">
                              {trainingData
                                .filter((d) => d.sourceType === "text")
                                .map((text) => (
                                  <TextItem key={text._id} text={text} />
                                ))}
                            </div>
                          ) : (
                            <p className="text-gray-500 dark:text-gray-400">
                              No text content added yet
                            </p>
                          )}
                        </div>
                      </div>
                    )}

                    {/* Website Tab */}
                    {activeTab === "website" && (
                      <div className="p-6">
                        <h3 className="text-lg font-bold text-gray-800 dark:text-white mb-4">
                          Add Website Content
                        </h3>
                        <div className="space-y-6">
                          <div className="space-y-2">
                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                              Website URL
                            </label>
                            <div className="flex gap-2">
                              <input
                                type="url"
                                className="flex-1 p-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-800 dark:text-gray-200"
                                placeholder="https://example.com"
                              />
                              <motion.button
                                whileHover={{ scale: 1.02 }}
                                whileTap={{ scale: 0.98 }}
                                className="px-4 py-2 bg-emerald-600 cursor-pointer hover:bg-emerald-700 text-white font-medium rounded-lg"
                              >
                                Crawl
                              </motion.button>
                            </div>
                          </div>

                          <div className="space-y-2">
                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                              Or Sitemap URL
                            </label>
                            <div className="flex gap-2">
                              <input
                                type="url"
                                className="flex-1 p-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-800 dark:text-gray-200"
                                placeholder="https://example.com/sitemap.xml"
                              />
                              <motion.button
                                whileHover={{ scale: 1.02 }}
                                whileTap={{ scale: 0.98 }}
                                className="px-4 py-2 bg-emerald-600 cursor-pointer hover:bg-emerald-700 text-white font-medium rounded-lg"
                              >
                                Load
                              </motion.button>
                            </div>
                          </div>
                        </div>

                        <div className="mt-8">
                          <h4 className="text-md font-semibold text-gray-700 dark:text-gray-300 mb-4">
                            Included Websites ({stats.website} chars)
                          </h4>
                          {trainingData.filter((d) => d.sourceType === "url")
                            .length > 0 ? (
                            <div className="space-y-3">
                              {trainingData
                                .filter((d) => d.sourceType === "url")
                                .map((url) => (
                                  <WebsiteItem key={url._id} url={url} />
                                ))}
                            </div>
                          ) : (
                            <p className="text-gray-500 dark:text-gray-400">
                              No website content added yet
                            </p>
                          )}
                        </div>
                      </div>
                    )}

                    {/* Q&A Tab */}
                    {activeTab === "qna" && (
                      <div className="p-6">
                        <h3 className="text-lg font-bold text-gray-800 dark:text-white mb-4">
                          Add Q&A Pairs
                        </h3>
                        <div className="space-y-4">
                          <div className="space-y-2">
                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                              Question
                            </label>
                            <input
                              type="text"
                              className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-800 dark:text-gray-200"
                              placeholder="Enter question"
                            />
                          </div>
                          <div className="space-y-2">
                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                              Answer
                            </label>
                            <textarea
                              className="w-full h-32 p-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-800 dark:text-gray-200"
                              placeholder="Enter answer"
                            />
                          </div>
                          <div className="flex justify-end">
                            <motion.button
                              whileHover={{ scale: 1.02 }}
                              whileTap={{ scale: 0.98 }}
                              className="px-6 py-2 bg-emerald-600 cursor-pointer hover:bg-emerald-700 text-white font-medium rounded-lg"
                            >
                              Add Q&A
                            </motion.button>
                          </div>
                        </div>

                        <div className="mt-8">
                          <h4 className="text-md font-semibold text-gray-700 dark:text-gray-300 mb-4">
                            Included Q&A Pairs ({stats.qna} chars)
                          </h4>
                          {trainingData.filter((d) => d.sourceType === "qa")
                            .length > 0 ? (
                            <div className="space-y-4">
                              {trainingData
                                .filter((d) => d.sourceType === "qa")
                                .map((qa) => (
                                  <QnAItem key={qa._id} qa={qa} />
                                ))}
                            </div>
                          ) : (
                            <p className="text-gray-500 dark:text-gray-400">
                              No Q&A pairs added yet
                            </p>
                          )}
                        </div>
                      </div>
                    )}
                  </motion.div>
                </AnimatePresence>
              </div>
            </div>

            {/* Right Column (25%) */}
            <div className="lg:w-1/4">
              <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6 sticky top-8">
                <h3 className="text-lg font-bold text-gray-800 dark:text-white mb-6 text-center">
                  Data Sources Summary
                </h3>

                <div className="space-y-4">
                  <StatItem
                    label="Files"
                    value={`${stats.files} chars`}
                    icon={<FileInput className="h-5 w-5 text-emerald-500" />}
                  />
                  <StatItem
                    label="Text"
                    value={`${stats.text} chars`}
                    icon={<FileText className="h-5 w-5 text-emerald-500" />}
                  />
                  <StatItem
                    label="Website"
                    value={`${stats.website} chars`}
                    icon={<Globe className="h-5 w-5 text-emerald-500" />}
                  />
                  <StatItem
                    label="Q&A"
                    value={`${stats.qna} chars`}
                    icon={<FileOutput className="h-5 w-5 text-emerald-500" />}
                  />

                  <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
                    <StatItem
                      label="Total Sources"
                      value={stats.count}
                      icon={<Plus className="h-5 w-5 text-emerald-500" />}
                    />
                    <StatItem
                      label="Total Characters"
                      value={`${stats.total} / 100,000 limit`}
                      icon={<FileText className="h-5 w-5 text-emerald-500" />}
                    />
                  </div>

                  <motion.button
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    onClick={trainAgent}
                    disabled={isTraining}
                    className={cn(
                      "w-full mt-6 cursor-pointer py-3 px-4 bg-emerald-600 hover:bg-emerald-700 text-white font-medium rounded-lg transition-colors",
                      isTraining && "opacity-70 cursor-not-allowed"
                    )}
                  >
                    {isTraining ? "Training..." : "Train Agent"}
                  </motion.button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Reusable components
const FileItem = ({ file }: { file: TrainingDataEntry }) => (
  <motion.div
    initial={{ opacity: 0 }}
    animate={{ opacity: 1 }}
    className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg"
  >
    <div className="flex items-center space-x-3">
      <div className="p-2 bg-emerald-100 dark:bg-emerald-900/50 rounded-lg">
        <FileText className="h-5 w-5 text-emerald-600 dark:text-emerald-400" />
      </div>
      <div>
        <p className="font-medium text-gray-800 dark:text-gray-200">
          {file.content.fileName}
        </p>
        <p className="text-sm text-gray-500 dark:text-gray-400">
          {file.content.characters} chars •{" "}
          {file.uploadedForTraining ? "Trained" : "Uploaded"}
        </p>
      </div>
    </div>
    <button className="text-red-500 hover:text-red-700 cursor-pointer">
      <Trash2 className="h-5 w-5" />
    </button>
  </motion.div>
);

const StatItem = ({ label, value, icon }: StatItemProps) => (
  <div className="flex items-center justify-between">
    <div className="flex items-center space-x-2">
      {icon}
      <span className="text-gray-600 dark:text-gray-300">{label}</span>
    </div>
    <span className="font-medium text-gray-800 dark:text-white">{value}</span>
  </div>
);

const TextItem = ({ text }: TextItemProps) => (
  <motion.div
    initial={{ opacity: 0 }}
    animate={{ opacity: 1 }}
    className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg"
  >
    <div className="flex-1">
      <p className="text-sm text-gray-500 dark:text-gray-400 line-clamp-2">
        {text.content.text}
      </p>
      <p className="text-xs text-gray-400 dark:text-gray-500 mt-1">
        {text.content.characters} chars •{" "}
        {text.uploadedForTraining ? "Trained" : "Uploaded"}
      </p>
    </div>
    <button className="text-red-500 hover:text-red-700 ml-4 cursor-pointer">
      <Trash2 className="h-5 w-5" />
    </button>
  </motion.div>
);

const WebsiteItem = ({ url }: WebsiteItemProps) => (
  <motion.div
    initial={{ opacity: 0 }}
    animate={{ opacity: 1 }}
    className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg"
  >
    <div className="flex-1 min-w-0">
      <p className="text-sm font-medium text-gray-800 dark:text-gray-200 truncate">
        {url.content.url}
      </p>
      <p className="text-xs text-gray-400 dark:text-gray-500 mt-1">
        {url.content.characters} chars •{" "}
        {url.uploadedForTraining ? "Trained" : "Uploaded"}
      </p>
    </div>
    <button className="text-red-500 hover:text-red-700 ml-4 cursor-pointer">
      <Trash2 className="h-5 w-5" />
    </button>
  </motion.div>
);

const QnAItem = ({ qa }: QnAItemProps) => (
  <motion.div
    initial={{ opacity: 0 }}
    animate={{ opacity: 1 }}
    className="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg"
  >
    <div className="flex justify-between items-start">
      <div className="flex-1">
        <p className="text-sm font-medium text-gray-800 dark:text-gray-200">
          Q: {qa.content.question}
        </p>
        <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">
          A: {qa.content.answer}
        </p>
        <p className="text-xs text-gray-400 dark:text-gray-500 mt-2">
          {qa.content.characters} chars •{" "}
          {qa.uploadedForTraining ? "Trained" : "Uploaded"}
        </p>
      </div>
      <button className="text-red-500 hover:text-red-700 ml-4 cursor-pointer">
        <Trash2 className="h-5 w-5" />
      </button>
    </div>
  </motion.div>
);
export default SourceManagement;
