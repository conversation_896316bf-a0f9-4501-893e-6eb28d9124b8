"use client";
import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Zap } from "lucide-react";
import { <PERSON>, <PERSON>H<PERSON>er, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>nt, CardFooter } from "@/components/ui/card";
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { format } from "date-fns";
import toast from 'react-hot-toast';
import { Slider } from "@/components/ui/slider";
import { useParams } from "next/navigation";
import { IAgent } from "@/models/agent";


interface AIConfigurationProps {
  agent: IAgent;
}

export default function AIConfiguration({ agent }: AIConfigurationProps) {
  const [model, setModel] = useState<string>(String(agent.model));
  const [temperature, setTemperature] = useState(agent.temperature);
  const [instructions, setInstructions] = useState(agent.instructions);
  const [isSaving, setIsSaving] = useState(false);
  const params = useParams();
  const slug = params.slug;

const handleSave = async () => {
  if (!model || !instructions) {
    toast.error("All fields for AI tuning are required");
    return;
  }

  setIsSaving(true);
  
  const savePromise = fetch(`/dashboard/${slug}/${agent._id}/settings/ai`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      chatbotModel: model,
      chatbotTemperature: temperature,
      chatbotInstructions: instructions,
    }),
  });

  toast.promise(
    savePromise,
    {
      loading: 'Saving AI configuration...',
      success: (response) => {
        if (!response.ok) throw new Error("Failed to save");
        return "AI configuration saved successfully";
      },
      error: (error) => error instanceof Error ? error.message : "Failed to save AI configuration",
    },
    {
      style: {
        minWidth: '250px',
      },
      success: {
        duration: 4000,
      },
      error: {
        duration: 5000,
      },
    }
  )
  .then(async (response) => {
    if (response instanceof Response) {
      const data = await response.json();
      return data.message || "AI configuration saved successfully";
    }
    return response;
  })
  .catch((error) => {
    throw error;
  })
  .finally(() => {
    setIsSaving(false);
  });
};

  return (
    <div className="space-y-6">
      {/* AI Configuration Card */}
      <Card className="border-emerald-300 dark:border-emerald-700 shadow-lg">
        <CardHeader>
          <CardTitle className="text-2xl font-bold text-emerald-600 dark:text-emerald-400">
            AI Configuration
          </CardTitle>
        </CardHeader>
        
        <CardContent className="space-y-6">
          {/* Model Selection */}
          <div className="space-y-2">
            <label className="flex items-center gap-2 text-sm font-medium">
              Model
              <span className="text-gray-400 hover:text-emerald-500 transition-colors cursor-pointer">
                <Info size={16} />
              </span>
            </label>
            
            <Select value={model} onValueChange={(value: string) => setModel(value)}>
              <SelectTrigger className="border-emerald-300 w-1/3 cursor-pointer dark:border-emerald-700">
                <SelectValue placeholder="Select a model" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="o1-mini" className="cursor-pointer">o1 mini</SelectItem>
                <SelectItem value="gpt-4o-mini" className="cursor-pointer">GPT-4o mini</SelectItem>
                <SelectItem value="gpt-4o" className="cursor-pointer">GPT-4o</SelectItem>
                <SelectItem value="gpt-4-turbo" className="cursor-pointer">GPT-4 Turbo</SelectItem>
                <SelectItem disabled value="claude-3-5-haiku-20241022" className="cursor-pointer">
                  Claude 3 Haiku
                </SelectItem>
                <SelectItem disabled value="claude-3-opus-20240229" className="cursor-pointer">
                  Claude 3 Opus
                </SelectItem>
                <SelectItem disabled value="claude-3-5-sonnet-20241022" className="cursor-pointer">
                  Claude 3.5 Sonnet
                </SelectItem>
                <SelectItem disabled value="gemini-1.5-flash" className="cursor-pointer">
                  Gemini 1.5 Flash
                </SelectItem>
                <SelectItem disabled value="gemini-1.5-pro" className="cursor-pointer">
                  Gemini 1.5 Pro
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Temperature Slider */}
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <label className="text-sm font-medium">Temperature</label>
              <span className="px-2 py-1 bg-emerald-100 dark:bg-emerald-900 rounded-md text-emerald-700 dark:text-emerald-300 text-sm">
                {temperature}
              </span>
            </div>
            
            <Slider
              min={0}
              max={1}
              step={0.1}
              value={[temperature]}
              onValueChange={([value]) => setTemperature(value)}
              className="[&>span]:bg-emerald-500 cursor-pointer"
            />
            
            <div className="flex justify-between text-sm text-gray-500 dark:text-gray-400">
              <span>Precise</span>
              <span>Balanced</span>
              <span>Creative</span>
            </div>
          </div>

          {/* System Prompt */}
          <div className="space-y-2">
            <label className="text-sm font-medium">System Prompt</label>
            <Select>
              <SelectTrigger className="border-emerald-300 w-1/3 dark:border-emerald-700 cursor-pointer">
                <SelectValue placeholder="Select a system prompt" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="chatbot" className="cursor-pointer">AI Chatbot</SelectItem>
                <SelectItem value="support_agent" className="cursor-pointer">Customer support agent</SelectItem>
                <SelectItem value="sales_agent" className="cursor-pointer">Sales agent</SelectItem>
                <SelectItem value="language_tutor" className="cursor-pointer">Language tutor</SelectItem>
                <SelectItem value="life_coach" className="cursor-pointer">Life coach</SelectItem>
                <SelectItem value="fashion_designer" className="cursor-pointer">Futuristic fashion designer</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Instructions */}
          <div className="space-y-2">
            <label className="flex items-center gap-2 text-sm font-medium">
              Instructions
              <span className="text-gray-400 hover:text-emerald-500 transition-colors cursor-pointer">
                <Info size={16} />
              </span>
            </label>
            
            <Textarea
              value={instructions ?? ""}
              onChange={(e) => setInstructions(e.target.value)}
              className="min-h-[200px] border-emerald-300 dark:border-emerald-700"
              placeholder="Enter detailed instructions for your AI..."
            />
          </div>
        </CardContent>
        
        <CardFooter className="flex justify-end border-t border-emerald-100 dark:border-emerald-800 pt-4">
          <Button
            onClick={handleSave}
            disabled={isSaving}
            className="bg-emerald-600 cursor-pointer hover:bg-emerald-700 text-white transition-colors flex items-center gap-2"
          >
            {isSaving ? "Saving..." : "Save Configuration"}
            <Zap size={16} />
          </Button>
        </CardFooter>
      </Card>

      {/* Training Status Card */}
      <Card className="border-blue-300 dark:border-blue-700 shadow-lg">
        <CardHeader>
          <CardTitle className="text-2xl font-bold text-blue-600 dark:text-blue-400">
            Training Status
          </CardTitle>
        </CardHeader>
        
        <CardContent>
          {agent.isTrained ? (
            <div className="space-y-2">
              <p className="text-sm text-gray-600 dark:text-gray-300">
                Last trained at:{" "}
                <span className="font-medium text-blue-700 dark:text-blue-300">
                  {format(new Date(agent.lastTrainedAt!), "MMMM d, yyyy 'at' h:mm a")}
                </span>
              </p>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Your chatbot is up-to-date with the latest training data.
              </p>
            </div>
          ) : (
            <div className="space-y-2">
              <p className="text-sm text-gray-600 dark:text-gray-300">
                <span className="font-medium text-yellow-600 dark:text-yellow-400">
                  Not yet trained
                </span>
              </p>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Your chatbot needs to be trained before it can be used.
              </p>
            </div>
          )}
        </CardContent>
        
        <CardFooter className="border-t border-blue-100 dark:border-blue-900 pt-4">
          <Button variant="outline" className="cursor-pointer flex items-center gap-2">
            <BookOpen size={16} />
            View Training Documentation
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}