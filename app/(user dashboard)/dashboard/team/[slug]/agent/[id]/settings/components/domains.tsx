"use client";
import { Globe, Info } from "lucide-react";
import { <PERSON>, <PERSON>Header, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>ontent, CardFooter } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { toast } from "react-hot-toast";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage, FormDescription } from "@/components/ui/form";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useSearchParams } from "next/navigation";
import { domainFormSchema } from "@/validations/validations";


type DomainFormValues = z.infer<typeof domainFormSchema>;

export default function DomainsSettings() {
  const searchParams = useSearchParams();
  const chatbotId = searchParams.get("id");

  const form = useForm<DomainFormValues>({
    resolver: zodResolver(domainFormSchema),
    defaultValues: {
      customDomain: "",
      customDomainEnabled: false,
    },
     mode:'onChange',
    reValidateMode:'onChange'
  });

  const { isSubmitting } = form.formState;

  const onSubmit = async (data: DomainFormValues) => {
    try {
      const response = await fetch(`/api/chatbots/${chatbotId}/settings/customdomains`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          customDomains: data.customDomainEnabled ? data.customDomain : "",
          customDomainsEnabled: data.customDomainEnabled,
        }),
      });

      if (!response.ok) throw new Error("Failed to save");

      const result = await response.json();
      toast.success(result.message || "Domain settings saved successfully");
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to save domain settings");
    }
  };

  return (
    <div className="space-y-6">
      <Card className="border-emerald-300 dark:border-emerald-700 shadow-lg">
        <CardHeader>
          <CardTitle className="text-2xl font-bold text-emerald-600 dark:text-emerald-400 flex items-center gap-2">
            <Globe className="h-6 w-6" />
            Custom Domain Settings
          </CardTitle>
        </CardHeader>
        
        <CardContent>
          <Alert className="mb-6 bg-emerald-50 dark:bg-emerald-900/30 border-emerald-200 dark:border-emerald-800">
            <Info className="h-4 w-4 text-emerald-600 dark:text-emerald-400" />
            <AlertDescription>
              Add a custom domain for your chatbot iframe scripts and URLs. 
              Useful for reselling chatbots to clients.
            </AlertDescription>
          </Alert>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <div className="flex items-start gap-4">
                <FormField
                  control={form.control}
                  name="customDomain"
                  render={({ field }) => (
                    <FormItem className="flex-1">
                      <FormLabel className="flex items-center gap-2">
                        <Globe className="h-4 w-4" />
                        Custom Domain
                      </FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          placeholder="chatbot.yourwebsite.com"
                          disabled={!form.watch("customDomainEnabled")}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="customDomainEnabled"
                  render={({ field }) => (
                    <FormItem className="flex flex-col pt-8">
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                          className="data-[state=checked]:bg-emerald-600 cursor-pointer"
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>
              <FormDescription>
                Applies to chatbot iframe scripts and URL links
              </FormDescription>

              <CardFooter className="flex justify-end p-0 pt-4 border-t border-emerald-100 dark:border-emerald-800">
                <Button
                  type="submit"
                  disabled={isSubmitting}
                  className="bg-emerald-600 cursor-pointer hover:bg-emerald-700 text-white transition-colors"
                >
                  {isSubmitting ? "Saving..." : "Save Domain Settings"}
                </Button>
              </CardFooter>
            </form>
          </Form>
        </CardContent>
      </Card>

      <Card className="border-emerald-300 dark:border-emerald-700 shadow-lg">
        <CardHeader>
          <CardTitle className="text-2xl font-bold text-emerald-600 dark:text-emerald-400">
            DNS Configuration
          </CardTitle>
        </CardHeader>
        
        <CardContent>
          <Alert className="mb-6 bg-emerald-50 dark:bg-emerald-900/30 border-emerald-200 dark:border-emerald-800">
            <Info className="h-4 w-4 text-emerald-600 dark:text-emerald-400" />
            <AlertDescription>
              Use these DNS settings in your domain registrar (Namecheap, GoDaddy, etc.)
            </AlertDescription>
          </Alert>

          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-emerald-200 dark:divide-emerald-800">
              <thead className="bg-emerald-50 dark:bg-emerald-900/20">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-emerald-700 dark:text-emerald-300 uppercase tracking-wider">
                    Record Type
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-emerald-700 dark:text-emerald-300 uppercase tracking-wider">
                    Host
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-emerald-700 dark:text-emerald-300 uppercase tracking-wider">
                    Target
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-900 divide-y divide-emerald-200 dark:divide-emerald-800">
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                    CNAME
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                    chatbot.yourwebsite.com
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                    {chatbotId}.chatzuri.com
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}