"use client";

import { useState } from "react";
import { Info, <PERSON>ert<PERSON><PERSON>gle, Trash2 } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  <PERSON>,
  <PERSON>H<PERSON>er,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ontent,
  CardFooter,
} from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import toast from "react-hot-toast";
import { IAgent } from "@/models/agent";
import { useParams } from "next/navigation";

export default function AgentSettingsOverview({
  agent,
  trainingData,
}: {
  agent: IAgent;
  //eslint-disable-next-line @typescript-eslint/no-explicit-any
  trainingData: any[];
}) {
  const [name, setName] = useState(agent.name);
  const [isSaving, setIsSaving] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const params = useParams();
  const slug = params.slug;
  const characterCount =
    trainingData.length > 0
      ? trainingData.reduce(
          (sum, item) => sum + (item.content.characters ?? 0),
          0
        )
      : 0;

  const handleSave = async () => {
    if (name.length < 2) {
      toast.error("Name must be at least 2 characters long");
      return;
    }

    setIsSaving(true);
    try {
      const response = await fetch(
        `/dashboard/${slug}/${agent._id}/settings/general`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ chatbotName: name }),
        }
      );

      if (!response.ok) throw new Error("Failed to save");

      const data = await response.json();
      toast.success(data.message || "Settings saved successfully");
    } catch (error) {
      const errorMessage =
        error instanceof Error && error.message
          ? error.message
          : "Failed to save settings";
      toast.error(errorMessage);
    } finally {
      setIsSaving(false);
    }
  };

  const handleDelete = async () => {
    if (!confirm("Are you absolutely sure? This action cannot be undone."))
      return;
    setIsDeleting(true);
    try {
      const response = await fetch(`/dashboard/${slug}/${agent._id}`, {
        method: "DELETE",
      });

      if (!response.ok) throw new Error("Failed to delete chatbot");

      const data = await response.json();
      toast.success(data.message || "Chatbot deleted successfully");
      // Redirect or update state as needed
    } catch (error) {
      const errorMessage =
        error instanceof Error && error.message
          ? error.message
          : "Failed to delete chatbot";
      toast.error(errorMessage);
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <div className="space-y-6 animate-fade-in">
      {/* General Settings Card */}
      <Card className="border-emerald-300 dark:border-emerald-700 shadow-lg transition-all hover:shadow-xl">
        <CardHeader>
          <CardTitle className="text-2xl font-bold text-emerald-600 dark:text-emerald-400">
            General Settings
          </CardTitle>
        </CardHeader>

        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-1">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                Chatbot ID
              </p>
              <p className="text-lg font-semibold text-emerald-700 dark:text-emerald-300">
                {String(agent._id)}
              </p>
            </div>

            <div className="space-y-1">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                Characters detected
              </p>
              <p className="text-lg font-semibold text-emerald-700 dark:text-emerald-300">
                {characterCount.toLocaleString()}
              </p>
            </div>
          </div>

          <div className="space-y-2">
            <label
              htmlFor="chatbot-name"
              className="flex items-center gap-2 text-sm font-medium"
            >
              Name
              <span
                className="text-gray-400 hover:text-emerald-500 transition-colors cursor-pointer"
                data-tooltip-id="name-tooltip"
                data-tooltip-content="The display name for your chatbot"
              >
                <Info size={16} />
              </span>
            </label>

            <Input
              id="chatbot-name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              className="border-emerald-300 dark:border-emerald-700 focus:ring-2 focus:ring-emerald-400"
            />
          </div>
        </CardContent>

        <CardFooter className="flex justify-end border-t border-emerald-100 dark:border-emerald-800 pt-4">
          <Button
            onClick={handleSave}
            disabled={isSaving}
            className="bg-emerald-600 cursor-pointer hover:bg-emerald-700 text-white transition-colors"
          >
            {isSaving ? "Saving..." : "Save Changes"}
          </Button>
        </CardFooter>
      </Card>

      {/* Danger Zone */}
      <div className="relative my-8">
        <div className="absolute inset-0 flex items-center">
          <div className="w-full border-t border-red-400 dark:border-red-600"></div>
        </div>
        <div className="relative flex justify-center">
          <span className="bg-white dark:bg-gray-900 px-4 py-2 rounded-full text-lg font-medium text-red-500 dark:text-red-400 flex items-center gap-2">
            <AlertTriangle size={18} />
            Danger Zone
          </span>
        </div>
      </div>

      {/* Delete Chatbot Card */}
      <Card className="border-red-300 dark:border-red-700 shadow-lg">
        <CardHeader>
          <CardTitle className="text-2xl font-bold text-red-600 dark:text-red-400">
            Delete Agent
          </CardTitle>
        </CardHeader>

        <CardContent>
          <Alert variant="destructive" className="mb-4">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              This action cannot be undone. All your uploaded data will be
              permanently deleted.
            </AlertDescription>
          </Alert>

          <p className="text-sm text-gray-600 dark:text-gray-300">
            Once you delete your agent, there is no going back. Please be
            certain.
          </p>
        </CardContent>

        <CardFooter className="flex justify-end border-t border-red-100 dark:border-red-900 pt-4">
          <Button
            onClick={handleDelete}
            disabled={isDeleting}
            variant="destructive"
            className=" cursor-pointer flex items-center gap-2 transition-all hover:scale-105"
          >
            <Trash2 size={18} />
            {isDeleting ? "Deleting..." : "Delete Agent"}
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}
