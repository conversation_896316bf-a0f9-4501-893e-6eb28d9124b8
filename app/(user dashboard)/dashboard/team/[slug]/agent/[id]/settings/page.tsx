"use client";
import AgentTabs from "@/components/tabs/agent-tabs";
import { AnimatePresence, motion } from "framer-motion";
import {
  Code2,
  Plug,
  Bell,
  Contact,
  LayoutPanelLeft,
  BrainCircuit,
  Globe,
  Shield,
} from "lucide-react";
import React, { useEffect, useState } from "react";
import { cn } from "@/lib/utils";
import { useParams } from "next/navigation";
import ErrorDialog from "@/components/error/error-state";
import { useAgent } from "@/hooks/use-agents";
import { Skeleton } from "@/components/ui/skeleton";
import { AgentSettingsOverview, AIConfiguration, ChatSettings, DomainsSettings, LeadsSettings, NotificationsSettings, SecuritySettings, WebhooksSettings } from "./components";

type TabType =
  | "general"
  | "ai"
  | "interface"
  | "leads"
  | "security"
  | "notifications"
  | "webhooks"
  | "domains";

const mockTrainingData = [
  { content: { characters: 1500 } },
  { content: { characters: 2300 } },
  { content: { characters: 1800 } },
];


 const mockAgentData = {
    _id: "chatbot_123",
    leadTitle: "Get in touch!",
    leadName: "Full Name",
    leadNameEnabled: true,
    leadEmail: "",
    leadEmailEnabled: false,
    leadPhone: "",
    leadPhoneEnabled: true
  };

const mockSecurityData = {
  _id: "chatbot_123",
  isPublic: false,
  allowedDomains: "",
  ipLimit: 5,
  ipLimitMessage: "Too many messages in a row"
};

const SettingsPage = () => {
  const [activeTab, setActiveTab] = useState<TabType>("general");
  const params = useParams();
  const { slug, id } = params;
  const [errorDialogOpen, setErrorDialogOpen] = useState(false);

  // Ensure slug and id are strings before using them
  const teamSlug =
    typeof slug === "string" ? slug : Array.isArray(slug) ? slug[0] : undefined;
  const agentId =
    typeof id === "string" ? id : Array.isArray(id) ? id[0] : undefined;

  const {
    data: agent,
    isLoading,
    error,
    refetch,
    isFetching,
  } = useAgent(teamSlug ?? "", agentId ?? "");

  console.log("agent:", agent);
  useEffect(() => {
    if (
      error &&
      typeof error === "object" &&
      error !== null &&
      "response" in error &&
      // @ts-expect-error: response might exist on custom error types
      error.response?.status !== 404
    ) {
      setErrorDialogOpen(true);
    }
  }, [error]);

  const sourceTabs: {
    value: TabType;
    icon: React.ReactNode;
    label: string;
  }[] = [
    {
      value: "general",
      icon: <Code2 className="h-5 w-5" />,
      label: "General Settings",
    },
    {
      value: "ai",
      icon: <BrainCircuit className="h-5 w-5" />,
      label: "AI Configuration",
    },
    {
      value: "interface",
      icon: <LayoutPanelLeft className="h-5 w-5" />,
      label: "Interface",
    },
    {
      value: "leads",
      icon: <Contact className="h-5 w-5" />,
      label: "Leads",
    }, {
      value: "security",
      icon: <Shield className="h-5 w-5" />,
      label: "Security",
    },
    {
      value: "notifications",
      icon: <Bell className="h-5 w-5" />,
      label: "Notifications",
    },
    {
      value: "webhooks",
      icon: <Plug className="h-5 w-5" />,
      label: "Webhooks",
    },
    {
      value: "domains",
      icon: <Globe className="h-5 w-5" />,
      label: "Domains",
    },
  ];

  if (
    error &&
    typeof error === "object" &&
    error !== null &&
    "response" in error &&
    // @ts-expect-error: response might exist on custom error types
    error.response?.status !== 404
  ) {
    return (
      <ErrorDialog
        error={error || new Error("Unknown problem has occured.")}
        open={errorDialogOpen}
        retry={refetch}
        isFetching={isFetching}
      />
    );
  }

  return (
    <div className="min-h-screen ">
      <div className="container mx-auto px-4 py-6">
        {/* Header */}
        <header className="mb-8">
          {isLoading ? (
            <div className="space-y-2">
              <Skeleton className="h-8 w-64" />
              <Skeleton className="h-4 w-80" />
            </div>
          ) : (
            <>
              <h1 className="text-3xl font-bold text-emerald-500 dark:text-emerald-400 mb-2">
                Agent Settings
              </h1>
              <p className="text-gray-600 dark:text-gray-300">
                Configure and test your AI assistant
              </p>
            </>
          )}
        </header>

        {/* Navigation Tabs */}
        <AgentTabs />

        {/* Main Content */}
        <div className="flex-1 max-w-8xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex flex-col lg:flex-row gap-8">
            {/* Left Sidebar */}
            <div className="lg:w-1/5">
              <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-4 sticky top-8">
                <nav className="flex flex-col space-y-2">
                  {sourceTabs.map((tab) => (
                    <motion.button
                      key={tab.value}
                      onClick={() => setActiveTab(tab.value as TabType)}
                      whileHover={{ x: 5 }}
                      whileTap={{ scale: 0.98 }}
                      className={cn(
                        "w-full px-4 py-3 cursor-pointer rounded-lg flex items-center space-x-3 transition-colors",
                        activeTab === tab.value
                          ? "bg-emerald-50 dark:bg-emerald-900/30 text-emerald-600 dark:text-emerald-400"
                          : "text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                      )}
                    >
                      <span
                        className={cn(
                          "p-2 rounded-lg",
                          activeTab === tab.value
                            ? "bg-emerald-100 dark:bg-emerald-800/50 text-emerald-600 dark:text-emerald-300"
                            : "bg-gray-100 dark:bg-gray-700 text-gray-500 dark:text-gray-400"
                        )}
                      >
                        {tab.icon}
                      </span>
                      <span className="font-medium">{tab.label}</span>
                    </motion.button>
                  ))}
                </nav>
              </div>
            </div>

            {/* Tab Content */}
            <div className="flex-1">
              <AnimatePresence mode="wait">
                {isLoading ? (
                  <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm overflow-hidden p-6 space-y-6">
                    <div className="space-y-4">
                      <Skeleton className="h-10 w-1/2" />
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Skeleton className="h-4 w-24" />
                          <Skeleton className="h-6 w-full" />
                        </div>
                        <div className="space-y-2">
                          <Skeleton className="h-4 w-24" />
                          <Skeleton className="h-6 w-full" />
                        </div>
                      </div>
                      <div className="space-y-2">
                        <div className="flex items-center gap-2">
                          <Skeleton className="h-4 w-16" />
                          <Skeleton className="h-4 w-4 rounded-full" />
                        </div>
                        <Skeleton className="h-10 w-full" />
                      </div>
                    </div>
                    <div className="flex justify-end">
                      <Skeleton className="h-10 w-32" />
                    </div>
                  </div>
                ) : (
                  <motion.div
                    key={activeTab}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    transition={{ duration: 0.2 }}
                    className="bg-white dark:bg-gray-800 rounded-xl shadow-sm overflow-hidden"
                  >
                    {/* General Tab */}
                    {activeTab === "general" && agent && (
                      <div className="p-6">
                        <AgentSettingsOverview
                          agent={agent}
                          trainingData={mockTrainingData}
                        />
                      </div>
                    )}

                    {/* Ai Tab */}
                    {activeTab === "ai" && agent && (
                      <div className="p-6">
                        <AIConfiguration agent={agent} />
                      </div>
                    )}

                    {/* Interface Tab */}
                    {activeTab === "interface" && (
                      <div className="p-6"><ChatSettings/></div>
                    )}

                    {/* Leads Tab */}
                    {activeTab === "leads" && (
                      <div className="p-6"><LeadsSettings agent={mockAgentData} /></div>
                    )}

                     {/* Leads Tab */}
                    {activeTab === "security" && (
                      <div className="p-6"><SecuritySettings agent={mockSecurityData}/></div>
                    )}

                    {/* Notifications Tab */}
                    {activeTab === "notifications" && (
                      <div className="p-6"><NotificationsSettings agent={mockAgentData}/></div>
                    )}

                    {/* Webhooks Tab */}
                    {activeTab === "webhooks" && (
                      <div className="p-6"><WebhooksSettings/></div>
                    )}

                    {/* Domains Tab */}
                    {activeTab === "domains" && (
                      <div className="p-6"><DomainsSettings/></div>
                    )}
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </div>
        </div>
      </div>

      <style jsx>{`
        .code-box {
          border: 1px solid #ccc;
          padding: 15px;
          background-color: #f4f4f4;
          font-family: monospace;
          white-space: pre-wrap;
          word-wrap: break-word;
          border-radius: 4px;
        }
        .dark .code-box {
          background-color: #2d3748;
          border-color: #4a5568;
        }
        .copy-btn {
          margin-top: 10px;
          padding: 8px 12px;
          background-color: #28a745;
          color: white;
          border: none;
          cursor: pointer;
          border-radius: 4px;
        }
        .copy-btn:hover {
          background-color: #218838;
        }
      `}</style>
    </div>
  );
};

export default SettingsPage;
