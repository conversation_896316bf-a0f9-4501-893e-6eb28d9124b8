"use client";
import { useState, useEffect } from "react";
import { useTheme } from "next-themes";
import {
  Info,
  Upload,
  Palette,
  MessageSquare,
  User,
  ImageIcon,
  AlignLeft,
  AlignRight,
  Clock,
  ThumbsUp,
  RefreshCw,
} from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import {
  Card,
  CardHeader,
  CardTitle,
  CardContent,
  CardFooter,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "@/components/ui/select";
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import {
  <PERSON><PERSON><PERSON>,
  Toolt<PERSON>Trigger,
  Toolt<PERSON><PERSON>ontent,
} from "@/components/ui/tooltip";
import { Badge } from "@/components/ui/badge";
import toast from "react-hot-toast";
import Image from "next/image";
import ChatInterface from "./chat-interface";

interface ChatSettingsProps {
  initialValues?: {
    initialMessages?: string;
    suggestedMessages?: string;
    messagePlaceholder?: string;
    displayName?: string;
    profilePicturePath?: string;
    iconPath?: string;
    footer?: string;
    theme?: string;
    customerMessageColor?: string;
    chatBubbleColor?: string;
    alignChatBubble?: string;
    collectFeedback?: boolean;
    regenerateMessage?: boolean;
    autoShowInitialDelay?: number;
  };
}

export default function ChatSettings({ initialValues }: ChatSettingsProps) {
  const { resolvedTheme } = useTheme();
  const [activeTab, setActiveTab] = useState("general");
  const [settings, setSettings] = useState({
    initialMessages:
      initialValues?.initialMessages || "Hello! How can I help you today?",
    suggestedMessages:
      initialValues?.suggestedMessages ||
      "What services do you offer?\nHow much does it cost?",
    messagePlaceholder:
      initialValues?.messagePlaceholder || "Type your message...",
    displayName: initialValues?.displayName || "Chat Assistant",
    profilePicturePath:
      initialValues?.profilePicturePath || "/images/default-profile.png",
    iconPath: initialValues?.iconPath || "/images/default-icon.png",
    footer: initialValues?.footer || "Powered by our chat service",
    theme: initialValues?.theme || "system",
    customerMessageColor: initialValues?.customerMessageColor || "#10b981",
    chatBubbleColor: initialValues?.chatBubbleColor || "#10b981",
    alignChatBubble: initialValues?.alignChatBubble || "right",
    collectFeedback: initialValues?.collectFeedback ?? true,
    regenerateMessage: initialValues?.regenerateMessage ?? true,
    autoShowInitialDelay: initialValues?.autoShowInitialDelay || 3,
  });

  const [characterCount, setCharacterCount] = useState(0);
  const [previewProfile, setPreviewProfile] = useState(
    settings.profilePicturePath
  );
  const [previewIcon, setPreviewIcon] = useState(settings.iconPath);

  useEffect(() => {
    setCharacterCount(settings.footer.length);
  }, [settings.footer]);

  type SettingName = keyof typeof settings;
  type SettingValue = string | number | boolean;

  const handleChange = (name: SettingName, value: SettingValue) => {
    setSettings((prev) => ({ ...prev, [name]: value }));
  };

  const handleFileChange = async (
    e: React.ChangeEvent<HTMLInputElement>,
    type: "profile" | "icon"
  ) => {
    const file = e.target.files?.[0];
    if (!file) return;

    if (file.size > 1024 * 1024) {
      // 1MB limit
      toast.error("Please upload an image smaller than 1MB", {
        style: {
          minWidth: "250px",
        },
        icon: "❌",
        duration: 5000,
      });
      return;
    }

    const reader = new FileReader();
    reader.onload = (event) => {
      const result = event.target?.result as string;
      if (type === "profile") {
        setPreviewProfile(result);
        handleChange("profilePicturePath", result);
      } else {
        setPreviewIcon(result);
        handleChange("iconPath", result);
      }
    };
    reader.readAsDataURL(file);
  };

  const currentTheme =
    settings.theme === "system" ? resolvedTheme : settings.theme;

  return (
    <div className="flex flex-col lg:flex-row gap-6 p-4">
      {/* Settings Panel */}
      <motion.div
        initial={{ opacity: 0, x: -20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.3 }}
        className="w-full lg:w-1/2"
      >
        <Tabs
          value={activeTab}
          onValueChange={setActiveTab}
          className="space-y-6"
        >
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="general" className="cursor-pointer">
              <MessageSquare className="w-4 h-4 mr-2" />
              General
            </TabsTrigger>
            <TabsTrigger value="appearance" className="cursor-pointer">
              <Palette className="w-4 h-4 mr-2" />
              Appearance
            </TabsTrigger>
          </TabsList>

          <AnimatePresence mode="wait">
            <motion.div
              key={activeTab}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              transition={{ duration: 0.2 }}
            >
              <TabsContent value="general" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <MessageSquare className="w-5 h-5" />
                      Chat Content
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <Label
                        htmlFor="initialMessages"
                        className="flex items-center gap-2"
                      >
                        Initial Messages
                        <Tooltip>
                          <TooltipTrigger>
                            <Info className="w-4 h-4 text-muted-foreground" />
                          </TooltipTrigger>
                          <TooltipContent>
                            These messages will appear when the chat first opens
                          </TooltipContent>
                        </Tooltip>
                      </Label>
                      <Textarea
                        id="initialMessages"
                        value={settings.initialMessages}
                        onChange={(e) =>
                          handleChange("initialMessages", e.target.value)
                        }
                        placeholder="Enter each message in a new line"
                        rows={3}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label
                        htmlFor="suggestedMessages"
                        className="flex items-center gap-2"
                      >
                        Suggested Messages
                        <Tooltip>
                          <TooltipTrigger>
                            <Info className="w-4 h-4 text-muted-foreground" />
                          </TooltipTrigger>
                          <TooltipContent>
                            Quick reply options for users
                          </TooltipContent>
                        </Tooltip>
                      </Label>
                      <Textarea
                        id="suggestedMessages"
                        value={settings.suggestedMessages}
                        onChange={(e) =>
                          handleChange("suggestedMessages", e.target.value)
                        }
                        placeholder="Enter each message in a new line"
                        rows={3}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="messagePlaceholder">
                        Message Placeholder
                      </Label>
                      <Input
                        id="messagePlaceholder"
                        value={settings.messagePlaceholder}
                        onChange={(e) =>
                          handleChange("messagePlaceholder", e.target.value)
                        }
                        placeholder="Type your message..."
                      />
                    </div>

                    <div className="space-y-2">
                      <Label
                        htmlFor="displayName"
                        className="flex items-center gap-2"
                      >
                        <User className="w-4 h-4" />
                        Display Name
                      </Label>
                      <Input
                        id="displayName"
                        value={settings.displayName}
                        onChange={(e) =>
                          handleChange("displayName", e.target.value)
                        }
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="footer">Footer Text</Label>
                      <Textarea
                        id="footer"
                        value={settings.footer}
                        onChange={(e) => handleChange("footer", e.target.value)}
                        maxLength={200}
                        rows={2}
                      />
                      <div className="flex justify-between">
                        <p className="text-sm text-muted-foreground">
                          You can add a disclaimer or privacy policy link
                        </p>
                        <Badge variant="outline">{characterCount}/200</Badge>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <ImageIcon className="w-5 h-5" />
                      Branding
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <Label>Profile Picture</Label>
                      <div className="flex items-center gap-4">
                        <div className="relative w-16 h-16 rounded-full overflow-hidden border-2 border-emerald-500">
                          <Image
                            src={previewProfile}
                            alt="Profile preview"
                            className="w-full h-full object-cover"
                            fill
                            sizes="64px"
                            style={{ objectFit: "cover" }}
                            priority
                          />
                        </div>
                        <div>
                          <Label
                            htmlFor="profile-upload"
                            className="cursor-pointer"
                          >
                            <Button
                              variant="outline"
                              className="cursor-pointer gap-2"
                            >
                              <Upload className="w-4 h-4" />
                              Upload
                            </Button>
                            <input
                              id="profile-upload"
                              type="file"
                              accept="image/*"
                              onChange={(e) => handleFileChange(e, "profile")}
                              className="hidden"
                            />
                          </Label>
                          <p className="text-sm text-muted-foreground mt-1">
                            JPG, PNG, SVG (max 1MB)
                          </p>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label>Chat Icon</Label>
                      <div className="flex items-center gap-4">
                        <div className="relative w-16 h-16 rounded-full overflow-hidden border-2 border-emerald-500">
                          <Image
                            src={previewIcon}
                            alt="Icon preview"
                            className="w-full h-full object-cover"
                            fill
                            sizes="64px"
                            style={{ objectFit: "cover" }}
                            priority
                          />
                        </div>
                        <div>
                          <Label
                            htmlFor="icon-upload"
                            className="cursor-pointer"
                          >
                            <Button
                              variant="outline"
                              className="cursor-pointer gap-2"
                            >
                              <Upload className="w-4 h-4" />
                              Upload
                            </Button>
                            <input
                              id="icon-upload"
                              type="file"
                              accept="image/*"
                              onChange={(e) => handleFileChange(e, "icon")}
                              className="hidden"
                            />
                          </Label>
                          <p className="text-sm text-muted-foreground mt-1">
                            JPG, PNG, SVG (max 1MB)
                          </p>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <ThumbsUp className="w-5 h-5" />
                      Features
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="collectFeedback"
                        checked={settings.collectFeedback}
                        onCheckedChange={(val) =>
                          handleChange("collectFeedback", val)
                        }
                        className="cursor-pointer"
                      />
                      <Label htmlFor="collectFeedback">
                        Collect user feedback
                      </Label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Switch
                        id="regenerateMessage"
                        checked={settings.regenerateMessage}
                        onCheckedChange={(val) =>
                          handleChange("regenerateMessage", val)
                        }
                        className="cursor-pointer"
                      />
                      <Label htmlFor="regenerateMessage">
                        Allow message regeneration
                      </Label>
                    </div>

                    <div className="space-y-2">
                      <Label
                        htmlFor="autoShowInitialDelay"
                        className="flex items-center gap-2"
                      >
                        <Clock className="w-4 h-4" />
                        Auto Show Delay (seconds)
                      </Label>
                      <Input
                        id="autoShowInitialDelay"
                        type="number"
                        min="0"
                        max="10"
                        value={settings.autoShowInitialDelay}
                        onChange={(e) =>
                          handleChange(
                            "autoShowInitialDelay",
                            parseInt(e.target.value)
                          )
                        }
                      />
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="appearance" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Palette className="w-5 h-5" />
                      Theme & Colors
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <Label>Theme</Label>
                      <Select
                        value={settings.theme}
                        onValueChange={(val) => handleChange("theme", val)}
                      >
                        <SelectTrigger className="w-full cursor-pointer">
                          <SelectValue placeholder="Select theme" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="system" className="cursor-pointer">
                            System
                          </SelectItem>
                          <SelectItem value="light" className="cursor-pointer">
                            Light
                          </SelectItem>
                          <SelectItem value="dark" className="cursor-pointer">
                            Dark
                          </SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label>Customer Message Color</Label>
                      <div className="flex items-center gap-4">
                        <input
                          type="color"
                          value={settings.customerMessageColor}
                          onChange={(e) =>
                            handleChange("customerMessageColor", e.target.value)
                          }
                          className="w-12 h-12 rounded-md border cursor-pointer"
                        />
                        <Badge variant="outline">
                          {settings.customerMessageColor}
                        </Badge>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label>Chat Bubble Color</Label>
                      <div className="flex items-center gap-4">
                        <input
                          type="color"
                          value={settings.chatBubbleColor}
                          onChange={(e) =>
                            handleChange("chatBubbleColor", e.target.value)
                          }
                          className="w-12 h-12 rounded-md border cursor-pointer"
                        />
                        <Badge variant="outline">
                          {settings.chatBubbleColor}
                        </Badge>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label>Bubble Alignment</Label>
                      <div className="flex gap-4">
                        <Button
                          variant={
                            settings.alignChatBubble === "left"
                              ? "default"
                              : "outline"
                          }
                          onClick={() =>
                            handleChange("alignChatBubble", "left")
                          }
                          className="cursor-pointer gap-2"
                        >
                          <AlignLeft className="w-4 h-4" />
                          Left
                        </Button>
                        <Button
                          variant={
                            settings.alignChatBubble === "right"
                              ? "default"
                              : "outline"
                          }
                          onClick={() =>
                            handleChange("alignChatBubble", "right")
                          }
                          className="cursor-pointer gap-2"
                        >
                          <AlignRight className="w-4 h-4" />
                          Right
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </motion.div>
          </AnimatePresence>

          <CardFooter className="flex justify-end p-0">
            <Button className="cursor-pointer gap-2">
              <RefreshCw className="w-4 h-4" />
              Save Settings
            </Button>
          </CardFooter>
        </Tabs>
      </motion.div>

      {/* Live Preview */}
      <motion.div
        initial={{ opacity: 0, x: 20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.3, delay: 0.1 }}
        className="w-full relative lg:w-1/2"
      >
        <Card className="sticky top-4">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-emerald-600 dark:text-emerald-400">
              Live Preview
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className=" h-[600px] rounded-xl overflow-hidden border">
              <ChatInterface
                initialMessages={settings.initialMessages.split("\n")}
                suggestedMessages={settings.suggestedMessages.split("\n")}
                messagePlaceholder={settings.messagePlaceholder}
                displayName={settings.displayName}
                profilePicturePath={previewProfile}
                iconPath={previewIcon}
                footer={settings.footer}
                theme={currentTheme as "light" | "dark"}
                customerMessageColor={settings.customerMessageColor}
                chatBubbleColor={settings.chatBubbleColor}
                alignChatBubble={settings.alignChatBubble as "left" | "right"}
                collectFeedback={settings.collectFeedback}
                autoShowInitialDelay={settings.autoShowInitialDelay}
              />
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
}
