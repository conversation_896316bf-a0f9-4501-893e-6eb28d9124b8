"use client";
import { Mail, Bell, Info } from "lucide-react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Footer } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage, FormDescription } from "@/components/ui/form";
import { Alert, AlertDescription } from "@/components/ui/alert";
import toast from "react-hot-toast";
import { notificationsFormSchema } from "@/validations/validations";
import { useParams } from "next/navigation";



type NotificationsFormValues = z.infer<typeof notificationsFormSchema>;

interface NotificationsSettingsProps {
  agent: {
    _id: string;
    leadsNotificationEmail?: string;
    leadsNotificationEmailEnabled?: boolean;
    conversationsNotificationEmail?: string;
    conversationsNotificationEmailEnabled?: boolean;
  };
}

export default function NotificationsSettings({ agent }: NotificationsSettingsProps) {
 const params = useParams();
  const slug = params.slug;
  const form = useForm<NotificationsFormValues>({
    resolver: zodResolver(notificationsFormSchema),
    defaultValues: {
      leadsNotificationEmail: agent.leadsNotificationEmail || "",
      leadsNotificationEnabled: agent.leadsNotificationEmailEnabled || false,
      conversationsNotificationEmail: agent.conversationsNotificationEmail || "",
      conversationsNotificationEnabled: agent.conversationsNotificationEmailEnabled || false,
    },
    mode:'onChange',
    reValidateMode:'onChange'
  });

  const { isSubmitting } = form.formState;

  const onSubmit = async (data: NotificationsFormValues) => {
    try {
      const response = await fetch(`/dashboard/${slug}/${agent._id}/settings/notifications`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          leadsNotificationEmail: data.leadsNotificationEnabled ? data.leadsNotificationEmail : "",
          leadsNotificationEmailEnabled: data.leadsNotificationEnabled,
          conversationsNotificationEmail: data.conversationsNotificationEnabled ? data.conversationsNotificationEmail : "",
          conversationsNotificationEmailEnabled: data.conversationsNotificationEnabled,
        }),
      });

      if (!response.ok) throw new Error("Failed to save");

      const result = await response.json();
      toast.success(result.message || "Notification settings saved successfully");
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to save notification settings");
    }
  };

  return (
    <Card className="border-emerald-300 dark:border-emerald-700 shadow-lg">
      <CardHeader>
        <CardTitle className="text-2xl font-bold text-emerald-600 dark:text-emerald-400 flex items-center gap-2">
          <Bell className="h-6 w-6" />
          Notification Settings
        </CardTitle>
      </CardHeader>
      
      <CardContent>
        <Alert className="mb-6 bg-emerald-50 dark:bg-emerald-900/30 border-emerald-200 dark:border-emerald-800">
          <Info className="h-4 w-4 text-emerald-600 dark:text-emerald-400" />
          <AlertDescription>
            Your notification settings allow you to receive email updates with chat records from your bot. 
            You can enable notifications by typing an email and toggling the switch.
          </AlertDescription>
        </Alert>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Leads Notifications */}
            <div className="space-y-4">
              <div className="flex items-start gap-4">
                <FormField
                  control={form.control}
                  name="leadsNotificationEmail"
                  render={({ field }) => (
                    <FormItem className="flex-1">
                      <FormLabel className="flex items-center gap-2">
                        <Mail className="h-4 w-4" />
                        Daily Leads Email
                      </FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          type="email"
                          placeholder="<EMAIL>"
                          disabled={!form.watch("leadsNotificationEnabled")}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="leadsNotificationEnabled"
                  render={({ field }) => (
                    <FormItem className="flex flex-col pt-8">
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                          className="data-[state=checked]:bg-emerald-600 cursor-pointer"
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>
              <FormDescription>
                Receive a daily email summary of collected leads
              </FormDescription>
            </div>

            {/* Conversations Notifications */}
            <div className="space-y-4">
              <div className="flex items-start gap-4">
                <FormField
                  control={form.control}
                  name="conversationsNotificationEmail"
                  render={({ field }) => (
                    <FormItem className="flex-1">
                      <FormLabel className="flex items-center gap-2">
                        <Mail className="h-4 w-4" />
                        Daily Conversations Email
                      </FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          type="email"
                          placeholder="<EMAIL>"
                          disabled={!form.watch("conversationsNotificationEnabled")}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="conversationsNotificationEnabled"
                  render={({ field }) => (
                    <FormItem className="flex flex-col pt-8">
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                          className="data-[state=checked]:bg-emerald-600 cursor-pointer"
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>
              <FormDescription>
                Receive a daily email summary of conversations
              </FormDescription>
            </div>

            <CardFooter className="flex justify-end p-0 pt-4 border-t border-emerald-100 dark:border-emerald-800">
              <Button
                type="submit"
                disabled={isSubmitting}
                className="bg-emerald-600 cursor-pointer hover:bg-emerald-700 text-white transition-colors"
              >
                {isSubmitting ? "Saving..." : "Save Notification Settings"}
              </Button>
            </CardFooter>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}