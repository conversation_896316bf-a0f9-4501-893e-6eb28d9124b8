"use client";
import { Link2, Info } from "lucide-react";
import { <PERSON>, <PERSON>Header, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>ontent, CardFooter } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { toast } from "react-hot-toast";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage, FormDescription } from "@/components/ui/form";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useParams } from "next/navigation";
import Link from "next/link";
import { webhookFormSchema } from "@/validations/validations";



type WebhookFormValues = z.infer<typeof webhookFormSchema>;

export default function WebhooksSettings() {
  const {slug,id:chatbotId} = useParams();

  const form = useForm<WebhookFormValues>({
    resolver: zodResolver(webhookFormSchema),
    defaultValues: {
      webhookUrl: "",
      webhookEnabled: false,
    },
     mode:'onChange',
    reValidateMode:'onChange'
  });

  const { isSubmitting } = form.formState;

  const onSubmit = async (data: WebhookFormValues) => {
    try {
      const response = await fetch(`/api/teams/${slug}/agents/${chatbotId}/settings/webhooks`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          webhooks: data.webhookEnabled ? data.webhookUrl : "",
          webhooksEnabled: data.webhookEnabled,
        }),
      });

      if (!response.ok) throw new Error("Failed to save");

      const result = await response.json();
      toast.success(result.message || "Webhook settings saved successfully");
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to save webhook settings");
    }
  };

  return (
    <Card className="border-emerald-300 dark:border-emerald-700 shadow-lg">
      <CardHeader>
        <CardTitle className="text-2xl font-bold text-emerald-600 dark:text-emerald-400 flex items-center gap-2">
          <Link2 className="h-6 w-6" />
          Webhook Settings
        </CardTitle>
      </CardHeader>
      
      <CardContent>
        <Alert className="mb-6 bg-emerald-50 dark:bg-emerald-900/30 border-emerald-200 dark:border-emerald-800">
          <Info className="h-4 w-4 text-emerald-600 dark:text-emerald-400" />
          <AlertDescription>
            See our <Link href="/docs/webhooks-api" className="text-emerald-700 dark:text-emerald-300 hover:underline">Documentation</Link> for details.
          </AlertDescription>
        </Alert>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="flex items-start gap-4">
              <FormField
                control={form.control}
                name="webhookUrl"
                render={({ field }) => (
                  <FormItem className="flex-1">
                    <FormLabel className="flex items-center gap-2">
                      <Link2 className="h-4 w-4" />
                      Webhook Endpoint URL
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        type="url"
                        placeholder="https://yourdomain.com/webhook"
                        disabled={!form.watch("webhookEnabled")}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="webhookEnabled"
                render={({ field }) => (
                  <FormItem className="flex flex-col pt-8">
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                        className="data-[state=checked]:bg-emerald-600 cursor-pointer"
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>
            <FormDescription>
              Currently supports &#39;lead&#39; events. The webhook will send POST requests to this URL.
            </FormDescription>

            <CardFooter className="flex justify-end p-0 pt-4 border-t border-emerald-100 dark:border-emerald-800">
              <Button
                type="submit"
                disabled={isSubmitting}
                className="bg-emerald-600  cursor-pointer hover:bg-emerald-700 text-white transition-colors"
              >
                {isSubmitting ? "Saving..." : "Save Webhook Settings"}
              </Button>
            </CardFooter>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}