"use client";
import { Contact, Mail, Phone, User, Info } from "lucide-react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>ontent, CardFooter } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Alert, AlertDescription } from "@/components/ui/alert";
import toast from "react-hot-toast";
import { useParams } from "next/navigation";
import { leadsFormSchema } from "@/validations/validations";



type LeadsFormValues = z.infer<typeof leadsFormSchema>;

interface LeadsSettingsProps {
  agent: {
    _id: string;
    leadTitle?: string;
    leadName?: string;
    leadNameEnabled?: boolean;
    leadEmail?: string;
    leadEmailEnabled?: boolean;
    leadPhone?: string;
    leadPhoneEnabled?: boolean;
  };
}

export default function LeadsSettings({agent}: LeadsSettingsProps) {
  const params = useParams();
  const slug = params.slug;
  
  const form = useForm<LeadsFormValues>({
    resolver: zodResolver(leadsFormSchema),
    defaultValues: {
      leadTitle: agent.leadTitle || "Contact Information",
      leadName: agent.leadName || "",
      leadNameEnabled: agent.leadNameEnabled || false,
      leadEmail: agent.leadEmail || "",
      leadEmailEnabled: agent.leadEmailEnabled || false,
      leadPhone: agent.leadPhone || "",
      leadPhoneEnabled: agent.leadPhoneEnabled || false,
    },
    mode:'onChange',
    reValidateMode:'onChange'
  });

  const { isSubmitting } = form.formState;

 const onSubmit = async (data: LeadsFormValues) => {
  const saveData = {
    leadTitle: data.leadTitle,
    leadName: data.leadNameEnabled ? data.leadName : "",
    leadNameEnabled: data.leadNameEnabled,
    leadEmail: data.leadEmailEnabled ? data.leadEmail : "",
    leadEmailEnabled: data.leadEmailEnabled,
    leadPhone: data.leadPhoneEnabled ? data.leadPhone : "",
    leadPhoneEnabled: data.leadPhoneEnabled,
  };

  const savePromise = fetch(`/dashboard/${slug}/${agent._id}/settings/lead`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(saveData),
  });

  try {
    await toast.promise(
      savePromise,
      {
        loading: 'Saving leads configuration...',
        success: (response) => {
          if (!response.ok) throw new Error("Failed to save");
          return "Leads settings saved successfully";
        },
        error: (err) => err instanceof Error ? err.message : "Failed to save leads settings",
      },
      {
        style: {
          minWidth: '250px',
          padding: '12px',
        },
        success: {
          duration: 4000,
          icon: '✅',
        },
        error: {
          duration: 5000,
          icon: '❌',
        },
      }
    );

    // Optional: refresh data after successful save
    const result = await savePromise.then(res => res.json());
    if (result.message) {
      toast.success(result.message);
    }
  } catch (error) {
    console.error('Error saving leads settings:', error);
    // Error toast is already handled by promise
  }
};

  return (
    <Card className="border-emerald-300 dark:border-emerald-700 shadow-lg">
      <CardHeader>
        <CardTitle className="text-2xl font-bold text-emerald-600 dark:text-emerald-400 flex items-center gap-2">
          <Contact className="h-6 w-6" />
          Leads Collection
        </CardTitle>
      </CardHeader>
      
      <CardContent>
        <Alert className="mb-6 bg-emerald-50 dark:bg-emerald-900/30 border-emerald-200 dark:border-emerald-800">
          <Info className="h-4 w-4 text-emerald-600 dark:text-emerald-400" />
          <AlertDescription>
            Note: Leads form only appears when chatting through the iframe or the chat bubble. 
            The lead form requires at least the title and one more field enabled to work.
          </AlertDescription>
        </Alert>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Lead Title */}
            <FormField
              control={form.control}
              name="leadTitle"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="flex items-center gap-2">
                    <Info className="h-4 w-4" />
                    Form Title
                  </FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      className=""
                      placeholder="Contact Information"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Name Field */}
            <div className="flex items-start gap-4">
              <FormField
                control={form.control}
                name="leadName"
                render={({ field }) => (
                  <FormItem className="flex-1">
                    <FormLabel className="flex items-center gap-2">
                      <User className="h-4 w-4" />
                      Name Field
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        className=""
                        placeholder="Full Name"
                        disabled={!form.watch("leadNameEnabled")}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="leadNameEnabled"
                render={({ field }) => (
                  <FormItem className="flex flex-col pt-8">
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                        className="data-[state=checked]:bg-emerald-600  cursor-pointer"
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>

            {/* Email Field */}
            <div className="flex items-start gap-4">
              <FormField
                control={form.control}
                name="leadEmail"
                render={({ field }) => (
                  <FormItem className="flex-1">
                    <FormLabel className="flex items-center gap-2">
                      <Mail className="h-4 w-4" />
                      Email Field
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        type="email"
                        className=""
                        placeholder="<EMAIL>"
                        disabled={!form.watch("leadEmailEnabled")}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="leadEmailEnabled"
                render={({ field }) => (
                  <FormItem className="flex flex-col pt-8">
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                        className="data-[state=checked]:bg-emerald-600 cursor-pointer"
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>

            {/* Phone Field */}
            <div className="flex items-start gap-4">
              <FormField
                control={form.control}
                name="leadPhone"
                render={({ field }) => (
                  <FormItem className="flex-1">
                    <FormLabel className="flex items-center gap-2">
                      <Phone className="h-4 w-4" />
                      Phone Field
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        type="tel"
                        className=""
                        placeholder="+****************"
                        disabled={!form.watch("leadPhoneEnabled")}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="leadPhoneEnabled"
                render={({ field }) => (
                  <FormItem className="flex flex-col pt-8">
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                        className="data-[state=checked]:bg-emerald-600  cursor-pointer"
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>

            <CardFooter className="flex justify-end p-0 pt-4 border-t border-emerald-100 dark:border-emerald-900">
              <Button
                type="submit"
                disabled={isSubmitting}
                className="bg-emerald-600 cursor-pointer hover:bg-emerald-700 text-white transition-colors"
              >
                {isSubmitting ? "Saving..." : "Save Leads Settings"}
              </Button>
            </CardFooter>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}