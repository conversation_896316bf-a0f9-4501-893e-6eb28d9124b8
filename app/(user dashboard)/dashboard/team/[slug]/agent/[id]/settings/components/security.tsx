"use client";
import { Lock, Globe, ShieldAlert, Info } from "lucide-react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON>ontent,
  CardFooter,
} from "@/components/ui/card";
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from "@/components/ui/form";
import toast from "react-hot-toast";
import { useParams } from "next/navigation";
import { securityFormSchema } from "@/validations/validations";

type SecurityFormValues = z.input<typeof securityFormSchema>;

interface SecuritySettingsProps {
  agent: {
    _id: string;
    isPublic: boolean;
    allowedDomains: string;
    ipLimit: number;
    ipLimitMessage: string;
  };
}

export default function SecuritySettings({ agent }: SecuritySettingsProps) {
  const params = useParams();
  const slug = params.slug;

  const form = useForm<SecurityFormValues>({
    resolver: zodResolver(securityFormSchema),
    defaultValues: {
      isPublic: agent.isPublic,
      allowedDomains: agent.allowedDomains, // always a string
      ipLimit: agent.ipLimit,
      ipLimitMessage: agent.ipLimitMessage || "Too many messages in a row",
    },
    mode: "onChange",
    reValidateMode: "onChange",
  });

  const { isSubmitting } = form.formState;

  const onSubmit = async (data: SecurityFormValues) => {
    const savePromise = fetch(
      `/api/teams/${slug}/agents/${agent._id}/settings/security`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          isPublic: data.isPublic,
          allowedDomains: data.allowedDomains,
          ipLimit: data.ipLimit,
          ipLimitMessage: data.ipLimitMessage,
        }),
      }
    );

    toast
      .promise(
        savePromise,
        {
          loading: "Saving security settings...",
          success: (response) => {
            if (!response.ok) throw new Error("Failed to save");
            return "Security settings saved successfully";
          },
          error: (error) =>
            error instanceof Error
              ? error.message
              : "Failed to save security settings",
        },
        {
          style: {
            minWidth: "250px",
          },
          success: {
            duration: 4000,
            icon: "✅",
          },
          error: {
            duration: 5000,
            icon: "❌",
          },
        }
      )
      .then(async (response) => {
        if (response instanceof Response) {
          const result = await response.json();
          return result.message || "Security settings saved successfully";
        }
        return response;
      })
      .catch((error) => {
        console.error("Error saving security settings:", error);
        throw error;
      });
  };

  return (
    <Card className="border-emerald-300 dark:border-emerald-700 shadow-lg">
      <CardHeader>
        <CardTitle className="text-2xl font-bold text-emerald-600 dark:text-emerald-400 flex items-center gap-2">
          <ShieldAlert className="h-6 w-6" />
          Security Settings
        </CardTitle>
      </CardHeader>

      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Visibility */}
            <FormField
              control={form.control}
              name="isPublic"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="flex items-center gap-2">
                    <Globe className="h-4 w-4" />
                    Visibility
                  </FormLabel>
                  <Select
                    onValueChange={(value) => field.onChange(value === "true")}
                    defaultValue={field.value ? "true" : "false"}
                  >
                    <FormControl>
                      <SelectTrigger className="border-emerald-300 dark:border-emerald-700 cursor-pointer w-1/3">
                        <SelectValue placeholder="Select visibility" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="false" className="cursor-pointer">
                        <div className="flex items-center gap-2">
                          <Lock className="h-4 w-4" />
                          Private
                        </div>
                      </SelectItem>
                      <SelectItem value="true"  className="cursor-pointer">
                        <div className="flex items-center gap-2">
                          <Globe className="h-4 w-4" />
                          Public
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    Private: Only you can access your chatbot
                  </FormDescription>
                  <FormDescription>
                    Public: Others can chat with your chatbot via link or embed
                  </FormDescription>
                </FormItem>
              )}
            />

            {/* Allowed Domains */}
            <FormField
              control={form.control}
              name="allowedDomains"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="flex items-center gap-2">
                    <Globe className="h-4 w-4" />
                    Allowed Domains
                    <Info className="h-4 w-4 text-gray-400" />
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      {...field}
                      placeholder="example.com\nanother.com"
                    />
                  </FormControl>
                  <FormDescription>
                    Enter each domain on a new line
                  </FormDescription>
                  <FormDescription>
                    Your chatbot must be public for domain restrictions to work
                  </FormDescription>
                </FormItem>
              )}
            />

            {/* Rate Limiting */}
            <FormField
              control={form.control}
              name="ipLimit"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="flex items-center gap-2">
                    <ShieldAlert className="h-4 w-4" />
                    Rate Limiting (messages per 60 seconds)
                  </FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      {...field}
                      onChange={(e) =>
                        field.onChange(parseInt(e.target.value) || 0)
                      }
                    />
                  </FormControl>
                  <FormDescription>
                    Limit messages per device to prevent abuse
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Rate Limit Message */}
            <FormField
              control={form.control}
              name="ipLimitMessage"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Rate Limit Message</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      placeholder="Too many messages in a row"
                    />
                  </FormControl>
                  <FormDescription>
                    Message shown when rate limit is exceeded
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <CardFooter className="flex justify-end p-0 pt-4 border-t border-emerald-100 dark:border-emerald-900">
              <Button
                type="submit"
                disabled={isSubmitting}
                className="bg-emerald-600  cursor-pointer hover:bg-emerald-700 text-white transition-colors"
              >
                {isSubmitting ? "Saving..." : "Save Security Settings"}
              </Button>
            </CardFooter>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
