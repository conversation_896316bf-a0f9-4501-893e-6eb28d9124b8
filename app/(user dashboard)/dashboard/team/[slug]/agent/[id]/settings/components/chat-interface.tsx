"use client";
import { motion, AnimatePresence } from "framer-motion";
import {
  Send,
  ThumbsUp,
  ThumbsDown,
  X,
  MessageSquare,
  ChevronUp,
  ChevronDown,
  Bot,
  User as UserIcon,
} from "lucide-react";
import { useState, useEffect, useRef } from "react";
import { useTheme } from "next-themes";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON>Footer, Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import {
  Tooltip,
  TooltipTrigger,
  TooltipContent,
} from "@/components/ui/tooltip";
import { cn } from "@/lib/utils";

interface Message {
  id: string;
  text: string;
  sender: "user" | "bot";
  timestamp: Date;
}

interface ChatInterfaceProps {
  initialMessages?: string[];
  suggestedMessages?: string[];
  messagePlaceholder?: string;
  displayName?: string;
  profilePicturePath?: string;
  iconPath?: string;
  footer?: string;
  theme?: "light" | "dark" | "system";
  customerMessageColor?: string;
  chatBubbleColor?: string;
  alignChatBubble?: "left" | "right";
  collectFeedback?: boolean;
  autoShowInitialDelay?: number;
}

const springTransition = {
  type: "spring",
  stiffness: 500,
  damping: 30,
};

export default function ChatInterface({
  initialMessages = ["Hello! How can I help you today?"],
  suggestedMessages = ["What services do you offer?", "How much does it cost?"],
  messagePlaceholder = "Type your message...",
  displayName = "Chat Assistant",
  profilePicturePath = "/default-profile.png",
  iconPath = "/default-icon.png",
  footer = "Powered by our chat service",
  theme = "light",
  customerMessageColor = "#10b981",
  chatBubbleColor = "#10b981",
  alignChatBubble = "right",
  collectFeedback = true,
  autoShowInitialDelay = 3,
}: ChatInterfaceProps) {
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState("");
  const [isOpen, setIsOpen] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);
  const [suggestedVisible, setSuggestedVisible] = useState(true);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const { resolvedTheme } = useTheme();

  // Generate unique IDs for messages
  const generateId = () => Math.random().toString(36).substring(2, 11);

  // Initialize with initial messages
  useEffect(() => {
    const initialBotMessages = initialMessages.map((text) => ({
      id: generateId(),
      text,
      sender: "bot" as const,
      timestamp: new Date(),
    }));
    setMessages(initialBotMessages);
  }, [initialMessages]);

  // Auto-open after delay
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsOpen(true);
    }, autoShowInitialDelay * 1000);
    return () => clearTimeout(timer);
  }, [autoShowInitialDelay]);

  // Scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  const handleSend = () => {
    if (inputValue.trim()) {
      const newMessage: Message = {
        id: generateId(),
        text: inputValue,
        sender: "user",
        timestamp: new Date(),
      };
      setMessages((prev) => [...prev, newMessage]);
      setInputValue("");
      setSuggestedVisible(false);

      // Simulate bot response after a delay
      setTimeout(() => {
        setMessages((prev) => [
          ...prev,
          {
            id: generateId(),
            text: "Thanks for your message! I'm a demo bot.",
            sender: "bot",
            timestamp: new Date(),
          },
        ]);
      }, 1000);
    }
  };

  const handleSuggestionClick = (text: string) => {
    setInputValue(text);
  };

  const toggleChat = () => {
    setIsOpen(!isOpen);
    if (!isOpen) {
      setIsMinimized(false);
    }
  };

  const toggleMinimize = () => {
    setIsMinimized(!isMinimized);
  };

  const currentTheme = theme === "system" ? resolvedTheme : theme;

  return (
    <div
      className={cn(
        "fixed z-50",
        alignChatBubble === "right" ? "right-6" : "left-6",
        isOpen ? "bottom-6" : "bottom-4"
      )}
    >
      {/* Chat Toggle Button */}
      <AnimatePresence>
        {!isOpen && (
          <motion.div
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.8, opacity: 0 }}
            transition={springTransition}
          >
            <Tooltip>
              <TooltipTrigger asChild className="cursor-pointer">
                <motion.button
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={toggleChat}
                  className={cn(
                    "rounded-full p-3 shadow-lg flex items-center justify-center",
                    `bg-[${chatBubbleColor}]`
                  )}
                  style={{ backgroundColor: chatBubbleColor }}
                  aria-label="Open chat"
                >
                  <Avatar className="w-10 h-10">
                    <AvatarImage src={iconPath} />
                    <AvatarFallback>
                      <MessageSquare className="w-5 h-5" />
                    </AvatarFallback>
                  </Avatar>
                </motion.button>
              </TooltipTrigger>
              <TooltipContent side="left">
                <p>Chat with {displayName}</p>
              </TooltipContent>
            </Tooltip>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Chat Container */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: 20, scale: 0.9 }}
            animate={{
              opacity: 1,
              y: 0,
              scale: 1,
              height: isMinimized ? "auto" : 500,
            }}
            exit={{ opacity: 0, y: 20, scale: 0.9 }}
            transition={springTransition}
            className="relative w-full max-w-md"
          >
            <Card className="overflow-hidden h-full">
              {/* Minimized Header */}
              {isMinimized && (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  className="flex items-center justify-between p-3 cursor-pointer"
                  onClick={toggleMinimize}
                >
                  <div className="flex items-center space-x-3">
                    <Avatar className="w-8 h-8">
                      <AvatarImage src={profilePicturePath} />
                      <AvatarFallback>
                        <Bot className="w-4 h-4" />
                      </AvatarFallback>
                    </Avatar>
                    <span className="font-medium">{displayName}</span>
                    <Badge variant="outline" className="h-5">
                      Online
                    </Badge>
                  </div>
                  <ChevronUp className="w-5 h-5" />
                </motion.div>
              )}

              {/* Expanded Chat */}
              {!isMinimized && (
                <div className="flex flex-col h-full">
                  {/* Chat Header */}
                  <CardHeader className="flex flex-row items-center justify-between p-4 border-b">
                    <div className="flex items-center space-x-3">
                      <Avatar className="w-10 h-10">
                        <AvatarImage src={profilePicturePath} />
                        <AvatarFallback>
                          <Bot className="w-5 h-5" />
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <h3 className="font-semibold">{displayName}</h3>
                        <Badge variant="outline" className="h-5">
                          Online
                        </Badge>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={toggleMinimize}
                            className="cursor-pointer"
                          >
                            <ChevronDown className="w-5 h-5" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>Minimize</TooltipContent>
                      </Tooltip>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={toggleChat}
                            className="cursor-pointer"
                          >
                            <X className="w-5 h-5" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>Close</TooltipContent>
                      </Tooltip>
                    </div>
                  </CardHeader>

                  {/* Chat Messages */}
                  <CardContent className="flex-1 overflow-y-auto p-4 space-y-3">
                    {messages.map((message) => (
                      <motion.div
                        key={message.id}
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className={cn(
                          "flex",
                          message.sender === "user"
                            ? "justify-end"
                            : "justify-start"
                        )}
                      >
                        <div
                          className={cn(
                            "max-w-xs lg:max-w-md px-4 py-2 rounded-lg relative",
                            message.sender === "user"
                              ? "rounded-tr-none"
                              : "rounded-tl-none",
                            message.sender === "user"
                              ? "text-white"
                              : currentTheme === "dark"
                              ? "bg-gray-700"
                              : "bg-gray-100"
                          )}
                          style={{
                            backgroundColor:
                              message.sender === "user"
                                ? customerMessageColor
                                : undefined,
                          }}
                        >
                          <div className="flex items-center gap-2 mb-1">
                            {message.sender === "user" ? (
                              <UserIcon className="w-4 h-4" />
                            ) : (
                              <Bot className="w-4 h-4" />
                            )}
                            <p className="text-sm font-medium">
                              {message.sender === "user" ? "You" : displayName}
                            </p>
                          </div>
                          <p>{message.text}</p>
                          <div className="flex justify-between items-center mt-2">
                            <p className="text-xs opacity-60">
                              {message.timestamp.toLocaleTimeString([], {
                                hour: "2-digit",
                                minute: "2-digit",
                              })}
                            </p>
                            {message.sender === "bot" && collectFeedback && (
                              <div className="flex space-x-1">
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  className="h-6 w-6"
                                >
                                  <ThumbsUp className="w-3 h-3" />
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  className="h-6 w-6"
                                >
                                  <ThumbsDown className="w-3 h-3" />
                                </Button>
                              </div>
                            )}
                          </div>
                        </div>
                      </motion.div>
                    ))}
                    <div ref={messagesEndRef} />
                  </CardContent>

                  {/* Suggested Messages */}
                  {suggestedVisible && suggestedMessages.length > 0 && (
                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="px-4 pb-2"
                    >
                      <div className="flex flex-wrap gap-2">
                        {suggestedMessages.map((text, index) => (
                          <motion.button
                            key={index}
                            whileHover={{ scale: 1.03 }}
                            whileTap={{ scale: 0.97 }}
                            onClick={() => handleSuggestionClick(text)}
                            className={cn(
                              "text-xs px-3 py-1.5 rounded-full",
                              currentTheme === "dark"
                                ? "bg-gray-700 hover:bg-gray-600"
                                : "bg-gray-100 hover:bg-gray-200"
                            )}
                          >
                            {text}
                          </motion.button>
                        ))}
                      </div>
                    </motion.div>
                  )}

                  {/* Chat Input */}
                  <div className="p-4 border-t">
                    <div className="relative">
                      <Input
                        value={inputValue}
                        onChange={(e) => setInputValue(e.target.value)}
                        placeholder={messagePlaceholder}
                        className="pr-12"
                        onKeyDown={(e) => {
                          if (e.key === "Enter" && !e.shiftKey) {
                            e.preventDefault();
                            handleSend();
                          }
                        }}
                      />
                      <Button
                        size="icon"
                        variant="ghost"
                        className="absolute right-2 top-1/2 -translate-y-1/2 h-8 w-8 hover:bg-transparent"
                        onClick={handleSend}
                        disabled={!inputValue.trim()}
                      >
                        <Send
                          className={`w-4 h-4 ${
                            inputValue.trim()
                              ? "text-emerald-500"
                              : "text-gray-400"
                          }`}
                        />
                      </Button>
                    </div>
                  </div>

                  {/* Chat Footer */}
                  <CardFooter className="p-2 text-center text-xs opacity-70 border-t">
                    {footer}
                  </CardFooter>
                </div>
              )}
            </Card>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}