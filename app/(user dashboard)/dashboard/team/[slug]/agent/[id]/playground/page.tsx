"use client";

import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {ChevronDown,
  ChevronRight,
  Info,
  Save,
  AlertTriangle,
} from "lucide-react";
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  Di<PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import AgentTabs from "@/components/tabs/agent-tabs";

interface Agent {
  _id: string;
  url: string;
  isTrained: boolean;
  model: string;
  temperature: number;
  instructions: string;
}

interface Team {
  url: string;
}

const defaultAgent: Agent = {
  _id: "",
  url: "",
  isTrained: false,
  model: "gpt-4o",
  temperature: 0.7,
  instructions: "",
};

const AgentPlayground = () => {

  const Agent: Partial<Agent> = {
    ...defaultAgent,
  };

  const team: Partial<Team> = {
    url: "",
  };
  const mergedAgent = { ...defaultAgent, ...Agent };
  const mergedTeam = { url: "", ...team };

  const [currentModel, setCurrentModel] = useState(mergedAgent.model);
  const [currentTemp, setCurrentTemp] = useState(mergedAgent.temperature);
  const [instructions, setInstructions] = useState(mergedAgent.instructions);
  const [selectedPrompt, setSelectedPrompt] = useState("");
  const [isConfigOpen, setIsConfigOpen] = useState(true);

  const [showTrainingAlert, setShowTrainingAlert] = useState(
    !mergedAgent.isTrained
  );

  const handleSave = async () => {
    try {
      const response = await fetch(
        `/api/Agents/${mergedTeam.url}/${mergedAgent._id}/settings`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            model: currentModel,
            temperature: currentTemp,
            instructions,
          }),
        }
      );

      if (!response.ok) throw new Error("Failed to save settings");
      alert("Settings saved successfully!");
    } catch (error) {
      console.error("Error saving settings:", error);
      alert("Error saving settings");
    }
  };

  const systemPrompts = [
    { value: "Agent", label: "AI Agent" },
    { value: "support_agent", label: "Customer support agent" },
    { value: "sales_agent", label: "Sales agent" },
    { value: "language_tutor", label: "Language tutor" },
    { value: "life_coach", label: "Life coach" },
    { value: "fashion_designer", label: "Futuristic fashion designer" },
  ];

  const models = [
    { value: "o1-mini", label: "o1 mini" },
    { value: "gpt-4o-mini", label: "GPT-4o mini" },
    { value: "gpt-4o", label: "GPT-4o" },
    { value: "gpt-4-turbo", label: "GPT-4 Turbo" },
    {
      value: "claude-3-5-haiku-20241022",
      label: "Claude 3 Haiku",
      disabled: true,
    },
    { value: "claude-3-opus-20240229", label: "Claude 3 Opus", disabled: true },
    {
      value: "claude-3-5-sonnet-20241022",
      label: "Claude 3.5 Sonnet",
      disabled: true,
    },
    { value: "gemini-1.5-flash", label: "Gemini 1.5 Flash", disabled: true },
    { value: "gemini-1.5-pro", label: "Gemini 1.5 Pro", disabled: true },
  ];

  return (
    <div className="min-h-screen ">
      <AnimatePresence>
        <Dialog
          open={showTrainingAlert && !mergedAgent.isTrained}
          onOpenChange={setShowTrainingAlert}
        >
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
          >
            <DialogContent className="sm:max-w-md bg-white dark:bg-gray-800 border-0 shadow-xl rounded-xl">
              <motion.div
                initial={{ scale: 0.9 }}
                animate={{ scale: 1 }}
                className="flex items-start"
              >
                <div className="flex-shrink-0 mt-1">
                  <AlertTriangle className="h-6 w-6 text-yellow-500 dark:text-yellow-400" />
                </div>
                <div className="ml-4">
                  <DialogHeader>
                    <DialogTitle className="text-left text-gray-900 dark:text-gray-100">
                      Your Agent isn&apos;t trained yet!
                    </DialogTitle>
                    <DialogDescription className="text-left mt-2 text-gray-600 dark:text-gray-300">
                      Train it under{" "}
                      <span className="font-semibold">&#39;Sources&#39;</span>{" "}
                      {">"}{" "}
                      <span className="font-semibold">
                        &#39;Train Agent&#39;
                      </span>{" "}
                      to start messaging. Customize its appearance and settings
                      in{" "}
                      <span className="font-semibold">&#39;Settings&#39;</span>{" "}
                      {">"}{" "}
                      <span className="font-semibold">&#39;Interface&#39;</span>
                      .
                    </DialogDescription>
                  </DialogHeader>
                  <DialogFooter className="sm:justify-end mt-4">
                    <Button
                      type="button"
                      className="cursor-pointer inline-flex justify-center px-4 py-2 text-sm font-medium text-emerald-500 dark:text-emerald-400 bg-emerald-100 dark:bg-emerald-900/50 rounded-md hover:bg-emerald-200 dark:hover:bg-emerald-800/50 focus:outline-none focus-visible:ring-2 focus-visible:ring-emerald-500 focus-visible:ring-offset-2"
                      onClick={() => setShowTrainingAlert(false)}
                    >
                      Got it!
                    </Button>
                  </DialogFooter>
                </div>
              </motion.div>
            </DialogContent>
          </motion.div>
        </Dialog>
      </AnimatePresence>
      <div className="container mx-auto px-4 py-6">
        {/* Header */}
        <header className="mb-8">
          <h1 className="text-3xl font-bold text-emerald-500 dark:text-emerald-400 mb-2">
            Agent Playground
          </h1>
          <p className="text-gray-600 dark:text-gray-300">
            Configure and test your AI assistant
          </p>
        </header>

        {/* Navigation Tabs */}
       <AgentTabs/>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Configuration Panel */}
          <motion.div
            className="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            <div
              className="p-4 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between cursor-pointer"
              onClick={() => setIsConfigOpen(!isConfigOpen)}
            >
              <div className="flex items-center">
                {isConfigOpen ? (
                  <ChevronDown className="text-emerald-500 dark:text-emerald-400 mr-2" />
                ) : (
                  <ChevronRight className="text-emerald-500 dark:text-emerald-400 mr-2" />
                )}
                <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200">
                  Customize Agent
                </h2>
              </div>
              <div className="flex items-center">
                <div className="flex items-center mr-4">
                  <div className="relative inline-block w-10 mr-2 align-middle select-none transition duration-200 ease-in">
                    <input
                      type="checkbox"
                      id="trained-toggle"
                      checked={Agent.isTrained}
                      disabled
                      className="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer"
                    />
                    <label
                      htmlFor="trained-toggle"
                      className={`toggle-label block overflow-hidden h-6 rounded-full cursor-pointer ${
                        Agent.isTrained
                          ? "bg-emerald-500 dark:bg-emerald-600"
                          : "bg-gray-300 dark:bg-gray-600"
                      }`}
                    ></label>
                  </div>
                  <span className="text-gray-700 dark:text-gray-300 text-sm">
                    {Agent.isTrained ? "Trained" : "Not trained"}
                  </span>
                </div>
              </div>
            </div>

            <AnimatePresence>
              {isConfigOpen && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: "auto" }}
                  exit={{ opacity: 0, height: 0 }}
                  transition={{ duration: 0.3 }}
                  className="p-6"
                >
                  <p className="text-sm text-gray-500 dark:text-gray-400 mb-6">
                    Once you change the configuration, save it to the Agent by
                    clicking the &quot;Save to Agent&quot; button.
                  </p>

                  {/* Model Selection */}
                  <div className="mb-6">
                    <label
                      htmlFor="model"
                      className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
                    >
                      Model
                    </label>
                    <select
                      id="model"
                      value={currentModel}
                      onChange={(e) => setCurrentModel(e.target.value)}
                      className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-emerald-500 focus:border-emerald-500"
                    >
                      {models.map((model) => (
                        <option
                          key={model.value}
                          value={model.value}
                          disabled={model.disabled}
                          className="dark:bg-gray-800"
                        >
                          {model.label}
                        </option>
                      ))}
                    </select>
                  </div>

                  {/* Temperature Slider */}
                  <div className="mb-6">
                    <div className="flex justify-between items-center mb-2">
                      <label
                        htmlFor="temperature"
                        className="block text-sm font-medium text-gray-700 dark:text-gray-300"
                      >
                        Temperature
                      </label>
                      <span className="text-sm font-medium bg-emerald-100 dark:bg-emerald-900 text-emerald-800 dark:text-emerald-200 px-2 py-1 rounded-full">
                        {currentTemp}
                      </span>
                    </div>
                    <input
                      type="range"
                      id="temperature"
                      min="0"
                      max="1"
                      step="0.1"
                      value={currentTemp}
                      onChange={(e) =>
                        setCurrentTemp(parseFloat(e.target.value))
                      }
                      className="w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer accent-emerald-500 dark:accent-emerald-600"
                    />
                    <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-1">
                      <span>Reserved</span>
                      <span>Creative</span>
                    </div>
                  </div>

                  <div className="border-t border-gray-200 dark:border-gray-700 my-6"></div>

                  {/* System Prompt */}
                  <div className="mb-6">
                    <label
                      htmlFor="system-prompt"
                      className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
                    >
                      System Prompt
                    </label>
                    <select
                      id="system-prompt"
                      value={selectedPrompt}
                      onChange={(e) => setSelectedPrompt(e.target.value)}
                      className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-emerald-500 focus:border-emerald-500"
                    >
                      <option value="" disabled>
                        Select a system prompt
                      </option>
                      {systemPrompts.map((prompt) => (
                        <option
                          key={prompt.value}
                          value={prompt.value}
                          className="dark:bg-gray-800"
                        >
                          {prompt.label}
                        </option>
                      ))}
                    </select>
                  </div>

                  {/* Instructions */}
                  <div className="mb-6">
                    <div className="flex items-center mb-2">
                      <label
                        htmlFor="instructions"
                        className="block text-sm font-medium text-gray-700 dark:text-gray-300"
                      >
                        Instructions
                      </label>
                      <button className="ml-2 text-gray-400 hover:text-gray-500 dark:hover:text-gray-300">
                        <Info className="h-4 w-4" />
                      </button>
                    </div>
                    <textarea
                      id="instructions"
                      rows={8}
                      value={instructions}
                      onChange={(e) => setInstructions(e.target.value)}
                      className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-emerald-500 focus:border-emerald-500"
                      placeholder="Enter instructions for your Agent..."
                    />
                  </div>

                  {/* Save Button */}
                  <motion.button
                    onClick={handleSave}
                    className="w-full flex items-center justify-center py-3 px-4 bg-emerald-500 dark:bg-emerald-600 hover:bg-emerald-600 dark:hover:bg-emerald-700 text-white font-medium rounded-lg transition-colors"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <Save className="mr-2 h-4 w-4" />
                    Save to Agent
                  </motion.button>
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>

          {/* Chat Preview */}
          <motion.div
            className="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.1 }}
          >
            <div className="p-4 border-b border-gray-200 dark:border-gray-700">
              <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200">
                Chat Preview
              </h2>
              <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                After saving a new configuration to your Agent, experiment by
                chatting with it to evaluate the changes.
              </p>
            </div>
            <div className="p-4 h-full">
              <div className="relative w-full h-[600px] rounded-lg overflow-hidden border border-gray-200 dark:border-gray-700">
                <iframe
                  src={`/p/iframe/${Agent._id}`}
                  className="absolute top-0 left-0 w-full h-full"
                  frameBorder="0"
                  allow="cross-origin"
                />
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default AgentPlayground;
