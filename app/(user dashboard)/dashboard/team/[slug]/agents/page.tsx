"use client";

import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Refresh<PERSON>w } from "lucide-react";
import { motion } from "framer-motion";
import { usePathname } from "next/navigation";
import { useTheme } from "next-themes";
import { useTeam } from "@/hooks/use-teams";
import { AgentCard } from "@/components/cards/agent-card";
import { ChatbotsLoadingState } from "@/components/loader/chatbot-loading-state";
import ErrorDialog from "@/components/error/error-state";
import { CreateAgentModal } from "@/components/modals/create-agent-modal";
import Link from "next/link";

const AgentsPage = () => {
  const pathname = usePathname();
  const slug = pathname.split("/").filter(Boolean)[2]; // Get team slug from URL
  const [isCreatingAgent, setIsCreatingAgent] = useState(false);

  const { resolvedTheme } = useTheme();
  const {
    data: teamData,
    isPending: isLoading,
    error,
    refetch,
    isFetching,
  } = useTeam(slug ?? "");
  const [errorDialogOpen, setErrorDialogOpen] = useState(false);

  useEffect(() => {
    if (error && error.response?.status !== 404) {
      setErrorDialogOpen(true);
    }
  }, [error]);

  const currentTeam = teamData?.data?.currentTeam;

  // Fetch agents data from API
  const [teamAgents, setTeamAgents] = useState<any[]>([]);
  const [agentsLoading, setAgentsLoading] = useState(true);
  const [agentsError, setAgentsError] = useState<string | null>(null);

  useEffect(() => {
    const fetchAgents = async () => {
      if (!currentTeam?._id) return;

      try {
        setAgentsLoading(true);
        const response = await fetch(`/api/agents?teamId=${currentTeam._id}`);
        const result = await response.json();

        if (!response.ok) {
          throw new Error(result.error || 'Failed to fetch agents');
        }

        // Convert date strings to Date objects
        const agentsWithDates = (result.data || []).map((agent: any) => ({
          ...agent,
          createdAt: new Date(agent.createdAt),
          updatedAt: new Date(agent.updatedAt),
          lastUsed: agent.lastUsed ? new Date(agent.lastUsed) : null,
          lastTrainedAt: agent.lastTrainedAt ? new Date(agent.lastTrainedAt) : null,
        }));
        setTeamAgents(agentsWithDates);
      } catch (error) {
        console.error("Error fetching agents:", error);
        setAgentsError(error instanceof Error ? error.message : 'Failed to fetch agents');
      } finally {
        setAgentsLoading(false);
      }
    };

    fetchAgents();
  }, [currentTeam?._id]);

  const refreshAgents = () => {
    if (currentTeam?._id) {
      setAgentsLoading(true);
      setAgentsError(null);

      fetch(`/api/agents?teamId=${currentTeam._id}`)
        .then(response => response.json())
        .then(result => {
          if (result.success) {
            // Convert date strings to Date objects
            const agentsWithDates = (result.data || []).map((agent: any) => ({
              ...agent,
              createdAt: new Date(agent.createdAt),
              updatedAt: new Date(agent.updatedAt),
              lastUsed: agent.lastUsed ? new Date(agent.lastUsed) : null,
              lastTrainedAt: agent.lastTrainedAt ? new Date(agent.lastTrainedAt) : null,
            }));
            setTeamAgents(agentsWithDates);
          } else {
            setAgentsError(result.error || 'Failed to fetch agents');
          }
        })
        .catch(error => {
          setAgentsError(error.message || 'Failed to fetch agents');
        })
        .finally(() => {
          setAgentsLoading(false);
        });
    }
  };

  if (isLoading || agentsLoading) return <ChatbotsLoadingState />;

  if (error && error.response?.status !== 404) {
    return (
      <ErrorDialog
        error={error || new Error("Unknown problem has occurred.")}
        open={errorDialogOpen}
        retry={refetch}
        isFetching={isFetching}
      />
    );
  }

  if (agentsError) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center py-16 bg-white/50 dark:bg-gray-800/50 rounded-xl border border-dashed border-red-300 dark:border-red-600">
          <div className="mx-auto h-24 w-24 text-red-400 dark:text-red-500 mb-4">
            <AlertTriangle className="h-full w-full" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-1">
            Error Loading Agents
          </h3>
          <p className="text-gray-500 dark:text-gray-400 mb-6">
            {agentsError}
          </p>
          <button
            onClick={() => window.location.reload()}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-emerald-600 hover:bg-emerald-700"
          >
            <RefreshCw className="h-5 w-5 mr-2" />
            Try Again
          </button>
        </div>
      </div>
    );
  }

  // Team not found case
  if (!teamData) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4 transition-colors duration-300">
        <div className="max-w-md w-full text-center">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.3 }}
            className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200/70 dark:border-gray-700/50 p-8 relative overflow-hidden"
          >
            <div className="absolute -top-20 -right-20 w-64 h-64 rounded-full bg-red-300/10 dark:bg-red-500/10 blur-3xl"></div>
            <div className="absolute -bottom-10 -left-10 w-48 h-48 rounded-full bg-amber-300/10 dark:bg-amber-500/10 blur-3xl"></div>

            <div className="relative z-10">
              <div className="mx-auto h-16 w-16 flex items-center justify-center rounded-full bg-red-100 dark:bg-red-900/50 text-red-500 dark:text-red-400 mb-4">
                <AlertTriangle className="h-8 w-8" />
              </div>
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                Team Not Found
              </h2>
              <p className="text-gray-600 dark:text-gray-300 mb-6">
                The team &quot;{slug}&quot; doesn&apos;t exist or you don&apos;t have access to it.
              </p>

              <div className="flex flex-col sm:flex-row gap-3 justify-center">
                <Link
                  href="/dashboard"
                  className="px-4 py-2 bg-emerald-600 hover:bg-emerald-700 text-white rounded-md transition-colors flex items-center justify-center gap-2"
                >
                  <Bot className="h-4 w-4" />
                  Go to Your Teams
                </Link>
                <button
                  onClick={() => refetch()}
                  className="cursor-pointer px-4 py-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-800 dark:text-gray-200 rounded-md transition-colors flex items-center justify-center gap-2"
                >
                  <RefreshCw className="h-4 w-4" />
                  Try Again
                </button>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="flex flex-col lg:flex-row">
        {/* Main Content Area */}
        <main className="flex-1">
          {/* Title and Create Button */}
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-8">
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4 sm:mb-0">
              AI Agents
              <span className="ml-2 text-emerald-500 dark:text-emerald-400 bg-emerald-100 dark:bg-emerald-900/30 px-2 py-1 rounded-full text-sm">
                {teamAgents.length}
              </span>
            </h1>

            <button
              onClick={() => setIsCreatingAgent(true)}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-emerald-600 hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500 dark:bg-emerald-500 dark:hover:bg-emerald-600 transition-colors"
            >
              <Plus className="h-5 w-5 mr-2" />
              New Agent
            </button>
          </div>

          {/* Agents Grid */}
          {teamAgents.length > 0 ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
              {teamAgents.map((agent) => (
                <AgentCard
                  key={agent._id}
                  agent={agent}
                  teamUrl={currentTeam?.url ?? ""}
                  theme={resolvedTheme || "light"}
                />
              ))}
            </div>
          ) : (
            <div className="text-center py-16 bg-white/50 dark:bg-gray-800/50 rounded-xl border border-dashed border-gray-300 dark:border-gray-600">
              <div className="mx-auto h-24 w-24 text-gray-400 dark:text-gray-500 mb-4">
                <Bot className="h-full w-full" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-1">
                No agents yet
              </h3>
              <p className="text-gray-500 dark:text-gray-400 mb-6">
                Get started by creating your first AI agent.
              </p>
              <button
                onClick={() => setIsCreatingAgent(true)}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-emerald-600 hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500 dark:bg-emerald-500 dark:hover:bg-emerald-600"
              >
                <Plus className="h-5 w-5 mr-2" />
                Create Agent
              </button>
            </div>
          )}
        </main>
      </div>

      {/* Create Agent Modal */}
      <CreateAgentModal
        isOpen={isCreatingAgent}
        onClose={() => setIsCreatingAgent(false)}
        teamId={currentTeam?._id?.toString() ?? ""}
        onSuccess={refreshAgents}
      />
    </div>
  );
};

export default AgentsPage;
