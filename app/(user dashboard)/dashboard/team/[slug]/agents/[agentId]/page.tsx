"use client";

import React, { useState, useEffect, use } from "react";
import { usePathname, useRouter } from "next/navigation";
import { ArrowLef<PERSON>, Settings, Play, Share, Plus, Workflow } from "lucide-react";
import Link from "next/link";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import { EditAgentModal } from "@/components/modals/edit-agent-modal";
import { CreateWorkflowModal } from "@/components/modals/create-workflow-modal";
import { WorkflowCard } from "@/components/cards/workflow-card";
import { useTheme } from "next-themes";

interface AgentDetailPageProps {
  params: Promise<{
    slug: string;
    agentId: string;
  }>;
}

const AgentDetailPage = ({ params }: AgentDetailPageProps) => {
  const { slug, agentId } = use(params);
  const router = useRouter();
  const { resolvedTheme } = useTheme();

  const [agent, setAgent] = useState<any>(null);
  const [workflows, setWorkflows] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isCreateWorkflowOpen, setIsCreateWorkflowOpen] = useState(false);

  useEffect(() => {
    const fetchAgent = async () => {
      try {
        setIsLoading(true);
        const response = await fetch(`/api/agents/${agentId}`);
        const result = await response.json();

        if (!response.ok) {
          throw new Error(result.error || 'Failed to fetch agent');
        }

        // Convert date strings to Date objects
        const agentWithDates = {
          ...result.data,
          createdAt: new Date(result.data.createdAt),
          updatedAt: new Date(result.data.updatedAt),
          lastUsed: result.data.lastUsed ? new Date(result.data.lastUsed) : null,
          lastTrainedAt: result.data.lastTrainedAt ? new Date(result.data.lastTrainedAt) : null,
        };

        setAgent(agentWithDates);

        // Fetch workflows for this agent
        const workflowsResponse = await fetch(`/api/workflows?agentId=${agentId}`);
        const workflowsResult = await workflowsResponse.json();

        if (workflowsResponse.ok && workflowsResult.success) {
          // Convert date strings to Date objects for workflows
          const workflowsWithDates = (workflowsResult.data || []).map((workflow: any) => ({
            ...workflow,
            createdAt: new Date(workflow.createdAt),
            updatedAt: new Date(workflow.updatedAt),
            lastExecuted: workflow.lastExecuted ? new Date(workflow.lastExecuted) : null,
            nodes: workflow.nodes?.length || 0, // Count of nodes for display
          }));
          setWorkflows(workflowsWithDates);
        } else {
          console.error("Failed to fetch workflows:", workflowsResult.error);
          setWorkflows([]);
        }

      } catch (error) {
        console.error("Error fetching agent:", error);
        setError(error instanceof Error ? error.message : 'Failed to fetch agent');
      } finally {
        setIsLoading(false);
      }
    };

    if (agentId) {
      fetchAgent();
    }
  }, [agentId]);

  const handleAgentUpdate = (updatedAgent: any) => {
    setAgent(updatedAgent);
  };

  if (isLoading) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-emerald-500"></div>
      </div>
    );
  }

  if (error || !agent) {
    return (
      <div className="h-full flex items-center justify-center p-4">
        <div className="max-w-md w-full text-center">
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200/70 dark:border-gray-700/50 p-8">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
              Agent Not Found
            </h2>
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              {error || "The agent you're looking for doesn't exist or you don't have access to it."}
            </p>
            <Link
              href={`/dashboard/team/${slug}/agents`}
              className="px-4 py-2 bg-emerald-600 hover:bg-emerald-700 text-white rounded-md transition-colors"
            >
              Back to Agents
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-6 py-4">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link
              href={`/dashboard/team/${slug}/agents`}
              className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
            >
              <ArrowLeft className="h-5 w-5" />
            </Link>
            <div>
              <h1 className="text-xl font-semibold text-gray-900 dark:text-white">
                {agent.name}
              </h1>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {agent.description || "No description"}
              </p>
            </div>
            <div className="px-2 py-1 bg-emerald-100 dark:bg-emerald-900/30 text-emerald-600 dark:text-emerald-400 text-xs rounded-full">
              {agent.model}
            </div>
          </div>

          <div className="flex items-center space-x-3">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsEditModalOpen(true)}
              className="flex items-center space-x-2"
            >
              <Settings className="h-4 w-4" />
              <span>Settings</span>
            </Button>

            <Button
              variant="outline"
              size="sm"
              className="flex items-center space-x-2"
            >
              <Share className="h-4 w-4" />
              <span>Share</span>
            </Button>

            <Button
              size="sm"
              className="bg-emerald-600 hover:bg-emerald-700 text-white flex items-center space-x-2"
            >
              <Play className="h-4 w-4" />
              <span>Test Agent</span>
            </Button>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-7xl mx-auto px-6 py-8">
        {/* Agent Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
            <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Usage Count</h3>
            <p className="text-2xl font-bold text-gray-900 dark:text-white">{agent.usageCount || 0}</p>
          </div>
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
            <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Workflows</h3>
            <p className="text-2xl font-bold text-gray-900 dark:text-white">{workflows.length}</p>
          </div>
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
            <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Tools</h3>
            <p className="text-2xl font-bold text-gray-900 dark:text-white">{agent.tools?.length || 0}</p>
          </div>
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
            <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Last Used</h3>
            <p className="text-sm text-gray-900 dark:text-white">
              {agent.lastUsed ? agent.lastUsed.toLocaleDateString() : 'Never'}
            </p>
          </div>
        </div>

        {/* Workflows Section */}
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-2">
              <Workflow className="h-5 w-5 text-emerald-600 dark:text-emerald-400" />
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                Workflows
              </h2>
              <span className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 text-xs rounded-full">
                {workflows.length}
              </span>
            </div>
            <Button
              onClick={() => setIsCreateWorkflowOpen(true)}
              className="bg-emerald-600 hover:bg-emerald-700 text-white flex items-center space-x-2"
            >
              <Plus className="h-4 w-4" />
              <span>New Workflow</span>
            </Button>
          </div>

          {workflows.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {workflows.map((workflow) => (
                <WorkflowCard
                  key={workflow._id}
                  workflow={workflow}
                  teamUrl={slug}
                  theme={resolvedTheme || "light"}
                  agentId={agentId}
                />
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <Workflow className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                No workflows yet
              </h3>
              <p className="text-gray-500 dark:text-gray-400 mb-6">
                Create your first workflow to automate tasks with this agent.
              </p>
              <Button
                onClick={() => setIsCreateWorkflowOpen(true)}
                className="bg-emerald-600 hover:bg-emerald-700 text-white"
              >
                <Plus className="h-4 w-4 mr-2" />
                Create Workflow
              </Button>
            </div>
          )}
        </div>
      </div>

      {/* Edit Agent Modal */}
      <EditAgentModal
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        agent={agent}
        onUpdate={handleAgentUpdate}
      />

      {/* Create Workflow Modal */}
      <CreateWorkflowModal
        isOpen={isCreateWorkflowOpen}
        onClose={() => setIsCreateWorkflowOpen(false)}
        agentId={agentId}
      />
    </div>
  );
};

export default AgentDetailPage;
