"use client";

import React, { useState, useEffect, use } from "react";
import { usePathname } from "next/navigation";
import { ArrowLeft, Play, Save, Settings, Share, Zap, Power } from "lucide-react";
import Link from "next/link";
import { motion } from "framer-motion";
import WorkflowBuilder from "@/components/workflow/workflow-builder";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";

interface WorkflowDetailPageProps {
  params: Promise<{
    slug: string;
    agentId: string;
    workflowId: string;
  }>;
}

const WorkflowDetailPage = ({ params }: WorkflowDetailPageProps) => {
  const { slug, agentId, workflowId } = use(params);

  const [workflow, setWorkflow] = useState<any>(null);
  const [agent, setAgent] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isSaving, setIsSaving] = useState(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  // Workflow settings
  const [executionMode, setExecutionMode] = useState<'direct' | 'async'>('direct');
  const [isWorkflowActive, setIsWorkflowActive] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);
        
        // Fetch workflow and agent data
        const [workflowResponse, agentResponse] = await Promise.all([
          fetch(`/api/workflows/${workflowId}`),
          fetch(`/api/agents/${agentId}`)
        ]);

        const [workflowResult, agentResult] = await Promise.all([
          workflowResponse.json(),
          agentResponse.json()
        ]);

        if (!workflowResponse.ok) {
          throw new Error(workflowResult.error || 'Failed to fetch workflow');
        }

        if (!agentResponse.ok) {
          throw new Error(agentResult.error || 'Failed to fetch agent');
        }

        setWorkflow(workflowResult.data);
        setAgent(agentResult.data);

        // Initialize workflow settings
        setExecutionMode(workflowResult.data?.executionMode || 'direct');
        setIsWorkflowActive(workflowResult.data?.isActive ?? true);

      } catch (error) {
        console.error("Error fetching data:", error);
        setError(error instanceof Error ? error.message : 'Failed to fetch data');
      } finally {
        setIsLoading(false);
      }
    };

    if (workflowId && agentId) {
      fetchData();
    }
  }, [workflowId, agentId]);

  const handleSave = async () => {
    if (!workflow) return;
    
    setIsSaving(true);
    try {
      const response = await fetch(`/api/workflows/${workflowId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          nodes: workflow.nodes,
          edges: workflow.edges,
          version: workflow.version,
          executionMode: executionMode,
          isActive: isWorkflowActive,
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to save workflow');
      }

      setWorkflow(result.data);
      setHasUnsavedChanges(false);
    } catch (error) {
      console.error("Error saving workflow:", error);
      alert(error instanceof Error ? error.message : 'Failed to save workflow');
    } finally {
      setIsSaving(false);
    }
  };

  const handleExecute = async () => {
    try {
      setIsSaving(true);

      // Execute workflow
      const response = await fetch(`/api/workflows/${workflowId}/execute`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          mode: executionMode === 'direct' ? 'sync' : 'async',
          input: { message: 'Manual execution' },
          variables: { executed_at: new Date().toISOString() }
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to execute workflow');
      }

      // Show success message with execution ID
      alert(`Workflow execution started!\nExecution ID: ${result.executionId}\n\nYou can monitor the progress in the browser console.`);

      // Optional: Open execution status in new tab
      // window.open(`/dashboard/team/${teamSlug}/agents/${agentId}/workflows/${workflowId}/executions/${result.executionId}`, '_blank');

      console.log('Workflow execution started:', result);

    } catch (error) {
      console.error("Error executing workflow:", error);
      alert(error instanceof Error ? error.message : 'Failed to execute workflow');
    } finally {
      setIsSaving(false);
    }
  };

  const handleExecutionModeChange = (checked: boolean) => {
    setExecutionMode(checked ? 'async' : 'direct');
    setHasUnsavedChanges(true);
  };

  const handleWorkflowActiveChange = (checked: boolean) => {
    setIsWorkflowActive(checked);
    setHasUnsavedChanges(true);
  };

  const handleWorkflowChange = (nodes: any[], edges: any[]) => {
    setWorkflow((prev: any) => ({
      ...prev,
      nodes,
      edges,
    }));
    setHasUnsavedChanges(true);
  };

  if (isLoading) {
    return (
      <div className="h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-emerald-500"></div>
      </div>
    );
  }

  if (error || !workflow || !agent) {
    return (
      <div className="h-screen flex items-center justify-center p-4 bg-gray-50 dark:bg-gray-900">
        <div className="max-w-md w-full text-center">
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200/70 dark:border-gray-700/50 p-8">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
              Workflow Not Found
            </h2>
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              {error || "The workflow you're looking for doesn't exist or you don't have access to it."}
            </p>
            <Link
              href={`/dashboard/team/${slug}/agents/${agentId}`}
              className="px-4 py-2 bg-emerald-600 hover:bg-emerald-700 text-white rounded-md transition-colors"
            >
              Back to Agent
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen flex flex-col bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link
              href={`/dashboard/team/${slug}/agents/${agentId}`}
              className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
            >
              <ArrowLeft className="h-5 w-5" />
            </Link>
            <div>
              <h1 className="text-xl font-semibold text-gray-900 dark:text-white">
                {workflow.name}
              </h1>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Agent: {agent.name} • {workflow.description || "No description"}
              </p>
            </div>
            {hasUnsavedChanges && (
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                className="px-2 py-1 bg-orange-100 dark:bg-orange-900/30 text-orange-600 dark:text-orange-400 text-xs rounded-full"
              >
                Unsaved changes
              </motion.div>
            )}
          </div>

          <div className="flex items-center space-x-6">
            {/* Workflow Settings Toggles */}
            <div className="flex items-center space-x-4">
              {/* Execution Mode Toggle */}
              <div className="flex items-center space-x-2">
                <Zap className="h-4 w-4 text-gray-500" />
                <Label htmlFor="execution-mode" className="text-sm font-medium">
                  {executionMode === 'direct' ? 'Direct' : 'Async'}
                </Label>
                <Switch
                  id="execution-mode"
                  checked={executionMode === 'async'}
                  onCheckedChange={handleExecutionModeChange}
                />
              </div>

              {/* Workflow Active Toggle */}
              <div className="flex items-center space-x-2">
                <Power className="h-4 w-4 text-gray-500" />
                <Label htmlFor="workflow-active" className="text-sm font-medium">
                  {isWorkflowActive ? 'Active' : 'Inactive'}
                </Label>
                <Switch
                  id="workflow-active"
                  checked={isWorkflowActive}
                  onCheckedChange={handleWorkflowActiveChange}
                />
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center space-x-3">
              <Button
                variant="outline"
                size="sm"
                onClick={handleSave}
                disabled={isSaving || !hasUnsavedChanges}
                className="flex items-center space-x-2"
              >
                <Save className="h-4 w-4" />
                <span>{isSaving ? "Saving..." : "Save"}</span>
              </Button>

            <Button
              variant="outline"
              size="sm"
              className="flex items-center space-x-2"
            >
              <Share className="h-4 w-4" />
              <span>Share</span>
            </Button>

            <Button
              variant="outline"
              size="sm"
              className="flex items-center space-x-2"
            >
              <Settings className="h-4 w-4" />
              <span>Settings</span>
            </Button>

            <Button
              size="sm"
              onClick={handleExecute}
              className="bg-emerald-600 hover:bg-emerald-700 text-white flex items-center space-x-2"
            >
              <Play className="h-4 w-4" />
              <span>Run</span>
            </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Workflow Builder */}
      <div className="flex-1 overflow-hidden">
        <WorkflowBuilder
          workflow={workflow}
          onChange={handleWorkflowChange}
        />
      </div>
    </div>
  );
};

export default WorkflowDetailPage;
