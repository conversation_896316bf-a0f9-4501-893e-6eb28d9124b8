"use client";

import React, { useState, useEffect } from "react";
import { usePathname } from "next/navigation";
import { ArrowLeft, Play, Save, Settings, Share, MessageSquare, Plug } from "lucide-react";
import Link from "next/link";
import { motion } from "framer-motion";
import { WorkflowBuilder } from "@/components/workflow/workflow-builder";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import toast from "react-hot-toast";
import { validateWorkflow, ValidationResult } from "@/lib/workflow-validation";
import { ChatInterface } from "@/components/chat/chat-interface";
import { CredentialManagerModal } from "@/components/credentials/credential-manager-modal";

const WorkflowDetailPage = () => {
  const pathname = usePathname();
  const pathParts = pathname.split("/").filter(Boolean);
  const slug = pathParts[2]; // team slug
  const workflowId = pathParts[4]; // workflow ID

  const [workflow, setWorkflow] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isSaving, setIsSaving] = useState(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [isChatPreviewOpen, setIsChatPreviewOpen] = useState(false);
  const [isCredentialManagerOpen, setIsCredentialManagerOpen] = useState(false);

  // Load workflow data
  useEffect(() => {
    const loadWorkflow = async () => {
      if (!workflowId) return;

      setIsLoading(true);
      setError(null);

      try {
        const response = await fetch(`/api/workflows/${workflowId}`);
        const result = await response.json();

        if (!response.ok) {
          throw new Error(result.error || 'Failed to load workflow');
        }

        if (result.success && result.data) {
          setWorkflow(result.data);
        } else {
          throw new Error('Workflow not found');
        }
      } catch (err) {
        console.error('Error loading workflow:', err);
        setError(err instanceof Error ? err.message : 'Failed to load workflow');
      } finally {
        setIsLoading(false);
      }
    };

    loadWorkflow();
  }, [workflowId]);

  const handleSave = async () => {
    if (!workflow) return;

    // Validate workflow before saving
    const validation: ValidationResult = validateWorkflow(workflow.nodes || [], workflow.edges || []);

    // Show validation errors if any
    if (!validation.isValid) {
      toast.error(`Cannot save workflow: ${validation.errors[0]}`);
      return;
    }

    setIsSaving(true);
    try {
      const response = await fetch(`/api/workflows/${workflowId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: workflow.name,
          description: workflow.description,
          nodes: workflow.nodes,
          edges: workflow.edges,
          triggers: workflow.triggers,
          status: workflow.status,
          executionMode: workflow.executionMode,
          isActive: workflow.isActive,
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to save workflow');
      }

      // Success toast
      toast.success("Workflow saved successfully!");

      // Show trigger warning if needed
      if (!validation.canExecuteAutomatically) {
        toast("⚠️ Your workflow can only be executed manually because you have not set a trigger", {
          duration: 5000,
          icon: "⚠️",
          style: {
            background: '#fbbf24',
            color: '#92400e',
          },
        });
      }

      // Show other warnings
      validation.warnings.forEach(warning => {
        toast(warning, {
          duration: 4000,
          icon: "⚠️",
          style: {
            background: '#fbbf24',
            color: '#92400e',
          },
        });
      });

      setHasUnsavedChanges(false);
    } catch (error) {
      console.error("❌ Error saving workflow:", error);
      toast.error(error instanceof Error ? error.message : 'Failed to save workflow');
    } finally {
      setIsSaving(false);
    }
  };

  const handleExecute = async () => {
    if (!workflow) return;

    try {
      const executionMode = workflow.executionMode || 'async';
      console.log("🚀 Executing workflow:", workflowId, "with mode:", executionMode);

      const response = await fetch(`/api/workflows/${workflowId}/execute`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          input: {},
          variables: {},
          mode: executionMode === 'direct' ? 'sync' : 'async', //  Backend expects 'sync' for direct execution
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to execute workflow');
      }

      console.log("✅ Workflow execution started:", result);

      // Trigger the visual execution monitor based on execution mode
      toast.success(`${executionMode === 'direct' ? 'Direct' : 'Async'} execution started! ID: ${result.executionId}`);
      if (executionMode === 'direct' && (window as any).executeWorkflowDirect) {
        (window as any).executeWorkflowDirect();
      } else if (executionMode === 'async' && (window as any).executeWorkflowAsync) {
        (window as any).executeWorkflowAsync();
      } else if ((window as any).executeWorkflow) {
        (window as any).executeWorkflow(); // Fallback to default
      }
    } catch (error) {
      console.error("❌ Error executing workflow:", error);
      alert(error instanceof Error ? error.message : 'Failed to execute workflow');
    }
  };

  const handleWorkflowChange = (nodes: any[], edges: any[]) => {
    setWorkflow((prev: any) => ({
      ...prev,
      nodes,
      edges,
    }));
    setHasUnsavedChanges(true);
  };

  // Loading state 
  if (isLoading) {
    return (
      <div className="h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-emerald-500"></div>
      </div>
    );
  }

  // Error state
  if (error || !workflow) {
    return (
      <div className="h-screen flex items-center justify-center p-4 bg-gray-50 dark:bg-gray-900">
        <div className="max-w-md w-full text-center">
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200/70 dark:border-gray-700/50 p-8">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
              Workflow Not Found
            </h2>
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              {error || "The workflow you're looking for doesn't exist or you don't have access to it."}
            </p>
            <Link
              href={`/dashboard/team/${slug}`}
              className="px-4 py-2 bg-emerald-600 hover:bg-emerald-700 text-white rounded-md transition-colors"
            >
              Back to Team
            </Link>
            <Link
              href={`/dashboard/team/${slug}/workflows`}
              className="px-4 py-2 bg-emerald-600 hover:bg-emerald-700 text-white rounded-md transition-colors"
            >
              Back to Workflows
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen flex flex-col bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link
              href={`/dashboard/team/${slug}/workflows`}
              className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
            >
              <ArrowLeft className="h-5 w-5" />
            </Link>
            <div>
              <h1 className="text-xl font-semibold text-gray-900 dark:text-white">
                {workflow.name}
              </h1>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {workflow.description}
              </p>
            </div>
            {hasUnsavedChanges && (
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                className="px-2 py-1 bg-orange-100 dark:bg-orange-900/30 text-orange-600 dark:text-orange-400 text-xs rounded-full"
              >
                Unsaved changes
              </motion.div>
            )}
          </div>

          <div className="flex items-center space-x-4">
            {/* Credentials Button */}
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsCredentialManagerOpen(true)}
              className="flex items-center space-x-2"
            >
              <Plug className="w-4 h-4" />
              <span>Credentials</span>
            </Button>

            {/* Chat Button */}
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsChatPreviewOpen(true)}
              className="flex items-center space-x-2"
            >
              <MessageSquare className="w-4 h-4" />
              <span>Chat</span>
            </Button>

            {/* Execution Mode Switch */}
            <div className="flex items-center space-x-2">
              <Label htmlFor="execution-mode" className="text-sm text-gray-600 dark:text-gray-400">
                {workflow.executionMode === 'direct' ? 'Direct' : 'Async'}
              </Label>
              <Switch
                id="execution-mode"
                checked={workflow.executionMode === 'async'}
                onCheckedChange={(checked) => {
                  setWorkflow((prev: any) => ({
                    ...prev,
                    executionMode: checked ? 'async' : 'direct'
                  }));
                  setHasUnsavedChanges(true);
                }}
              />
            </div>

            {/* Active/Inactive Switch */}
            <div className="flex items-center space-x-2">
              <Label htmlFor="workflow-active" className="text-sm text-gray-600 dark:text-gray-400">
                {workflow.isActive ? 'Active' : 'Inactive'}
              </Label>
              <Switch
                id="workflow-active"
                checked={workflow.isActive}
                onCheckedChange={(checked) => {
                  setWorkflow((prev: any) => ({
                    ...prev,
                    isActive: checked
                  }));
                  setHasUnsavedChanges(true);
                }}
              />
            </div>

            <Button
              variant="outline"
              size="sm"
              onClick={handleSave}
              disabled={isSaving || !hasUnsavedChanges}
              className="flex items-center space-x-2"
            >
              <Save className="h-4 w-4" />
              <span>{isSaving ? "Saving..." : "Save"}</span>
            </Button>

            <Button
              variant="outline"
              size="sm"
              className="flex items-center space-x-2"
            >
              <Share className="h-4 w-4" />
              <span>Share</span>
            </Button>

            <Button
              variant="outline"
              size="sm"
              className="flex items-center space-x-2"
            >
              <Settings className="h-4 w-4" />
              <span>Settings</span>
            </Button>

            <Button
              size="sm"
              onClick={handleExecute}
              className="bg-emerald-600 hover:bg-emerald-700 text-white flex items-center space-x-2"
            >
              <Play className="h-4 w-4" />
              <span>Run</span>
            </Button>
          </div>
        </div>
      </div>

      {/* Workflow Builder */}
      <div className="flex-1 overflow-hidden">
        <WorkflowBuilder
          workflow={workflow}
          onChange={handleWorkflowChange}
          teamId={slug}
        />
      </div>

      {/* Chat Preview Dialog */}
      <Dialog open={isChatPreviewOpen} onOpenChange={setIsChatPreviewOpen}>
        <DialogContent className="max-w-4xl h-[80vh] flex flex-col p-0">
          <DialogHeader className="px-6 py-4 border-b flex-shrink-0">
            <DialogTitle>Chat Preview - {workflow?.name}</DialogTitle>
          </DialogHeader>
          <div className="flex-1 overflow-hidden min-h-0">
            {workflow && (
              <ChatInterface
                workflowId={workflowId}
                config={getChatConfig(workflow)}
                isPreview={true}
                onMessage={(message) => {
                  console.log('Preview message:', message);
                }}
              />
            )}
          </div>
        </DialogContent>
      </Dialog>

      {/* Credential Manager Modal */}
      <CredentialManagerModal
        isOpen={isCredentialManagerOpen}
        onClose={() => setIsCredentialManagerOpen(false)}
        teamId={slug}
      />
    </div>
  );
};

// Helper function to extract chat config from workflow
const getChatConfig = (workflow: any) => {
  const messageTrigger = workflow.nodes?.find((node: any) => node.type === 'message');
  const config = messageTrigger?.data?.config || {};

  return {
    initialMessages: config.initialMessages || ["Hello! How can I help you today?"],
    suggestedMessages: config.suggestedMessages || ["Tell me about your services", "How does this work?", "Contact support"],
    messagePlaceholder: config.messagePlaceholder || "Type your message...",
    footer: config.footer || "Powered by AI Assistant",
    theme: config.theme || "light",
    displayName: config.displayName || "AI Assistant",
    iconPath: config.iconPath,
    customerMessageColor: config.customerMessageColor || "#007bff",
    customerMessageColorAsChatbotHeader: config.customerMessageColorAsChatbotHeader || false,
    hasChatBubbleToggle: false, // Disable for preview
    loadPreviousSession: false // Disable for preview
  };
};

export default WorkflowDetailPage;
