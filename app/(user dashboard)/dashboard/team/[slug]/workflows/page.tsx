"use client";

import React, { useState, useEffect } from "react";
import { <PERSON><PERSON><PERSON><PERSON>gle, RefreshCw, Workflow } from "lucide-react";
import { motion } from "framer-motion";
import { usePathname } from "next/navigation";
import { useTeam } from "@/hooks/use-teams";
import { ChatbotsLoadingState } from "@/components/loader/chatbot-loading-state";
import ErrorDialog from "@/components/error/error-state";
import { WorkflowsSection } from "@/components/sections/workflows-section";
import Link from "next/link";

const WorkflowsPage = () => {
  const pathname = usePathname();
  const slug = pathname.split("/").filter(Boolean)[2]; // Get team slug from URL

  const {
    data: teamData,
    isPending: isLoading,
    error,
    refetch,
    isFetching,
  } = useTeam(slug ?? "");
  const [errorDialogOpen, setErrorDialogOpen] = useState(false);

  useEffect(() => {
    if (error && error.response?.status !== 404) {
      setErrorDialogOpen(true);
    }
  }, [error]);

  const currentTeam = teamData?.data?.currentTeam;

  if (isLoading) return <ChatbotsLoadingState />;

  if (error && error.response?.status !== 404) {
    return (
      <ErrorDialog
        error={error || new Error("Unknown problem has occurred.")}
        open={errorDialogOpen}
        retry={refetch}
        isFetching={isFetching}
      />
    );
  }

  // Team not found case
  if (!teamData) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4 transition-colors duration-300">
        <div className="max-w-md w-full text-center">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.3 }}
            className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200/70 dark:border-gray-700/50 p-8 relative overflow-hidden"
          >
            <div className="absolute -top-20 -right-20 w-64 h-64 rounded-full bg-red-300/10 dark:bg-red-500/10 blur-3xl"></div>
            <div className="absolute -bottom-10 -left-10 w-48 h-48 rounded-full bg-amber-300/10 dark:bg-amber-500/10 blur-3xl"></div>

            <div className="relative z-10">
              <div className="mx-auto h-16 w-16 flex items-center justify-center rounded-full bg-red-100 dark:bg-red-900/50 text-red-500 dark:text-red-400 mb-4">
                <AlertTriangle className="h-8 w-8" />
              </div>
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                Team Not Found
              </h2>
              <p className="text-gray-600 dark:text-gray-300 mb-6">
                The team &quot;{slug}&quot; doesn&apos;t exist or you don&apos;t have access to it.
              </p>

              <div className="flex flex-col sm:flex-row gap-3 justify-center">
                <Link
                  href="/dashboard"
                  className="px-4 py-2 bg-emerald-600 hover:bg-emerald-700 text-white rounded-md transition-colors flex items-center justify-center gap-2"
                >
                  <Workflow className="h-4 w-4" />
                  Go to Your Teams
                </Link>
                <button
                  onClick={() => refetch()}
                  className="cursor-pointer px-4 py-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-800 dark:text-gray-200 rounded-md transition-colors flex items-center justify-center gap-2"
                >
                  <RefreshCw className="h-4 w-4" />
                  Try Again
                </button>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    );
  }

  // Don't render WorkflowsSection until we have team data
  if (!currentTeam?._id) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center py-16">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-emerald-500 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Loading team data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <WorkflowsSection
        teamId={currentTeam._id.toString()}
        teamUrl={currentTeam.url}
        title="Workflows"
        showCreateButton={true}
      />
    </div>
  );
};

export default WorkflowsPage;
