'use client';

import React, { useState } from 'react';
import { useParams } from 'next/navigation';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { 
  ArrowLeft, 
  Plus, 
  Search, 
  Filter, 
  Edit, 
  Trash2, 
  TestTube, 
  Loader2,
  CheckCircle,
  XCircle,
  Settings,
  Plug
} from 'lucide-react';
import Link from 'next/link';
import { useCredentials } from '@/hooks/use-credentials';
import { useIntegrations } from '@/hooks/use-integrations';
import { CreateCredentialModal } from '@/components/credentials/create-credential-modal';
import { EditCredentialModal } from '@/components/credentials/edit-credential-modal';
import { ConfirmDialog } from '@/components/ui/confirm-dialog';
import { Credential, AppType } from '@/lib/integrations/types/integration-types';

export default function TeamCredentialsPage() {
  const params = useParams();
  const slug = params.slug as string;

  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [editingCredential, setEditingCredential] = useState<Credential | null>(null);
  const [deletingCredential, setDeletingCredential] = useState<Credential | null>(null);
  const [testingCredentials, setTestingCredentials] = useState<Set<string>>(new Set());

  const { 
    credentials, 
    loading, 
    error,
    deleteCredential,
    testCredential,
    refresh
  } = useCredentials({ teamId: slug });

  const { integrations, getIntegration } = useIntegrations();

  // Filter credentials
  const filteredCredentials = credentials.filter(credential => {
    const matchesSearch = credential.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         credential.appType.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesCategory = selectedCategory === 'all' || 
                           getIntegration(credential.appType)?.category === selectedCategory;
    
    return matchesSearch && matchesCategory;
  });

  // Handle test credential
  const handleTestCredential = async (credential: Credential) => {
    setTestingCredentials(prev => new Set(prev).add(credential.id));
    
    try {
      await testCredential(credential.id);
    } finally {
      setTestingCredentials(prev => {
        const newSet = new Set(prev);
        newSet.delete(credential.id);
        return newSet;
      });
    }
  };

  // Handle delete credential
  const handleDeleteCredential = async () => {
    if (!deletingCredential) return;
    
    const success = await deleteCredential(deletingCredential.id);
    if (success) {
      setDeletingCredential(null);
    }
  };

  // Get integration info
  const getIntegrationInfo = (appType: AppType) => {
    const integration = getIntegration(appType);
    return {
      name: integration?.name || appType,
      icon: integration?.icon || '🔌',
      color: integration?.color || '#6B7280'
    };
  };

  // Get status badge
  const getStatusBadge = (credential: Credential) => {
    if (!credential.isActive) {
      return <Badge variant="secondary">Inactive</Badge>;
    }
    
    if (credential.lastUsedAt) {
      const daysSinceUsed = Math.floor(
        (Date.now() - new Date(credential.lastUsedAt).getTime()) / (1000 * 60 * 60 * 24)
      );
      
      if (daysSinceUsed < 7) {
        return <Badge variant="default" className="bg-green-100 text-green-800">Active</Badge>;
      } else if (daysSinceUsed < 30) {
        return <Badge variant="secondary">Recently Used</Badge>;
      }
    }
    
    return <Badge variant="outline">Unused</Badge>;
  };

  // Get category stats
  const categoryStats = credentials.reduce((acc, cred) => {
    const category = getIntegration(cred.appType)?.category || 'other';
    acc[category] = (acc[category] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Link href={`/dashboard/team/${slug}`}>
            <Button variant="outline" size="sm">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Team
            </Button>
          </Link>
          
          <div>
            <h1 className="text-2xl font-bold flex items-center gap-2">
              <Plug className="w-6 h-6" />
              Team Credentials
            </h1>
            <p className="text-gray-600">
              Manage API keys, tokens, and authentication credentials for your team
            </p>
          </div>
        </div>

        <Button 
          onClick={() => setShowCreateModal(true)}
          className="flex items-center gap-2"
        >
          <Plus className="w-4 h-4" />
          Add Credential
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                <Plug className="w-4 h-4 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Total</p>
                <p className="text-xl font-bold">{credentials.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                <CheckCircle className="w-4 h-4 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Active</p>
                <p className="text-xl font-bold">
                  {credentials.filter(c => c.isActive).length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                <Settings className="w-4 h-4 text-purple-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">AI Services</p>
                <p className="text-xl font-bold">
                  {categoryStats.ai_services || 0}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <div className="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
                <Settings className="w-4 h-4 text-orange-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Business Apps</p>
                <p className="text-xl font-bold">
                  {categoryStats.business_apps || 0}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Search credentials..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>

            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="px-3 py-2 border rounded-md bg-white"
            >
              <option value="all">All Categories</option>
              <option value="ai_services">AI Services</option>
              <option value="business_apps">Business Apps</option>
              <option value="communication">Communication</option>
              <option value="storage">Storage</option>
            </select>

            <Button
              variant="outline"
              onClick={refresh}
              disabled={loading}
              className="flex items-center gap-2"
            >
              {loading ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <Filter className="w-4 h-4" />
              )}
              Refresh
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Credentials List */}
      <Card>
        <CardHeader>
          <CardTitle>Credentials ({filteredCredentials.length})</CardTitle>
          <CardDescription>
            Manage your team's API keys and authentication credentials
          </CardDescription>
        </CardHeader>
        <CardContent>
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
              <div className="flex items-center gap-2 text-red-800">
                <XCircle className="w-4 h-4" />
                {error}
              </div>
            </div>
          )}

          {loading && credentials.length === 0 ? (
            <div className="flex items-center justify-center py-12">
              <Loader2 className="w-6 h-6 animate-spin mr-2" />
              Loading credentials...
            </div>
          ) : filteredCredentials.length === 0 ? (
            <div className="text-center py-12">
              <div className="text-gray-400 text-lg mb-2">🔌</div>
              <p className="text-gray-600 mb-4">
                {searchQuery || selectedCategory !== 'all' 
                  ? 'No credentials match your filters' 
                  : 'No credentials found'
                }
              </p>
              <Button onClick={() => setShowCreateModal(true)}>
                Create your first credential
              </Button>
            </div>
          ) : (
            <div className="space-y-3">
              {filteredCredentials.map((credential) => {
                const integration = getIntegrationInfo(credential.appType);
                const isTesting = testingCredentials.has(credential.id);
                
                return (
                  <div
                    key={credential.id}
                    className="border rounded-lg p-4 hover:bg-gray-50 transition-colors"
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div 
                          className="w-10 h-10 rounded-lg flex items-center justify-center text-white text-lg"
                          style={{ backgroundColor: integration.color }}
                        >
                          {integration.icon}
                        </div>
                        
                        <div>
                          <div className="flex items-center gap-2">
                            <h3 className="font-medium">{credential.name}</h3>
                            {getStatusBadge(credential)}
                          </div>
                          <p className="text-sm text-gray-600">
                            {integration.name}
                            {credential.lastUsedAt && (
                              <span className="ml-2">
                                • Last used {new Date(credential.lastUsedAt).toLocaleDateString()}
                              </span>
                            )}
                            {credential.usageCount > 0 && (
                              <span className="ml-2">
                                • Used {credential.usageCount} times
                              </span>
                            )}
                          </p>
                        </div>
                      </div>

                      <div className="flex items-center gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleTestCredential(credential)}
                          disabled={isTesting}
                          className="flex items-center gap-1"
                        >
                          {isTesting ? (
                            <Loader2 className="w-3 h-3 animate-spin" />
                          ) : (
                            <TestTube className="w-3 h-3" />
                          )}
                          Test
                        </Button>

                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setEditingCredential(credential)}
                          className="flex items-center gap-1"
                        >
                          <Edit className="w-3 h-3" />
                          Edit
                        </Button>

                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setDeletingCredential(credential)}
                          className="flex items-center gap-1 text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="w-3 h-3" />
                          Delete
                        </Button>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Create Credential Modal */}
      <CreateCredentialModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        teamId={slug}
        onSuccess={refresh}
      />

      {/* Edit Credential Modal */}
      {editingCredential && (
        <EditCredentialModal
          isOpen={!!editingCredential}
          onClose={() => setEditingCredential(null)}
          credential={editingCredential}
          teamId={slug}
          onSuccess={refresh}
        />
      )}

      {/* Delete Confirmation */}
      <ConfirmDialog
        isOpen={!!deletingCredential}
        onClose={() => setDeletingCredential(null)}
        onConfirm={handleDeleteCredential}
        title="Delete Credential"
        description={`Are you sure you want to delete "${deletingCredential?.name}"? This action cannot be undone.`}
        confirmText="Delete"
        confirmVariant="destructive"
      />
    </div>
  );
}
