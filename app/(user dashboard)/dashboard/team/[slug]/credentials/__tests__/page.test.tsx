import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import TeamCredentialsPage from '../page'
import { useCredentials } from '@/hooks/use-credentials'
import { useIntegrations } from '@/hooks/use-integrations'

// Mock hooks and components
jest.mock('@/hooks/use-credentials')
jest.mock('@/hooks/use-integrations')
jest.mock('@/components/credentials/create-credential-modal', () => ({
  CreateCredentialModal: ({ isOpen, onClose }: any) => 
    isOpen ? <div data-testid="create-modal">Create Modal</div> : null
}))
jest.mock('@/components/credentials/edit-credential-modal', () => ({
  EditCredentialModal: ({ isOpen, onClose }: any) => 
    isOpen ? <div data-testid="edit-modal">Edit Modal</div> : null
}))
jest.mock('@/components/ui/confirm-dialog', () => ({
  ConfirmDialog: ({ isOpen, onConfirm, title }: any) => 
    isOpen ? (
      <div data-testid="confirm-dialog">
        <p>{title}</p>
        <button onClick={onConfirm}>Confirm</button>
      </div>
    ) : null
}))

const mockUseCredentials = useCredentials as jest.MockedFunction<typeof useCredentials>
const mockUseIntegrations = useIntegrations as jest.MockedFunction<typeof useIntegrations>

describe('TeamCredentialsPage', () => {
  const mockCredentials = [
    {
      id: 'cred-1',
      name: 'OpenAI Production',
      appType: 'openai' as const,
      isActive: true,
      lastUsedAt: new Date('2024-01-01'),
      usageCount: 5,
      teamId: 'team-123',
      encryptedCredentials: 'encrypted',
      createdAt: new Date(),
      updatedAt: new Date(),
      createdBy: 'user-1'
    },
    {
      id: 'cred-2',
      name: 'Groq Development',
      appType: 'groq' as const,
      isActive: false,
      lastUsedAt: null,
      usageCount: 0,
      teamId: 'team-123',
      encryptedCredentials: 'encrypted',
      createdAt: new Date(),
      updatedAt: new Date(),
      createdBy: 'user-1'
    }
  ]

  const mockIntegrations = [
    {
      id: 'openai',
      name: 'OpenAI',
      icon: '🤖',
      color: '#10A37F',
      category: 'ai_services' as const
    },
    {
      id: 'groq',
      name: 'Groq',
      icon: '⚡',
      color: '#F55036',
      category: 'ai_services' as const
    }
  ]

  beforeEach(() => {
    mockUseCredentials.mockReturnValue({
      credentials: mockCredentials,
      loading: false,
      error: null,
      total: 2,
      loadCredentials: jest.fn(),
      createCredential: jest.fn(),
      updateCredential: jest.fn(),
      deleteCredential: jest.fn().mockResolvedValue(true),
      testCredential: jest.fn().mockResolvedValue({ isValid: true }),
      testNewCredentials: jest.fn(),
      getCredential: jest.fn(),
      getCredentialsByAppType: jest.fn(),
      refresh: jest.fn()
    })

    mockUseIntegrations.mockReturnValue({
      integrations: mockIntegrations,
      loading: false,
      error: null,
      categories: ['ai_services'],
      loadIntegrations: jest.fn(),
      getIntegration: jest.fn((appType) => 
        mockIntegrations.find(i => i.id === appType)
      ),
      getIntegrationsByCategory: jest.fn(),
      getAIServices: jest.fn(),
      getBusinessApps: jest.fn(),
      searchIntegrations: jest.fn(),
      refresh: jest.fn()
    })
  })

  afterEach(() => {
    jest.clearAllMocks()
  })

  describe('rendering', () => {
    it('should render page header and navigation', () => {
      render(<TeamCredentialsPage />)
      
      expect(screen.getByText('Team Credentials')).toBeInTheDocument()
      expect(screen.getByText('Back to Team')).toBeInTheDocument()
      expect(screen.getByText('Add Credential')).toBeInTheDocument()
    })

    it('should render statistics cards', () => {
      render(<TeamCredentialsPage />)
      
      expect(screen.getByText('Total')).toBeInTheDocument()
      expect(screen.getByText('2')).toBeInTheDocument() // Total credentials
      expect(screen.getByText('Active')).toBeInTheDocument()
      expect(screen.getByText('1')).toBeInTheDocument() // Active credentials
    })

    it('should render search and filter controls', () => {
      render(<TeamCredentialsPage />)
      
      expect(screen.getByPlaceholderText('Search credentials...')).toBeInTheDocument()
      expect(screen.getByDisplayValue('All Categories')).toBeInTheDocument()
      expect(screen.getByText('Refresh')).toBeInTheDocument()
    })

    it('should render credentials list', () => {
      render(<TeamCredentialsPage />)
      
      expect(screen.getByText('OpenAI Production')).toBeInTheDocument()
      expect(screen.getByText('Groq Development')).toBeInTheDocument()
      expect(screen.getByText('OpenAI')).toBeInTheDocument()
      expect(screen.getByText('Groq')).toBeInTheDocument()
    })
  })

  describe('credential management', () => {
    it('should open create modal when add button is clicked', async () => {
      const user = userEvent.setup()
      render(<TeamCredentialsPage />)
      
      await user.click(screen.getByText('Add Credential'))
      
      expect(screen.getByTestId('create-modal')).toBeInTheDocument()
    })

    it('should open edit modal when edit button is clicked', async () => {
      const user = userEvent.setup()
      render(<TeamCredentialsPage />)
      
      const editButtons = screen.getAllByText('Edit')
      await user.click(editButtons[0])
      
      expect(screen.getByTestId('edit-modal')).toBeInTheDocument()
    })

    it('should open delete confirmation when delete button is clicked', async () => {
      const user = userEvent.setup()
      render(<TeamCredentialsPage />)
      
      const deleteButtons = screen.getAllByText('Delete')
      await user.click(deleteButtons[0])
      
      expect(screen.getByTestId('confirm-dialog')).toBeInTheDocument()
      expect(screen.getByText('Delete Credential')).toBeInTheDocument()
    })

    it('should delete credential when confirmed', async () => {
      const user = userEvent.setup()
      const deleteCredential = jest.fn().mockResolvedValue(true)
      
      mockUseCredentials.mockReturnValue({
        ...mockUseCredentials(),
        deleteCredential
      })

      render(<TeamCredentialsPage />)
      
      const deleteButtons = screen.getAllByText('Delete')
      await user.click(deleteButtons[0])
      
      await user.click(screen.getByText('Confirm'))
      
      expect(deleteCredential).toHaveBeenCalledWith('cred-1')
    })
  })

  describe('credential testing', () => {
    it('should test credential when test button is clicked', async () => {
      const user = userEvent.setup()
      const testCredential = jest.fn().mockResolvedValue({ isValid: true })
      
      mockUseCredentials.mockReturnValue({
        ...mockUseCredentials(),
        testCredential
      })

      render(<TeamCredentialsPage />)
      
      const testButtons = screen.getAllByText('Test')
      await user.click(testButtons[0])
      
      expect(testCredential).toHaveBeenCalledWith('cred-1')
    })

    it('should show loading state during test', async () => {
      const user = userEvent.setup()
      const testCredential = jest.fn(() => new Promise(resolve => setTimeout(resolve, 100)))
      
      mockUseCredentials.mockReturnValue({
        ...mockUseCredentials(),
        testCredential
      })

      render(<TeamCredentialsPage />)
      
      const testButtons = screen.getAllByText('Test')
      await user.click(testButtons[0])
      
      // Should show loading spinner
      expect(screen.getByTestId('loading-spinner')).toBeInTheDocument()
    })
  })

  describe('filtering and search', () => {
    it('should filter credentials by search query', async () => {
      const user = userEvent.setup()
      render(<TeamCredentialsPage />)
      
      const searchInput = screen.getByPlaceholderText('Search credentials...')
      await user.type(searchInput, 'OpenAI')
      
      expect(screen.getByText('OpenAI Production')).toBeInTheDocument()
      expect(screen.queryByText('Groq Development')).not.toBeInTheDocument()
    })

    it('should filter credentials by category', async () => {
      const user = userEvent.setup()
      render(<TeamCredentialsPage />)
      
      const categorySelect = screen.getByDisplayValue('All Categories')
      await user.selectOptions(categorySelect, 'ai_services')
      
      // Both credentials should still be visible as they're both AI services
      expect(screen.getByText('OpenAI Production')).toBeInTheDocument()
      expect(screen.getByText('Groq Development')).toBeInTheDocument()
    })

    it('should refresh credentials when refresh button is clicked', async () => {
      const user = userEvent.setup()
      const refresh = jest.fn()
      
      mockUseCredentials.mockReturnValue({
        ...mockUseCredentials(),
        refresh
      })

      render(<TeamCredentialsPage />)
      
      await user.click(screen.getByText('Refresh'))
      
      expect(refresh).toHaveBeenCalled()
    })
  })

  describe('status badges', () => {
    it('should show correct status badges', () => {
      render(<TeamCredentialsPage />)
      
      expect(screen.getByText('Active')).toBeInTheDocument()
      expect(screen.getByText('Inactive')).toBeInTheDocument()
    })

    it('should show usage information', () => {
      render(<TeamCredentialsPage />)
      
      expect(screen.getByText(/Used 5 times/)).toBeInTheDocument()
      expect(screen.getByText(/Last used/)).toBeInTheDocument()
    })
  })

  describe('loading states', () => {
    it('should show loading state when credentials are loading', () => {
      mockUseCredentials.mockReturnValue({
        ...mockUseCredentials(),
        loading: true,
        credentials: []
      })

      render(<TeamCredentialsPage />)
      
      expect(screen.getByText('Loading credentials...')).toBeInTheDocument()
    })
  })

  describe('error states', () => {
    it('should show error message when there is an error', () => {
      mockUseCredentials.mockReturnValue({
        ...mockUseCredentials(),
        error: 'Failed to load credentials'
      })

      render(<TeamCredentialsPage />)
      
      expect(screen.getByText('Failed to load credentials')).toBeInTheDocument()
    })
  })

  describe('empty states', () => {
    it('should show empty state when no credentials exist', () => {
      mockUseCredentials.mockReturnValue({
        ...mockUseCredentials(),
        credentials: []
      })

      render(<TeamCredentialsPage />)
      
      expect(screen.getByText('No credentials found')).toBeInTheDocument()
      expect(screen.getByText('Create your first credential')).toBeInTheDocument()
    })

    it('should show filtered empty state when search has no results', async () => {
      const user = userEvent.setup()
      render(<TeamCredentialsPage />)
      
      const searchInput = screen.getByPlaceholderText('Search credentials...')
      await user.type(searchInput, 'nonexistent')
      
      expect(screen.getByText('No credentials match your filters')).toBeInTheDocument()
    })
  })
})
