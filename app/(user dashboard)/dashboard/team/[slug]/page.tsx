"use client";

import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  Settings,
  Code,
  Key,
  Link as LinkIcon,
  Calendar,
  Users,
  AlertTriangle,
  RefreshCw,
} from "lucide-react";
import { useParams } from "next/navigation";
import { useEffect, useState } from "react";
import { useTheme } from "next-themes";
import { useTeam } from "@/hooks/use-teams";
import { TeamSidebar } from "@/components/sidebar/team-sidebar";
import { AgentCard } from "@/components/cards/agent-card";
import ErrorDialog from "@/components/error/error-state";
import { motion } from "framer-motion";
import { CodeBlock } from "@/components/code-block/code-block";
import { CopyButton } from "@/components/copy-button/copy-button";
import Link from "next/link";
import { TeamSettingsModal } from "@/components/modals/team-settings-modal";
import { CreateAgentModal } from "@/components/modals/create-agent-modal";
import { useAgents } from "@/hooks/use-agents";
import { useInView } from "react-intersection-observer";
import { AgentsLoadingState } from "@/components/loader/agent-loading-state";


const TeamPage = () => {
  const params = useParams();
  const slug = (params.slug ?? "") as string;
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const { resolvedTheme } = useTheme();
  const { data: teamData, error, refetch, isFetching } = useTeam(slug);
  const [errorDialogOpen, setErrorDialogOpen] = useState(false);
  const [isCreatingAgent, setIsCreatingAgent] = useState(false);

  useEffect(() => {
    if (error && error.response?.status !== 404) {
      setErrorDialogOpen(true);
    }
  }, [error]);

  const currentTeam = teamData?.data?.currentTeam;
  const relatedTeams = teamData?.data?.relatedTeams;
  const {
    data: agentsData,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading: isLoadingAgents,
  } = useAgents(slug, currentTeam?._id?.toString() ?? "");

  const [loadMoreRef, inView] = useInView();

  useEffect(() => {
    if (inView && hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  }, [inView, hasNextPage, fetchNextPage, isFetchingNextPage]);

  // Flatten the pages array
  const teamAgents = agentsData?.pages.flatMap((page) => page.data) || [];

  if (isLoadingAgents) return <AgentsLoadingState />;

  if (error && error.response?.status !== 404) {
    return (
      <ErrorDialog
        error={error || new Error("Unknown problem has occured.")}
        open={errorDialogOpen}
        retry={refetch}
        isFetching={isFetching}
      />
    );
  }

  if (!isLoadingAgents) {
    if (
      error?.response?.status === 404 ||
      (currentTeam === undefined && !isFetching)
    ) {
      return (
        <div className="flex items-center justify-center p-4 transition-colors duration-300">
          <div className="max-w-md w-full text-center">
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.3 }}
              className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200/70 dark:border-gray-700/50 p-8 relative overflow-hidden"
            >
              <div className="absolute -top-20 -right-20 w-64 h-64 rounded-full bg-red-300/10 dark:bg-red-500/10 blur-3xl"></div>
              <div className="absolute -bottom-10 -left-10 w-48 h-48 rounded-full bg-amber-300/10 dark:bg-amber-500/10 blur-3xl"></div>

              <div className="relative z-10">
                <div className="mx-auto h-16 w-16 flex items-center justify-center rounded-full bg-red-100 dark:bg-red-900/50 text-red-500 dark:text-red-400 mb-4">
                  <AlertTriangle className="h-8 w-8" />
                </div>
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                  Team Not Found
                </h2>
                <p className="text-gray-600 dark:text-gray-300 mb-6">
                  The team &quot;{slug}&quot; doesn&apos;t exist or you
                  don&apos;t have access to it.
                </p>

                <div className="flex flex-col sm:flex-row gap-3 justify-center">
                  <Link
                    href="/dashboard"
                    className="px-4 py-2 bg-emerald-600 hover:bg-emerald-700 text-white rounded-md transition-colors flex items-center justify-center gap-2"
                  >
                    <Users className="h-4 w-4" />
                    Go to Your Teams
                  </Link>
                  <button
                    onClick={() => refetch()}
                    className="cursor-pointer px-4 py-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-800 dark:text-gray-200 rounded-md transition-colors flex items-center justify-center gap-2"
                  >
                    <RefreshCw className="h-4 w-4" />
                    Try Again
                  </button>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      );
    }
  }

  return (
    <div className="min-h-screen transition-colors duration-300">
      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="relative flex flex-col lg:flex-row">
          {/* Main Content Area */}
          <main className="flex-1">
            <div className="mb-8 relative overflow-hidden">
              <div className="absolute -top-20 -right-20 w-64 h-64 rounded-full bg-emerald-300/10 dark:bg-emerald-500/10 blur-3xl"></div>
              <div className="absolute -bottom-10 -left-10 w-48 h-48 rounded-full bg-purple-300/10 dark:bg-purple-500/10 blur-3xl"></div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4 }}
                className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200/70 dark:border-gray-700/50 backdrop-blur-sm relative overflow-hidden"
              >
                {/* Team header with gradient */}
                <div
                  className="h-2 w-full"
                  style={{
                    background: `linear-gradient(90deg, ${currentTeam?.color} 0%, #10b981 100%)`,
                  }}
                ></div>

                <div className="p-6">
                  <div className="flex items-start justify-between mb-6">
                    <div>
                      <motion.h2
                        className="text-2xl font-bold text-gray-900 dark:text-white flex items-center lowercase first-letter:uppercase"
                        whileHover={{ scale: 1.01 }}
                      >
                        <motion.span
                          className="w-4 h-4 rounded-full mr-3 flex-shrink-0"
                          style={{ backgroundColor: currentTeam?.color }}
                          animate={{
                            scale: [1, 1.1, 1],
                            boxShadow: [
                              `0 0 0 0 rgba(16, 185, 129, 0)`,
                              `0 0 0 6px rgba(16, 185, 129, 0.1)`,
                              `0 0 0 0 rgba(16, 185, 129, 0)`,
                            ],
                          }}
                          transition={{
                            repeat: Infinity,
                            repeatDelay: 3,
                            duration: 1.5,
                          }}
                        ></motion.span>
                        <span className="lowercase first-letter:uppercase">
                          {" "}
                          {currentTeam?.name}
                        </span>
                        {currentTeam?.isFavorite && (
                          <motion.div
                            className="ml-2"
                            whileHover={{ scale: 1.2, rotate: 15 }}
                          >
                            <Star
                              className="h-5 w-5 text-yellow-400"
                              fill="currentColor"
                            />
                          </motion.div>
                        )}
                      </motion.h2>
                      <p className="text-gray-600 dark:text-gray-300 mt-2 max-w-lg  lowercase first-letter:uppercase line-clamp-5">
                        {currentTeam?.description}
                      </p>
                    </div>

                    <motion.div
                      whileHover={{ scale: 1.05 }}
                      className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gradient-to-r from-emerald-100 to-emerald-50 dark:from-emerald-900/30 dark:to-emerald-800/20 text-emerald-800 dark:text-emerald-400 shadow-inner border border-emerald-200/50 dark:border-emerald-700/30"
                    >
                      <Users className="h-4 w-4 mr-1.5" />
                      {currentTeam?.membersCount.toLocaleString() ??
                        "0"}{" "}
                      member
                      {currentTeam?.membersCount !== 1 ? "s" : ""}
                    </motion.div>
                  </div>

                  {/* Team details grid */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-8">
                    {/* Left column */}
                    <div className="space-y-6">
                      <motion.div
                        whileHover={{ x: 3 }}
                        className="bg-gray-50/50 dark:bg-gray-700/30 p-4 cursor-pointer rounded-xl border border-gray-200/50 dark:border-gray-600/30"
                      >
                        <div className="flex items-start justify-between gap-6">
                          {/* Team URL Section - Left side */}
                          <div className="flex-1">
                            <div className="flex items-center mb-2">
                              <LinkIcon className="h-4 w-4 text-gray-400 dark:text-gray-500 mr-2" />
                              <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">
                                Team URL
                              </h3>
                            </div>
                            <div className="flex items-center">
                              <span className="text-sm text-gray-400 dark:text-gray-500 mr-1">
                                chatzuri.team/
                              </span>
                              <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                                {currentTeam?.url}
                              </p>
                            </div>
                          </div>

                          {/* Created At Section - Right side */}
                          <div className="flex-1 text-right">
                            <div className="flex items-center justify-end mb-2">
                              <Calendar className="h-4 w-4 text-gray-400 dark:text-gray-500 mr-2" />
                              <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">
                                Created
                              </h3>
                            </div>
                            <p className="text-sm text-gray-900 dark:text-white">
                              {currentTeam?.createdAt
                                ? new Date(
                                    currentTeam.createdAt
                                  ).toLocaleDateString("en-US", {
                                    year: "numeric",
                                    month: "short",
                                    day: "numeric",
                                  })
                                : "Unknown"}
                            </p>
                          </div>
                        </div>
                      </motion.div>
                    </div>

                    {/* Right column */}
                    <div className="space-y-6">
                      {currentTeam?.openAiKey && (
                        <motion.div
                          whileHover={{ x: 3 }}
                          className="bg-gray-50/50 dark:bg-gray-700/30 p-4  cursor-pointer rounded-xl border border-gray-200/50 dark:border-gray-600/30"
                        >
                          <div className="flex items-center mb-2">
                            <Key className="h-4 w-4 text-gray-400 dark:text-gray-500 mr-2" />
                            <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">
                              OpenAI Key
                            </h3>
                          </div>
                          <div className="w-full flex items-center gap-2 bg-gray-100 dark:bg-gray-700 rounded-md p-2">
                            <div className="flex-1 min-w-0">
                              <div className="w-full text-sm font-mono text-gray-900 dark:text-white bg-gray-200/50 dark:bg-gray-600/50 px-3 py-2 rounded truncate">
                                {currentTeam?.openAiKey ? (
                                  <>
                                    {currentTeam?.openAiKey.substring(0, 8)}...
                                    {currentTeam?.openAiKey.substring(
                                      currentTeam?.openAiKey.length - 4
                                    )}
                                  </>
                                ) : (
                                  <span className="text-gray-500 dark:text-gray-400">
                                    No OpenAI key configured
                                  </span>
                                )}
                              </div>
                            </div>
                            {currentTeam?.openAiKey && (
                              <CopyButton
                                textToCopy={currentTeam?.openAiKey}
                                buttonId="openai-key-copy-button"
                              />
                            )}
                          </div>
                        </motion.div>
                      )}

                      {currentTeam?.metaData && (
                        <motion.div
                          whileHover={{ x: 3 }}
                          className="bg-gray-50/50 dark:bg-gray-700/30  relative cursor-pointer p-4 rounded-xl border border-gray-200/50 dark:border-gray-600/30"
                        >
                          <div className="flex items-center mb-2">
                            <Code className="h-4 w-4 text-gray-400 dark:text-gray-500 mr-2" />
                            <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">
                              Custom Metadata
                            </h3>
                          </div>
                          <div className=" mt-1 text-sm text-gray-900 dark:text-white bg-gray-100 dark:bg-gray-700 p-3 rounded-lg overflow-x-auto max-h-40 overflow-y-auto">
                            <CodeBlock language="json">
                              {JSON.stringify(
                                JSON.parse(currentTeam?.metaData),
                                null,
                                2
                              )}
                            </CodeBlock>
                            <CopyButton
                              textToCopy={JSON.stringify(
                                JSON.parse(currentTeam?.metaData),
                                null,
                                2
                              )}
                              buttonId={`${currentTeam?._id}-endpoint-url`}
                              classNames="absolute top-2 right-2"
                            />
                          </div>
                        </motion.div>
                      )}
                    </div>
                  </div>

                  {/* Floating action button */}
                  <motion.div
                    className="absolute bottom-6 right-6 z-10"
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <button
                      onClick={() => setIsModalOpen(true)}
                      className="p-2 cursor-pointer rounded-full bg-emerald-500 text-white shadow-lg hover:bg-emerald-600 transition-colors"
                    >
                      <Settings className="h-5 w-5" />
                    </button>
                  </motion.div>
                </div>
              </motion.div>
            </div>
            {/* Title and Create Button */}
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-8">
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4 sm:mb-0">
                Agents
                <span className="ml-2 text-emerald-500 dark:text-emerald-400 bg-emerald-100 dark:bg-emerald-900/30 px-2 py-1 rounded-full text-sm">
                  {teamAgents.length}
                </span>
              </h1>

              <button
                onClick={() => setIsCreatingAgent(true)}
                className="cursor-pointer inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-emerald-600 hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500 dark:bg-emerald-500 dark:hover:bg-emerald-600 transition-colors"
              >
                <Plus className="h-5 w-5 mr-2" />
                New Agent
              </button>
            </div>
            {/* Agents Grid */}
            {teamAgents.length > 0 ? (
              <>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                  {teamAgents.map((agent) => (
                    <AgentCard
                      key={agent._id as string}
                      agent={{
                        ...agent,
                        _id: (
                          agent._id as string | { toString(): string }
                        ).toString(),
                      }}
                      teamUrl={currentTeam?.url ?? ""}
                      theme={resolvedTheme || "light"}
                    />
                  ))}
                </div>

                {/* Load more trigger */}
                <div ref={loadMoreRef} className="mt-8">
                  {isFetchingNextPage && (
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="flex flex-col items-center justify-center gap-3"
                    >
                      <RefreshCw className="h-8 w-8 animate-spin text-emerald-500 dark:text-emerald-400" />
                      <span className="text-sm font-medium text-emerald-600 dark:text-emerald-300">
                        Loading more agents...
                      </span>
                    </motion.div>
                  )}

                  {!hasNextPage && teamAgents.length > 0 && (
                    <motion.div
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ duration: 0.5 }}
                      className="text-center py-6"
                    >
                      <div className="inline-flex items-center justify-center px-4 py-2 rounded-full bg-emerald-50 dark:bg-emerald-900/20 border border-emerald-100 dark:border-emerald-800/50">
                        <svg
                          className="h-5 w-5 text-emerald-500 dark:text-emerald-400 mr-2"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M5 13l4 4L19 7"
                          />
                        </svg>
                        <span className="text-sm font-medium text-emerald-600 dark:text-emerald-300">
                          You&apos;ve reached the end of your agents
                        </span>
                      </div>
                      <p className="mt-2 text-xs text-gray-500 dark:text-gray-400">
                        {teamAgents.length} agents loaded
                      </p>
                    </motion.div>
                  )}
                </div>
              </>
            ) : (
              <div className="text-center py-16 bg-white/50 dark:bg-gray-800/50 rounded-xl border border-dashed border-gray-300 dark:border-gray-600">
                <div className="mx-auto h-24 w-24 text-gray-400 dark:text-gray-500 mb-4">
                  <Bot className="h-full w-full" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-1">
                  No agents yet
                </h3>
                <p className="text-gray-500 dark:text-gray-400 mb-6">
                  Get started by creating your first agent.
                </p>
                <button
                  onClick={() => setIsCreatingAgent(true)}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-emerald-600 hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500 dark:bg-emerald-500 dark:hover:bg-emerald-600"
                >
                  <Plus className="h-5 w-5 mr-2" />
                  Create agent
                </button>
              </div>
            )}
          </main>

          {/* Sidebar with Related Teams */}
          {currentTeam && (
            <div className="lg:sticky lg:top-20 lg:self-start">
              <TeamSidebar
                currentTeam={currentTeam}
                relatedTeams={relatedTeams ?? []}
              />
            </div>
          )}
        </div>
      </div>

      {/* Create agent Modal */}
      <CreateAgentModal
        isOpen={isCreatingAgent}
        onClose={() => setIsCreatingAgent(false)}
        teamId={currentTeam?._id?.toString() ?? ""}
      />
 
      {currentTeam && (
        <TeamSettingsModal
          team={currentTeam}
          isOpen={isModalOpen}
          onOpenChange={setIsModalOpen}
        />
      )}
    </div>
  );
};


export default TeamPage;