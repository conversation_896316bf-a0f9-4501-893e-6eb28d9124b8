"use client";

import { AgentsLoadingState } from "@/components/loader/agent-loading-state";
import {
  TeamSettingsLoading,
  TeamSettingsMobileLoading,
} from "@/components/loader/loading-settings-modal";
import { TeamSettingsContent } from "@/components/teams/settings/team-settings-content";
import { useIsMobile } from "@/hooks/use-mobile";
import { useTeam } from "@/hooks/use-teams";
import { notFound, useParams } from "next/navigation";

export default function TeamSettingsPage() {
  const params = useParams();
  const slug = params.slug as string;
  const { data: team, isLoading, error } = useTeam(slug);
  const isMobile = useIsMobile();

  if (isLoading)
    return isMobile ? <TeamSettingsMobileLoading /> : <TeamSettingsLoading />;

  if (error || !team) return notFound();

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {team.data?.currentTeam && (
        <TeamSettingsContent team={team.data.currentTeam} isMobile={isMobile} />
      )}
    </div>
  );
}
