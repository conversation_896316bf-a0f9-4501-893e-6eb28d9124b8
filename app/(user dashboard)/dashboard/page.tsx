"use client";

import React, { useState, useEffect } from "react";
import { Plus } from "lucide-react";
import { motion } from "framer-motion";
import { useTeams, useCreateTeam } from "@/hooks/use-teams";
import LoadingState from "@/components/loader/loading-state";
import ErrorDialog from "@/components/error/error-state";
import { CreateTeamModal } from "@/components/modals/create-team-modal";
import { TeamCard } from "@/components/cards/team-card";
import { Team } from "@/types/types";
import { Pagination } from "@/components/pagination/pagination";

export default function DashboardPage() {
  const [page, setPage] = useState<number>(1);
  const limit = 8; // Number of items per page

  const {
    data: teamsData,
    isPending: isLoading,
    error,
    refetch,
    isFetching,
  } = useTeams(page, limit); // Pass page and limit to the hook

  const [localTeams, setLocalTeams] = useState<Team[]>([]);
  const [errorDialogOpen, setErrorDialogOpen] = useState(false);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);

  const createTeam = useCreateTeam();

  // Update local teams when query data changes
  useEffect(() => {
    if (teamsData?.data && Array.isArray(teamsData.data)) {
      setLocalTeams(teamsData?.data);
    }
  }, [teamsData]);

  useEffect(() => {
    if (error) {
      setErrorDialogOpen(true);
    }
  }, [error]);

  // Handle page change
  const handlePageChange = (newPage: number) => {
    setPage(newPage);
    // Optional: scroll to top when page changes
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  if (isLoading) return <LoadingState />;

  if (error) {
    return (
      <ErrorDialog
        error={error || new Error("Unknown problem has occured.")}
        open={errorDialogOpen}
        retry={refetch}
        isFetching={isFetching}
      />
    );
  }

  return (
    <div className="p-6 max-w-7xl mx-auto">
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
          Your Teams
        </h1>
        <div className="text-sm text-gray-500 dark:text-gray-400">
          Page {page} of {teamsData?.pagination?.totalPages || 1}
        </div>
      </div>

      <div className="flex flex-col min-h-screen">
        <div className="flex-grow">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3  gap-6">
            {/* Create New Team Card */}
            <motion.div
              whileHover={{ scale: 1.03 }}
              whileTap={{ scale: 0.98 }}
              onClick={() => setIsCreateModalOpen(true)}
              className={`relative group cursor-pointer rounded-xl border-2 border-dashed border-gray-300 dark:border-gray-700 hover:border-emerald-500 dark:hover:border-emerald-500 transition-all h-full min-h-[200px] flex flex-col items-center justify-center p-6 ${
                createTeam.isPending ? "opacity-70" : ""
              }`}
            >
              {createTeam.isPending ? (
                <div className="flex flex-col items-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-emerald-500 mb-2"></div>
                  <p className="text-gray-500 dark:text-gray-400">
                    Creating...
                  </p>
                </div>
              ) : (
                <>
                  <div className="w-14 h-14 rounded-full bg-emerald-100 dark:bg-emerald-900/30 flex items-center justify-center mb-4 group-hover:bg-emerald-200 dark:group-hover:bg-emerald-800/50 transition-colors">
                    <Plus className="h-6 w-6 text-emerald-600 dark:text-emerald-400" />
                  </div>
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-1">
                    Create New Team
                  </h3>
                  <p className="text-sm text-center text-gray-500 dark:text-gray-400">
                    Start collaborating with a brand new team
                  </p>
                </>
              )}
            </motion.div>

            <CreateTeamModal
              open={isCreateModalOpen}
              onOpenChange={setIsCreateModalOpen}
            />

            {/* Team Cards */}
            {localTeams.map((team, index) => (
              <TeamCard
                key={team._id.toString()}
                team={team}
                index={index}
                teams={localTeams}
                setTeams={setLocalTeams}
              />
            ))}
          </div>
        </div>

        {teamsData?.pagination?.totalPages > 1 && (
          <div className="mt-8 rounded-lg shadow-lg sticky bottom-0 bg-white dark:bg-gray-900 py-4 border-t border-gray-200 dark:border-gray-800">
            <Pagination
              currentPage={page}
              totalPages={teamsData.pagination.totalPages}
              onPageChange={handlePageChange}
              className="mx-auto" 
            />
          </div>
        )}
      </div>
    </div>
  );
}