"use client";

import { useState } from 'react';
import { Button } from '@/components/ui/button';

export default function TestExecutionPage() {
  const [result, setResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  const testWorkflowExecution = async () => {
    setLoading(true);
    setResult(null);
    
    try {
      const response = await fetch('/api/workflows/test123/execute', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          mode: 'sync',
          input: { message: 'Test execution' },
          variables: { test: true }
        }),
      });

      const data = await response.json();
      setResult({ success: response.ok, data });
      
    } catch (error) {
      setResult({ 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      });
    } finally {
      setLoading(false);
    }
  };

  const testNodeExecution = async () => {
    setLoading(true);
    setResult(null);
    
    try {
      const response = await fetch('/api/workflows/test123/nodes/node123/test', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          mode: 'sync',
          input: { message: 'Test node' },
          variables: { test: true }
        }),
      });

      const data = await response.json();
      setResult({ success: response.ok, data });
      
    } catch (error) {
      setResult({ 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto p-8">
      <h1 className="text-2xl font-bold mb-6">Test Workflow Execution</h1>
      
      <div className="space-y-4 mb-6">
        <Button 
          onClick={testWorkflowExecution} 
          disabled={loading}
          className="mr-4"
        >
          {loading ? 'Testing...' : 'Test Workflow Execution'}
        </Button>
        
        <Button 
          onClick={testNodeExecution} 
          disabled={loading}
          variant="outline"
        >
          {loading ? 'Testing...' : 'Test Node Execution'}
        </Button>
      </div>

      {result && (
        <div className={`p-4 rounded-lg border ${
          result.success 
            ? 'bg-green-50 border-green-200' 
            : 'bg-red-50 border-red-200'
        }`}>
          <h3 className="font-semibold mb-2">
            Result: {result.success ? 'Success' : 'Failed'}
          </h3>
          <pre className="text-sm overflow-auto">
            {JSON.stringify(result, null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
}
