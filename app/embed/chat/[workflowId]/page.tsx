"use client";

import React, { useState, useEffect } from 'react';
import { useParams, useSearchParams } from 'next/navigation';
import { ChatInterface } from '@/components/chat/chat-interface';

const EmbedChatPage = () => {
  const params = useParams();
  const searchParams = useSearchParams();
  const workflowId = params.workflowId as string;
  const mode = searchParams.get('mode') || 'fullscreen'; // 'fullscreen' or 'bubble'
  
  const [chatConfig, setChatConfig] = useState<Record<string, any> | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [sessionId, setSessionId] = useState<string>('');

  const loadChatConfig = React.useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch(`/api/workflows/${workflowId}/chat-config`);

      if (!response.ok) {
        throw new Error('Failed to load chat configuration');
      }

      const config = await response.json();

      // Check if chat is public
      if (!config.isPublic) {
        throw new Error('This chat is not publicly accessible');
      }

      // Check domain restrictions
      if (config.allowedDomains && config.allowedDomains.length > 0 && !config.allowedDomains.includes('*')) {
        const currentDomain = window.location.hostname;
        if (!config.allowedDomains.includes(currentDomain)) {
          throw new Error('This domain is not authorized to access this chat');
        }
      }

      setChatConfig(config);
    } catch (error) {
      console.error('Error loading chat config:', error);
      setError(error instanceof Error ? error.message : 'Failed to load chat');
    } finally {
      setIsLoading(false);
    }
  }, [workflowId]);

  useEffect(() => {
    const generateSessionId = () => {
      return `embed_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    };

    setSessionId(generateSessionId());
    loadChatConfig();
  }, [workflowId, loadChatConfig]);

  // Apply theme to document - always call this hook
  useEffect(() => {
    if (chatConfig?.theme === 'dark') {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  }, [chatConfig?.theme]);

  // Auto-show delay for bubble mode - always call this hook
  useEffect(() => {
    if (mode === 'bubble' && chatConfig?.autoShowInitialDelay && chatConfig.autoShowInitialDelay > 0) {
      const timer = setTimeout(() => {
        // Auto-open logic can be implemented here
      }, chatConfig.autoShowInitialDelay * 1000);

      return () => clearTimeout(timer);
    }
  }, [mode, chatConfig?.autoShowInitialDelay]);

  const handleMessage = async (message: string, files?: File[]) => {
    console.log('Embed chat message:', message, files);
    // Message handling is done within ChatInterface component
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen bg-gray-50 dark:bg-gray-900">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Loading chat...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-screen bg-gray-50 dark:bg-gray-900">
        <div className="text-center max-w-md mx-auto p-6">
          <div className="text-red-500 text-6xl mb-4">⚠️</div>
          <h1 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
            Chat Unavailable
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            {error}
          </p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  if (!chatConfig) {
    return null;
  }

  return (
    <div 
      className={`h-screen w-full ${
        chatConfig.theme === 'dark' ? 'dark bg-gray-900' : 'bg-white'
      }`}
      style={{
        fontFamily: 'system-ui, -apple-system, sans-serif'
      }}
    >
      <ChatInterface
        workflowId={workflowId}
        config={chatConfig}
        isEmbedded={true}
        sessionId={sessionId}
        onMessage={handleMessage}
      />
    </div>
  );
};

export default EmbedChatPage;
