import { NextRequest, NextResponse } from 'next/server';
import { readFileSync } from 'fs';
import { join } from 'path';

export async function GET(request: NextRequest) {
  try {
    // Read the widget script from public directory
    const scriptPath = join(process.cwd(), 'public', 'embed', 'widget', 'chat-widget.js');
    const scriptContent = readFileSync(scriptPath, 'utf8');

    // Set appropriate headers
    const response = new NextResponse(scriptContent, {
      status: 200,
      headers: {
        'Content-Type': 'application/javascript',
        'Cache-Control': 'public, max-age=3600', // Cache for 1 hour
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET',
        'Access-Control-Allow-Headers': 'Content-Type',
      },
    });

    return response;

  } catch (error) {
    console.error('Error serving chat widget script:', error);
    return new NextResponse('Chat widget script not found', { status: 404 });
  }
}
