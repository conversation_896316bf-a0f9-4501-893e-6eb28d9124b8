"use client";

import { useSearchParams } from "next/navigation";
import Head from "next/head";
import { motion } from "framer-motion";
import Link from "next/link";
import { containerVariants, itemVariants } from "@/variants/variants";

export default function AuthErrorPage() {
  const searchParams = useSearchParams();
  const error = searchParams.get("error");
  const message = searchParams.get("message");

  const errorMessages: Record<string, string> = {
    OAuthSignin: "Error signing in with OAuth provider",
    OAuthCallback: "Error handling OAuth callback",
    OAuthCreateAccount: "Error creating account with OAuth",
    EmailCreateAccount: "Error creating account with email",
    Callback: "Error in callback handler",
    EmailSignin: "Error sending verification email",
    CredentialsSignin: "Invalid credentials",
    SessionRequired: "Please sign in to access this page",
    OAuthAccountNotLinked:
      "This email is already registered with a different method.",
    AccountDisabled: "This account is disabled. Please contact support.",
    Default: "An unknown error occurred",
  };

  // Get the appropriate error message
  const getErrorMessage = () => {
    if (message) {
      return decodeURIComponent(message);
    }
    if (error && error in errorMessages) {
      return errorMessages[error];
    }
    return errorMessages.Default;
  };

  const displayMessage = getErrorMessage();

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 to-blue-100 flex flex-col items-center justify-center p-4 overflow-hidden">
      <Head>
        <title>Authentication Error</title>
      </Head>

      <motion.div
        initial="hidden"
        animate="visible"
        variants={containerVariants}
        className="w-full max-w-md bg-white rounded-xl shadow-lg overflow-hidden p-8"
      >
        <motion.div variants={itemVariants} className="text-center">
          <div className="flex justify-center mb-6">
            <motion.div
              animate={{
                scale: [1, 1.1, 1],
                rotate: [0, 10, -10, 0],
              }}
              transition={{
                duration: 1.5,
                repeat: Infinity,
                repeatType: "mirror",
              }}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-16 w-16 text-red-500"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
                />
              </svg>
            </motion.div>
          </div>

          <motion.h1
            variants={itemVariants}
            className="text-2xl font-bold text-gray-800 mb-2"
          >
            Authentication Error
          </motion.h1>

          <motion.p variants={itemVariants} className="text-gray-600 mb-6">
            {displayMessage}
          </motion.p>

          <motion.div variants={itemVariants} className="cursor-pointer">
            <Link
              href="/login"
              className="cursor-pointer inline-flex items-center px-4 py-2 bg-emerald-500 dark:bg-emerald-800 text-white rounded-lg hover:bg-emerald-700 transition-colors duration-300"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5 mr-2"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"
                  clipRule="evenodd"
                />
              </svg>
              Return to Login
            </Link>
          </motion.div>
        </motion.div>
      </motion.div>

      {/* Floating animated circles in background */}
      {[...Array(8)].map((_, i) => (
        <motion.div
          key={i}
          className="absolute rounded-full bg-indigo-200 opacity-20"
          style={{
            width: Math.random() * 100 + 50,
            height: Math.random() * 100 + 50,
            left: `${Math.random() * 100}%`,
            top: `${Math.random() * 100}%`,
          }}
          animate={{
            y: [0, Math.random() * 100 - 50],
            x: [0, Math.random() * 100 - 50],
          }}
          transition={{
            duration: Math.random() * 10 + 10,
            repeat: Infinity,
            repeatType: "reverse",
            ease: "easeInOut",
          }}
        />
      ))}
    </div>
  );
}
