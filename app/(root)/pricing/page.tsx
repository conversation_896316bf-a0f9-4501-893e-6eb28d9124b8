"use client";
import { useState } from "react";
import {
  Zap,
  Globe,
  Shield,
  MessageSquare,
  Users,
  Link as LinkIcon,
} from "lucide-react";
import { motion } from "framer-motion";

const PricingPage = () => {
  const [billingInterval, setBillingInterval] = useState<"monthly" | "annual">(
    "monthly"
  );
  const [currency, setCurrency] = useState<"KES" | "USD">("KES");
  const exchangeRate = 129.5;

  const plans = [
    {
      name: "Starter",
      description: "Perfect for individuals and small teams",
      priceMonthly: 700,
      priceAnnual: 7000,
      maxMembers: 3,
      maxChatbots: 2,
      monthlyMessageCredits: 5000,
      maxTrainingChars: "400K",
      maxTrainingLinks: 5,
      models: "GPT-3.5",
      hasApiAccess: false,
      hasBasicAnalytics: true,
      hasCustomBranding: false,
      hasCustomDomains: false,
      hasNotifications: false,
    },
    {
      name: "Standard",
      description: "For growing businesses with more needs",
      priceMonthly: 3500,
      priceAnnual: 35000,
      maxMembers: 10,
      maxChatbots: 5,
      monthlyMessageCredits: 20000,
      maxTrainingChars: "11M",
      maxTrainingLinks: 20,
      models: "GPT-3.5, GPT-4",
      hasApiAccess: true,
      hasBasicAnalytics: true,
      hasCustomBranding: true,
      hasCustomDomains: false,
      hasNotifications: true,
    },
    {
      name: "Pro",
      description: "For businesses with advanced requirements",
      priceMonthly: 7000,
      priceAnnual: 70000,
      maxMembers: 25,
      maxChatbots: 15,
      monthlyMessageCredits: 50000,
      maxTrainingChars: "11M",
      maxTrainingLinks: 50,
      models: "All models",
      hasApiAccess: true,
      hasBasicAnalytics: true,
      hasCustomBranding: true,
      hasCustomDomains: true,
      hasNotifications: true,
    },
    {
      name: "Enterprise",
      description: "Custom solutions for large organizations",
      priceMonthly: 15000,
      priceAnnual: 150000,
      maxMembers: "Unlimited",
      maxChatbots: "Unlimited",
      monthlyMessageCredits: 100000,
      maxTrainingChars: "11M+",
      maxTrainingLinks: 100,
      models: "All models + fine-tuning",
      hasApiAccess: true,
      hasBasicAnalytics: true,
      hasCustomBranding: true,
      hasCustomDomains: true,
      hasNotifications: true,
    },
  ];

  const addOns = [
    {
      title: "Autorecharge Credits",
      description: "Automatically add credits when your balance gets low",
      priceKES: 700,
      priceUSD: 5,
      icon: <Zap className="w-8 h-8" />,
      darkBg: true,
    },
    {
      title: "Extra Message Credits",
      description: "Purchase additional message credits",
      priceKES: 700,
      priceUSD: 5,
      icon: <MessageSquare className="w-8 h-8" />,
      darkBg: false,
    },
    {
      title: "Extra Chatbots",
      description: "Add more chatbots to your account",
      priceKES: 700,
      priceUSD: 5,
      icon: <Users className="w-8 h-8" />,
      darkBg: false,
    },
    {
      title: "Custom Domains",
      description: "Use your own domain for chatbot links",
      priceKES: 3500,
      priceUSD: 25,
      icon: <Globe className="w-8 h-8" />,
      darkBg: true,
    },
    {
      title: "Remove Branding",
      description: 'Remove "Powered by Chatzuri" from your chatbot',
      priceKES: 3500,
      priceUSD: 25,
      icon: <Shield className="w-8 h-8" />,
      darkBg: true,
    },
    {
      title: "Advanced Integrations",
      description: "Connect with WhatsApp, Slack, and more",
      priceKES: 3500,
      priceUSD: 25,
      icon: <LinkIcon className="w-8 h-8" />,
      darkBg: false,
    },
  ];

  const faqs = [
    {
      question: "How do message credits work?",
      answer:
        "Each request made to the AI model costs a certain number of message credits depending on your chosen model. If you have any actions enabled, the chatbot might make multiple requests to the AI model to generate the response.",
    },
    {
      question: "When are my message credits renewed?",
      answer:
        "Your message credits are renewed at the start of every calendar month regardless of when you subscribed.",
    },
    {
      question: "How do auto-recharge message credits work?",
      answer:
        "When your credits fall below the threshold you set, we'll automatically add non-expiring credits to your account, ensuring that your service remains uninterrupted.",
    },
    {
      question:
        "What is the difference between subscription credits and auto-recharge credits?",
      answer:
        "Subscription credits renew monthly and unused credits don't carry over. Auto-recharge credits don't expire and are always available.",
    },
    {
      question: "How many users can use my chatbot?",
      answer:
        "If your chatbot is private, only you have access to it. If you make it public, anyone with the link can interact with it.",
    },
  ];

  const formatPrice = (price: number) => {
    return currency === "USD"
      ? (price / exchangeRate).toLocaleString("en-US", {
          style: "currency",
          currency: "USD",
          minimumFractionDigits: 0,
        })
      : price.toLocaleString("en-US", {
          style: "currency",
          currency: "KES",
          minimumFractionDigits: 0,
        });
  };

  const getPrice = (plan: (typeof plans)[0]) => {
    return billingInterval === "annual" ? plan.priceAnnual : plan.priceMonthly;
  };

  return (
    <div className="mb-6">
      {/* Hero Section */}
      <div className="relative isolate px-6 pt-14 lg:px-8">
        <div
          className="absolute inset-x-0 -top-40 -z-10 transform-gpu overflow-hidden blur-3xl sm:-top-80"
          aria-hidden="true"
        >
          <div
            className="relative left-[calc(50%-11rem)] aspect-1155/678 w-[36.125rem] -translate-x-1/2 rotate-[30deg] bg-linear-to-tr from-[#ff80b5] to-[#00a67e] opacity-30 sm:left-[calc(50%-30rem)] sm:w-[72.1875rem]"
            style={{
              backgroundImage: `linear-gradient(rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.3)),url('/images/affiliate-bg.jpg')`,
              clipPath:
                "polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)",
              WebkitClipPath:
                "polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)",
            }}
          ></div>
        </div>
        <div className="mx-auto max-w-2xl py-10 sm:py-12 lg:py-11">
          <div className="text-center">
            <motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="dark:text-white text-4xl font-semibold tracking-tight text-balance text-gray-900 sm:text-7xl"
            >
              Simple, Scalable Pricing
            </motion.h1>
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="dark:text-white mt-8 text-lg font-medium text-pretty text-gray-500 sm:text-xl/8"
            >
              Choose the perfect plan for your business needs. Whether
              you&apos;re just getting started or scaling rapidly, our flexible
              pricing grows with you — no hidden fees, no surprises.
            </motion.p>
          </div>
        </div>
        <div
          className="absolute inset-x-0 top-[calc(100%-13rem)] -z-10 transform-gpu overflow-hidden blur-3xl sm:top-[calc(100%-30rem)]"
          aria-hidden="true"
        >
          <div
            className="relative left-[calc(50%+3rem)] aspect-1155/678 w-[36.125rem] -translate-x-1/2 bg-linear-to-tr from-[#ff80b5] to-[#9089fc] opacity-30 sm:left-[calc(50%+36rem)] sm:w-[72.1875rem]"
            style={{
              clipPath:
                "polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)",
              WebkitClipPath:
                "polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)",
            }}
          ></div>
        </div>
      </div>
      {/* Pricing Toggle */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-12">
        <div className="flex flex-col items-center">
          <div className="relative flex items-center mb-8">
            <motion.button
              onClick={() => setCurrency("KES")}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              transition={{ type: "spring", stiffness: 300 }}
              className={`px-4 py-2 cursor-pointer rounded-l-lg border ${
                currency === "KES"
                  ? "bg-emerald-600 text-white border-emerald-700 dark:border-emerald-800"
                  : "bg-white dark:bg-gray-800 text-gray-800 dark:text-gray-200 border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700"
              }`}
            >
              KES
            </motion.button>
            <motion.button
              onClick={() => setCurrency("USD")}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              transition={{ type: "spring", stiffness: 300 }}
              className={`px-4 py-2 cursor-pointer rounded-r-lg border ${
                currency === "USD"
                  ? "bg-emerald-600 text-white border-emerald-700 dark:border-emerald-800"
                  : "bg-white dark:bg-gray-800 text-gray-800 dark:text-gray-200 border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700"
              }`}
            >
              USD
            </motion.button>
          </div>

          <div className="relative flex items-center mb-12">
            <motion.button
              onClick={() => setBillingInterval("monthly")}
              whileHover={{ scale: 1.03 }}
              whileTap={{ scale: 0.97 }}
              transition={{ type: "spring", stiffness: 400, damping: 10 }}
              className={`px-6 py-3 rounded-l-lg cursor-pointer border relative overflow-hidden ${
                billingInterval === "monthly"
                  ? "bg-emerald-600 text-white border-emerald-700 dark:border-emerald-800"
                  : "bg-white dark:bg-gray-800 text-gray-800 dark:text-gray-200 border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700"
              }`}
            >
              {billingInterval === "monthly" && (
                <motion.span
                  layoutId="billingIndicator"
                  className="absolute inset-0 bg-emerald-700 dark:bg-emerald-800 z-0"
                  initial={false}
                  transition={{ type: "spring", stiffness: 300, damping: 30 }}
                />
              )}
              <span className="relative z-10">Monthly</span>
            </motion.button>

            <motion.button
              onClick={() => setBillingInterval("annual")}
              whileHover={{ scale: 1.03 }}
              whileTap={{ scale: 0.97 }}
              transition={{ type: "spring", stiffness: 400, damping: 10 }}
              className={`px-6 py-3 rounded-r-lg cursor-pointer border relative overflow-hidden ${
                billingInterval === "annual"
                  ? "bg-emerald-600 text-white border-emerald-700 dark:border-emerald-800"
                  : "bg-white dark:bg-gray-800 text-gray-800 dark:text-gray-200 border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700"
              }`}
            >
              {billingInterval === "annual" && (
                <motion.span
                  layoutId="billingIndicator"
                  className="absolute inset-0 bg-emerald-700 dark:bg-emerald-800 z-0"
                  initial={false}
                  transition={{ type: "spring", stiffness: 300, damping: 30 }}
                />
              )}
              <span className="relative z-10">Annual (Save 20%)</span>
            </motion.button>
          </div>
        </div>

        {/* Plans */}
        {/* Plans */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"
        >
          {plans.map((plan, index) => (
            <motion.div
              key={index}
              whileHover={{ y: -10, scale: 1.02 }}
              transition={{ type: "spring", stiffness: 300 }}
              className={`relative h-full flex flex-col rounded-2xl overflow-hidden ${
                index === 1
                  ? "ring-4 ring-emerald-400/30 dark:ring-emerald-400/50 shadow-xl md:transform md:scale-105"
                  : "border border-gray-200/80 dark:border-gray-700"
              }`}
            >
              {/* Popular badge for highlighted plan */}
              {index === 1 && (
                <div className="absolute top-3 right-0 -translate-y-1/2 bg-gradient-to-r from-emerald-500 to-teal-500 text-white text-xs font-bold px-4 py-2 rounded-bl-lg shadow-md z-10">
                  MOST POPULAR
                </div>
              )}

              <div className="p-8 bg-white/80 dark:bg-gray-800/90 backdrop-blur-sm flex-grow">
                <motion.div
                  whileHover={{ scale: 1.02 }}
                  className="flex justify-between items-start mb-6"
                >
                  <div className="flex-1">
                    <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                      {plan.name}
                    </h2>
                    <p className="text-gray-600 dark:text-gray-300 mt-2 text-sm">
                      {plan.description}
                    </p>
                  </div>
                 
                </motion.div>

                <div className="mb-8">
                  <motion.div
                    whileHover={{ scale: 1.02 }}
                    className="flex items-end"
                  >
                    <span className="text-4xl font-bold bg-gradient-to-r from-emerald-600 to-teal-500 bg-clip-text text-transparent">
                      {formatPrice(getPrice(plan))}
                    </span>
                    <span className="text-gray-500 dark:text-gray-400 ml-2 mb-1">
                      /{billingInterval === "monthly" ? "month" : "year"}
                    </span>
                  </motion.div>
                  {billingInterval === "annual" && (
                    <p className="text-sm text-emerald-600 dark:text-emerald-400 mt-1">
                      Save {Math.round(((plan.priceMonthly * 12 - plan.priceAnnual) / (plan.priceMonthly * 12)) * 100)}% annually
                    </p>
                  )}
                </div>

                <motion.button
                  whileHover={{
                    scale: 1.03,
                    boxShadow: "0 10px 25px -5px rgba(5, 150, 105, 0.4)",
                  }}
                  whileTap={{ scale: 0.98 }}
                  className={`w-full cursor-pointer py-3 px-6 rounded-xl font-semibold transition-all ${
                    index === 1
                      ? "bg-gradient-to-r from-emerald-500 to-teal-500 text-white shadow-lg shadow-emerald-500/20"
                      : "bg-gray-900 hover:bg-gray-800 text-white dark:bg-gray-700 dark:hover:bg-gray-600"
                  }`}
                >
                  Get Started
                </motion.button>
              </div>

              <motion.div
                initial={{ height: 0 }}
                animate={{ height: "auto" }}
                transition={{ duration: 0.4 }}
                className="flex flex-col"
              >
                <div className="flex-grow border-t border-gray-200 dark:border-gray-700 px-8 py-6 bg-gray-50/50 dark:bg-gray-700/50">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                    Plan Features
                  </h3>
                  <ul className="space-y-3">
                    {[
                      `${plan.monthlyMessageCredits.toLocaleString()} message credits/month`,
                      `Up to ${plan.maxMembers} members`,
                      `Up to ${plan.maxChatbots} chatbots`,
                      `Up to ${plan.maxTrainingChars} training characters/bot`,
                      `Up to ${plan.maxTrainingLinks} links to train on`,
                      `Embed on <b>Unlimited</b> websites`,
                      plan.models,
                      ...(plan.hasApiAccess ? ["API access"] : []),
                      ...(plan.hasCustomBranding ? ["Custom branding"] : []),
                      ...(plan.hasCustomDomains ? ["Custom domains"] : []),
                    ].map((feature, i) => (
                      <motion.li
                        key={i}
                        initial={{ opacity: 0, x: -10 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: i * 0.05 }}
                        className="flex items-start"
                      >
                        <div className="flex-shrink-0 mt-1">
                          <svg
                            className="h-5 w-5 text-emerald-500"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M5 13l4 4L19 7"
                            />
                          </svg>
                        </div>
                        <span
                          className="text-gray-700 dark:text-gray-300 ml-3 text-sm"
                          dangerouslySetInnerHTML={{ __html: feature }}
                        />
                      </motion.li>
                    ))}
                  </ul>
                </div>
              </motion.div>

              {/* Glow effect for highlighted plan */}
              {index === 1 && (
                <div className="absolute inset-0 -z-10 bg-gradient-to-br from-emerald-500/5 to-teal-500/10 rounded-2xl" />
              )}
            </motion.div>
          ))}
        </motion.div>

        {/* Add-ons */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.2 }}
          className="mt-20 p-5 text-center bg-white dark:bg-gray-900 rounded-lg shadow-lg"
        >
          <motion.h2
            whileHover={{ scale: 1.02 }}
            className="text-3xl font-bold text-gray-900 dark:text-white mb-2"
          >
            Add-ons
          </motion.h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 mb-12">
            Enhance your plan with powerful add-ons
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {addOns.map((addOn, index) => (
              <motion.div
                key={index}
                whileHover={{ y: -5, scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className={`rounded-xl border border-gray-200 overflow-hidden ${
                  addOn.darkBg
                    ? "bg-gray-900 text-white"
                    : "bg-white dark:bg-gray-800 dark:border-gray-700"
                }`}
              >
                <div className="p-6">
                  <motion.div
                    whileHover={{ rotate: 10 }}
                    className={`w-12 h-12 rounded-full flex items-center justify-center mb-4 ${
                      addOn.darkBg
                        ? "bg-emerald-500"
                        : "bg-emerald-100 dark:bg-emerald-900/50"
                    }`}
                  >
                    {addOn.icon}
                  </motion.div>
                  <h3 className="text-xl font-bold mb-2">{addOn.title}</h3>
                  <p
                    className={`mb-4 ${
                      addOn.darkBg
                        ? "text-gray-300"
                        : "text-gray-600 dark:text-gray-300"
                    }`}
                  >
                    {addOn.description}
                  </p>
                  <div className="text-2xl font-bold mb-4">
                    {currency === "KES"
                      ? `${addOn.priceKES.toLocaleString()} KES`
                      : `$${addOn.priceUSD}`}
                  </div>
                  <motion.button
                    whileHover={{ scale: 1.03 }}
                    whileTap={{ scale: 0.97 }}
                    className={`w-full py-2 px-4  cursor-pointer rounded-lg font-medium ${
                      addOn.darkBg
                        ? "bg-white text-gray-900 hover:bg-gray-100"
                        : "bg-emerald-600 hover:bg-emerald-700 text-white"
                    }`}
                  >
                    Add to Plan
                  </motion.button>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* FAQ */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.3 }}
          className="mt-20 mb-16 pb-10"
        >
          <motion.h2
            whileHover={{ scale: 1.02 }}
            className="text-3xl font-bold text-center text-gray-900 dark:text-white mb-2"
          >
            Frequently Asked Questions
          </motion.h2>
          <p className="text-xl text-center text-gray-600 dark:text-gray-300 mb-12">
            Find answers to common questions
          </p>

          <div className="max-w-3xl mx-auto space-y-4">
            {faqs.map((faq, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden"
              >
                <motion.button
                  whileHover={{ backgroundColor: "#f9fafb" }}
                  className="w-full flex cursor-pointer justify-between items-center p-6 text-left bg-white dark:bg-gray-800"
                >
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                    {faq.question}
                  </h3>
                  <motion.svg
                    animate={{ rotate: 180 }}
                    className="w-5 h-5 text-gray-500 dark:text-gray-400"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M19 9l-7 7-7-7"
                    />
                  </motion.svg>
                </motion.button>
                <motion.div
                  initial={{ height: 0, opacity: 0 }}
                  animate={{ height: "auto", opacity: 1 }}
                  className="p-6 pt-0 bg-white dark:bg-gray-800"
                >
                  <p className="text-gray-600 dark:text-gray-300">
                    {faq.answer}
                  </p>
                </motion.div>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default PricingPage;
