"use client";
import { motion } from "framer-motion";
import { Shield, Mail, Lock, Server, HelpCircle, BookType } from "lucide-react";
import Link from "next/link";

export default function PrivacyPolicy() {
  const sections = [
    {
      title: "Introduction",
      icon: <Shield className="w-5 h-5 text-emerald-500" />,
      content: (
        <>
          <p className="mb-4">
            Chatzuri AI is a platform for building AI chatbots tailored to
            enhance customer support, sales, and user engagement.
          </p>
          <p>
            We respect your privacy and are committed to protecting your
            personal data. This Privacy Notice outlines Chatzuri&apos;s policies
            regarding the collection, use, and protection of your personal
            information and describes your privacy rights. As privacy standards
            evolve, we will update this policy to reflect new practices and
            policies.
          </p>
        </>
      ),
    },
    {
      title: "Contacting Us About Data Protection",
      icon: <Mail className="w-5 h-5 text-emerald-500" />,
      content: (
        <>
          <p className="mb-4">
            Chatzuri AI is based in Nairobi, Kenya. If you have any questions
            about how we handle your personal data or wish to exercise your
            privacy rights, please contact our dedicated Privacy Team. We are
            here to assist you and ensure your data is protected.
          </p>
          <p className="font-medium mb-2">Privacy Team Contact Information:</p>
          <address className="not-italic bg-emerald-50 dark:bg-emerald-900/30 p-4 rounded-lg">
            Chatzuri Ltd.
            <br />
            123 Innovation Park, Nairobi, Kenya
            <br />
            Email:{" "}
            <a
              href="mailto:<EMAIL>"
              className="text-emerald-600 dark:text-emerald-400 hover:underline"
            >
              <EMAIL>
            </a>
          </address>
        </>
      ),
    },
    {
      title: "How We Collect and Use Your Personal Information",
      icon: <Lock className="w-5 h-5 text-emerald-500" />,
      content: (
        <>
          <p className="mb-4">
            Chatzuri collects limited personal information about its users and
            website visitors, including:
          </p>
          <ul className="list-disc pl-6 mb-4 space-y-2">
            <li>Name</li>
            <li>Job title</li>
            <li>Company name</li>
            <li>Work email</li>
            <li>Phone number</li>
          </ul>
          <p>
            This data is used to deliver our services and improve user
            experience. We do not sell personal information and only share it
            with trusted third parties facilitating our services.
          </p>
        </>
      ),
    },
    {
      title: "Compliance with Data Protection Laws",
      icon: <Shield className="w-5 h-5 text-emerald-500" />,
      content: (
        <p>
          Chatzuri is committed to international data protection laws, including
          compliance with OpenAI&apos;s guidelines. Additionally, we adhere to
          Kenya&apos;s
          <strong> Data Protection Act, 2019</strong> and are regulated by the
          <strong> Office of the Data Protection Commissioner</strong>.
        </p>
      ),
    },
    {
      title: "Use of the Chatzuri Website",
      icon: <Server className="w-5 h-5 text-emerald-500" />,
      content: (
        <>
          <p className="mb-4">
            We may automatically collect certain information when you visit our
            website, including:
          </p>
          <ul className="list-disc pl-6 mb-4 space-y-2">
            <li>IP address</li>
            <li>Browser type</li>
            <li>Operating system</li>
            <li>Pages viewed</li>
          </ul>
          <p>
            This information helps us optimize our website and enhance user
            experience. Chatzuri uses cookies and similar technologies for this
            purpose. See our{" "}
            <Link
              href="/cookie-policy"
              className="text-emerald-600 dark:text-emerald-400 hover:underline"
            >
              Cookie Policy
            </Link>{" "}
            for more details.
          </p>
        </>
      ),
    },
    {
      title: "Cookies and Tracking Technologies",
      icon: <Server className="w-5 h-5 text-emerald-500" />,
      content: (
        <>
          <p className="mb-4">
            Chatzuri uses cookies and similar technologies like single-pixel
            gifs and web beacons. Chatzuri uses both session-based and
            persistent cookies. Chatzuri sets and accesses cookies on the
            domains operated by Chatzuri and its corporate affiliates
            (collectively, the &quot;Sites&quot;). In addition, Chatzuri uses third party
            cookies, like Google Analytics.
          </p>
          <p className="mb-4">
            Some cookies are associated with your account and personal
            information to remember that you are logged in and which workspaces
            you are logged into. Other cookies are not tied to your account but
            are unique and allow us to carry out analytics and customization,
            among other similar things.
          </p>
          <p>
            Cookies can be used to recognize you when you visit a Site or use
            our Services, remember your preferences, and give you a personalized
            experience that is consistent with your settings. Cookies also make
            your interactions faster and more secure.
          </p>
        </>
      ),
    },
    {
      title: "Sharing Information with Third Parties",
      icon: <Lock className="w-5 h-5 text-emerald-500" />,
      content: (
        <p>
          Your personal information may be stored on third-party servers located
          globally. These providers are only authorized to use your data for
          secure storage and retrieval purposes. We may also share your data
          with trusted service providers for specific tasks, such as email
          notifications or product updates.
        </p>
      ),
    },
    {
      title: "Data Protection Mechanisms",
      icon: <Lock className="w-5 h-5 text-emerald-500" />,
      content: (
        <>
          <p className="mb-4">
            We use robust mechanisms to safeguard your data, including:
          </p>
          <ul className="list-disc pl-6 space-y-2">
            <li>Encryption for data at rest and in transit</li>
            <li>Access control with user permissions</li>
            <li>Rate limiting to prevent abuse</li>
            <li>Domain-specific chatbot embedding</li>
            <li>
              Compliance with GDPR and Kenya&apos;s Data Protection Act, 2019
            </li>
            <li>
              Infrastructure Security: Our database and application run on AWS
              infrastructure (us-east-1)
            </li>
            <li>
              No AI Training: We do not use your data to train AI models. We use
              Retrieval-Augmented Generation (RAG) to generate responses without
              compromising your data.
            </li>
          </ul>
        </>
      ),
    },
    {
      title: "Data Storage and Retention",
      icon: <Server className="w-5 h-5 text-emerald-500" />,
      content: (
        <p>
          Your data is stored on secure servers and retained for the duration of
          our business relationship. Upon request, your data can be deleted or
          transferred. For more details, contact
          <a
            href="mailto:<EMAIL>"
            className="text-emerald-600 dark:text-emerald-400 hover:underline"
          >
            {" "}
            <EMAIL>
          </a>
          .
        </p>
      ),
    },
    {
      title: "Children's Data",
      icon: <BookType className="w-5 h-5 text-emerald-500" />,
      content: (
        <p>
          Chatzuri does not knowingly collect or process data from children
          under the age of 18.
        </p>
      ),
    },
    {
      title: "Questions, Concerns, or Complaints",
      icon: <HelpCircle className="w-5 h-5 text-emerald-500" />,
      content: (
        <>
          <p className="mb-4">
            If you have any concerns about your personal data or wish to
            exercise your privacy rights, please reach out to:
          </p>
          <address className="not-italic bg-emerald-50 dark:bg-emerald-900/30 p-4 rounded-lg">
            Chatzuri Ltd.
            <br />
            123 Innovation Park, Nairobi, Kenya
            <br />
            Email:{" "}
            <a
              href="mailto:<EMAIL>"
              className="text-emerald-600 dark:text-emerald-400 hover:underline"
            >
              <EMAIL>
            </a>
          </address>
        </>
      ),
    },
  ];

  return (
    <>
      {/* Hero Section */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5 }}
        className="relative isolate px-6 pt-14 lg:px-8"
      >
        <div
          className="absolute inset-x-0 -top-40 -z-10 transform-gpu overflow-hidden blur-3xl sm:-top-80"
          aria-hidden="true"
        >
          <div
            className="relative left-[calc(50%-11rem)] aspect-1155/678 w-[36.125rem] -translate-x-1/2 rotate-[30deg] bg-linear-to-tr from-[#ff80b5] to-[#00a67e] opacity-30 sm:left-[calc(50%-30rem)] sm:w-[72.1875rem]"
            style={{
              backgroundImage: `linear-gradient(rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.3)),url('/images/affiliate-bg.jpg')`,
              clipPath:
                "polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)",
              WebkitClipPath:
                "polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)",
            }}
          ></div>
        </div>
        <div className="mx-auto max-w-2xl py-10 sm:py-12 lg:py-11">
          <div className="text-center">
            <motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="dark:text-white text-4xl font-semibold tracking-tight text-balance text-gray-900 sm:text-7xl"
            >
              Your Privacy. Our Priority.
            </motion.h1>
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="dark:text-white mt-8 text-lg font-medium text-pretty text-gray-500 sm:text-xl/8"
            >
              At Chatzuri, we&apos;re committed to keeping your data safe and
              secure. We respect your privacy and ensure that your personal
              information is protected at every step — no tracking, no selling,
              no compromises.
            </motion.p>
          </div>
        </div>
        <div
          className="absolute inset-x-0 top-[calc(100%-13rem)] -z-10 transform-gpu overflow-hidden blur-3xl sm:top-[calc(100%-30rem)]"
          aria-hidden="true"
        >
          <div
            className="relative left-[calc(50%+3rem)] aspect-1155/678 w-[36.125rem] -translate-x-1/2 bg-linear-to-tr from-[#ff80b5] to-[#9089fc] opacity-30 sm:left-[calc(50%+36rem)] sm:w-[72.1875rem]"
            style={{
              clipPath:
                "polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)",
              WebkitClipPath:
                "polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)",
            }}
          ></div>
        </div>
      </motion.div>

      <motion.main
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5 }}
        className="container py-16 mx-auto px-4 sm:px-6 lg:px-8"
      >
        <motion.div
          initial={{ y: -20 }}
          animate={{ y: 0 }}
          className="max-w-4xl mx-auto"
        >
          <motion.h1
            className="text-4xl font-bold text-gray-900 dark:text-white mb-8 pb-4 border-b border-gray-200 dark:border-gray-700"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.1 }}
          >
            Privacy Statement
          </motion.h1>

          <div className="space-y-12">
            {sections.map((section, index) => (
              <motion.section
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 + index * 0.1 }}
                className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300"
              >
                <div className="flex items-center mb-4">
                  {section.icon}
                  <h2 className="text-2xl font-semibold text-gray-800 dark:text-white ml-2 pb-2 border-b border-emerald-500/30">
                    {section.title}
                  </h2>
                </div>
                <div className="text-gray-600 dark:text-gray-300">
                  {section.content}
                </div>
              </motion.section>
            ))}
          </div>
        </motion.div>
      </motion.main>
    </>
  );
}
