"use client";

import { motion } from "framer-motion";
import { Home, ArrowR<PERSON>, Menu, X, ShieldAlert } from "lucide-react";
import Head from "next/head";
import Link from "next/link";
import { useState } from "react";
import {
  Breadcrumb,
  BreadcrumbI<PERSON>,
  BreadcrumbLink,
  Breadcrumb<PERSON>ist,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { DocsSidebar } from "@/components/sidebar/docs-sidebar";
import SocialLinks from "@/components/social-links/social-links";
import { CopyButton } from "@/components/copy-button/copy-button";
import { itemVariants } from "@/variants/variants";
import { CodeBlock } from "@/components/code-block/code-block";

const GetLeadsPage = () => {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [activeTab, setActiveTab] =
    useState<keyof typeof codeExamples>("javascript");

  const codeExamples = {
    javascript: `// streamer.js
  const axios = require('axios');
  const { Readable } = require('stream');
  
  const apiKey = '<Your-Secret-Key>';
  const chatId = '<Your Chatbot ID>';
  const apiUrl = 'https://www.chatzuri.com/api/v1/chat';
  
  const messages = [
    { content: '<Your query here>', role: 'user' }
  ];
  
  const authorizationHeader = \`Bearer \${apiKey}\`;
  
  async function readChatbotReply() {
    try {
      const response = await axios.post(apiUrl, {
        messages,
        chatId,
        stream: true,
        temperature: 0
      }, {
        headers: {
          Authorization: authorizationHeader,
          'Content-Type': 'application/json'
        },
        responseType: 'stream'
      });
  
      const readable = new Readable({
        read() {}
      });
  
      response.data.on('data', (chunk) => {
        readable.push(chunk);
      });
  
      response.data.on('end', () => {
        readable.push(null);
      });
  
      const decoder = new TextDecoder();
      let done = false;
  
      readable.on('data', (chunk) => {
        const chunkValue = decoder.decode(chunk);
        process.stdout.write(chunkValue);
      });
  
      readable.on('end', () => {
        done = true;
      });
    } catch (error) {
      console.log('Error:', error.message);
    }
  }
  
  readChatbotReply();`,
    python: `# streamer.py
  import requests
  
  api_url = 'https://www.chatzuri.com/api/v1/chat'
  api_key = '<Your-Secret-Key>'
  chat_id = '<Your Chatbot ID>'
  
  messages = [
      { 'content': '<Your query here>', 'role': 'user' }
  ]
  
  authorization_header = f'Bearer {api_key}'
  
  def read_chatbot_reply():
      try:
          headers = {
              'Authorization': authorization_header,
              'Content-Type': 'application/json'
          }
          
          data = {
              'messages': messages,
              'chatId': chat_id,
              'stream': True,
              'temperature': 0
          }
          
          response = requests.post(api_url, json=data, headers=headers, stream=True)
          response.raise_for_status()
          
          decoder = response.iter_content(chunk_size=None)
          for chunk in decoder:
              chunk_value = chunk.decode('utf-8')
              print(chunk_value, end='', flush=True)
          
      except requests.exceptions.RequestException as error:
          print('Error:', error)
  
  read_chatbot_reply()`,
    php: `// streamer.php
  <?php
  
  $apiUrl = 'https://www.chatzuri.com/api/v1/chat';
  $apiKey = '<Your-Secret-Key>';
  $chatId = '<Your Chatbot ID>';
  
  $messages = [
      ['content' => '<Your query here>', 'role' => 'user']
  ];
  
  $options = [
      'http' => [
          'method' => 'POST',
          'header' => [
              'Authorization: Bearer ' . $apiKey,
              'Content-Type: application/json',
          ],
          'content' => json_encode([
              'messages' => $messages,
              'chatId' => $chatId,
              'stream' => true,
              'temperature' => 0
          ])
      ]
  ];
  
  $context = stream_context_create($options);
  $stream = fopen($apiUrl, 'r', false, $context);
  
  if ($stream === false) {
      die('Error opening stream');
  }
  
  while (!feof($stream)) {
      $chunk = fread($stream, 1024);
      echo $chunk;
      flush();
  }
  
  fclose($stream);`,
  } as const;

  return (
    <div className="min-h-screen">
      <Head>
        <title>Get Leads | Chatzuri API Docs</title>
      </Head>
      <div className="flex flex-col lg:flex-row">
        {/* Mobile Menu Button */}
        <div className="lg:hidden fixed top-4 right-4 z-50">
          <button
            onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            className="p-2 rounded-lg bg-white cursor-pointer dark:bg-gray-800 shadow-md"
          >
            {mobileMenuOpen ? (
              <X className="w-6 h-6" />
            ) : (
              <Menu className="w-6 h-6" />
            )}
          </button>
        </div>

        {/* Sidebar Navigation*/}
        <DocsSidebar />

        {/* Main Content */}
        <div className="flex-1 lg:ml-72 mt-16 lg:mt-0">
          {/* Toolbar Section */}
          <motion.div
            className="py-7 md:py-10 px-4 sm:px-6 lg:px-8"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
          >
            <div className="flex flex-col gap-4 w-full">
              <div className="lg:mt-10">
                {/* Breadcrumb */}
                <Breadcrumb>
                  <BreadcrumbList>
                    <BreadcrumbItem>
                      <BreadcrumbLink
                        href="/"
                        className="text-emerald-600 dark:text-emerald-400 hover:text-emerald-700 dark:hover:text-emerald-300"
                      >
                        <Home className="inline mr-1 h-4 w-4" />
                        Home
                      </BreadcrumbLink>
                    </BreadcrumbItem>
                    <BreadcrumbSeparator />
                    <BreadcrumbItem>
                      <BreadcrumbLink
                        href="/docs"
                        className="text-gray-700 dark:text-gray-300"
                      >
                        API Docs
                      </BreadcrumbLink>
                    </BreadcrumbItem>
                    <BreadcrumbSeparator />
                    <BreadcrumbItem>
                      <BreadcrumbLink className="text-gray-500 dark:text-gray-400">
                        Get Leads
                      </BreadcrumbLink>
                    </BreadcrumbItem>
                  </BreadcrumbList>
                </Breadcrumb>
              </div>
            </div>
          </motion.div>

          {/* Content Section */}
          <motion.div
            className="px-4 sm:px-6 lg:px-8 pb-12"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
          >
            <div className="bg-white px-4 py-3 dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden mb-4">
              {/* Page Header */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4 }}
                className="mb-10"
                whileHover={{ scale: 1.01 }}
                variants={itemVariants}
              >
                <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                  Get Leads
                </h1>
                <p className="text-lg text-gray-600 dark:text-gray-300 font-medium">
                  This guide assists you with gathering leads.
                </p>
                <div className="my-6 h-px bg-gradient-to-r from-transparent via-emerald-500 to-transparent dark:via-emerald-400" />
              </motion.div>

              <motion.div
                className="mb-8"
                whileHover={{ scale: 1.01 }}
                variants={itemVariants}
              >
                <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4">
                  Get Leads API
                </h2>
                <p className="text-gray-600 dark:text-gray-300">
                  The Get Leads API endpoint allows you to retrieve leads
                  generated from a chatbot&apos;s conversations. By using this
                  endpoint, you can obtain names, phone numbers, and emails from
                  users who interact with your chatbots.
                </p>
              </motion.div>

              {/* Endpoint Section */}
              <motion.div
                className="mb-8"
                whileHover={{ scale: 1.01 }}
                variants={itemVariants}
              >
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
                  Endpoint
                </h3>
                <div className="flex items-center gap-2">
                  <CodeBlock language="http">
                    GET:https://www.chatzuri.com/api/v1/get-leads
                  </CodeBlock>
                  <CopyButton
                    textToCopy="GET https://www.chatzuri.com/api/v1/get-leads"
                    buttonId="get-leads-api-endpoint"
                    classNames="ml-2"
                  />
                </div>
              </motion.div>

              {/* Request Headers Section */}
              <motion.div
                className="mb-8"
                whileHover={{ scale: 1.01 }}
                variants={itemVariants}
              >
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
                  Request Headers
                </h3>
                <p className="text-gray-600 dark:text-gray-300 mb-4">
                  The API request must include the following headers.
                </p>
                <ul className="space-y-3">
                  <li className="flex items-start gap-3">
                    <div className="mt-1 h-2 w-2 rounded-full bg-emerald-500 flex-shrink-0"></div>
                    <div>
                      <div className="inline-flex items-center gap-2 bg-gray-100 dark:bg-gray-700 px-3 py-1.5 rounded-lg">
                        <span className="text-emerald-600 dark:text-emerald-400 font-mono text-sm">
                          Authorization:
                        </span>
                        <span className="font-mono text-sm">
                          Bearer {"<Your-Secret-Key>"}
                        </span>
                      </div>
                      <span className="ml-2 text-gray-600 dark:text-gray-300">
                        - The secret key for authenticating the API request.
                      </span>
                    </div>
                  </li>
                  <li className="flex items-start gap-3">
                    <div className="mt-1 h-2 w-2 rounded-full bg-emerald-500 flex-shrink-0"></div>
                    <div>
                      <div className="inline-flex items-center gap-2 bg-gray-100 dark:bg-gray-700 px-3 py-1.5 rounded-lg">
                        <span className="text-emerald-600 dark:text-emerald-400 font-mono text-sm">
                          Content-Type:
                        </span>
                        <span className="font-mono text-sm">
                          application/json
                        </span>
                      </div>
                      <span className="ml-2 text-gray-600 dark:text-gray-300">
                        - The content type of the request payload.
                      </span>
                    </div>
                  </li>
                </ul>
              </motion.div>

              {/* Request Parameters Section */}
              <motion.div
                className="mb-8"
                whileHover={{ scale: 1.01 }}
                variants={itemVariants}
              >
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
                  Request Parameters
                </h3>
                <p className="text-gray-600 dark:text-gray-300 mb-4">
                  The request should include the following query parameters:
                </p>

                <div className="space-y-4">
                  <div className="bg-gray-50 dark:bg-gray-800/50 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                    <h4 className="font-medium text-gray-800 dark:text-gray-200 mb-3">
                      Required Parameters
                    </h4>
                    <ul className="space-y-3">
                      <li className="flex items-start gap-3">
                        <span className="inline-flex items-center justify-center h-6 w-6 rounded-full bg-emerald-100 dark:bg-emerald-900/50 text-emerald-600 dark:text-emerald-300 mt-0.5 flex-shrink-0">
                          <ShieldAlert className="w-4 h-4" />
                        </span>
                        <div>
                          <span className="font-mono bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded-md text-sm">
                            chatbotId
                          </span>
                          <span className="ml-2 text-gray-600 dark:text-gray-300">
                            - (string) The ID of the chatbot for which you want
                            to retrieve leads.
                          </span>
                        </div>
                      </li>
                    </ul>
                  </div>

                  <div className="bg-gray-50 dark:bg-gray-800/50 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                    <h4 className="font-medium text-gray-800 dark:text-gray-200 mb-3">
                      Optional Parameters
                    </h4>
                    <ul className="space-y-3">
                      {[
                        {
                          code: "startDate",
                          desc: "(string) Start date in 'YYYY-MM-DD' format for filtering leads.",
                        },
                        {
                          code: "endDate",
                          desc: "(string) End date in 'YYYY-MM-DD' format for filtering leads.",
                        },
                        {
                          code: "page",
                          desc: "(string) Page number for pagination.",
                        },
                        {
                          code: "size",
                          desc: "(string) Number of results per page.",
                        },
                      ].map((item, index) => (
                        <li key={index} className="flex items-start gap-3">
                          <span className="inline-flex items-center justify-center h-6 w-6 rounded-full bg-emerald-100 dark:bg-emerald-900/50 text-emerald-600 dark:text-emerald-300 mt-0.5 flex-shrink-0">
                            <ShieldAlert className="w-4 h-4" />
                          </span>
                          <div>
                            <span className="font-mono bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded-md text-sm">
                              {item.code}
                            </span>
                            <span className="ml-2 text-gray-600 dark:text-gray-300">
                              - {item.desc}
                            </span>
                          </div>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </motion.div>

              {/* Example Request Section */}
              <motion.div
                className="mb-8"
                whileHover={{ scale: 1.01 }}
                variants={itemVariants}
              >
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
                  Example Request
                </h3>

                {/* Language Tabs */}
                <div className="mb-4">
                  <div className="flex overflow-x-auto border-b border-gray-200 dark:border-gray-700">
                    {(
                      Object.keys(codeExamples) as Array<
                        keyof typeof codeExamples
                      >
                    ).map((lang) => (
                      <button
                        key={lang}
                        onClick={() => setActiveTab(lang)}
                        className={`px-4 py-2 font-medium cursor-pointer text-sm capitalize ${
                          activeTab === lang
                            ? "text-emerald-600 dark:text-emerald-400 border-b-2 border-emerald-600 dark:border-emerald-400"
                            : "text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"
                        }`}
                      >
                        {lang}
                      </button>
                    ))}
                  </div>
                </div>

                {/* Code Block */}
                <div className="relative rounded-lg overflow-hidden">
                  <CopyButton
                    textToCopy={codeExamples[activeTab]}
                    buttonId={`lang-${activeTab}-api-url-endpoint`}
                    classNames="absolute top-3 right-2 z-10 bg-gray-800 hover:bg-gray-700 text-white"
                  />
                  <CodeBlock language={activeTab}>
                    {codeExamples[activeTab]}
                  </CodeBlock>
                </div>
              </motion.div>

              {/* Response Section */}
              <motion.div
                className="mb-8"
                whileHover={{ scale: 1.01 }}
                variants={itemVariants}
              >
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
                  Response
                </h3>
                <p className="text-gray-600 dark:text-gray-300 mb-4">
                  The API response will be a JSON object with the following
                  structure:
                </p>

                <div className="relative rounded-lg overflow-hidden mb-6">
                  <CopyButton
                    textToCopy={`{
                "data": [  
                  {  
                    "id": 0,  
                    "created_at": "string",  
                    "chatbot_owner_user_id": "string",  
                    "chatbot_id": "string",  
                    "phone": "string",  
                    "email": "string",  
                    "name": "string"  
                  }  
                ]  
              }`}
                    buttonId="response-structure-copy"
                    classNames="absolute top-3 right-2 z-10 bg-gray-800 hover:bg-gray-700 text-white"
                  />
                  <CodeBlock language="json">
                    {`{
  "data": [
    {
      "id": 0,
      "created_at": "string",
      "chatbot_owner_user_id": "string",
      "chatbot_id": "string",
      "phone": "string",
      "email": "string",
      "name": "string"
    }
  ]
}`}
                  </CodeBlock>
                </div>

                <p className="text-gray-600 dark:text-gray-300 mb-4">
                  Response codes:
                </p>
                <div className="grid gap-3">
                  {[
                    {
                      code: 202,
                      text: "Returns an array of leads that match the provided filters",
                      color:
                        "bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200",
                    },
                    {
                      code: 400,
                      text: "If the start/end dates provided are invalid",
                      color:
                        "bg-amber-100 dark:bg-amber-900/30 text-amber-800 dark:text-amber-200",
                    },
                    {
                      code: 401,
                      text: "If the API request is unauthorized",
                      color:
                        "bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-200",
                    },
                    {
                      code: 404,
                      text: "If the chatbotId provided is invalid",
                      color:
                        "bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-200",
                    },
                    {
                      code: 500,
                      text: "If there is an internal server error",
                      color:
                        "bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200",
                    },
                  ].map((item, index) => (
                    <motion.div
                      key={index}
                      whileHover={{ x: 5 }}
                      className="flex items-start gap-3 p-3 rounded-lg border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800/50"
                    >
                      <div
                        className={`flex-shrink-0 w-12 h-12 rounded-lg flex items-center justify-center ${item.color}`}
                      >
                        <span className="font-bold">{item.code}</span>
                      </div>
                      <div className="flex-1">
                        <p className="text-gray-700 dark:text-gray-300">
                          {item.text}
                        </p>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </motion.div>

              {/* Conclusion Section */}
              <motion.div
                className="mb-8"
                whileHover={{ scale: 1.01 }}
                variants={itemVariants}
              >
                <p className="text-gray-600 dark:text-gray-300">
                  By making use of the Get Leads API endpoint, you can retrieve
                  and analyze potential leads generated through the chatbot
                  interactions.
                </p>
              </motion.div>
            </div>

            {/* Next Step Card */}
            <motion.div
              whileHover={{ y: -5 }}
              variants={itemVariants}
              className="mb-8 bg-gradient-to-r from-emerald-50 to-emerald-100 dark:from-emerald-900/30 dark:to-emerald-800/30 rounded-xl p-6 text-center border border-emerald-100 dark:border-emerald-800/50 shadow-sm"
            >
              <Link
                href="/docs/get-conversations"
                className="flex items-center justify-center gap-2 text-emerald-700 dark:text-emerald-300 font-medium"
              >
                Next:: Get Conversations
                <ArrowRight className="w-5 h-5" />
              </Link>
            </motion.div>

            {/* Social Links */}
            <SocialLinks />
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default GetLeadsPage;
