"use client";

import { motion } from "framer-motion";
import { <PERSON>, <PERSON>R<PERSON>, <PERSON>u, X } from "lucide-react";
import Head from "next/head";
import Link from "next/link";
import { useState } from "react";
import {
  Breadcrumb,
  BreadcrumbItem,
  Bread<PERSON>rumbLink,
  B<PERSON><PERSON>rumb<PERSON>ist,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { DocsSidebar } from "@/components/sidebar/docs-sidebar";
import SocialLinks from "@/components/social-links/social-links";
import { CopyButton } from "@/components/copy-button/copy-button";
import { itemVariants } from "@/variants/variants";
import { CodeBlock } from "@/components/code-block/code-block";
import ErrorResponse from "@/components/error-response/error-response";

const MessageChatbotPage = () => {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [activeStreamTab, setActiveStreamTab] = useState<
    "javascript" | "python"
  >("javascript");
  const [activeTab, setActiveTab] =
    useState<keyof typeof codeExamples>("javascript");

  const codeExamples = {
    javascript: `const response = await fetch('https://www.chatzuri.com/api/v1/chat', {
  method: 'POST',
  headers: {
    Authorization: 'Bearer <API-Key>'
  },
  body: JSON.stringify({
    messages: [
      { content: 'How can I help you?', role: 'assistant' },
      { content: 'What is chatzuri?', role: 'user' }
    ],
    chatbotId: '<Chatbot-ID>',
    stream: false,
    model: 'gpt-3.5-turbo',
    temperature: 0
  })
});

if (!response.ok) {
  const errorData = await response.json();
  throw Error(errorData.message);
}
const data = await response.json(); 
console.log(data); // { "text": "..."}`,
    python: `import requests
import json

url = 'https://www.chatzuri.com/api/v1/chat'
headers = {
    'Authorization': 'Bearer <API-KEY>',
    'Content-Type': 'application/json'
}
data = {
    "messages": [
        {"content": "How can I help you?", "role": "assistant"},
        {"content": "What is chatzuri?", "role": "user"}
    ],
    "chatbotId": "<Chatbot-ID>",
    "stream": False,
    "temperature": 0
}

response = requests.post(url, headers=headers, data=json.dumps(data))
json_data = response.json()

if response.status_code == 200:
  print("response:", json_data['text'])
else:
  print('Error:' + json_data['message'])`,
    shell: `curl https://www.chatzuri.com/api/v1/chat \\
  -H 'Authorization: Bearer <Your-Secret-Key>' \\
  -d '{
  "messages": [
    {"content": "How can I help you?", "role": "assistant"},
    {"content": "What is chatzuri?", "role": "user"}
  ],
  "chatbotId": "<Your Chatbot ID>",
  "stream": false,
  "temperature": 0
}'`,
    http: `POST /api/v1/chat HTTP/1.1
Host: www.chatzuri.com
Authorization: Bearer <Your-Secret-Key>
Content-Type: application/json

{
  "messages": [
    {"content": "How can I help you?", "role": "assistant"},
    {"content": "What is chatzuri?", "role": "user"}
  ],
  "chatbotId": "<Your Chatbot ID>",
  "stream": false,
  "temperature": 0
}`,
  };

  const streamExamples = {
    javascript: `const response = await fetch('https://www.chatzuri.com/api/v1/chat', {
  method: 'POST',
  headers: {
    Authorization: 'Bearer <API-KEY'
  },
  body: JSON.stringify({
    messages: [
      { content: 'How can I help you?', role: 'assistant' },
      { content: 'What is chatzuri?', role: 'user' }
    ],
    chatbotId: '<Chatbot-ID>',
    stream: true,
    temperature: 0,
    model: 'gpt-3.5-turbo'
  })
});

if (!response.ok) {
  const errorData = await response.json();
  throw Error(errorData.message);
}

const data = response.body;

if (!data) {
  // error happened
}

const reader = data.getReader();
const decoder = new TextDecoder();
let done = false;

while (!done) {
  const { value, done: doneReading } = await reader.read();
  done = doneReading;
  const chunkValue = decoder.decode(value);
  console.log(chunkValue); // This will log chunks of the chatbot reply until the reply is finished.
}`,
    python: `import requests
import json

url = 'https://www.chatzuri.com/api/v1/chat'
headers = {
    'Authorization': 'Bearer <API-KEY>',
    'Content-Type': 'application/json'
}
data = {
    "messages": [
        {"content": "How can I help you?", "role": "assistant"},
        {"content": "What is chatzuri?", "role": "user"}
    ],
    "chatbotId": "<Chatbot-ID>",
    "stream": True,
    "temperature": 0
}

response = requests.post(url, headers=headers, data=json.dumps(data), stream=True)

if response.status_code != 200:
    json_data = response.json()
    print('Error:' + json_data['message'])
  
else:
  decoder = response.iter_content(chunk_size=None)
  for chunk in decoder:
    chunk_value = chunk.decode('utf-8')
    print(chunk_value, end='', flush=True)`,
  };

  return (
    <div className="min-h-screen ">
      <Head>
        <title>Message Chatbot | Chatzuri API Docs</title>
      </Head>
      <div className="flex flex-col lg:flex-row">
        {/* Mobile Menu Button */}
        <div className="lg:hidden fixed top-4 right-4 z-50">
          <button
            onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            className="p-2 rounded-lg bg-white cursor-pointer dark:bg-gray-800 shadow-md"
          >
            {mobileMenuOpen ? (
              <X className="w-6 h-6" />
            ) : (
              <Menu className="w-6 h-6" />
            )}
          </button>
        </div>

        {/* Sidebar Navigation*/}
        <DocsSidebar />

        {/* Main Content */}
        <div className="flex-1 lg:ml-72 mt-16 lg:mt-0">
          {/* Toolbar Section */}
          <motion.div
            className="py-7 md:py-10 px-4 sm:px-6 lg:px-8"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
            whileHover={{ scale: 1.01 }}
            variants={itemVariants}
          >
            <div className="flex flex-col gap-4 w-full">
              <div className="lg:mt-10">
                {/* Breadcrumb */}
                <Breadcrumb>
                  <BreadcrumbList>
                    <BreadcrumbItem>
                      <BreadcrumbLink
                        href="/"
                        className="text-emerald-600 dark:text-emerald-400 hover:text-emerald-700 dark:hover:text-emerald-300"
                      >
                        <Home className="inline mr-1 h-4 w-4" />
                        Home
                      </BreadcrumbLink>
                    </BreadcrumbItem>
                    <BreadcrumbSeparator />
                    <BreadcrumbItem>
                      <BreadcrumbLink
                        href="/docs"
                        className="text-gray-700 dark:text-gray-300"
                      >
                        API Docs
                      </BreadcrumbLink>
                    </BreadcrumbItem>
                    <BreadcrumbSeparator />
                    <BreadcrumbItem>
                      <BreadcrumbLink className="text-gray-500 dark:text-gray-400">
                        Message a Chatbot
                      </BreadcrumbLink>
                    </BreadcrumbItem>
                  </BreadcrumbList>
                </Breadcrumb>
              </div>
            </div>
          </motion.div>

          {/* Content Section */}
          <motion.div
            className="px-4 sm:px-6 lg:px-8 pb-12"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
          >
            <div className="bg-white px-4 py-3 dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden mb-4">
              <div className="mb-8">
                <motion.h1
                  whileHover={{ scale: 1.01 }}
                  variants={itemVariants}
                  className="text-2xl md:text-3xl font-bold text-gray-900 dark:text-white mb-4"
                >
                  Message a Chatbot
                </motion.h1>
                <p className="text-lg text-gray-600 dark:text-gray-300 font-semibold">
                  This page helps you message a Chatbot.
                </p>
                <div className="my-6 h-px bg-gradient-to-r from-transparent via-emerald-500 to-transparent dark:via-emerald-400" />
              </div>

              {/* API Description */}
              <motion.div
                className="space-y-4 mb-10"
                whileHover={{ scale: 1.01 }}
                variants={itemVariants}
              >
                <p className="text-gray-700 dark:text-gray-300">
                  The Chatbot Interaction API allows you to interact with your
                  chatbots using a POST request. This API is available for users
                  subscribed to a paid plan and provides a way to communicate
                  with your chatbot programmatically.
                </p>
              </motion.div>

              {/* Endpoint Section */}
              <motion.div
                className="mb-6 pb-6 mt-8"
                whileHover={{ scale: 1.01 }}
                variants={itemVariants}
              >
                <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
                  Endpoint
                </h2>
                <div className="flex items-center gap-2">
                  <CodeBlock language="json">
                    POST https://www.chatzuri.com/api/v1/chat
                  </CodeBlock>
                  <CopyButton
                    textToCopy="POST https://www.chatzuri.com/api/v1/chat"
                    buttonId="post-api-url-endpoint"
                  />
                </div>
              </motion.div>

              {/* Request Headers Section */}
              <motion.div
                className="mb-6 pb-6 mt-8"
                whileHover={{ scale: 1.01 }}
                variants={itemVariants}
              >
                <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
                  Request Headers
                </h2>
                <p className="text-gray-700 dark:text-gray-300 mb-4">
                  The API request must include the following headers.
                </p>
                <ul className="space-y-2">
                  <li className="text-gray-700 dark:text-gray-300">
                    <code className="bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded font-mono">
                      <span className="text-purple-600 dark:text-purple-400">
                        Authorization
                      </span>
                      :{" "}
                      <span className="text-teal-600 dark:text-teal-400">
                        Bearer
                      </span>{" "}
                      &lt;Your-Secret-Key&gt;
                    </code>{" "}
                    - The secret key for authenticating the API request.
                  </li>
                  <li className="text-gray-700 dark:text-gray-300">
                    <code className="bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded font-mono">
                      <span className="text-purple-600 dark:text-purple-400">
                        Content-Type
                      </span>
                      :{" "}
                      <span className="text-yellow-700 dark:text-yellow-300">
                        application/json
                      </span>
                    </code>{" "}
                    - The content type of the request payload.
                  </li>
                </ul>
              </motion.div>

              {/* Request Body Section */}
              <motion.div
                className="mb-6 pb-6 mt-8"
                whileHover={{ scale: 1.01 }}
                variants={itemVariants}
              >
                <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
                  Request Body
                </h2>
                <p className="text-gray-700 dark:text-gray-300 mb-4">
                  The request body should contain the following parameters:
                </p>
                <ul className="space-y-2 text-sm">
                  <li className="text-gray-700 dark:text-gray-300">
                    <code className="bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded font-mono">
                      <span className="text-blue-600 dark:text-blue-400">
                        messages
                      </span>
                    </code>{" "}
                    -{" "}
                    <span className="text-emerald-600">(array, required)</span>:
                    An array containing <strong>all</strong> messages between
                    the user and the assistant. Each message object should have{" "}
                    <code className="font-mono">content</code> and{" "}
                    <code className="font-mono">role</code> properties.
                  </li>

                  <li className="text-gray-700 dark:text-gray-300">
                    <code className="bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded font-mono">
                      <span className="text-blue-600 dark:text-blue-400">
                        chatbotId
                      </span>
                    </code>{" "}
                    -{" "}
                    <span className="text-emerald-600">(string, required)</span>
                    : Refers to the ID of the chatbot you want to interact with.
                  </li>

                  <li className="text-gray-700 dark:text-gray-300">
                    <code className="bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded font-mono">
                      <span className="text-blue-600 dark:text-blue-400">
                        stream
                      </span>
                    </code>{" "}
                    -{" "}
                    <span className="text-yellow-600">(boolean, optional)</span>
                    : Whether to stream back partial progress.
                  </li>

                  <li className="text-gray-700 dark:text-gray-300">
                    <code className="bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded font-mono">
                      <span className="text-blue-600 dark:text-blue-400">
                        temperature
                      </span>
                    </code>{" "}
                    -{" "}
                    <span className="text-yellow-600">(number, optional)</span>:
                    What sampling temperature to use.
                  </li>

                  <li className="text-gray-700 dark:text-gray-300">
                    <code className="bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded font-mono">
                      <span className="text-blue-600 dark:text-blue-400">
                        conversationId
                      </span>
                    </code>{" "}
                    -{" "}
                    <span className="text-yellow-600">(string, optional)</span>:
                    Refers to the ID of the current conversation.
                  </li>

                  <li className="text-gray-700 dark:text-gray-300">
                    <code className="bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded font-mono">
                      <span className="text-blue-600 dark:text-blue-400">
                        model
                      </span>
                    </code>{" "}
                    -{" "}
                    <span className="text-yellow-600">(string, optional)</span>:
                    If added to the body, this takes precedence over the model
                    set in the chatbot settings.
                  </li>
                </ul>

                <div className="mt-6">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                    <svg
                      className="w-5 h-5 mr-2 text-emerald-500"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2z"
                      />
                    </svg>
                    Supported AI Models
                  </h3>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    {/* OpenAI Card */}
                    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-shadow">
                      <div className="flex items-center mb-3">
                        <div className="bg-gray-100 dark:bg-gray-700 p-2 rounded-full mr-3">
                          <svg
                            className="w-6 h-6 text-gray-800 dark:text-gray-200"
                            fill="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z" />
                          </svg>
                        </div>
                        <h4 className="font-semibold text-gray-800 dark:text-gray-200">
                          OpenAI
                        </h4>
                      </div>
                      <ul className="space-y-2">
                        {[
                          "gpt-3.5-turbo",
                          "gpt-4-turbo",
                          "gpt-4",
                          "gpt-4o",
                        ].map((model) => (
                          <li
                            key={model}
                            className="flex items-center text-gray-700 dark:text-gray-300 text-sm"
                          >
                            <svg
                              className="w-4 h-4 mr-2 text-emerald-500"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M5 13l4 4L19 7"
                              />
                            </svg>
                            {model}
                          </li>
                        ))}
                      </ul>
                    </div>

                    {/* Claude Card */}
                    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-shadow">
                      <div className="flex items-center mb-3">
                        <div className="bg-gray-100 dark:bg-gray-700 p-2 rounded-full mr-3">
                          <svg
                            className="w-6 h-6 text-gray-800 dark:text-gray-200"
                            fill="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z" />
                          </svg>
                        </div>
                        <h4 className="font-semibold text-gray-800 dark:text-gray-200">
                          Claude
                        </h4>
                      </div>
                      <ul className="space-y-2">
                        {[
                          "claude-3-5-sonnet",
                          "claude-3-haiku",
                          "claude-3-opus",
                        ].map((model) => (
                          <li
                            key={model}
                            className="flex items-center text-gray-700 dark:text-gray-300 text-sm"
                          >
                            <svg
                              className="w-4 h-4 mr-2 text-emerald-500"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M5 13l4 4L19 7"
                              />
                            </svg>
                            {model}
                          </li>
                        ))}
                      </ul>
                    </div>

                    {/* Gemini Card */}
                    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-shadow">
                      <div className="flex items-center mb-3">
                        <div className="bg-gray-100 dark:bg-gray-700 p-2 rounded-full mr-3">
                          <svg
                            className="w-6 h-6 text-gray-800 dark:text-gray-200"
                            fill="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z" />
                          </svg>
                        </div>
                        <h4 className="font-semibold text-gray-800 dark:text-gray-200">
                          Gemini
                        </h4>
                      </div>
                      <ul className="space-y-2">
                        {["gemini-1.5-flash", "gemini-1.5-pro"].map((model) => (
                          <li
                            key={model}
                            className="flex items-center text-gray-700 dark:text-gray-300 text-sm"
                          >
                            <svg
                              className="w-4 h-4 mr-2 text-emerald-500"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M5 13l4 4L19 7"
                              />
                            </svg>
                            {model}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              </motion.div>

              {/* Code Examples Section */}
              <motion.div
                className="mb-6 pb-6 mt-8"
                whileHover={{ scale: 1.01 }}
                variants={itemVariants}
              >
                <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-4">
                  Example Request
                </h2>

                {/* Language Tabs */}
                <div className="flex border-b border-gray-200 dark:border-gray-700 mb-4">
                  {(["javascript", "python", "shell", "http"] as const).map(
                    (lang) => (
                      <button
                        key={lang}
                        onClick={() => setActiveTab(lang)}
                        className={`px-4 py-2 cursor-pointer font-medium ${
                          activeTab === lang
                            ? "text-emerald-600 dark:text-emerald-400 border-b-2 border-emerald-500"
                            : "text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"
                        }`}
                      >
                        {lang.charAt(0).toUpperCase() + lang.slice(1)}
                      </button>
                    )
                  )}
                </div>

                {/* Code Block */}
                <div className="relative">
                  <CodeBlock
                    language={activeTab === "http" ? "http" : activeTab}
                  >
                    {codeExamples[activeTab]}
                  </CodeBlock>
                  <CopyButton
                    textToCopy={codeExamples[activeTab]}
                    buttonId={`lang-${activeTab}-api-url-endpoint`}
                    classNames="absolute top-2 right-2"
                  />
                </div>
              </motion.div>

              {/* Response Section */}
              <motion.div
                className="mb-6 pb-6 mt-8"
                whileHover={{ scale: 1.01 }}
                variants={itemVariants}
              >
                <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
                  Response
                </h2>
                <p className="text-gray-700 dark:text-gray-300 mb-4">
                  The API response will be a JSON object with the following
                  structure:
                </p>
                <div className="relative">
                  <CodeBlock language="json">
                    {`{
  "text": "Chatzuri is an AI chatbot builder that lets you create a GPT-based chatbot that knows data."
}`}
                  </CodeBlock>
                  <CopyButton
                    textToCopy={`{
  "text": "Chatzuri is an AI chatbot builder that lets you create a GPT-based chatbot that knows data."
}`}
                    buttonId={`response-api-url-endpoint`}
                    classNames="absolute top-2 right-2"
                  />
                </div>
              </motion.div>

              {/* Streaming Section */}
              <motion.div
                className="mb-6 pb-6 mt-8"
                whileHover={{ scale: 1.01 }}
                variants={itemVariants}
              >
                <h2 className="text-xl font-bold mb-2">
                  Example Request with Streaming Functionality
                </h2>
                <p className="text-gray-700 dark:text-gray-300 mb-4">
                  If the stream parameter is set to true, words will be sent
                  back as data-only server-sent events as they become available.
                </p>

                {/* Language Tabs */}
                <div className="flex border-b border-gray-200 dark:border-gray-700 mb-4">
                  {(["javascript", "python"] as const).map((lang) => (
                    <button
                      key={lang}
                      onClick={() => setActiveStreamTab(lang)}
                      className={`px-4 py-2 cursor-pointer font-medium ${
                        activeStreamTab === lang
                          ? "text-emerald-600 dark:text-emerald-400 border-b-2 border-emerald-500"
                          : "text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"
                      }`}
                    >
                      {lang.charAt(0).toUpperCase() + lang.slice(1)}
                    </button>
                  ))}
                </div>

                {/* Code Block */}
                <div className="relative">
                  <CodeBlock language={activeStreamTab}>
                    {streamExamples[activeStreamTab]}
                  </CodeBlock>
                  <CopyButton
                    textToCopy={streamExamples[activeStreamTab]}
                    buttonId={`stream-examples-$activeStreamTab}-api-url-endpoint`}
                    classNames="absolute top-2 right-2"
                  />
                </div>
              </motion.div>

              {/* Saving Conversations Section */}
              <motion.div
                className="mb-6 pb-6 mt-8"
                whileHover={{ scale: 1.01 }}
                variants={itemVariants}
              >
                <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
                  Saving Conversations
                </h2>
                <p className="text-gray-700 dark:text-gray-300">
                  In order to save conversations to the dashboard, a
                  conversationId parameter needs to be included in the request
                  body. The full conversation needs to be sent on every API call
                  because Chatzuri doesn&apos;t save previous messages. The
                  messages received in the latest API call for a given
                  conversationId overrides the conversation already saved there.
                </p>
              </motion.div>

              {/* Error Handling Section */}
              <ErrorResponse/>
            </div>

            {/* Next Step Card */}
            <motion.div
              whileHover={{ y: -5 }}
              variants={itemVariants}
              className="mb-8 bg-gradient-to-r from-emerald-50 to-emerald-100 dark:from-emerald-900/30 dark:to-emerald-800/30 rounded-xl p-6 text-center border border-emerald-100 dark:border-emerald-800/50 shadow-sm"
            >
              <Link
                href="/docs/update-chatbot"
                className="flex items-center justify-center gap-2 text-emerald-700 dark:text-emerald-300 font-medium"
              >
                Next:: Update a Chatbot
                <ArrowRight className="w-5 h-5" />
              </Link>
            </motion.div>

            {/* Social Links */}
            <SocialLinks />
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default MessageChatbotPage;
