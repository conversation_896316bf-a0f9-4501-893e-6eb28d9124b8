"use client";

import { motion } from "framer-motion";
import { <PERSON>, <PERSON>R<PERSON>, <PERSON>u, <PERSON>, <PERSON>, Co<PERSON> } from "lucide-react";
import Head from "next/head";
import Link from "next/link";
import { useState } from "react";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { DocsSidebar } from "@/components/sidebar/docs-sidebar";
import SocialLinks from "@/components/social-links/social-links";
import { CopyButton } from "@/components/copy-button/copy-button";
import { itemVariants } from "@/variants/variants";
import { CodeBlock } from "@/components/code-block/code-block";
import ErrorResponse from "@/components/error-response/error-response";

const DeleteChatbotPage = () => {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [activeTab, setActiveTab] =
    useState<keyof typeof codeExamples>("javascript");
  const [isCopied, setIsCopied] = useState(false);

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    setIsCopied(true);
    setTimeout(() => setIsCopied(false), 2000);
  };

  const codeExamples = {
    javascript: `const res = await fetch(
  'https://www.chatzuri.com/api/v1/delete-chatbot?chatbotId=exampleId-123',
  {
    method: 'DELETE',
    headers: {
      Authorization: \`Bearer <Your-Secret-Key>\`
    }
  }
);

const data = await res.json();

console.log(data); // {message: 'Deleted successfully'}`,
    python: `import requests

url = 'https://www.chatzuri.com/api/v1/delete-chatbot'
params = {'chatbotId': 'exampleId-123'}
headers = {
  'Authorization': 'Bearer <Your-Secret-Key>'
}

response = requests.delete(url, params=params, headers=headers)
data = response.json()

print(data)  # {'message': 'Deleted successfully'}`,
    shell: `curl "https://www.chatzuri.com/api/v1/delete-chatbot?chatbotId=exampleId-123" \\
--request "DELETE" \\
-H 'Authorization: Bearer <Your-API-Key>'`,
    http: `DELETE /api/v1/delete-chatbot?chatbotId=exampleId-123 HTTP/1.1
Host: www.chatzuri.com
Authorization: Bearer <Your-Secret-Key>`,
  };

  const responseExample = `{
  "message": "Deleted successfully"
}`;

  return (
    <div className="min-h-screen">
      <Head>
        <title>Delete Chatbot | Chatzuri API Docs</title>
      </Head>
      <div className="flex flex-col lg:flex-row">
        {/* Mobile Menu Button */}
        <div className="lg:hidden fixed top-4 right-4 z-50">
          <button
            onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            className="p-2 rounded-lg bg-white cursor-pointer dark:bg-gray-800 shadow-md"
          >
            {mobileMenuOpen ? (
              <X className="w-6 h-6" />
            ) : (
              <Menu className="w-6 h-6" />
            )}
          </button>
        </div>

        {/* Sidebar Navigation*/}
        <DocsSidebar />

        {/* Main Content */}
        <div className="flex-1 lg:ml-72 mt-16 lg:mt-0">
          {/* Toolbar Section */}
          <motion.div
            className="py-7 md:py-10 px-4 sm:px-6 lg:px-8"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
          >
            <div className="flex flex-col gap-4 w-full">
              <div className="lg:mt-10">
                {/* Breadcrumb */}
                <Breadcrumb>
                  <BreadcrumbList>
                    <BreadcrumbItem>
                      <BreadcrumbLink
                        href="/"
                        className="text-emerald-600 dark:text-emerald-400 hover:text-emerald-700 dark:hover:text-emerald-300"
                      >
                        <Home className="inline mr-1 h-4 w-4" />
                        Home
                      </BreadcrumbLink>
                    </BreadcrumbItem>
                    <BreadcrumbSeparator />
                    <BreadcrumbItem>
                      <BreadcrumbLink
                        href="/docs"
                        className="text-gray-700 dark:text-gray-300"
                      >
                        API Docs
                      </BreadcrumbLink>
                    </BreadcrumbItem>
                    <BreadcrumbSeparator />
                    <BreadcrumbItem>
                      <BreadcrumbLink className="text-gray-500 dark:text-gray-400">
                        Delete Chatbot
                      </BreadcrumbLink>
                    </BreadcrumbItem>
                  </BreadcrumbList>
                </Breadcrumb>
              </div>
            </div>
          </motion.div>

          {/* Content Section */}
          <motion.div
            className="px-4 sm:px-6 lg:px-8 pb-12"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
          >
            <div className="bg-white px-4 py-3 dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden mb-4">
              {/* Page Header */}
              <motion.div
                className="mb-10"
                variants={itemVariants}
                whileHover={{ scale: 1.01 }}
              >
                <motion.h1
                  className="text-3xl font-bold mb-2 dark:text-white text-gray-900"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.2 }}
                >
                  Delete a Chatbot
                </motion.h1>
                <motion.p
                  className="text-lg dark:text-gray-300 text-gray-600"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.3 }}
                >
                  This page helps you delete a Chatbot.
                </motion.p>
                <div className="my-6 h-px bg-gradient-to-r from-transparent via-emerald-500 to-transparent dark:via-emerald-400" />
              </motion.div>

              {/* API Description */}
              <motion.div
                className="mb-12"
                variants={itemVariants}
                whileHover={{ scale: 1.01 }}
              >
                <motion.p
                  className="text-lg mb-8 dark:text-gray-300 text-gray-700"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.5 }}
                >
                  The Chatbot Deletion API allows you to delete a chatbot by
                  providing the chatbot ID. This API can be used to remove a
                  chatbot and its associated data from the system.
                </motion.p>
              </motion.div>

              {/* Endpoint Section */}
              <motion.div
                className="mb-12"
                variants={itemVariants}
                whileHover={{ scale: 1.01 }}
              >
                <motion.h2
                  className="text-2xl font-bold mb-4 dark:text-white text-gray-900"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.6 }}
                >
                  Endpoint
                </motion.h2>
                <motion.div
                  className="flex items-center gap-2 p-4 rounded-md"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.7 }}
                >
                  <CodeBlock language="http">
                    DELETE https://www.chatzuri.com/api/v1/delete-chatbot
                  </CodeBlock>
                  <CopyButton
                    textToCopy="DELETE https://www.chatzuri.com/api/v1/delete-chatbot"
                    buttonId="endpoint-url"
                  />
                </motion.div>
              </motion.div>

              {/* Request Headers Section */}
              <motion.div
                className="mb-12"
                variants={itemVariants}
                whileHover={{ scale: 1.01 }}
              >
                <motion.h2
                  className="text-2xl font-bold mb-4 text-gray-900 dark:text-white"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.8 }}
                >
                  Request Headers
                </motion.h2>
                <motion.p
                  className="text-lg mb-4 text-gray-700 dark:text-gray-300"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.9 }}
                >
                  The API request must include the following headers.
                </motion.p>
                <motion.ul
                  className="list-disc pl-6 mb-6"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 1.0 }}
                >
                  <motion.li className="flex flex-wrap items-start gap-2 text-gray-700 dark:text-gray-300">
                    <div className="flex-1 min-w-0">
                      <CodeBlock language="http">
                        Authorization: Bearer &lt;Your-Secret-Key&gt;
                      </CodeBlock>
                    </div>
                    <CopyButton
                      textToCopy="Authorization: Bearer <Your-Secret-Key>"
                      buttonId="auth-header"
                    />
                    <span className="w-full text-sm mt-1">
                      - The secret key for authenticating the API request.
                    </span>
                  </motion.li>
                </motion.ul>
              </motion.div>

              {/* Example Request Section */}
              <motion.div
                className="mb-12"
                variants={itemVariants}
                whileHover={{ scale: 1.01 }}
              >
                <motion.h2
                  className="text-2xl font-bold mb-4 dark:text-white text-gray-900"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 1.4 }}
                >
                  Example Request URL
                </motion.h2>
                <motion.p
                  className="text-lg mb-4 dark:text-gray-300 text-gray-700"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 1.5 }}
                >
                  Request URL
                </motion.p>
                <motion.div
                  className="flex items-center gap-2 p-2 rounded-md w-full"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 1.6 }}
                >
                  <div className="flex-1 min-w-0">
                    <CodeBlock language="http">
                      DELETE
                      https://www.chatzuri.com/api/v1/delete-chatbot?chatbotId=exampleId-123
                    </CodeBlock>
                  </div>
                  <CopyButton
                    textToCopy="DELETE https://www.chatzuri.com/api/v1/delete-chatbot?chatbotId=exampleId-123"
                    buttonId="endpoint-url"
                  />
                </motion.div>
              </motion.div>

              {/* Code Examples Tabs */}
              <motion.div
                className="mb-12"
                variants={itemVariants}
                whileHover={{ scale: 1.01 }}
              >
                <motion.div
                  className="flex overflow-x-auto mb-6"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 1.7 }}
                >
                  <div className="flex border-b border-gray-200 dark:border-gray-700 mb-4">
                    {(["javascript", "python", "shell", "http"] as const).map(
                      (lang) => (
                        <button
                          key={lang}
                          onClick={() => setActiveTab(lang)}
                          className={`px-4 py-2 font-medium cursor-pointer ${
                            activeTab === lang
                              ? "text-emerald-600 dark:text-emerald-400 border-b-2 border-emerald-500"
                              : "text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"
                          }`}
                        >
                          {lang.charAt(0).toUpperCase() + lang.slice(1)}
                        </button>
                      )
                    )}
                  </div>
                </motion.div>

                <motion.div
                  className="relative rounded-b-lg rounded-r-lg overflow-hidden bg-gray-100 dark:bg-gray-800"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 1.8 }}
                >
                  <CodeBlock
                    language={activeTab === "http" ? "http" : activeTab}
                  >
                    {codeExamples[activeTab]}
                  </CodeBlock>
                  <button
                    onClick={() => copyToClipboard(codeExamples[activeTab])}
                    className={`absolute top-3 right-2 px-3 py-1 rounded-full flex items-center gap-1 cursor-pointer
                                                  ${
                                                    isCopied
                                                      ? "bg-emerald-700 dark:bg-emerald-800 text-white"
                                                      : "bg-emerald-500 hover:bg-emerald-600 dark:bg-emerald-600 dark:hover:bg-emerald-700 text-white"
                                                  }
                                                  text-sm font-medium transition-colors duration-200`}
                  >
                    {isCopied ? (
                      <Check className="w-4 h-4" />
                    ) : (
                      <Copy className="w-4 h-4" />
                    )}
                    {isCopied ? "Copied" : "Copy"}
                  </button>
                </motion.div>
              </motion.div>

              {/* Response Section */}
              <motion.div
                className="mb-12"
                variants={itemVariants}
                whileHover={{ scale: 1.01 }}
              >
                <motion.h2
                  className="text-2xl font-bold mb-4 text-gray-900 dark:text-white"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 1.9 }}
                >
                  Response
                </motion.h2>
                <motion.p
                  className="text-lg mb-4 text-gray-700 dark:text-gray-300"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 2.0 }}
                >
                  The API response will be a JSON object with the following
                  structure:
                </motion.p>
                <motion.div
                  className="relative rounded-lg overflow-hidden bg-gray-100 dark:bg-gray-800"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 2.1 }}
                >
                  <CodeBlock language="json">{responseExample}</CodeBlock>
                </motion.div>
                <motion.p
                  className="mt-4 text-gray-700 dark:text-gray-300"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 2.2 }}
                >
                  The{" "}
                  <code className="font-mono px-2 py-1 rounded bg-gray-100 dark:bg-gray-700">
                    message
                  </code>{" "}
                  field in the response indicates the success of the chatbot
                  deletion operation.
                </motion.p>
              </motion.div>

              {/* Error Handling Section */}
              <ErrorResponse/>
            </div>
            {/* Next Step Card */}
            <motion.div
              whileHover={{ y: -5 }}
              className="mb-8 bg-gradient-to-r from-emerald-50 to-emerald-100 dark:from-emerald-900/30 dark:to-emerald-800/30 rounded-xl p-6 text-center border border-emerald-100 dark:border-emerald-800/50 shadow-sm"
            >
              <Link
                href="/docs/stream-messages"
                className="flex items-center justify-center gap-2 text-emerald-700 dark:text-emerald-300 font-medium"
              >
                Next:: Stream Messages
                <ArrowRight className="w-5 h-5" />
              </Link>
            </motion.div>

            {/* Social Links */}
            <SocialLinks />
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default DeleteChatbotPage;
