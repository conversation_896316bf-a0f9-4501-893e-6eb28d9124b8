"use client";

import { AnimatePresence, motion } from "framer-motion";
import {
  <PERSON>,
  ArrowR<PERSON>,
  Menu,
  X,
  Info,
  AlertTriangle,
} from "lucide-react";
import Head from "next/head";
import Link from "next/link";
import { useState } from "react";
import {
  <PERSON><PERSON>crumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { DocsSidebar } from "@/components/sidebar/docs-sidebar";
import SocialLinks from "@/components/social-links/social-links";
import { CopyButton } from "@/components/copy-button/copy-button";
import { itemVariants } from "@/variants/variants";
import { CodeBlock } from "@/components/code-block/code-block";

type CodeLanguage = "javascript" | "python" | "php" | "shell" | "http";

const GetChatbotsPage = () => {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [activeTab, setActiveTab] =
    useState<keyof typeof codeExamples>("javascript");

  const codeExamples: Record<CodeLanguage, string> = {
    javascript: `const options = {
    method: 'GET',
    headers: { accept: 'application/json' }
  };
  
  fetch('https://www.chatzuri.com/api/v1/get-chatbots', options)
    .then(response => response.json())
    .then(response => console.log(response))
    .catch(err => console.error(err));`,

    python: `import requests
  
  url = "https://www.chatzuri.com/api/v1/get-chatbots"
  headers = {
      "accept": "application/json"
  }
  
  response = requests.get(url, headers=headers)
  print(response.json())`,

    php: `<?php
  $url = 'https://www.chatzuri.com/api/v1/get-chatbots';
  $headers = [
      'accept: application/json'
  ];
  
  $ch = curl_init();
  curl_setopt($ch, CURLOPT_URL, $url);
  curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'GET');
  curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
  curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
  
  $response = curl_exec($ch);
  curl_close($ch);
  echo $response;
  ?>`,

    shell: `curl -X GET \\
    -H "accept: application/json" \\
    "https://www.chatzuri.com/api/v1/get-chatbots"`,

    http: `GET /api/v1/get-chatbots HTTP/1.1
  Host: www.chatzuri.com
  accept: application/json`,
  };

  const responseExample=`{
    "chatbots": [
      {
        "chatbot_id": "string",
        "created_at": "string",
        "chatbot_owner_user_id": "string",
        "chatbot_name": "string",
        "phone": "string",
        "email": "string",
        "source_text": "string"
      },
      {
        "chatbot_id": "string",
        "created_at": "string",
        "chatbot_owner_user_id": "string",
        "chatbot_name": "string",
        "phone": "string",
        "email": "string",
        "source_text": "string"
      }
    ]
  }`

  return (
    <div className="min-h-screen">
      <Head>
        <title> Get Chatbots | Chatzuri API Docs</title>
      </Head>
      <div className="flex flex-col lg:flex-row">
        {/* Mobile Menu Button */}
        <div className="lg:hidden fixed top-4 right-4 z-50">
          <button
            onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            className="p-2 rounded-lg bg-white cursor-pointer dark:bg-gray-800 shadow-md"
          >
            {mobileMenuOpen ? (
              <X className="w-6 h-6" />
            ) : (
              <Menu className="w-6 h-6" />
            )}
          </button>
        </div>

        {/* Sidebar Navigation*/}
        <DocsSidebar />

        {/* Main Content */}
        <div className="flex-1 lg:ml-72 mt-16 lg:mt-0">
          {/* Toolbar Section */}
          <motion.div
            className="py-7 md:py-10 px-4 sm:px-6 lg:px-8"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
          >
            <div className="flex flex-col gap-4 w-full">
              <div className="lg:mt-10">
                {/* Breadcrumb */}
                <Breadcrumb>
                  <BreadcrumbList>
                    <BreadcrumbItem>
                      <BreadcrumbLink
                        href="/"
                        className="text-emerald-600 dark:text-emerald-400 hover:text-emerald-700 dark:hover:text-emerald-300"
                      >
                        <Home className="inline mr-1 h-4 w-4" />
                        Home
                      </BreadcrumbLink>
                    </BreadcrumbItem>
                    <BreadcrumbSeparator />
                    <BreadcrumbItem>
                      <BreadcrumbLink
                        href="/docs"
                        className="text-gray-700 dark:text-gray-300"
                      >
                        API Docs
                      </BreadcrumbLink>
                    </BreadcrumbItem>
                    <BreadcrumbSeparator />
                    <BreadcrumbItem>
                      <BreadcrumbLink className="text-gray-500 dark:text-gray-400">
                        Get Chatbots
                      </BreadcrumbLink>
                    </BreadcrumbItem>
                  </BreadcrumbList>
                </Breadcrumb>
              </div>
            </div>
          </motion.div>

          {/* Content Section */}
          <motion.div
            className="px-4 sm:px-6 lg:px-8 pb-12"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
          >
            <div className="bg-white px-4 py-3 dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden mb-4">
              {/* Page Header */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4 }}
                variants={itemVariants}
                whileHover={{ scale: 1.01 }}
                className="mb-10"
              >
                <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                  Get Chatbots API Guide
                </h1>
                <p className="text-lg text-gray-600 dark:text-gray-300 font-medium">
                  Get a list of all chatbots under your API key.
                </p>
                <div className="my-6 h-px bg-gradient-to-r from-transparent via-emerald-500 to-transparent dark:via-emerald-400" />
              </motion.div>

              {/* Introduction Section */}
              <motion.section
                variants={itemVariants}
                className="mb-8"
                whileHover={{ scale: 1.01 }}
              >
                <div className="relative group">
                  <div className="absolute -inset-1 bg-gradient-to-r from-emerald-500 to-purple-600 rounded-lg blur opacity-10 group-hover:opacity-20 transition duration-300"></div>
                  <div className="relative p-6 rounded-lg border bg-white border-gray-200 dark:bg-gray-800/50 dark:border-gray-700">
                    <p className="text-gray-700 dark:text-gray-300 text-lg mb-4">
                      The Get Chatbots API endpoint allows you to retrieve a
                      list of all chatbots associated with your API key.
                    </p>
                  </div>
                </div>
              </motion.section>

              {/* Endpoint Section */}
              <motion.section
                variants={itemVariants}
                className="mb-8"
                whileHover={{ scale: 1.01 }}
              >
                <div className="relative group">
                  <div className="absolute -inset-1 bg-gradient-to-r from-emerald-500 to-purple-600 rounded-lg blur opacity-10 group-hover:opacity-20 transition duration-300"></div>
                  <div className="relative p-6 rounded-lg border bg-white border-gray-200 dark:bg-gray-800/50 dark:border-gray-700">
                    <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                      Endpoint
                    </h2>
                    <div className="p-4 inline-flex gap-2  bg-gray-100 dark:bg-gray-700 rounded-md max-w-full overflow-x-auto">
                      <div className="min-w-0">
                      <CodeBlock language="http">
                        POST https://www.chatzuri.com/api/v1/get-chatbots
                      </CodeBlock>
                      </div>
                      <CopyButton
                        textToCopy="POST https://www.chatzuri.com/api/v1/get-chatbots"
                        buttonId="get-conversations-api-endpoint"
                      />
                    </div>
                  </div>
                </div>
              </motion.section>

              {/* Request Headers Section */}
              <motion.section
                variants={itemVariants}
                className="mb-8"
                whileHover={{ scale: 1.01 }}
              >
                <div className="relative group">
                  <div className="absolute -inset-1 bg-gradient-to-r from-emerald-500 to-purple-600 rounded-lg blur opacity-10 group-hover:opacity-20 transition duration-300"></div>
                  <div className="relative p-6 rounded-lg border bg-white border-gray-200 dark:bg-gray-800/50 dark:border-gray-700">
                    <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                      Request Headers
                    </h2>
                    <p className="text-gray-700 dark:text-gray-300 mb-4">
                      The API request must include the following headers.
                    </p>

                    <motion.ul
                      className="space-y-3 mb-6"
                      initial="hidden"
                      animate="visible"
                      variants={{
                        visible: {
                          transition: {
                            staggerChildren: 0.1,
                          },
                        },
                      }}
                    >
                      <motion.li
                        className="flex items-start group"
                        variants={{
                          hidden: { opacity: 0, x: -10 },
                          visible: {
                            opacity: 1,
                            x: 0,
                            transition: { type: "spring", stiffness: 300 },
                          },
                        }}
                        whileHover={{ x: 3 }}
                      >
                        <span className="flex items-start">
                          <span className="flex-shrink-0 mt-1 mr-3 text-emerald-500 dark:text-emerald-400">
                            <svg
                              width="16"
                              height="16"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="currentColor"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            >
                              <circle cx="12" cy="12" r="10"></circle>
                              <path d="M8 14s1.5 2 4 2 4-2 4-2"></path>
                              <line x1="9" y1="9" x2="9.01" y2="9"></line>
                              <line x1="15" y1="9" x2="15.01" y2="9"></line>
                            </svg>
                          </span>
                          <span className="text-gray-700 dark:text-gray-300">
                            <code className="bg-emerald-50 dark:bg-emerald-900/30 text-emerald-600 dark:text-emerald-300 px-2 py-1 rounded-md font-mono text-sm border border-emerald-100 dark:border-emerald-800/50 group-hover:bg-emerald-100 dark:group-hover:bg-emerald-900/50 transition-colors">
                              Authorization: Bearer {"<Your-Secret-Key>"}
                            </code>
                            <span className="ml-2 text-gray-600 dark:text-gray-400 text-sm">
                              - The secret key for authenticating the API
                              request.
                            </span>
                          </span>
                        </span>
                      </motion.li>

                      <motion.li
                        className="flex items-start group"
                        variants={{
                          hidden: { opacity: 0, x: -10 },
                          visible: {
                            opacity: 1,
                            x: 0,
                            transition: { type: "spring", stiffness: 300 },
                          },
                        }}
                        whileHover={{ x: 3 }}
                      >
                        <span className="flex items-start">
                          <span className="flex-shrink-0 mt-1 mr-3 text-emerald-500 dark:text-emerald-400">
                            <svg
                              width="16"
                              height="16"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="currentColor"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            >
                              <circle cx="12" cy="12" r="10"></circle>
                              <path d="M8 14s1.5 2 4 2 4-2 4-2"></path>
                              <line x1="9" y1="9" x2="9.01" y2="9"></line>
                              <line x1="15" y1="9" x2="15.01" y2="9"></line>
                            </svg>
                          </span>
                          <span className="text-gray-700 dark:text-gray-300">
                            <code className="bg-emerald-50 dark:bg-emerald-900/30 text-emerald-600 dark:text-emerald-300 px-2 py-1 rounded-md font-mono text-sm border border-emerald-100 dark:border-emerald-800/50 group-hover:bg-emerald-100 dark:group-hover:bg-emerald-900/50 transition-colors">
                              Accept: application/json
                            </code>
                            <span className="ml-2 text-gray-600 dark:text-gray-400 text-sm">
                              - The accepted response content type.
                            </span>
                          </span>
                        </span>
                      </motion.li>

                      <motion.li
                        className="flex items-start group"
                        variants={{
                          hidden: { opacity: 0, x: -10 },
                          visible: {
                            opacity: 1,
                            x: 0,
                            transition: { type: "spring", stiffness: 300 },
                          },
                        }}
                        whileHover={{ x: 3 }}
                      >
                        <span className="flex items-start">
                          <span className="flex-shrink-0 mt-1 mr-3 text-emerald-500 dark:text-emerald-400">
                            <svg
                              width="16"
                              height="16"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="currentColor"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            >
                              <circle cx="12" cy="12" r="10"></circle>
                              <path d="M8 14s1.5 2 4 2 4-2 4-2"></path>
                              <line x1="9" y1="9" x2="9.01" y2="9"></line>
                              <line x1="15" y1="9" x2="15.01" y2="9"></line>
                            </svg>
                          </span>
                          <span className="text-gray-700 dark:text-gray-300">
                            <code className="bg-emerald-50 dark:bg-emerald-900/30 text-emerald-600 dark:text-emerald-300 px-2 py-1 rounded-md font-mono text-sm border border-emerald-100 dark:border-emerald-800/50 group-hover:bg-emerald-100 dark:group-hover:bg-emerald-900/50 transition-colors">
                              Content-Type: application/json
                            </code>
                            <span className="ml-2 text-gray-600 dark:text-gray-400 text-sm">
                              - The content type of the request.
                            </span>
                          </span>
                        </span>
                      </motion.li>
                    </motion.ul>
                  </div>
                </div>
              </motion.section>

              {/* Example Request Section */}
              <motion.section
                variants={itemVariants}
                className="mb-8"
                whileHover={{ scale: 1.01 }}
              >
                <div className="relative group">
                  <div className="absolute -inset-1 bg-gradient-to-r from-emerald-500 to-purple-600 rounded-lg blur opacity-10 group-hover:opacity-20 transition duration-300"></div>
                  <div className="relative p-6 rounded-lg border bg-white border-gray-200 dark:bg-gray-800/50 dark:border-gray-700">
                    <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                      Example Request
                    </h2>

                    {/* Tabs */}
                    <div className="mb-6">
                      {(
                        [
                          "javascript",
                          "python",
                          "php",
                          "shell",
                          "http",
                        ] as CodeLanguage[]
                      ).map((lang) => (
                        <button
                          key={lang}
                          onClick={() => setActiveTab(lang)}
                          className={`px-4 py-2 font-medium cursor-pointer
                          ${
                            activeTab === lang
                              ? "text-emerald-600 dark:text-emerald-400 border-b-2 border-emerald-500"
                              : "text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-b-2 hover:border-gray-300 dark:hover:border-gray-300"
                          }`}
                        >
                          {lang.charAt(0).toUpperCase() + lang.slice(1)}
                        </button>
                      ))}
                    </div>

                    <AnimatePresence mode="wait">
                      <motion.div
                        key={activeTab}
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        exit={{ opacity: 0 }}
                        className="relative"
                        transition={{ duration: 0.2 }}
                      >
                        <CodeBlock
                          language={activeTab}
                          showLineNumbers
                          wrapLines
                        >
                          {codeExamples[activeTab]}
                        </CodeBlock>
                        <CopyButton
                          textToCopy={codeExamples[activeTab]}
                          buttonId={`code-blocks-4578hfedckhk5tg-${activeTab}`}
                          classNames="absolute top-2 right-2"
                        />
                      </motion.div>
                    </AnimatePresence>
                  </div>
                </div>
              </motion.section>

              {/* Response Section */}
              {/* Response Section */}
              <motion.section
                variants={itemVariants}
                className="mb-10"
                whileHover={{ scale: 1.005 }}
              >
                <div className="relative group">
                  <div className="absolute -inset-1 bg-gradient-to-r from-emerald-400 to-purple-500 rounded-xl blur opacity-10 group-hover:opacity-20 transition duration-500"></div>
                  <div className="relative p-8 rounded-xl bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border border-gray-200/70 dark:border-gray-700/50 shadow-sm hover:shadow-md transition-all">
                    {/* Header */}
                    <div className="flex items-center gap-3 mb-6">
                      <div className="p-2 rounded-lg bg-emerald-100/50 dark:bg-emerald-900/20 text-emerald-600 dark:text-emerald-300">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="24"
                          height="24"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        >
                          <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
                        </svg>
                      </div>
                      <div>
                        <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                          Response
                        </h2>
                        <p className="text-gray-600 dark:text-gray-400">
                          The API response codes and structure
                        </p>
                      </div>
                    </div>

                    {/* Status Codes */}
                    <div className="mb-8">
                      <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4 flex items-center gap-2">
                        <span className="w-2 h-2 rounded-full bg-emerald-500"></span>
                        HTTP Status Codes
                      </h3>

                      <motion.ul
                        className="space-y-3 mb-6"
                        initial="hidden"
                        animate="visible"
                        variants={{
                          visible: { transition: { staggerChildren: 0.1 } },
                        }}
                      >
                        {[
                          {
                            code: "200",
                            description:
                              "Returns an array chatbots[] containing each chatbot's parameters",
                          },
                          {
                            code: "401",
                            description: "If the API request is unauthorized",
                          },
                          {
                            code: "500",
                            description:
                              "If there is an internal server error while processing the request",
                          },
                        ].map((item, index) => (
                          <motion.li
                            key={index}
                            className="flex items-start group"
                            variants={{
                              hidden: { opacity: 0, y: 5 },
                              visible: {
                                opacity: 1,
                                y: 0,
                                transition: { type: "spring", stiffness: 300 },
                              },
                            }}
                          >
                            <span className="flex items-start">
                              <span
                                className={`flex-shrink-0 mt-1 mr-3 ${
                                  item.code === "200"
                                    ? "text-emerald-500"
                                    : item.code === "401"
                                    ? "text-amber-500"
                                    : "text-red-500"
                                }`}
                              >
                                <svg
                                  width="16"
                                  height="16"
                                  viewBox="0 0 24 24"
                                  fill="none"
                                  stroke="currentColor"
                                  strokeWidth="2"
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                >
                                  <circle cx="12" cy="12" r="10"></circle>
                                  {item.code === "200" ? (
                                    <path d="M8 14s1.5 2 4 2 4-2 4-2"></path>
                                  ) : (
                                    <line x1="9" y1="9" x2="15" y2="15"></line>
                                  )}
                                  <line x1="9" y1="9" x2="9.01" y2="9"></line>
                                  <line x1="15" y1="9" x2="15.01" y2="9"></line>
                                </svg>
                              </span>
                              <span className="text-gray-700 dark:text-gray-300">
                                <code
                                  className={`${
                                    item.code === "200"
                                      ? "bg-emerald-50 dark:bg-emerald-900/30 text-emerald-600 dark:text-emerald-300 border-emerald-100 dark:border-emerald-800/50"
                                      : item.code === "401"
                                      ? "bg-amber-50 dark:bg-amber-900/30 text-amber-600 dark:text-amber-300 border-amber-100 dark:border-amber-800/50"
                                      : "bg-red-50 dark:bg-red-900/30 text-red-600 dark:text-red-300 border-red-100 dark:border-red-800/50"
                                  } px-2 py-1 rounded-md font-mono text-sm border group-hover:shadow-xs transition-all`}
                                >
                                  {item.code}
                                </code>
                                <span className="ml-2 text-gray-600 dark:text-gray-400">
                                  - {item.description}
                                </span>
                              </span>
                            </span>
                          </motion.li>
                        ))}
                      </motion.ul>
                    </div>

                    {/* Response Structure */}
                    <div>
                      <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4 flex items-center gap-2">
                        <span className="w-2 h-2 rounded-full bg-purple-500"></span>
                        Response Structure
                      </h3>

                      <p className="text-gray-700 dark:text-gray-300 mb-5">
                        The API response will be a JSON object with the
                        following structure:
                      </p>

                      <div className="relative rounded-xl overflow-shadow-sm">
                        <CodeBlock
                          language="json"
                          showLineNumbers
                          wrapLines
                        >
                          {responseExample}
                        </CodeBlock>
                        <CopyButton
                          textToCopy={responseExample}
                          buttonId="response-structure-copy"
                          classNames="absolute top-3 right-3 bg-white/80 dark:bg-gray-700/80 hover:bg-white dark:hover:bg-gray-600 backdrop-blur-sm"
                        />
                      </div>

                      <p className="mt-6 text-gray-700 dark:text-gray-300">
                        The Get Chatbots API endpoint provides a way to retrieve
                        a list of all chatbots associated with your API key.
                        Each entry in the response array contains all parameters
                        of the respective chatbot.
                      </p>
                    </div>
                  </div>
                </div>
              </motion.section>

              {/* Error Handling Section */}
              <motion.section
                variants={itemVariants}
                className="mb-8"
                whileHover={{ scale: 1.01 }}
              >
                <div className="relative group">
                  <div className="absolute -inset-1 bg-gradient-to-r from-red-500 to-purple-600 rounded-lg blur opacity-10 group-hover:opacity-20 transition duration-300"></div>
                  <div className="relative p-6 rounded-lg border bg-white border-gray-200 dark:bg-gray-800/50 dark:border-gray-700">
                    <div className="flex items-start mb-4">
                      <div className="p-2 mr-3 rounded-full bg-red-100 text-red-600 dark:bg-red-900/20 dark:text-red-400">
                        <AlertTriangle className="w-5 h-5" />
                      </div>
                      <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                        Error Handling
                      </h2>
                    </div>

                    <div className="lg:pl-14 space-y-4">
                      <motion.div
                        whileHover={{ x: 3 }}
                        className="p-4 rounded-lg bg-gray-50 dark:bg-gray-700/50"
                      >
                        <p className="text-gray-700 dark:text-gray-300">
                          If there are any errors during the API request,
                          appropriate HTTP status codes will be returned along
                          with error messages in the response body.
                        </p>
                      </motion.div>

                      <motion.div
                        whileHover={{ x: 3 }}
                        className="p-4 rounded-lg bg-emerald-100 dark:bg-emerald-900/20"
                      >
                        <p className="text-gray-700 dark:text-gray-300">
                          That&apos;s it! You should now be able to retrieve
                          list of chatbot&apos;s from your account using our API.
                        </p>
                      </motion.div>
                    </div>

                    <div className="mt-6 flex items-center text-sm text-gray-500 dark:text-gray-400">
                      <Info className="w-4 h-4 mr-2" />
                      <span>
                        Remember to handle errors gracefully in your application
                      </span>
                    </div>
                  </div>
                </div>
              </motion.section>
            </div>

            {/* Next Step Card */}
            <motion.div
              whileHover={{ y: -5 }}
              variants={itemVariants}
              className="mb-8 bg-gradient-to-r from-emerald-50 to-emerald-100 dark:from-emerald-900/30 dark:to-emerald-800/30 rounded-xl p-6 text-center border border-emerald-100 dark:border-emerald-800/50 shadow-sm"
            >
              <Link
                href="/docs/webhooks-api"
                className="flex items-center justify-center gap-2 text-emerald-700 dark:text-emerald-300 font-medium"
              >
                Next::Webhooks API
                <ArrowRight className="w-5 h-5" />
              </Link>
            </motion.div>

            {/* Social Links */}
            <SocialLinks />
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default GetChatbotsPage;
