"use client";

import { AnimatePresence, motion } from "framer-motion";
import {
  Home,
  ArrowRight,
  Menu,
  X,
  Shield,
  Info,
  <PERSON><PERSON><PERSON>riangle,
  Smile,
} from "lucide-react";
import Head from "next/head";
import Link from "next/link";
import { useState } from "react";
import {
  <PERSON><PERSON>crumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { DocsSidebar } from "@/components/sidebar/docs-sidebar";
import SocialLinks from "@/components/social-links/social-links";
import { CopyButton } from "@/components/copy-button/copy-button";
import { itemVariants } from "@/variants/variants";
import { CodeBlock } from "@/components/code-block/code-block";

type CodeLanguage = "javascript" | "python" | "php" | "shell" | "http";

const WebhookAPIPage = () => {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [activeTab, setActiveTab] = useState<CodeLanguage>("javascript");

  const codeExamples: Record<CodeLanguage, string> = {
    javascript: `import crypto from 'crypto'
import { Request, Response } from 'express'

export async function webhookHandler(req: Request, res: Response) {
  if (req.method === 'POST') {
    const { SECRET_KEY } = process.env
    
    const receivedJson = req.body
    const rawBody = Buffer.from(JSON.stringify(receivedJson))
    const bodySignature = sha1(rawBody, SECRET_KEY)

    if (bodySignature !== req.headers['x-chatzuri-signature']) {
      return res.status(400).json({ message: "Signature didn't match" })
    }

    console.log('Received:', receivedJson)
    
    res.status(200).json({ status: "accepted" })
  } else {
    res.setHeader('Allow', 'POST')
    res.status(405).end('Method Not Allowed')
  }
}

function sha1(data: Buffer, secret: string): string {
  return crypto.createHmac('sha1', secret).update(data).digest('hex')
}`,

    python: `from flask import Flask, request, jsonify
import hmac
import hashlib
import os

app = Flask(__name__)

@app.route('/webhook', methods=['POST'])
def webhook():
    secret_key = os.environ.get('SECRET_KEY')
    received_signature = request.headers.get('x-chatzuri-signature')
    
    # Generate signature
    generated_signature = hmac.new(
        secret_key.encode(),
        request.data,
        hashlib.sha1
    ).hexdigest()
    
    if received_signature != generated_signature:
        return jsonify({"error": "Invalid signature"}), 400
    
    print("Received payload:", request.json)
    return jsonify({"status": "accepted"})

if __name__ == '__main__':
    app.run(port=5000)`,

    php: `<?php
$secretKey = getenv('SECRET_KEY');
$receivedSignature = $_SERVER['HTTP_X_CHATZURI_SIGNATURE'];
$payload = file_get_contents('php://input');

// Calculate signature
$calculatedSignature = hash_hmac('sha1', $payload, $secretKey);

if ($receivedSignature !== $calculatedSignature) {
    http_response_code(400);
    echo json_encode(['error' => 'Invalid signature']);
    exit;
}

// Process webhook
$data = json_decode($payload, true);
error_log(print_r($data, true));

// Respond
header('Content-Type: application/json');
echo json_encode(['status' => 'accepted']);`,

    shell: `# Example using ngrok to test locally
# Install ngrok first: https://ngrok.com/download
ngrok http 3000

# Then set your webhook URL in Chatzuri dashboard to:
# https://your-subdomain.ngrok.io/webhook`,

    http: `POST /webhook HTTP/1.1
Host: your-server.com
Content-Type: application/json
X-Chatzuri-Signature: sha1-generated-signature
Accept: application/json

{
  "eventType": "lead.submit",
  "chatbotId": "xxxxxxxx",
  "payload": {
    "conversationId": "xxxxxxxx",
    "customerEmail": "<EMAIL>",
    "customerName": "Example",
    "customerPhone": "123"
  }
}`,
  };

  const payloadStructure = `{
  "eventType": "string",  // event type (eg. lead.submit)
  "chatbotId": "string",  // Chatbot ID
  "payload": {           // Payload of the event
    // ... event specific data
  }
}`;

  const leadSubmitPayload = `{
  "conversationId": "string",
  "customerEmail": "string",
  "customerName": "string",
  "customerPhone": "string"
}`;

  const responseExample = `{
  "status": "accepted"
}`;

  return (
    <div className="min-h-screen">
      <Head>
        <title>Webhook API | Chatzuri API Docs</title>
      </Head>
      <div className="flex flex-col lg:flex-row">
        {/* Mobile Menu Button */}
        <div className="lg:hidden fixed top-4 right-4 z-50">
          <button
            onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            className="p-2 rounded-lg bg-white cursor-pointer dark:bg-gray-800 shadow-md"
          >
            {mobileMenuOpen ? (
              <X className="w-6 h-6" />
            ) : (
              <Menu className="w-6 h-6" />
            )}
          </button>
        </div>

        {/* Sidebar Navigation*/}
        <DocsSidebar />

        {/* Main Content */}
        <div className="flex-1 lg:ml-72 mt-16 lg:mt-0">
          {/* Toolbar Section */}
          <motion.div
            className="py-7 md:py-10 px-4 sm:px-6 lg:px-8"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
          >
            <div className="flex flex-col gap-4 w-full">
              <div className="lg:mt-10">
                {/* Breadcrumb */}
                <Breadcrumb>
                  <BreadcrumbList>
                    <BreadcrumbItem>
                      <BreadcrumbLink
                        href="/"
                        className="text-emerald-600 dark:text-emerald-400 hover:text-emerald-700 dark:hover:text-emerald-300"
                      >
                        <Home className="inline mr-1 h-4 w-4" />
                        Home
                      </BreadcrumbLink>
                    </BreadcrumbItem>
                    <BreadcrumbSeparator />
                    <BreadcrumbItem>
                      <BreadcrumbLink
                        href="/docs"
                        className="text-gray-700 dark:text-gray-300"
                      >
                        API Docs
                      </BreadcrumbLink>
                    </BreadcrumbItem>
                    <BreadcrumbSeparator />
                    <BreadcrumbItem>
                      <BreadcrumbLink className="text-gray-500 dark:text-gray-400">
                        Webhook API
                      </BreadcrumbLink>
                    </BreadcrumbItem>
                  </BreadcrumbList>
                </Breadcrumb>
              </div>
            </div>
          </motion.div>

          {/* Content Section */}
          <motion.div
            className="px-4 sm:px-6 lg:px-8 pb-12"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
          >
            <div className="bg-white px-4 py-3 dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden mb-4">
              {/* Page Header */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4 }}
                variants={itemVariants}
                whileHover={{ scale: 1.01 }}
                className="mb-10"
              >
                <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                  Webhook API Guide
                </h1>
                <p className="text-lg text-gray-600 dark:text-gray-300 font-medium">
                  This page helps you setting up your chatbot webhooks.
                </p>
                <div className="my-6 h-px bg-gradient-to-r from-transparent via-emerald-500 to-transparent dark:via-emerald-400" />
              </motion.div>

              {/* Introduction */}
              <motion.section
                variants={itemVariants}
                className="mb-8"
                whileHover={{ scale: 1.01 }}
              >
                <div className="relative group">
                  <div className="absolute -inset-1 bg-gradient-to-r from-emerald-500 to-purple-600 rounded-lg blur opacity-10 group-hover:opacity-20 transition duration-300"></div>
                  <div className="relative p-6 rounded-lg border bg-white border-gray-200 dark:bg-gray-800/50 dark:border-gray-700">
                    <p className="text-gray-700 dark:text-gray-300 text-lg">
                      The Webhook API guide allows you to set-up webhooks to
                      receive a POST request when an event is triggered.
                    </p>
                  </div>
                </div>
              </motion.section>

              {/* Payload Structure */}
              <motion.section
                variants={itemVariants}
                className="mb-8"
                whileHover={{ scale: 1.01 }}
              >
                <div className="relative group">
                  <div className="absolute -inset-1 bg-gradient-to-r from-emerald-500 to-purple-600 rounded-lg blur opacity-10 group-hover:opacity-20 transition duration-300"></div>
                  <div className="relative p-6 rounded-lg border bg-white border-gray-200 dark:bg-gray-800/50 dark:border-gray-700">
                    <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                      Payload Structure
                    </h2>

                    <div className="overflow-x-auto">
                      <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                        <thead className="bg-gray-50 dark:bg-gray-700">
                          <tr>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                              Key
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                              Type
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                              Description
                            </th>
                          </tr>
                        </thead>
                        <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                          <tr>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-mono text-emerald-600 dark:text-emerald-400">
                              eventType
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700 dark:text-gray-300">
                              string
                            </td>
                            <td className="px-6 py-4 text-sm text-gray-700 dark:text-gray-300">
                              event type (eg. lead.submit)
                            </td>
                          </tr>
                          <tr>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-mono text-emerald-600 dark:text-emerald-400">
                              chatbotId
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700 dark:text-gray-300">
                              string
                            </td>
                            <td className="px-6 py-4 text-sm text-gray-700 dark:text-gray-300">
                              Chatbot ID
                            </td>
                          </tr>
                          <tr>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-mono text-emerald-600 dark:text-emerald-400">
                              payload
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700 dark:text-gray-300">
                              Object
                            </td>
                            <td className="px-6 py-4 text-sm text-gray-700 dark:text-gray-300">
                              Payload of the event
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </div>

                    <div className="relative rounded-xl overflow-hidden shadow-sm">
                      <CodeBlock language="json" showLineNumbers wrapLines>
                        {payloadStructure}
                      </CodeBlock>
                      <CopyButton
                        textToCopy={payloadStructure}
                        buttonId="payload-structure-copy"
                        classNames="absolute top-3 right-3 bg-white/80 dark:bg-gray-700/80 hover:bg-white dark:hover:bg-gray-600 backdrop-blur-sm"
                      />
                    </div>
                  </div>
                </div>
              </motion.section>

              {/* Event Types */}
              <motion.section
                variants={itemVariants}
                className="mb-8"
                whileHover={{ scale: 1.01 }}
              >
                <div className="relative group">
                  <div className="absolute -inset-1 bg-gradient-to-r from-emerald-500 to-purple-600 rounded-lg blur opacity-10 group-hover:opacity-20 transition duration-300"></div>
                  <div className="relative p-6 rounded-lg border bg-white border-gray-200 dark:bg-gray-800/50 dark:border-gray-700">
                    <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                      Event Types
                    </h2>
                    <p className="text-gray-700 dark:text-gray-300 mb-4">
                      These are the list of events supported in webhooks.
                    </p>

                    <div className="space-y-4">
                      <div className="p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg border border-gray-200 dark:border-gray-600">
                        <motion.div
                          className="flex items-center gap-1 group"
                          whileHover={{ x: 2 }}
                          transition={{ type: "spring", stiffness: 400 }}
                        >
                          <div className="flex-shrink-0 p-2 rounded-lg bg-emerald-100/50 dark:bg-emerald-900/20 text-emerald-600 dark:text-emerald-300 group-hover:bg-emerald-200/50 dark:group-hover:bg-emerald-900/30 transition-colors">
                            <Smile className="w-5 h-5" />
                          </div>
                          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                            <span className="bg-gradient-to-r from-emerald-600 to-emerald-400 dark:from-emerald-400 dark:to-emerald-300 bg-clip-text text-transparent">
                              lead.submit
                            </span>
                          </h3>
                        </motion.div>
                        <div className="flex-shrink-0 mt-1 mr-3 text-gray-500 dark:text-gray-400">
                          <p className="my-2 text-gray-700 dark:text-gray-300">
                            When a customer submits their info (Name, Email, and
                            Phone) to your chatbot.
                          </p>
                          <div className="w-full relative rounded-xl overflow-hidden shadow-sm">
                            <CodeBlock
                              language="json"
                              showLineNumbers
                              wrapLines
                              className="w-full"
                            >
                              {leadSubmitPayload}
                            </CodeBlock>
                            <CopyButton
                              textToCopy={leadSubmitPayload}
                              buttonId="lead-payload-copy"
                              classNames="absolute top-3 right-3 bg-white/80 dark:bg-gray-700/80 hover:bg-white dark:hover:bg-gray-600 backdrop-blur-sm"
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </motion.section>

              {/* Receiving Webhooks */}
              <motion.section
                variants={itemVariants}
                className="mb-8"
                whileHover={{ scale: 1.01 }}
              >
                <div className="relative group">
                  <div className="absolute -inset-1 bg-gradient-to-r from-emerald-500 to-purple-600 rounded-lg blur opacity-10 group-hover:opacity-20 transition duration-300"></div>
                  <div className="relative p-6 rounded-lg border bg-white border-gray-200 dark:bg-gray-800/50 dark:border-gray-700">
                    <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                      Receiving the Webhook Request
                    </h2>

                    <div className="space-y-4">
                      <div className="p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                        <p className="text-gray-700 dark:text-gray-300">
                          You can receive the payload by accessing the body same
                          as any request.
                        </p>
                      </div>

                      <div className="p-4 bg-amber-50 dark:bg-amber-900/20 rounded-lg">
                        <div className="flex items-start">
                          <div className="flex-shrink-0 mt-1 mr-3 text-amber-500 dark:text-amber-400">
                            <Shield className="w-5 h-5" />
                          </div>
                          <p className="text-gray-700 dark:text-gray-300">
                            It is recommended to check the request header{" "}
                            <code className="bg-gray-100 dark:bg-gray-700 px-1 py-0.5 rounded text-sm">
                              x-chatzuri-signature
                            </code>{" "}
                            for securing your endpoint from spam.
                          </p>
                        </div>
                      </div>

                      <div className="p-4 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                        <p className="text-gray-700 dark:text-gray-300">
                          You can achieve this by using SHA-1 (Secure Hash
                          Algorithm 1) function to generate a signature for the
                          request and compare it with{" "}
                          <code className="bg-gray-100 dark:bg-gray-700 px-1 py-0.5 rounded text-sm">
                            x-chatzuri-signature
                          </code>{" "}
                          found in the request headers. If they are identical
                          then the request is from Chatzuri.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </motion.section>

              {/* Example Request */}
              <motion.section
                variants={itemVariants}
                className="mb-8"
                whileHover={{ scale: 1.01 }}
              >
                <div className="relative group">
                  <div className="absolute -inset-1 bg-gradient-to-r from-emerald-500 to-purple-600 rounded-lg blur opacity-10 group-hover:opacity-20 transition duration-300"></div>
                  <div className="relative p-6 rounded-lg border bg-white border-gray-200 dark:bg-gray-800/50 dark:border-gray-700">
                    <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                      Example Request
                    </h2>

                    {/* Tabs */}
                    <div className="mb-6">
                      <div className="border-b border-gray-200 dark:border-gray-700">
                        <nav className="-mb-px flex space-x-8">
                          {Object.keys(codeExamples).map((lang) => (
                            <button
                              key={lang}
                              onClick={() => setActiveTab(lang as CodeLanguage)}
                              className={`whitespace-nowrap py-4 cursor-pointer px-1 border-b-2 font-medium text-sm ${
                                activeTab === lang
                                  ? "border-emerald-500 text-emerald-600 dark:text-emerald-400"
                                  : "border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600"
                              }`}
                            >
                              {lang === "javascript"
                                ? "Node.js"
                                : lang.charAt(0).toUpperCase() + lang.slice(1)}
                            </button>
                          ))}
                        </nav>
                      </div>
                    </div>

                    {/* Code Block */}
                    <div className="relative rounded-xl overflow-hidden  shadow-sm">
                      <AnimatePresence mode="wait">
                        <motion.div
                          key={activeTab}
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          exit={{ opacity: 0 }}
                          className="relative"
                          transition={{ duration: 0.2 }}
                        >
                          <CodeBlock
                            language={activeTab}
                            showLineNumbers
                            wrapLines
                          >
                            {codeExamples[activeTab]}
                          </CodeBlock>
                          <CopyButton
                            textToCopy={codeExamples[activeTab]}
                            buttonId={`code-blocks-4578hfedckhk5tg-${activeTab}`}
                            classNames="absolute top-2 right-2"
                          />
                        </motion.div>
                      </AnimatePresence>
                    </div>
                  </div>
                </div>
              </motion.section>

              {/* Respond to Webhook */}
              <motion.section
                variants={itemVariants}
                className="mb-8"
                whileHover={{ scale: 1.01 }}
              >
                <div className="relative group">
                  <div className="absolute -inset-1 bg-gradient-to-r from-emerald-500 to-purple-600 rounded-lg blur opacity-10 group-hover:opacity-20 transition duration-300"></div>
                  <div className="relative p-6 rounded-lg border bg-white border-gray-200 dark:bg-gray-800/50 dark:border-gray-700">
                    <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                      Respond to the Event Webhook
                    </h2>

                    <p className="text-gray-700 dark:text-gray-300 mb-4">
                      Return a JSON object with the following structure to
                      indicate your endpoint has received the event webhook:
                    </p>

                    <div className="relative rounded-xl overflow-hidden  shadow-sm">
                      <CodeBlock language="json" showLineNumbers wrapLines>
                        {responseExample}
                      </CodeBlock>
                      <CopyButton
                        textToCopy={responseExample}
                        buttonId="response-example-copy"
                        classNames="absolute top-3 right-3 bg-white/80 dark:bg-gray-700/80 hover:bg-white dark:hover:bg-gray-600 backdrop-blur-sm"
                      />
                    </div>
                  </div>
                </div>
              </motion.section>

              {/* Error Handling Section */}
              <motion.section
                variants={itemVariants}
                className="mb-8"
                whileHover={{ scale: 1.01 }}
              >
                <div className="relative group">
                  <div className="absolute -inset-1 bg-gradient-to-r from-red-500 to-purple-600 rounded-lg blur opacity-10 group-hover:opacity-20 transition duration-300"></div>
                  <div className="relative p-6 rounded-lg border bg-white border-gray-200 dark:bg-gray-800/50 dark:border-gray-700">
                    <div className="flex items-start mb-4">
                      <div className="p-2 mr-3 rounded-full bg-red-100 text-red-600 dark:bg-red-900/20 dark:text-red-400">
                        <AlertTriangle className="w-5 h-5" />
                      </div>
                      <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                        Error Handling
                      </h2>
                    </div>

                    <div className="lg:pl-14 space-y-4">
                      <motion.div
                        whileHover={{ x: 3 }}
                        className="p-4 rounded-lg bg-gray-50 dark:bg-gray-700/50"
                      >
                        <p className="text-gray-700 dark:text-gray-300">
                          If there are any errors during the API request,
                          appropriate HTTP status codes will be returned along
                          with error messages in the response body.
                        </p>
                      </motion.div>

                      <motion.div
                        whileHover={{ x: 3 }}
                        className="p-4 rounded-lg bg-emerald-100 dark:bg-emerald-900/20"
                      >
                        <p className="text-gray-700 dark:text-gray-300">
                          That&apos;s it! You should now be able to retrieve
                          list of chatbot&apos;s from your account using our
                          API.
                        </p>
                      </motion.div>
                    </div>

                    <div className="mt-6 flex items-center text-sm text-gray-500 dark:text-gray-400">
                      <Info className="w-4 h-4 mr-2" />
                      <span>
                        Remember to handle errors gracefully in your application
                      </span>
                    </div>
                  </div>
                </div>
              </motion.section>
            </div>

            {/* Next Step Card */}
            <motion.div
              whileHover={{ y: -5 }}
              variants={itemVariants}
              className="mb-8 bg-gradient-to-r from-emerald-50 to-emerald-100 dark:from-emerald-900/30 dark:to-emerald-800/30 rounded-xl p-6 text-center border border-emerald-100 dark:border-emerald-800/50 shadow-sm"
            >
              <Link
                href="/docs/getting-setup"
                className="flex items-center justify-center gap-2 text-emerald-700 dark:text-emerald-300 font-medium"
              >
                Home: API Documentation
                <ArrowRight className="w-5 h-5" />
              </Link>
            </motion.div>

            {/* Social Links */}
            <SocialLinks />
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default WebhookAPIPage;
