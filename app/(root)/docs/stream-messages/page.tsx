"use client";

import { motion } from "framer-motion";
import { <PERSON>, <PERSON>R<PERSON>, <PERSON>u, X } from "lucide-react";
import Head from "next/head";
import Link from "next/link";
import { useState } from "react";
import {
  Breadcrumb,
  BreadcrumbItem,
  Bread<PERSON>rumbLink,
  B<PERSON><PERSON><PERSON>b<PERSON>ist,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { DocsSidebar } from "@/components/sidebar/docs-sidebar";
import SocialLinks from "@/components/social-links/social-links";
import { CopyButton } from "@/components/copy-button/copy-button";
import { itemVariants } from "@/variants/variants";
import { CodeBlock } from "@/components/code-block/code-block";
import ErrorResponse from "@/components/error-response/error-response";

const APIStreamingPage = () => {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [activeTab, setActiveTab] =
    useState<keyof typeof codeExamples>("javascript");

  const codeExamples = {
    javascript: `// streamer.js
  const axios = require('axios');
  const { Readable } = require('stream');
  
  const apiKey = '<Your-Secret-Key>';
  const chatId = '<Your Chatbot ID>';
  const apiUrl = 'https://www.chatzuri.com/api/v1/chat';
  
  const messages = [
    { content: '<Your query here>', role: 'user' }
  ];
  
  const authorizationHeader = \`Bearer \${apiKey}\`;
  
  async function readChatbotReply() {
    try {
      const response = await axios.post(apiUrl, {
        messages,
        chatId,
        stream: true,
        temperature: 0
      }, {
        headers: {
          Authorization: authorizationHeader,
          'Content-Type': 'application/json'
        },
        responseType: 'stream'
      });
  
      const readable = new Readable({
        read() {}
      });
  
      response.data.on('data', (chunk) => {
        readable.push(chunk);
      });
  
      response.data.on('end', () => {
        readable.push(null);
      });
  
      const decoder = new TextDecoder();
      let done = false;
  
      readable.on('data', (chunk) => {
        const chunkValue = decoder.decode(chunk);
        process.stdout.write(chunkValue);
      });
  
      readable.on('end', () => {
        done = true;
      });
    } catch (error) {
      console.log('Error:', error.message);
    }
  }
  
  readChatbotReply();`,
    python: `# streamer.py
  import requests
  
  api_url = 'https://www.chatzuri.com/api/v1/chat'
  api_key = '<Your-Secret-Key>'
  chat_id = '<Your Chatbot ID>'
  
  messages = [
      { 'content': '<Your query here>', 'role': 'user' }
  ]
  
  authorization_header = f'Bearer {api_key}'
  
  def read_chatbot_reply():
      try:
          headers = {
              'Authorization': authorization_header,
              'Content-Type': 'application/json'
          }
          
          data = {
              'messages': messages,
              'chatId': chat_id,
              'stream': True,
              'temperature': 0
          }
          
          response = requests.post(api_url, json=data, headers=headers, stream=True)
          response.raise_for_status()
          
          decoder = response.iter_content(chunk_size=None)
          for chunk in decoder:
              chunk_value = chunk.decode('utf-8')
              print(chunk_value, end='', flush=True)
          
      except requests.exceptions.RequestException as error:
          print('Error:', error)
  
  read_chatbot_reply()`,
    php: `// streamer.php
  <?php
  
  $apiUrl = 'https://www.chatzuri.com/api/v1/chat';
  $apiKey = '<Your-Secret-Key>';
  $chatId = '<Your Chatbot ID>';
  
  $messages = [
      ['content' => '<Your query here>', 'role' => 'user']
  ];
  
  $options = [
      'http' => [
          'method' => 'POST',
          'header' => [
              'Authorization: Bearer ' . $apiKey,
              'Content-Type: application/json',
          ],
          'content' => json_encode([
              'messages' => $messages,
              'chatId' => $chatId,
              'stream' => true,
              'temperature' => 0
          ])
      ]
  ];
  
  $context = stream_context_create($options);
  $stream = fopen($apiUrl, 'r', false, $context);
  
  if ($stream === false) {
      die('Error opening stream');
  }
  
  while (!feof($stream)) {
      $chunk = fread($stream, 1024);
      echo $chunk;
      flush();
  }
  
  fclose($stream);`,
  } as const;

  return (
    <div className="min-h-screen">
      <Head>
        <title>Stream Mesages | Chatzuri API Docs</title>
      </Head>
      <div className="flex flex-col lg:flex-row">
        {/* Mobile Menu Button */}
        <div className="lg:hidden fixed top-4 right-4 z-50">
          <button
            onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            className="p-2 rounded-lg bg-white cursor-pointer dark:bg-gray-800 shadow-md"
          >
            {mobileMenuOpen ? (
              <X className="w-6 h-6" />
            ) : (
              <Menu className="w-6 h-6" />
            )}
          </button>
        </div>

        {/* Sidebar Navigation*/}
        <DocsSidebar />

        {/* Main Content */}
        <div className="flex-1 lg:ml-72 mt-16 lg:mt-0">
          {/* Toolbar Section */}
          <motion.div
            className="py-7 md:py-10 px-4 sm:px-6 lg:px-8"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
          >
            <div className="flex flex-col gap-4 w-full">
              <div className="lg:mt-10">
                {/* Breadcrumb */}
                <Breadcrumb>
                  <BreadcrumbList>
                    <BreadcrumbItem>
                      <BreadcrumbLink
                        href="/"
                        className="text-emerald-600 dark:text-emerald-400 hover:text-emerald-700 dark:hover:text-emerald-300"
                      >
                        <Home className="inline mr-1 h-4 w-4" />
                        Home
                      </BreadcrumbLink>
                    </BreadcrumbItem>
                    <BreadcrumbSeparator />
                    <BreadcrumbItem>
                      <BreadcrumbLink
                        href="/docs"
                        className="text-gray-700 dark:text-gray-300"
                      >
                        API Docs
                      </BreadcrumbLink>
                    </BreadcrumbItem>
                    <BreadcrumbSeparator />
                    <BreadcrumbItem>
                      <BreadcrumbLink className="text-gray-500 dark:text-gray-400">
                        Stream Messages
                      </BreadcrumbLink>
                    </BreadcrumbItem>
                  </BreadcrumbList>
                </Breadcrumb>
              </div>
            </div>
          </motion.div>

          {/* Content Section */}
          <motion.div
            className="px-4 sm:px-6 lg:px-8 pb-12"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
          >
            <div className="bg-white px-4 py-3 dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden mb-4">
              {/* Page Header */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4 }}
                className="mb-10"
                whileHover={{ scale: 1.01 }}
                variants={itemVariants}
              >
                <motion.h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                  Streaming Messages
                </motion.h1>
                <p className="text-lg text-gray-600 dark:text-gray-300 font-medium">
                  Template for messaging a bot with streaming using the provided
                  API calls.
                </p>
                <div className="my-6 h-px bg-gradient-to-r from-transparent via-emerald-500 to-transparent dark:via-emerald-400" />
              </motion.div>

              <motion.div whileHover={{ scale: 1.01 }} variants={itemVariants}>
                <div className="mb-8">
                  <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4">
                    Implementing Streaming Functionality
                  </h2>
                  <p className="text-gray-600 dark:text-gray-300 mb-4">
                    The following code can be used to output the response of the
                    Chatzuri API in a stream format, giving the appearance of
                    natural, hand typed text.
                  </p>
                  <p className="text-gray-600 dark:text-gray-300">
                    Replace{" "}
                    <code className="bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded-md text-emerald-600 dark:text-emerald-400">
                      {"<Your-Secret-Key>"}
                    </code>{" "}
                    and{" "}
                    <code className="bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded-md text-emerald-600 dark:text-emerald-400">
                      {"<Your Chatbot ID>"}
                    </code>{" "}
                    before implementation.
                  </p>
                </div>
              </motion.div>

              {/* Code Tabs */}
              <motion.div
                className="mb-10"
                whileHover={{ scale: 1.01 }}
                variants={itemVariants}
              >
                <div className="flex overflow-x-auto border-b border-gray-200 dark:border-gray-700">
                  {(
                    Object.keys(codeExamples) as Array<
                      keyof typeof codeExamples
                    >
                  ).map((lang) => (
                    <button
                      key={lang}
                      onClick={() => setActiveTab(lang)}
                      className={`px-4 py-2 font-medium cursor-pointer text-sm capitalize ${
                        activeTab === lang
                          ? "text-emerald-600 dark:text-emerald-400 border-b-2 border-emerald-600 dark:border-emerald-400"
                          : "text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"
                      }`}
                    >
                      {lang}
                    </button>
                  ))}
                </div>

                {/* Code Block */}
                <div className="relative mt-4 rounded-lg overflow-hidden">
                  <CopyButton
                    textToCopy={codeExamples[activeTab]}
                    buttonId={`lang-${activeTab}-api-url-endpoint`}
                    classNames="absolute top-3 right-2 z-10 bg-gray-800 hover:bg-gray-700 text-white"
                  />
                  <CodeBlock language={activeTab}>
                    {codeExamples[activeTab]}
                  </CodeBlock>
                </div>
              </motion.div>

              <ErrorResponse/>
            </div>

            {/* Next Step Card */}
            <motion.div
              whileHover={{ y: -5 }}
              variants={itemVariants}
              className="mb-8 bg-gradient-to-r from-emerald-50 to-emerald-100 dark:from-emerald-900/30 dark:to-emerald-800/30 rounded-xl p-6 text-center border border-emerald-100 dark:border-emerald-800/50 shadow-sm"
            >
              <Link
                href="/docs/update-chatbot-settings"
                className="flex items-center justify-center gap-2 text-emerald-700 dark:text-emerald-300 font-medium"
              >
                Next:: Update Chatbot Settings
                <ArrowRight className="w-5 h-5" />
              </Link>
            </motion.div>

            {/* Social Links */}
            <SocialLinks />
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default APIStreamingPage;
