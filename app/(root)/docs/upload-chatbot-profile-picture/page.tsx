"use client";

import { AnimatePresence, motion } from "framer-motion";
import {
  <PERSON>,
  ArrowRight,
  Menu,
  X,
  Shield,
  Info,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ff,
  Eye,
} from "lucide-react";
import Head from "next/head";
import Link from "next/link";
import { useState } from "react";
import {
  B<PERSON>crumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { DocsSidebar } from "@/components/sidebar/docs-sidebar";
import SocialLinks from "@/components/social-links/social-links";
import { CopyButton } from "@/components/copy-button/copy-button";
import { itemVariants } from "@/variants/variants";
import { CodeBlock } from "@/components/code-block/code-block";

type CodeLanguage = "javascript" | "python" | "php" | "shell" | "http";

const UploadChatbotProfilePicturePage = () => {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [activeTab, setActiveTab] = useState<CodeLanguage>("javascript");
  const [showApiKey, setShowApiKey] = useState(false);

  const codeExamples = {
    javascript: `const form = new FormData();
form.append('chatbotId', '[Your Chatbot ID]');
form.append('profilePictureFile', fileInput.files[0]);

const options = {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer YOUR_API_KEY',
    'accept': 'application/json'
  },
  body: form
};

fetch('https://www.chatzuri.com/api/v1/upload-chatbot-profile-picture', options)
  .then(response => response.json())
  .then(data => console.log(data))
  .catch(err => console.error(err));`,
    python: `import requests

url = "https://www.chatzuri.com/api/v1/upload-chatbot-profile-picture"
headers = {
    "Authorization": "Bearer YOUR_API_KEY",
    "accept": "application/json"
}
files = {
    "chatbotId": (None, "[Your Chatbot ID]"),
    "profilePictureFile": ("profile.png", open("profile.png", "rb"), "image/png")
}

response = requests.post(url, headers=headers, files=files)
print(response.json())`,
    php: `<?php
$url = 'https://www.chatzuri.com/api/v1/upload-chatbot-profile-picture';
$headers = [
    'Authorization: Bearer YOUR_API_KEY',
    'accept: application/json'
];
$data = [
    'chatbotId' => '[Your Chatbot ID]',
    'profilePictureFile' => new CURLFile('path/to/profile.png')
];

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_POST, 1);
curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

$response = curl_exec($ch);
curl_close($ch);
echo $response;
?>`,
    shell: `curl -X POST \\
  -H "Authorization: Bearer YOUR_API_KEY" \\
  -H "accept: application/json" \\
  -F "chatbotId=[Your Chatbot ID]" \\
  -F "profilePictureFile=@/path/to/profile.png" \\
  https://www.chatzuri.com/api/v1/upload-chatbot-profile-picture`,
    http: `POST /api/v1/upload-chatbot-profile-picture HTTP/1.1
Host: www.chatzuri.com
Authorization: Bearer YOUR_API_KEY
accept: application/json
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW

----WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="chatbotId"

[Your Chatbot ID]
----WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="profilePictureFile"; filename="profile.png"
Content-Type: image/png

(binary data)
----WebKitFormBoundary7MA4YWxkTrZu0gW--`,
  };

  const responseExample = `{
  "success": true,
  "message": "Chatbot profile picture uploaded successfully.",
  "profilePictureUrl": "https://cdn.chatzuri.com/profile/your-chatbot-id.png"
}`;

  return (
    <div className="min-h-screen">
      <Head>
        <title> Upload Chatbot Profile Picture | Chatzuri API Docs</title>
      </Head>
      <div className="flex flex-col lg:flex-row">
        {/* Mobile Menu Button */}
        <div className="lg:hidden fixed top-4 right-4 z-50">
          <button
            onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            className="p-2 rounded-lg bg-white cursor-pointer dark:bg-gray-800 shadow-md"
          >
            {mobileMenuOpen ? (
              <X className="w-6 h-6" />
            ) : (
              <Menu className="w-6 h-6" />
            )}
          </button>
        </div>

        {/* Sidebar Navigation*/}
        <DocsSidebar />

        {/* Main Content */}
        <div className="flex-1 lg:ml-72 mt-16 lg:mt-0">
          {/* Toolbar Section */}
          <motion.div
            className="py-7 md:py-10 px-4 sm:px-6 lg:px-8"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
          >
            <div className="flex flex-col gap-4 w-full">
              <div className="lg:mt-10">
                {/* Breadcrumb */}
                <Breadcrumb>
                  <BreadcrumbList>
                    <BreadcrumbItem>
                      <BreadcrumbLink
                        href="/"
                        className="text-emerald-600 dark:text-emerald-400 hover:text-emerald-700 dark:hover:text-emerald-300"
                      >
                        <Home className="inline mr-1 h-4 w-4" />
                        Home
                      </BreadcrumbLink>
                    </BreadcrumbItem>
                    <BreadcrumbSeparator />
                    <BreadcrumbItem>
                      <BreadcrumbLink
                        href="/docs"
                        className="text-gray-700 dark:text-gray-300"
                      >
                        API Docs
                      </BreadcrumbLink>
                    </BreadcrumbItem>
                    <BreadcrumbSeparator />
                    <BreadcrumbItem>
                      <BreadcrumbLink className="text-gray-500 dark:text-gray-400">
                        Upload Chatbot Profile Picture
                      </BreadcrumbLink>
                    </BreadcrumbItem>
                  </BreadcrumbList>
                </Breadcrumb>
              </div>
            </div>
          </motion.div>

          {/* Content Section */}
          <motion.div
            className="px-4 sm:px-6 lg:px-8 pb-12"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
          >
            <div className="bg-white px-4 py-3 dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden mb-4">
              {/* Page Header */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4 }}
                variants={itemVariants}
                whileHover={{ scale: 1.01 }}
                className="mb-10"
              >
                <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                  Upload Chatbot Profile Picture
                </h1>
                <p className="text-lg text-gray-600 dark:text-gray-300 font-medium">
                  Update the profile picture for your chatbot through our API
                </p>
                <div className="my-6 h-px bg-gradient-to-r from-transparent via-emerald-500 to-transparent dark:via-emerald-400" />
              </motion.div>

              {/* Introduction */}
              <motion.div
                variants={itemVariants}
                whileHover={{ scale: 1.01 }}
                initial="hidden"
                animate="visible"
                className="mb-8"
              >
                <p className="text-gray-700 dark:text-gray-300 mb-6">
                  The Upload Chatbot Profile Picture API endpoint allows you to
                  update the profile picture associated with a specific chatbot.
                </p>
              </motion.div>

              {/* API Key Section */}
              <motion.div
                variants={itemVariants}
                className="mb-8"
                whileHover={{ scale: 1.01 }}
              >
                <div className="flex items-center mb-4">
                  <Shield className="w-5 h-5 mr-2 text-emerald-600 dark:text-emerald-400" />
                  <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                    API Authentication
                  </h2>
                </div>
                <div className="p-4 rounded-lg bg-gray-100 dark:bg-gray-700 mb-4">
                  <div className="flex items-center justify-between mb-2">
                    <CodeBlock language="http" showLineNumbers wrapLines>
                      POST
                      https://www.chatzuri.com/api/v1/upload-chatbot-profile-picture
                    </CodeBlock>
                    <CopyButton
                      textToCopy="POST
                      https://www.chatzuri.com/api/v1/upload-chatbot-profile-picture"
                      buttonId={`code-blocks-1256jdf3tg-${activeTab}`}
                    />
                  </div>
                </div>
                {/* API Key Reveal */}
                <div className="relative group cursor-pointer">
                  <div
                    className={`
                    p-3 pr-12 rounded-lg flex items-center 
                    transition-all duration-300
                    ${
                      showApiKey
                        ? "bg-emerald-50 dark:bg-emerald-900/30 border border-emerald-200 dark:border-emerald-800"
                        : "bg-gray-100 dark:bg-gray-800 border border-gray-200 dark:border-gray-700"
                    }
                    hover:shadow-sm hover:border-emerald-300 dark:hover:border-emerald-600
                  `}
                  >
                    <span
                      className={`
                      font-mono text-sm 
                      ${
                        showApiKey
                          ? "text-emerald-600 dark:text-emerald-400"
                          : "text-gray-600 dark:text-gray-300"
                      }
                      transition-all duration-300
                    `}
                    >
                      {showApiKey
                        ? "Bearer sk_test_1234567890abcdef"
                        : "Bearer ****************"}
                    </span>

                    <button
                      onClick={() => setShowApiKey(!showApiKey)}
                      className={`
                        absolute right-3 p-2 cursor-pointer rounded-full 
                        transition-all duration-300
                        ${
                          showApiKey
                            ? "text-emerald-600 dark:text-emerald-400 hover:bg-emerald-100 dark:hover:bg-emerald-800/50"
                            : "text-gray-500 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-700"
                        }
                        hover:scale-110 active:scale-95
                      `}
                      aria-label={showApiKey ? "Hide API key" : "Show API key"}
                    >
                      {showApiKey ? (
                        <EyeOff className="w-5 h-5" />
                      ) : (
                        <Eye className="w-5 h-5" />
                      )}
                    </button>

                    {showApiKey && (
                      <motion.div
                        initial={{ opacity: 0, y: -5 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="absolute -top-2 -right-2"
                      >
                        <span className="px-2 py-1 text-xs font-medium rounded-full bg-emerald-500 text-white shadow-sm flex items-center">
                          <Shield className="w-3 h-3 mr-1" />
                          Visible
                        </span>
                      </motion.div>
                    )}
                  </div>
                </div>
              </motion.div>

              {/* Request Headers Section */}
              <motion.section
                variants={itemVariants}
                className="mb-8"
                whileHover={{ scale: 1.01 }}
              >
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                  Request Headers
                </h2>
                <div className="mb-4 text-gray-700 dark:text-gray-300">
                  The API request must include the following headers:
                </div>

                <ul className="space-y-4 pl-4" role="list">
                  <motion.li
                    whileHover={{ x: 5 }}
                    className="flex items-start p-3 rounded-lg bg-gray-50 dark:bg-gray-700/50 border border-gray-100 dark:border-gray-700"
                  >
                    <Shield className="w-4 h-4 mt-1 mr-3 flex-shrink-0 text-emerald-600 dark:text-emerald-400" />
                    <div>
                      <div className="font-medium text-gray-900 dark:text-gray-100 mb-1">
                        <code className="font-mono text-emerald-600 dark:text-emerald-400">
                          Authorization: Bearer {"<Your-Secret-Key>"}
                        </code>
                      </div>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        The secret key for authenticating the API request. Keep
                        this secure.
                      </p>
                    </div>
                  </motion.li>

                  <motion.li
                    whileHover={{ x: 5 }}
                    className="flex items-start p-3 rounded-lg bg-gray-50 dark:bg-gray-700/50 border border-gray-100 dark:border-gray-700"
                  >
                    <Shield className="w-4 h-4 mt-1 mr-3 flex-shrink-0 text-emerald-600 dark:text-emerald-400" />
                    <div>
                      <div className="font-medium text-gray-900 dark:text-gray-100 mb-1">
                        <code className="font-mono text-emerald-600 dark:text-emerald-400">
                          Content-Type: multipart/form-data
                        </code>
                      </div>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        Required header for file upload operations.
                      </p>
                    </div>
                  </motion.li>
                </ul>
              </motion.section>

              {/* Request Parameters Section */}
              <motion.section
                variants={itemVariants}
                className="mb-8"
                whileHover={{ scale: 1.01 }}
              >
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                  Request Parameters
                </h2>
                <div className="mb-4 text-gray-700 dark:text-gray-300">
                  The request should include the following parameters:
                </div>

                <ul className="space-y-4 pl-4" role="list">
                  <motion.li
                    whileHover={{ x: 5 }}
                    className="flex items-start p-3 rounded-lg bg-gray-50 dark:bg-gray-700/50 border border-gray-100 dark:border-gray-700"
                  >
                    <Shield className="w-4 h-4 mt-1 mr-3 flex-shrink-0 text-emerald-600 dark:text-emerald-400" />
                    <div>
                      <div className="font-medium text-gray-900 dark:text-gray-100 mb-1">
                        <code className="font-mono text-emerald-600 dark:text-emerald-400">
                          chatbotId
                        </code>
                        <span className="text-sm text-gray-500 dark:text-gray-400 ml-2">
                          (string, required)
                        </span>
                      </div>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        A unique identifier for the chatbot you want to update.
                      </p>
                    </div>
                  </motion.li>

                  <motion.li
                    whileHover={{ x: 5 }}
                    className="flex items-start p-3 rounded-lg bg-gray-50 dark:bg-gray-700/50 border border-gray-100 dark:border-gray-700"
                  >
                    <Shield className="w-4 h-4 mt-1 mr-3 flex-shrink-0 text-emerald-600 dark:text-emerald-400" />
                    <div>
                      <div className="font-medium text-gray-900 dark:text-gray-100 mb-1">
                        <code className="font-mono text-emerald-600 dark:text-emerald-400">
                          profilePictureFile
                        </code>
                        <span className="text-sm text-gray-500 dark:text-gray-400 ml-2">
                          (file, required)
                        </span>
                      </div>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        Square image file (PNG/JPG) under 1MB to use as the
                        profile picture.
                      </p>
                      <div className="mt-2 flex items-center text-xs text-gray-500 dark:text-gray-400">
                        <Info className="w-3 h-3 mr-1" />
                        Recommended dimensions: 512×512 pixels
                      </div>
                    </div>
                  </motion.li>
                </ul>
              </motion.section>

              {/* Code Examples Section */}
              <motion.section
                variants={itemVariants}
                className="mb-8"
                whileHover={{ scale: 1.01 }}
              >
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                  Example Request
                </h2>

                <div className="flex border-b border-gray-200 dark:border-gray-700 mb-4">
                  {(
                    [
                      "javascript",
                      "python",
                      "php",
                      "shell",
                      "http",
                    ] as CodeLanguage[]
                  ).map((lang) => (
                    <button
                      key={lang}
                      onClick={() => setActiveTab(lang)}
                      className={`px-4 cursor-pointer py-2 font-medium ${
                        activeTab === lang
                          ? "text-emerald-600 dark:text-emerald-400 border-b-2 border-emerald-500"
                          : "text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"
                      }`}
                    >
                      {lang.charAt(0).toUpperCase() + lang.slice(1)}
                    </button>
                  ))}
                </div>

                <div className="p-0">
                  <AnimatePresence mode="wait">
                    <motion.div
                      key={activeTab}
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      exit={{ opacity: 0 }}
                      className="relative"
                      transition={{ duration: 0.2 }}
                    >
                      <CodeBlock language={activeTab} showLineNumbers wrapLines>
                        {codeExamples[activeTab]}
                      </CodeBlock>
                      <CopyButton
                        textToCopy={codeExamples[activeTab]}
                        buttonId={`code-blocks-1tr4&67hk5tg-${activeTab}`}
                        classNames="absolute top-2 right-2"
                      />
                    </motion.div>
                  </AnimatePresence>
                </div>
              </motion.section>

              {/* Response Section */}
              <motion.section
                variants={itemVariants}
                className="mb-8"
                whileHover={{ scale: 1.01 }}
              >
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                  Response
                </h2>

                <div className="mb-4 text-gray-700 dark:text-gray-300">
                  The API response codes:
                </div>

                <ul className="space-y-3 mb-6">
                  {[
                    {
                      code: "200",
                      description:
                        "Success - Profile picture uploaded successfully",
                    },
                    {
                      code: "400",
                      description:
                        "Bad Request - Missing or invalid parameters",
                    },
                    {
                      code: "401",
                      description: "Unauthorized - Invalid or missing API key",
                    },
                    {
                      code: "404",
                      description: "Not Found - Chatbot not found",
                    },
                    {
                      code: "413",
                      description:
                        "Payload Too Large - Image file exceeds 1MB limit",
                    },
                    {
                      code: "415",
                      description:
                        "Unsupported Media Type - Invalid image format",
                    },
                    {
                      code: "500",
                      description: "Server Error - Internal server error",
                    },
                  ].map((item) => (
                    <li key={item.code} className="flex items-start">
                      <span
                        className={`inline-flex items-center justify-center mr-2 mt-1 w-8 px-2 py-1 rounded text-xs font-mono ${
                          item.code === "200"
                            ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300"
                            : item.code === "400" || item.code === "404"
                            ? "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300"
                            : "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300"
                        }`}
                      >
                        {item.code}
                      </span>
                      <div className="text-gray-700 dark:text-gray-300">
                        {item.description}
                      </div>
                    </li>
                  ))}
                </ul>

                <div className="mb-4 text-gray-700 dark:text-gray-300">
                  Example successful response:
                </div>

                <CodeBlock language="json">{responseExample}</CodeBlock>
              </motion.section>

              {/* Error Handling Section */}
              <motion.section
                variants={itemVariants}
                className="mb-8"
                whileHover={{ scale: 1.01 }}
              >
                <div className="relative group">
                  <div className="absolute -inset-1 bg-gradient-to-r from-red-500 to-purple-600 rounded-lg blur opacity-10 group-hover:opacity-20 transition duration-300"></div>
                  <div className="relative p-6 rounded-lg border bg-white border-gray-200 dark:bg-gray-800/50 dark:border-gray-700">
                    <div className="flex items-start mb-4">
                      <div className="p-2 mr-3 rounded-full bg-red-100 text-red-600 dark:bg-red-900/20 dark:text-red-400">
                        <AlertTriangle className="w-5 h-5" />
                      </div>
                      <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                        Error Handling
                      </h2>
                    </div>

                    <div className="lg:pl-14 space-y-4">
                      <motion.div
                        whileHover={{ x: 3 }}
                        className="p-4 rounded-lg bg-gray-50 dark:bg-gray-700/50"
                      >
                        <p className="text-gray-700 dark:text-gray-300">
                          If there are any errors during the API request,
                          appropriate HTTP status codes will be returned along
                          with error messages in the response body.
                        </p>
                      </motion.div>

                      <motion.div
                        whileHover={{ x: 3 }}
                        className="p-4 rounded-lg bg-emerald-100 dark:bg-emerald-900/20"
                      >
                        <p className="text-gray-700 dark:text-gray-300">
                          That&apos;s it! You should now be able to upload 
                          chatbot&apos;s profile picture using our API.
                        </p>
                      </motion.div>
                    </div>

                    <div className="mt-6 flex items-center text-sm text-gray-500 dark:text-gray-400">
                      <Info className="w-4 h-4 mr-2" />
                      <span>
                        Remember to handle errors gracefully in your application
                      </span>
                    </div>
                  </div>
                </div>
              </motion.section>
            </div>

            {/* Next Step Card */}
            <motion.div
              whileHover={{ y: -5 }}
              variants={itemVariants}
              className="mb-8 bg-gradient-to-r from-emerald-50 to-emerald-100 dark:from-emerald-900/30 dark:to-emerald-800/30 rounded-xl p-6 text-center border border-emerald-100 dark:border-emerald-800/50 shadow-sm"
            >
              <Link
                href="/docs/get-chatbots"
                className="flex items-center justify-center gap-2 text-emerald-700 dark:text-emerald-300 font-medium"
              >
                Next::Get Chatbots
                <ArrowRight className="w-5 h-5" />
              </Link>
            </motion.div>

            {/* Social Links */}
            <SocialLinks />
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default UploadChatbotProfilePicturePage;