"use client";

import { motion } from "framer-motion";
import { <PERSON>, <PERSON>R<PERSON>, <PERSON>u, <PERSON> } from "lucide-react";
import Head from "next/head";
import Link from "next/link";
import { useState } from "react";
import {
  Breadcrumb,
  BreadcrumbItem,
  Bread<PERSON>rumbLink,
  B<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { DocsSidebar } from "@/components/sidebar/docs-sidebar";
import SocialLinks from "@/components/social-links/social-links";
import { CopyButton } from "@/components/copy-button/copy-button";
import { itemVariants } from "@/variants/variants";
import { CodeBlock } from "@/components/code-block/code-block";
import ErrorResponse from "@/components/error-response/error-response";

const UpdateChatbotPage = () => {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [activeTab, setActiveTab] =
    useState<keyof typeof codeExamples>("javascript");

  const codeExamples = {
    javascript: `const response = await fetch('https://www.chatzuri.com/api/v1/chat', {
  method: 'POST',
  headers: {
    Authorization: 'Bearer <API-Key>'
  },
  body: JSON.stringify({
    messages: [
      { content: 'How can I help you?', role: 'assistant' },
      { content: 'What is chatzuri?', role: 'user' }
    ],
    chatbotId: '<Chatbot-ID>',
    stream: false,
    model: 'gpt-3.5-turbo',
    temperature: 0
  })
});

if (!response.ok) {
  const errorData = await response.json();
  throw Error(errorData.message);
}
const data = await response.json(); 
console.log(data); // { "text": "..."}`,
    python: `import requests
import json

url = 'https://www.chatzuri.com/api/v1/chat'
headers = {
    'Authorization': 'Bearer <API-KEY>',
    'Content-Type': 'application/json'
}
data = {
    "messages": [
        {"content": "How can I help you?", "role": "assistant"},
        {"content": "What is chatzuri?", "role": "user"}
    ],
    "chatbotId": "<Chatbot-ID>",
    "stream": False,
    "temperature": 0
}

response = requests.post(url, headers=headers, data=json.dumps(data))
json_data = response.json()

if response.status_code == 200:
  print("response:", json_data['text'])
else:
  print('Error:' + json_data['message'])`,
    shell: `curl https://www.chatzuri.com/api/v1/chat \\
  -H 'Authorization: Bearer <Your-Secret-Key>' \\
  -d '{
  "messages": [
    {"content": "How can I help you?", "role": "assistant"},
    {"content": "What is chatzuri?", "role": "user"}
  ],
  "chatbotId": "<Your Chatbot ID>",
  "stream": false,
  "temperature": 0
}'`,
    http: `POST /api/v1/chat HTTP/1.1
Host: www.chatzuri.com
Authorization: Bearer <Your-Secret-Key>
Content-Type: application/json

{
  "messages": [
    {"content": "How can I help you?", "role": "assistant"},
    {"content": "What is chatzuri?", "role": "user"}
  ],
  "chatbotId": "<Your Chatbot ID>",
  "stream": false,
  "temperature": 0
}`,
  };

  return (
    <div className="min-h-screen ">
      <Head>
        <title>Update Chatbot | Chatzuri API Docs</title>
      </Head>
      <div className="flex flex-col lg:flex-row">
        {/* Mobile Menu Button */}
        <div className="lg:hidden fixed top-4 right-4 z-50">
          <button
            onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            className="p-2 rounded-lg bg-white cursor-pointer dark:bg-gray-800 shadow-md"
          >
            {mobileMenuOpen ? (
              <X className="w-6 h-6" />
            ) : (
              <Menu className="w-6 h-6" />
            )}
          </button>
        </div>

        {/* Sidebar Navigation*/}
        <DocsSidebar />

        {/* Main Content */}
        <div className="flex-1 lg:ml-72 mt-16 lg:mt-0">
          {/* Toolbar Section */}
          <motion.div
            className="py-7 md:py-10 px-4 sm:px-6 lg:px-8"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
          >
            <div className="flex flex-col gap-4 w-full">
              <div className="lg:mt-10">
                {/* Breadcrumb */}
                <Breadcrumb>
                  <BreadcrumbList>
                    <BreadcrumbItem>
                      <BreadcrumbLink
                        href="/"
                        className="text-emerald-600 dark:text-emerald-400 hover:text-emerald-700 dark:hover:text-emerald-300"
                      >
                        <Home className="inline mr-1 h-4 w-4" />
                        Home
                      </BreadcrumbLink>
                    </BreadcrumbItem>
                    <BreadcrumbSeparator />
                    <BreadcrumbItem>
                      <BreadcrumbLink
                        href="/docs"
                        className="text-gray-700 dark:text-gray-300"
                      >
                        API Docs
                      </BreadcrumbLink>
                    </BreadcrumbItem>
                    <BreadcrumbSeparator />
                    <BreadcrumbItem>
                      <BreadcrumbLink className="text-gray-500 dark:text-gray-400">
                        Update Chatbot
                      </BreadcrumbLink>
                    </BreadcrumbItem>
                  </BreadcrumbList>
                </Breadcrumb>
              </div>
            </div>
          </motion.div>

          {/* Content Section */}
          <motion.div
            className="px-4 sm:px-6 lg:px-8 pb-12"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
          >
            <div className="bg-white mb-3 dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden">
              <div className="p-6 sm:p-10">
                {/* Main Content */}
                <div className="mb-14">
                  <motion.div className="mb-8" variants={itemVariants}>
                    <h1 className="text-2xl md:text-3xl font-bold text-gray-900 dark:text-white mb-4">
                      Update a Chatbot
                    </h1>
                    <p className="text-lg text-gray-600 dark:text-gray-300 font-semibold">
                      This page helps you update a Chatbot.
                    </p>
                    <div className="my-6 h-px bg-gradient-to-r from-transparent via-emerald-500 to-transparent dark:via-emerald-400" />
                  </motion.div>

                  {/* API Description */}
                  <motion.div
                    className="mb-6 pb-6 mt-8"
                    whileHover={{ scale: 1.01 }}
                    variants={itemVariants}
                  >
                    <p className="text-gray-700 dark:text-gray-300">
                      The Chatbot Data Update API allows you to update the data
                      for a chatbot by providing the chatbot ID, new name, and
                      source text for text content. This API can be used to
                      replace the existing data of a chatbot with new
                      information.
                    </p>
                  </motion.div>

                  {/* Endpoint Section */}
                  <motion.div
                    className="mb-6 pb-6 mt-8"
                    whileHover={{ scale: 1.01 }}
                    variants={itemVariants}
                  >
                    <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
                      Endpoint
                    </h2>
                    <div className="flex items-center gap-2">
                      <CodeBlock language="json">
                        POST https://www.chatzuri.com/api/v1/update-chatbot-data
                      </CodeBlock>

                      <CopyButton
                        textToCopy={`POST https://www.chatzuri.com/api/v1/update-chatbot-data`}
                        buttonId={`endpoint3456-api-url-endpoint`}
                      />
                    </div>
                  </motion.div>

                  {/* Request Headers Section */}
                  <motion.div
                    className="mb-6 pb-6 mt-8"
                    whileHover={{ scale: 1.01 }}
                    variants={itemVariants}
                  >
                    <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
                      Request Headers
                    </h2>
                    <p className="text-gray-700 dark:text-gray-300 mb-4">
                      The API request must include the following headers.
                    </p>
                    <ul className="space-y-4">
                      <li className="flex flex-wrap items-baseline gap-2 rounded-md text-sm bg-gray-100 dark:bg-gray-600 p-2">
                        <div className="flex-1 min-w-0 max-w-[calc(100%-80px)]">
                          <CodeBlock language="json">
                            Authorization: Bearer &lt;Your-Secret-Key&gt;
                          </CodeBlock>
                        </div>
                        <CopyButton
                          textToCopy="Authorization: Bearer <Your-Secret-Key>"
                          buttonId="auth-header"
                        />
                        <span className="w-full text-gray-700 dark:text-gray-300 mt-1 text-xs">
                          - The secret key for authenticating the API request.
                        </span>
                      </li>
                      <li className="flex flex-wrap items-baseline gap-2 rounded-md text-sm bg-gray-100 dark:bg-gray-600 p-2">
                        <div className="flex-1 min-w-0 max-w-[calc(100%-80px)]">
                          <CodeBlock language="json">
                            Content-Type: application/json
                          </CodeBlock>
                        </div>
                        <CopyButton
                          textToCopy="Authorization: Bearer <Your-Secret-Key>"
                          buttonId="auth-header"
                        />
                        <span className="w-full text-gray-700 dark:text-gray-300 mt-1 text-xs">
                          - The content type of the request payload.
                        </span>
                      </li>
                    </ul>
                  </motion.div>

                  {/* Request Body Section */}
                  <motion.div
                    className="mb-6 pb-6 mt-8"
                    whileHover={{ scale: 1.01 }}
                    variants={itemVariants}
                  >
                    <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
                      Request Body
                    </h2>
                    <p className="text-gray-700 dark:text-gray-300 mb-4">
                      The request body should contain the following parameters:
                    </p>
                    <ul className="space-y-4">
                      <li className="flex  gap-2  items-center text-gray-700 dark:text-gray-300">
                        <CodeBlock language="json">chatbotId</CodeBlock> -
                        (required): A unique identifier for the chatbot.
                      </li>
                      <li className="flex gap-2 items-center text-gray-700 dark:text-gray-300">
                        <CodeBlock language="json">chatbotName</CodeBlock>{" "}
                        -(required): The new name for the chatbot.
                      </li>
                      <li className=" flex gap-2  items-center text-gray-700 dark:text-gray-300">
                        <CodeBlock language="json">sourceText</CodeBlock> -
                        (optional): The new source text to update the chatbot.
                      </li>
                    </ul>
                  </motion.div>

                  {/* Code Examples Section */}
                  <motion.div
                    className="mb-6 pb-6 mt-8"
                    whileHover={{ scale: 1.01 }}
                    variants={itemVariants}
                  >
                    <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-4">
                      Example Request
                    </h2>

                    {/* Language Tabs */}
                    <div className="flex border-b border-gray-200 dark:border-gray-700 mb-4">
                      {(["javascript", "python", "shell", "http"] as const).map(
                        (lang) => (
                          <button
                            key={lang}
                            onClick={() => setActiveTab(lang)}
                            className={`px-4 py-2 font-medium cursor-pointer ${
                              activeTab === lang
                                ? "text-emerald-600 dark:text-emerald-400 border-b-2 border-emerald-500"
                                : "text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"
                            }`}
                          >
                            {lang.charAt(0).toUpperCase() + lang.slice(1)}
                          </button>
                        )
                      )}
                    </div>

                    {/* Code Block */}
                    <div className="relative">
                      <CodeBlock
                        language={activeTab === "http" ? "http" : activeTab}
                      >
                        {codeExamples[activeTab]}
                      </CodeBlock>
                      <CopyButton
                        textToCopy={codeExamples[activeTab]}
                        buttonId={`codeblocks-${activeTab}-api-url-endpoint`}
                        classNames="absolute top-2 right-2"
                      />
                    </div>
                  </motion.div>

                  {/* Response Section */}
                  <motion.div
                    className="mb-6 pb-6 mt-8"
                    whileHover={{ scale: 1.01 }}
                    variants={itemVariants}
                  >
                    <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
                      Response
                    </h2>
                    <p className="text-gray-700 dark:text-gray-300 mb-4">
                      The API response will be a JSON object with the following
                      structure:
                    </p>
                    <div className="relative">
                      <CodeBlock language="json">
                        {`{
  "chatbotId": "exampleId-123"
}`}
                      </CodeBlock>
                      <CopyButton
                        textToCopy={`{"chatbotId": "exampleId-123"}`}
                        buttonId={`codeblocks-${activeTab}-api-url-endpoint`}
                        classNames="absolute top-2 right-2"
                      />
                    </div>

                    <p className="text-gray-700 dark:text-gray-300 mt-4">
                      The{" "}
                      <code className="bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">
                        chatbotId
                      </code>{" "}
                      field in the response contains the unique identifier
                      assigned to the updated chatbot.
                    </p>
                  </motion.div>

                  {/* Error Handling Section */}
                  <ErrorResponse />
                </div>
              </div>
            </div>

            {/* Next Step Card */}
            <motion.div
              whileHover={{ y: -5 }}
              variants={itemVariants}
              className="mb-8 bg-gradient-to-r from-emerald-50 to-emerald-100 dark:from-emerald-900/30 dark:to-emerald-800/30 rounded-xl p-6 text-center border border-emerald-100 dark:border-emerald-800/50 shadow-sm"
            >
              <Link
                href="/docs/delete-chatbot"
                className="flex items-center justify-center gap-2 text-emerald-700 dark:text-emerald-300 font-medium"
              >
                Next:: Delete Chatbot
                <ArrowRight className="w-5 h-5" />
              </Link>
            </motion.div>

            {/* Social Links */}
            <SocialLinks />
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default UpdateChatbotPage;
