"use client";

import { motion } from "framer-motion";
import { Home, ArrowR<PERSON>, Menu, X, <PERSON><PERSON><PERSON><PERSON>, Code2 } from "lucide-react";
import Head from "next/head";
import Link from "next/link";
import { useState } from "react";
import {
  Breadcrumb,
  BreadcrumbI<PERSON>,
  BreadcrumbLink,
  Breadcrumb<PERSON>ist,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { DocsSidebar } from "@/components/sidebar/docs-sidebar";
import SocialLinks from "@/components/social-links/social-links";
import { CopyButton } from "@/components/copy-button/copy-button";
import { itemVariants } from "@/variants/variants";
import { CodeBlock } from "@/components/code-block/code-block";
import ErrorResponse from "@/components/error-response/error-response";

const UpdateChatbotSettingsPage = () => {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [activeTab, setActiveTab] =
    useState<keyof typeof codeExamples>("javascript");

  const codeExamples = {
    javascript: `const options = {
      method: 'POST',
      headers: { 
        'Authorization': 'Bearer <Your-Secret-Key>',
        'accept': 'application/json', 
        'content-type': 'application/json' 
      },
      body: JSON.stringify({
        collectCustomerInformation: {
          name: {label: 'Name', active: true},
          email: {label: 'Email', active: true},
          title: 'Let us know how to contact you'
        },
        styles: {
          theme: 'dark',
          userMessageColor: '#3B81F7',
          buttonColor: '#3B81F7',
          displayName: 'Product Hunt',
          autoOpenChatWindowAfter: 4,
          alignChatButton: 'left'
        },
        chatbotId: '[Your ChatbotID]',
        name: 'my Chatbot',
        instructions: 'I want you to act as a document that I am having a conversation with...',
        initialMessages: ['Hi! What can I help you with?'],
        suggestedMessages: ['Hi! What are you?'],
        visibility: 'public',
        onlyAllowOnAddedDomains: true,
        domains: ['example.com'],
        ipLimit: 20,
        ipLimitTimeframe: 240,
        ipLimitMessage: 'Too many messages in a row',
        model: 'gpt-4',
        temp: 0
      })
    };
    
    fetch('https://www.chatzuri.com/api/v1/update-chatbot-settings', options)
      .then(response => response.json())
      .then(response => console.log(response))
      .catch(err => console.error(err));`,
    python: `import requests
    
    url = "https://www.chatzuri.com/api/v1/update-chatbot-settings"
    
    payload = {
        "collectCustomerInformation": { 
            "name": {
                "label": "Name",
                "active": True
            },
            "email": {
                "label": "Email",
                "active": True
            },
            "title": "Let us know how to contact you"
        },
        "styles": {
            "theme": "dark",
            "userMessageColor": "#3B81F7",
            "buttonColor": "#3B81F7",
            "displayName": "Product Hunt",
            "autoOpenChatWindowAfter": 4,
            "alignChatButton": "left"
        },
        "chatbotId": "[Your ChatbotID]",
        "name": "my Chatbot",
        "instructions": "I want you to act as a document that I am having a conversation with...",
        "initialMessages": ["Hi! What can I help you with?"],
        "suggestedMessages": ["Hi! What are you?"],
        "visibility": "private",
        "onlyAllowOnAddedDomains": True,
        "domains": ["example.com"],
        "ipLimit": 20,
        "ipLimitTimeframe": 240,
        "ipLimitMessage": "Too many messages in a row",
        "model": "gpt-4",
        "temp": 0.2
    }
    headers = {
        "Authorization": "Bearer <Your-Secret-Key>",
        "accept": "application/json",
        "content-type": "application/json"
    }
    
    response = requests.post(url, json=payload, headers=headers)
    
    print(response.text)`,
    php: `<?php
    
    $url = 'https://www.chatzuri.com/api/v1/update-chatbot-settings';
    $apiKey = '<Your-Secret-Key>';
    
    $data = [
        'collectCustomerInformation' => [
            'name' => ['label' => 'Name', 'active' => true],
            'email' => ['label' => 'Email', 'active' => true],
            'title' => 'Let us know how to contact you'
        ],
        'styles' => [
            'theme' => 'dark',
            'userMessageColor' => '#3B81F7',
            'buttonColor' => '#3B81F7',
            'displayName' => 'Product Hunt',
            'autoOpenChatWindowAfter' => 4,
            'alignChatButton' => 'left'
        ],
        'chatbotId' => '[Your ChatbotID]',
        'name' => 'my Chatbot',
        'instructions' => 'I want you to act as a document that I am having a conversation with...',
        'initialMessages' => ['Hi! What can I help you with?'],
        'suggestedMessages' => ['Hi! What are you?'],
        'visibility' => 'private',
        'onlyAllowOnAddedDomains' => true,
        'domains' => ['example.com'],
        'ipLimit' => 20,
        'ipLimitTimeframe' => 240,
        'ipLimitMessage' => 'Too many messages in a row',
        'model' => 'gpt-4',
        'temp' => 0.2
    ];
    
    $options = [
        'http' => [
            'method' => 'POST',
            'header' => [
                'Authorization: Bearer ' . $apiKey,
                'Content-Type: application/json',
                'Accept: application/json'
            ],
            'content' => json_encode($data)
        ]
    ];
    
    $context = stream_context_create($options);
    $result = file_get_contents($url, false, $context);
    
    if ($result === FALSE) {
        // Handle error
        die('Error');
    }
    
    var_dump(json_decode($result, true));`,
    shell: `curl --request POST \\
         --url https://www.chatzuri.com/api/v1/update-chatbot-settings \\
         --header 'Authorization: Bearer <Your-Secret-Key>' \\
         --header 'accept: application/json' \\
         --header 'content-type: application/json' \\
         --data '{
      "collectCustomerInformation": {
        "name": {
          "label": "Name",
          "active": true
        },
        "email": {
          "label": "Email",
          "active": true
        },
        "title": "Let us know how to contact you"
      },
      "styles": {
        "theme": "dark",
        "userMessageColor": "#3B81F7",
        "buttonColor": "#3B81F7",
        "displayName": "Product Hunt",
        "autoOpenChatWindowAfter": 4,
        "alignChatButton": "left"
      },
      "chatbotId": "[Your ChatbotID]",
      "name": "my Chatbot",
      "instructions": "I want you to act as a document that I am having a conversation with...",
      "initialMessages": [
        "Hi! What can I help you with?"
      ],
      "suggestedMessages": [
        "Hi! What are you?"
      ],
      "visibility": "private",
      "onlyAllowOnAddedDomains": true,
      "domains": [
        "example.com"
      ],
      "ipLimit": 20,
      "ipLimitTimeframe": 240,
      "ipLimitMessage": "Too many messages in a row",
      "model": "gpt-4",
      "temp": 0.2
    }'`,
    http: `POST /api/v1/update-chatbot-settings HTTP/1.1
    Host: www.chatzuri.com
    Authorization: Bearer <Your-Secret-Key>
    Accept: application/json
    Content-Type: application/json
    Content-Length: 898
    
    {
      "collectCustomerInformation": {
        "name": {
          "label": "Name",
          "active": true
        },
        "email": {
          "label": "Email",
          "active": true
        },
        "title": "Let us know how to contact you"
      },
      "styles": {
        "theme": "dark",
        "userMessageColor": "#3B81F7",
        "buttonColor": "#3B81F7",
        "displayName": "Product Hunt",
        "autoOpenChatWindowAfter": 4,
        "alignChatButton": "left"
      },
      "chatbotId": "[Your ChatbotID]",
      "name": "my Chatbot",
      "instructions": "I want you to act as a document that I am having a conversation with...",
      "initialMessages": ["Hi! What can I help you with?"],
      "suggestedMessages": ["Hi! What are you?"],
      "visibility": "private",
      "onlyAllowOnAddedDomains": true,
      "domains": ["example.com"],
      "ipLimit": 20,
      "ipLimitTimeframe": 240,
      "ipLimitMessage": "Too many messages in a row",
      "model": "gpt-4",
      "temp": 0.2
    }`,
  };

  interface StatusTexts {
    [key: number]: string;
  }

  function getStatusText(code: number): string {
    const statusTexts: StatusTexts = {
      202: "Accepted",
      400: "Bad Request",
      401: "Unauthorized",
      404: "Not Found",
      500: "Internal Server Error",
    };
    return statusTexts[code] || "";
  }
  return (
    <div className="min-h-screen">
      <Head>
        <title>Update Chatbot Settings | Chatzuri API Docs</title>
      </Head>
      <div className="flex flex-col lg:flex-row">
        {/* Mobile Menu Button */}
        <div className="lg:hidden fixed top-4 right-4 z-50">
          <button
            onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            className="p-2 rounded-lg bg-white cursor-pointer dark:bg-gray-800 shadow-md"
          >
            {mobileMenuOpen ? (
              <X className="w-6 h-6" />
            ) : (
              <Menu className="w-6 h-6" />
            )}
          </button>
        </div>

        {/* Sidebar Navigation*/}
        <DocsSidebar />

        {/* Main Content */}
        <div className="flex-1 lg:ml-72 mt-16 lg:mt-0">
          {/* Toolbar Section */}
          <motion.div
            className="py-7 md:py-10 px-4 sm:px-6 lg:px-8"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
          >
            <div className="flex flex-col gap-4 w-full">
              <div className="lg:mt-10">
                {/* Breadcrumb */}
                <Breadcrumb>
                  <BreadcrumbList>
                    <BreadcrumbItem>
                      <BreadcrumbLink
                        href="/"
                        className="text-emerald-600 dark:text-emerald-400 hover:text-emerald-700 dark:hover:text-emerald-300"
                      >
                        <Home className="inline mr-1 h-4 w-4" />
                        Home
                      </BreadcrumbLink>
                    </BreadcrumbItem>
                    <BreadcrumbSeparator />
                    <BreadcrumbItem>
                      <BreadcrumbLink
                        href="/docs"
                        className="text-gray-700 dark:text-gray-300"
                      >
                        API Docs
                      </BreadcrumbLink>
                    </BreadcrumbItem>
                    <BreadcrumbSeparator />
                    <BreadcrumbItem>
                      <BreadcrumbLink className="text-gray-500 dark:text-gray-400">
                        Update Chatbot Settings
                      </BreadcrumbLink>
                    </BreadcrumbItem>
                  </BreadcrumbList>
                </Breadcrumb>
              </div>
            </div>
          </motion.div>

          {/* Content Section */}
          <motion.div
            className="px-4 sm:px-6 lg:px-8 pb-12"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
          >
            <div className="bg-white px-4 py-3 dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden mb-4">
              {/* Page Header */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4 }}
                className="mb-10"
                whileHover={{ scale: 1.01 }}
                variants={itemVariants}
              >
                <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                  Update a Chatbot Settings
                </h1>
                <p className="text-lg text-gray-600 dark:text-gray-300 font-medium">
                  This page helps you update your Chatbot settings.
                </p>
                <div className="my-6 h-px bg-gradient-to-r from-transparent via-emerald-500 to-transparent dark:via-emerald-400" />
              </motion.div>

              <motion.div
                className="mb-8"
                whileHover={{ scale: 1.01 }}
                variants={itemVariants}
              >
                <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4">
                  Update a Chatbot Settings API guide
                </h2>
                <p className="text-gray-600 dark:text-gray-300">
                  The Update Chatbot Settings API allows you to edit the
                  settings of your chatbot, such as its name, base prompt,
                  initial messages, suggested messages, visibility status,
                  domain restrictions, IP limits, AI model, and more.
                </p>
              </motion.div>

              {/* Endpoint Section */}
              <motion.div
                className="mb-8"
                whileHover={{ scale: 1.01 }}
                variants={itemVariants}
              >
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
                  Endpoint
                </h3>
                <div className="flex items-center gap-2">
                  <CodeBlock language="http">
                    POST:https://www.chatzuri.com/api/v1/update-chatbot-settings
                  </CodeBlock>
                  <CopyButton
                    textToCopy="POST https://www.chatzuri.com/api/v1/update-chatbot-settings"
                    buttonId="post-api-url-endpoint"
                    classNames="ml-2"
                  />
                </div>
              </motion.div>

              {/* Request Headers Section */}
              <motion.div
                className="mb-8"
                whileHover={{ scale: 1.01 }}
                variants={itemVariants}
              >
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
                  Request Headers
                </h3>
                <p className="text-gray-600 dark:text-gray-300 mb-4">
                  The API request must include the following headers.
                </p>
                <ul className="space-y-3">
                  <li className="flex items-start gap-3">
                    <span className="inline-flex items-center justify-center h-6 w-6 rounded-full bg-emerald-100 dark:bg-emerald-900/50 text-emerald-600 dark:text-emerald-300 mt-0.5 flex-shrink-0">
                      <ShieldAlert className="w-4 h-4" />
                    </span>
                    <div className="flex-1">
                      <div className="inline-flex items-center gap-2 bg-gray-100 dark:bg-gray-700 px-3 py-1.5 rounded-lg">
                        <span className="text-emerald-600 dark:text-emerald-400 font-mono text-sm">
                          Authorization:
                        </span>
                        <span className="font-mono text-sm">
                          Bearer {"<Your-Secret-Key>"}
                        </span>
                      </div>
                      <span className="ml-2 text-gray-600 dark:text-gray-300">
                        - The secret key for authenticating the API request.
                      </span>
                    </div>
                  </li>

                  <li className="flex items-start gap-3">
                    <span className="inline-flex items-center justify-center h-6 w-6 rounded-full bg-emerald-100 dark:bg-emerald-900/50 text-emerald-600 dark:text-emerald-300 mt-0.5 flex-shrink-0">
                      <ShieldAlert className="w-4 h-4" />
                    </span>
                    <div className="flex-1">
                      <div className="inline-flex items-center gap-2 bg-gray-100 dark:bg-gray-700 px-3 py-1.5 rounded-lg">
                        <span className="text-emerald-600 dark:text-emerald-400 font-mono text-sm">
                          Content-Type:
                        </span>
                        <span className="font-mono text-sm">
                          application/json
                        </span>
                      </div>
                      <span className="ml-2 text-gray-600 dark:text-gray-300">
                        - The content type of the request payload.
                      </span>
                    </div>
                  </li>
                </ul>
              </motion.div>

              {/* Request Body Section */}
              <motion.div
                className="mb-8"
                whileHover={{ scale: 1.01 }}
                variants={itemVariants}
              >
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
                  Request Body
                </h3>
                <p className="text-gray-600 dark:text-gray-300 mb-4">
                  The request body should contain the following parameters:
                </p>

                <div className="space-y-6">
                  <motion.div
                    whileHover={{ scale: 1.01 }}
                    variants={itemVariants}
                    className="bg-gray-50 dark:bg-gray-800/50 p-4 rounded-lg border border-gray-200 dark:border-gray-700"
                  >
                    <h4 className="font-medium text-gray-800 dark:text-gray-200 mb-3 flex items-center gap-2">
                      Required Parameters
                    </h4>
                    <ul className="space-y-3">
                      <li className="flex items-start gap-3">
                        <ShieldAlert className="h-5 w-5 text-emerald-600 dark:text-emerald-400 mt-0.5 flex-shrink-0" />
                        <div>
                          <span className="font-mono bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded-md text-sm">
                            chatbotId
                          </span>
                          <span className="ml-2 text-gray-600 dark:text-gray-300">
                            - (string) Refers to the ID of the chatbot you want
                            to interact with.
                          </span>
                        </div>
                      </li>
                    </ul>
                  </motion.div>

                  <motion.div
                    whileHover={{ scale: 1.01 }}
                    variants={itemVariants}
                    className="bg-gray-50 dark:bg-gray-800/50 p-4 rounded-lg border border-gray-200 dark:border-gray-700"
                  >
                    <h4 className="font-medium text-gray-800 dark:text-gray-200 mb-3 flex items-center gap-2">
                      Optional Parameters
                    </h4>
                    <ul className="space-y-3">
                      {[
                        {
                          code: "name",
                          desc: "(string) The new name for the chatbot.",
                        },
                        {
                          code: "instructions",
                          desc: "(string) The base prompt; instructions for how the chatbot should behave.",
                        },
                        {
                          code: "initialMessages",
                          desc: "(array of strings) An array of initial greeting messages.",
                        },
                        {
                          code: "suggestedMessages",
                          desc: "(array of strings) An array of suggested messages.",
                        },
                        {
                          code: "visibility",
                          desc: "(string) Either 'private' or 'public'.",
                        },
                        {
                          code: "onlyAllowOnAddedDomains",
                          desc: "(boolean) Restrict to specific domains.",
                        },
                        {
                          code: "domains",
                          desc: "(array of strings) Allowed domains.",
                        },
                        {
                          code: "ipLimit",
                          desc: "(integer) Message limit per timeframe.",
                        },
                        {
                          code: "ipLimitTimeframe",
                          desc: "(integer) Timeframe in seconds.",
                        },
                        {
                          code: "ipLimitMessage",
                          desc: "(string) Message when limit is exceeded.",
                        },
                        {
                          code: "model",
                          desc: "(string) AI model ('gpt-4' or 'gpt-3.5-turbo').",
                        },
                        {
                          code: "temp",
                          desc: "(number) Temperature parameter [0, 1].",
                        },
                      ].map((item, index) => (
                        <li key={index} className="flex items-start gap-3">
                          <ShieldAlert className="h-5 w-5 text-emerald-600 dark:text-emerald-400 mt-0.5 flex-shrink-0" />
                          <div>
                            <span className="font-mono bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded-md text-sm">
                              {item.code}
                            </span>
                            <span className="ml-2 text-gray-600 dark:text-gray-300">
                              - {item.desc}
                            </span>
                          </div>
                        </li>
                      ))}
                    </ul>
                  </motion.div>

                  <motion.div
                    whileHover={{ scale: 1.01 }}
                    variants={itemVariants}
                    className="bg-gray-50 dark:bg-gray-800/50 p-4 rounded-lg border border-gray-200 dark:border-gray-700"
                  >
                    <h4 className="font-medium text-gray-800 dark:text-gray-200 mb-3 flex items-center gap-2">
                      collectCustomerInformation Object
                    </h4>
                    <ul className="space-y-3">
                      <li className="flex items-start gap-3">
                        <ShieldAlert className="h-5 w-5 text-emerald-600 dark:text-emerald-400 mt-0.5 flex-shrink-0" />
                        <div>
                          <span className="font-mono bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded-md text-sm">
                            title
                          </span>
                          <span className="ml-2 text-gray-600 dark:text-gray-300">
                            - (string) Title for contact details section.
                          </span>
                        </div>
                      </li>
                      <li className="flex items-start gap-3">
                        <ShieldAlert className="h-5 w-5 text-emerald-600 dark:text-emerald-400 mt-0.5 flex-shrink-0" />
                        <div>
                          <span className="font-mono bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded-md text-sm">
                            name
                          </span>
                          <span className="ml-2 text-gray-600 dark:text-gray-300">
                            - (object) Customer name collection settings.
                          </span>
                          <ul className="mt-2 ml-6 space-y-2">
                            <li className="flex items-start gap-2">
                              <ShieldAlert className="h-4 w-4 text-emerald-600 dark:text-emerald-400 mt-0.5 flex-shrink-0" />
                              <div>
                                <span className="font-mono bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded-md text-sm">
                                  label
                                </span>
                                <span className="ml-2 text-gray-600 dark:text-gray-300">
                                  - Field label
                                </span>
                              </div>
                            </li>
                            <li className="flex items-start gap-2">
                              <ShieldAlert className="h-4 w-4 text-emerald-600 dark:text-emerald-400 mt-0.5 flex-shrink-0" />
                              <div>
                                <span className="font-mono bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded-md text-sm">
                                  active
                                </span>
                                <span className="ml-2 text-gray-600 dark:text-gray-300">
                                  - Whether to collect
                                </span>
                              </div>
                            </li>
                          </ul>
                        </div>
                      </li>
                      {/* Similar structure for email and phone */}
                    </ul>
                  </motion.div>

                  <motion.div
                    whileHover={{ scale: 1.01 }}
                    variants={itemVariants}
                    className="p-4 rounded-lg border border-gray-200 dark:border-gray-700"
                  >
                    <h4 className="font-medium text-gray-800 dark:text-gray-200 mb-3 flex items-center gap-2">
                      Styles Object
                    </h4>
                    <ul className="space-y-3">
                      {[
                        { code: "theme", desc: "(string) 'dark' or 'light'." },
                        {
                          code: "userMessageColor",
                          desc: "(string) Hex color.",
                        },
                        { code: "buttonColor", desc: "(string) Hex color." },
                        { code: "displayName", desc: "(string) Branded name." },
                        {
                          code: "autoOpenChatWindowAfter",
                          desc: "(integer) Seconds.",
                        },
                        {
                          code: "alignChatButton",
                          desc: "(string) 'left' or 'right'.",
                        },
                      ].map((item, index) => (
                        <li key={index} className="flex items-start gap-3">
                          <ShieldAlert className="h-5 w-5 text-emerald-600 dark:text-emerald-400 mt-0.5 flex-shrink-0" />
                          <div>
                            <span className="font-monopx-2 py-1 rounded-md text-sm">
                              {item.code}
                            </span>
                            <span className="ml-2 text-gray-600 dark:text-gray-300">
                              - {item.desc}
                            </span>
                          </div>
                        </li>
                      ))}
                    </ul>
                  </motion.div>
                </div>
              </motion.div>

              {/* Example Request Section */}
              <motion.div
                className="mb-8"
                whileHover={{ scale: 1.01 }}
                variants={itemVariants}
              >
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
                  Example Request
                </h3>

                {/* Language Tabs */}
                <motion.div
                  className="mb-4"
                  whileHover={{ scale: 1.01 }}
                  variants={itemVariants}
                >
                  <div className="flex overflow-x-auto border-b border-gray-200 dark:border-gray-700">
                    {(
                      Object.keys(codeExamples) as Array<
                        keyof typeof codeExamples
                      >
                    ).map((lang) => (
                      <button
                        key={lang}
                        onClick={() => setActiveTab(lang)}
                        className={`px-4 py-2 font-medium cursor-pointer text-sm capitalize ${
                          activeTab === lang
                            ? "text-emerald-600 dark:text-emerald-400 border-b-2 border-emerald-600 dark:border-emerald-400"
                            : "text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"
                        }`}
                      >
                        {lang}
                      </button>
                    ))}
                  </div>
                </motion.div>

                {/* Code Block */}
                <motion.div
                  className="relative rounded-lg overflow-hidden"
                  whileHover={{ scale: 1.01 }}
                  variants={itemVariants}
                >
                  <div className="absolute top-2 right-2 z-10">
                    <CopyButton
                      textToCopy={codeExamples[activeTab]}
                      buttonId={`lang-${activeTab}-api-url-endpoint`}
                      classNames="bg-gray-800 hover:bg-gray-700 text-white"
                    />
                  </div>
                  <CodeBlock language={activeTab}>
                    {codeExamples[activeTab]}
                  </CodeBlock>
                </motion.div>
              </motion.div>

              {/* Response Section */}
              <motion.div
                className="mb-8"
                whileHover={{ scale: 1.01 }}
                variants={itemVariants}
              >
                <div className="flex items-start gap-3 mb-4">
                  <div className="bg-emerald-100 dark:bg-emerald-900/30 p-2 rounded-lg">
                    <Code2 className="h-5 w-5 text-emerald-600 dark:text-emerald-400" />
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                      Response Codes
                    </h3>
                    <p className="text-gray-600 dark:text-gray-300">
                      The API response will return the following status codes:
                    </p>
                  </div>
                </div>

                <div className="grid gap-3">
                  {[
                    {
                      code: 202,
                      text: "Success message indicating settings were updated",
                      color:
                        "bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200",
                    },
                    {
                      code: 400,
                      text: "Missing chatbotId parameter",
                      color:
                        "bg-amber-100 dark:bg-amber-900/30 text-amber-800 dark:text-amber-200",
                    },
                    {
                      code: 401,
                      text: "Unauthorized request",
                      color:
                        "bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-200",
                    },
                    {
                      code: 404,
                      text: "Invalid chatbotId",
                      color:
                        "bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-200",
                    },
                    {
                      code: 500,
                      text: "Internal server error",
                      color:
                        "bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200",
                    },
                  ].map((item, index) => (
                    <motion.div
                      key={index}
                      whileHover={{ x: 5 }}
                      className="flex items-start gap-3 p-3 rounded-lg border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800/50"
                    >
                      <div
                        className={`flex-shrink-0 w-12 h-12 rounded-lg flex items-center justify-center ${item.color}`}
                      >
                        <span className="font-bold">{item.code}</span>
                      </div>
                      <div className="flex-1">
                        <p className="text-gray-700 dark:text-gray-300">
                          {item.text}
                        </p>
                        <div className="mt-1 flex items-center gap-2">
                          <span className="text-xs text-gray-500 dark:text-gray-400">
                            HTTP Status Code
                          </span>
                          <span className="w-1 h-1 rounded-full bg-gray-400"></span>
                          <span className="text-xs text-gray-500 dark:text-gray-400">
                            {getStatusText(item.code)}
                          </span>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </motion.div>

              {/* Error Handling Section */}
             <ErrorResponse/>
            </div>
            {/* Next Step Card */}
            <motion.div
              whileHover={{ y: -5 }}
              variants={itemVariants}
              className="mb-8 bg-gradient-to-r from-emerald-50 to-emerald-100 dark:from-emerald-900/30 dark:to-emerald-800/30 rounded-xl p-6 text-center border border-emerald-100 dark:border-emerald-800/50 shadow-sm"
            >
              <Link
                href="/docs/get-leads"
                className="flex items-center justify-center gap-2 text-emerald-700 dark:text-emerald-300 font-medium"
              >
                Next::Get Leads
                <ArrowRight className="w-5 h-5" />
              </Link>
            </motion.div>

            {/* Social Links */}
            <SocialLinks />
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default UpdateChatbotSettingsPage;
