"use client";

import { motion } from "framer-motion";
import { <PERSON>, <PERSON>R<PERSON>, <PERSON>u, X } from "lucide-react";
import Head from "next/head";
import Link from "next/link";
import { useState } from "react";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  Bread<PERSON>rumb<PERSON>ist,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { DocsSidebar } from "@/components/sidebar/docs-sidebar";
import SocialLinks from "@/components/social-links/social-links";
import { CopyButton } from "@/components/copy-button/copy-button";
import { itemVariants } from "@/variants/variants";
import { CodeBlock } from "@/components/code-block/code-block";
import ErrorResponse from "@/components/error-response/error-response";

const languageMap = {
  javascript: "javascript",
  python: "python",
  shell: "bash",
  http: "http",
};

const CreateChatbotPage = () => {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  const [activeTab, setActiveTab] =
    useState<keyof typeof codeExamples>("javascript");

  const codeExamples = {
    javascript: `const res = await fetch('https://www.chatzuri.com/api/v1/create-chatbot', {
    method: 'POST',
    headers: {
      Authorization: \`Bearer <Your-Secret-Key>\`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      chatbotName: 'example chatbot',
      sourceText: 'Source text......'
    })
  });
  
  const data = await res.json();
  
  console.log(data); // {chatbotId: 'exampleId-123'}`,
    python: `import requests
  import json
  
  url = 'https://www.chatzuri.com/api/v1/create-chatbot'
  headers = {
      'Authorization': 'Bearer <Your-Secret-Key>',
      'Content-Type': 'application/json'
  }
  data = {
      'chatbotName': 'example chatbot',
      'sourceText': 'Source text......'
  }
  
  response = requests.post(url, headers=headers, data=json.dumps(data))
  data = response.json()
  
  print(data)  # {'chatbotId': 'exampleId-123'}`,
    shell: `curl https://www.chatzuri.com/api/v1/create-chatbot \\
    -H 'Content-Type: application/json' \\
    -H 'Authorization: Bearer <Your-API-Key>' \\
    -d '{"urlsToScrape": ["https://www.chatzuri.com/docs/chat", "https://www.chatzuri.com/docs/create-chatbot"], "chatbotName": "Chatzuri"}'`,
    http: `POST /api/create-chatbot HTTP/1.1
  Host: https://www.chatzuri.com/api/v1/create-chatbot
  Authorization: Bearer <Your-Secret-Key>
  Content-Type: application/json
  
  {
    "chatbotName": "example chatbot",
    "sourceText": "Source text......"
  }`,
  };

  return (
    <div className="min-h-scree">
      <Head>
        <title>Create Cahtbot | Chatzuri API Docs</title>
      </Head>
      <div className="flex flex-col lg:flex-row">
        {/* Mobile Menu Button */}
        <div className="lg:hidden fixed top-4 right-4 z-50">
          <button
            onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            className="p-2 rounded-lg bg-white dark:bg-gray-800 shadow-md"
          >
            {mobileMenuOpen ? (
              <X className="w-6 h-6" />
            ) : (
              <Menu className="w-6 h-6" />
            )}
          </button>
        </div>

        {/* Sidebar Navigation*/}
        <DocsSidebar />

        {/* Main Content */}
        <div className="flex-1 lg:ml-72 mt-16 lg:mt-0">
          {/* Toolbar Section */}
          <motion.div
            className="py-7 md:py-10 px-4 sm:px-6 lg:px-8"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
          >
            <div className="flex flex-col gap-4 w-full">
              <div className="lg:mt-10 ">
                {/* Breadcrumb */}
                <Breadcrumb>
                  <BreadcrumbList>
                    <BreadcrumbItem>
                      <BreadcrumbLink
                        href="/"
                        className="text-emerald-600 dark:text-emerald-400 hover:text-emerald-700 dark:hover:text-emerald-300"
                      >
                        <Home className="inline mr-1 h-4 w-4" />
                        Home
                      </BreadcrumbLink>
                    </BreadcrumbItem>
                    <BreadcrumbSeparator />
                    <BreadcrumbItem>
                      <BreadcrumbLink
                        href="/docs"
                        className="text-gray-700 dark:text-gray-300"
                      >
                        API Docs
                      </BreadcrumbLink>
                    </BreadcrumbItem>
                    <BreadcrumbSeparator />
                    <BreadcrumbItem>
                      <BreadcrumbLink className="text-gray-500 dark:text-gray-400">
                        Create a Chatbot
                      </BreadcrumbLink>
                    </BreadcrumbItem>
                  </BreadcrumbList>
                </Breadcrumb>
              </div>
            </div>
          </motion.div>

          {/* Content Section */}
          <motion.div
            className="px-4 sm:px-6 lg:px-8 pb-12 "
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
          >
            <div className="bg-white px-4 py-3 dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden mb-4">
              <div className="mb-8">
                <motion.h1
                  variants={itemVariants}
                  className="text-2xl md:text-3xl font-bold text-gray-900 dark:text-white mb-4"
                >
                  Create a Chatbot
                </motion.h1>
                <p className="text-lg text-gray-600 dark:text-gray-300 font-semibold">
                  This page helps you create a Chatbot.
                </p>
                <div className="my-6 h-px bg-gradient-to-r from-transparent via-emerald-500 to-transparent dark:via-emerald-400" />
              </div>

              {/* API Description */}
              <motion.div className="space-y-4 mb-10 " variants={itemVariants}>
                <p className="text-gray-700 dark:text-gray-300">
                  The Chatbot Creation API allows you to create a new chatbot by
                  making a POST request to the{" "}
                  <code className="bg-yellow-100 dark:bg-yellow-800 text-yellow-800 dark:text-yellow-100 font-mono px-2 py-1 rounded text-sm">
                    /api/v1/create-chatbot
                  </code>{" "}
                  endpoint.
                </p>
              </motion.div>

              {/* Endpoint Section */}
              <motion.div
                className="mb-6 pb-6 mt-8"
                whileHover={{ scale: 1.01 }}
                variants={itemVariants}
              >
                <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
                  Endpoint
                </h2>
                <div className="flex items-center gap-2">
                  <span className="inline-flex items-center gap-1 max-w-full overflow-x-auto">
                    <div className="min-w-0">
                      <CodeBlock language="http">
                        POST https://www.chatzuri.com/api/v1/create-chatbot
                      </CodeBlock>
                    </div>
                    <CopyButton
                      textToCopy="POST https://www.chatzuri.com/api/v1/create-chatbot"
                      buttonId="api-key-url"
                      classNames="flex-shrink-0"
                    />
                  </span>
                </div>
              </motion.div>

              {/* Request Headers Section */}
              <motion.div
                className="mb-6 pb-6 mt-8"
                whileHover={{ scale: 1.01 }}
                variants={itemVariants}
              >
                <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
                  Request Headers
                </h2>
                <p className="text-gray-700 dark:text-gray-300 mb-4">
                  The API request must include the following headers.
                </p>
                <ul className="space-y-2">
                  <li className="text-gray-700 dark:text-gray-300">
                    <code className="bg-blue-100 dark:bg-blue-800 text-blue-900 dark:text-blue-100 font-mono px-2 py-1 rounded">
                      Authorization: Bearer &lt;Your-Secret-Key&gt;
                    </code>{" "}
                    - The secret key for authenticating the API request.
                  </li>
                  <li className="text-gray-700 dark:text-gray-300">
                    <code className="bg-green-100 dark:bg-green-800 text-green-900 dark:text-green-100 font-mono px-2 py-1 rounded">
                      Content-Type: application/json
                    </code>{" "}
                    - The content type of the request payload.
                  </li>
                </ul>
              </motion.div>

              {/* Request Body Section */}
              <motion.div
                className="mb-6 pb-6 mt-8"
                whileHover={{ scale: 1.01 }}
                variants={itemVariants}
              >
                <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
                  Request Body
                </h2>
                <p className="text-gray-700 dark:text-gray-300 mb-4">
                  The request body should contain the following parameters:
                </p>
                <ul className="space-y-2">
                  <li className="text-gray-700 dark:text-gray-300">
                    <code className="bg-yellow-100 dark:bg-yellow-800 text-yellow-900 dark:text-yellow-100 font-mono px-2 py-1 rounded">
                      chatbotName
                    </code>{" "}
                    -{" "}
                    <span className="text-sm text-gray-600 dark:text-gray-400">
                      (string, required)
                    </span>
                    : The name of the chatbot to be created.
                  </li>

                  <li className="text-gray-700 dark:text-gray-300">
                    <code className="bg-purple-100 dark:bg-purple-800 text-purple-900 dark:text-purple-100 font-mono px-2 py-1 rounded">
                      sourceText
                    </code>{" "}
                    -{" "}
                    <span className="text-sm text-gray-600 dark:text-gray-400">
                      (string, optional)
                    </span>
                    : The text data for the chatbot.
                  </li>
                </ul>
              </motion.div>

              {/* Code Examples Section */}
              <motion.div
                className="mb-6 pb-6 mt-8"
                whileHover={{ scale: 1.01 }}
                variants={itemVariants}
              >
                <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-4">
                  Example Request
                </h2>

                {/* Language Tabs */}
                <div className="flex border-b border-gray-200 dark:border-gray-700 mb-4">
                  {(["javascript", "python", "shell", "http"] as const).map(
                    (lang) => (
                      <button
                        key={lang}
                        onClick={() => setActiveTab(lang)}
                        className={`px-4 cursor-pointer py-2 font-medium ${
                          activeTab === lang
                            ? "text-emerald-600 dark:text-emerald-400 border-b-2 border-emerald-500"
                            : "text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"
                        }`}
                      >
                        {lang.charAt(0).toUpperCase() + lang.slice(1)}
                      </button>
                    )
                  )}
                </div>

                {/* Code Block */}
                <div className="relative">
                  <CodeBlock language={languageMap[activeTab]}>
                    {codeExamples[activeTab]}
                  </CodeBlock>
                  <CopyButton
                    textToCopy={codeExamples[activeTab]}
                    buttonId="api-response"
                    classNames="absolute top-2 right-2"
                  />
                </div>
              </motion.div>

              {/* Response Section */}
              <motion.div
                className="mb-6 pb-6 mt-8"
                whileHover={{ scale: 1.01 }}
                variants={itemVariants}
              >
                <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
                  Response
                </h2>
                <p className="text-gray-700 dark:text-gray-300 mb-4">
                  The API response will be a JSON object with the following
                  structure:
                </p>
                <div className="relative">
                  <CodeBlock language="json">
                    {`{
  "chatbotId": "exampleId-123"
}`}
                  </CodeBlock>
                  <CopyButton
                    textToCopy='{"chatbotId": "exampleId-123"}'
                    buttonId="api-response"
                    classNames="absolute top-2 right-2"
                  />
                </div>
                <p className="text-gray-700 dark:text-gray-300 mt-4">
                  The{" "}
                  <code className="bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">
                    chatbotId
                  </code>{" "}
                  field in the response contains the unique identifier assigned
                  to the created chatbot.
                </p>
              </motion.div>

              {/* Error Handling Section */}
              <ErrorResponse/>
            </div>

            {/* Next Step Card */}
            <motion.div
              whileHover={{ y: -5 }}
              variants={itemVariants}
              className="mb-8 bg-gradient-to-r from-emerald-50 to-emerald-100 dark:from-emerald-900/30 dark:to-emerald-800/30 rounded-xl p-6 text-center border border-emerald-100 dark:border-emerald-800/50 shadow-sm"
            >
              <Link
                href="/docs/message-chatbot"
                className="flex items-center justify-center gap-2 text-emerald-700 dark:text-emerald-300 font-medium"
              >
                Next: Message a Chatbot
                <ArrowRight className="w-5 h-5" />
              </Link>
            </motion.div>

            {/* Social Links */}
            <SocialLinks />
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default CreateChatbotPage;
