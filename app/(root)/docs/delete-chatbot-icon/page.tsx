"use client";

import { motion } from "framer-motion";
import {
  <PERSON>,
  <PERSON>R<PERSON>,
  Menu,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  EyeOff,
  Eye,
  Key,
  Shield,
} from "lucide-react";
import Head from "next/head";
import Link from "next/link";
import { useState } from "react";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { DocsSidebar } from "@/components/sidebar/docs-sidebar";
import SocialLinks from "@/components/social-links/social-links";
import { CopyButton } from "@/components/copy-button/copy-button";
import { itemVariants } from "@/variants/variants";
import { CodeBlock } from "@/components/code-block/code-block";

const DeleteChatbotIconPage = () => {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [showApiKey, setShowApiKey] = useState(false);

  return (
    <div className="min-h-screen">
      <Head>
        <title>Delete Chatbot icon | Chatzuri API Docs</title>
      </Head>
      <div className="flex flex-col lg:flex-row">
        {/* Mobile Menu Button */}
        <div className="lg:hidden fixed top-4 right-4 z-50">
          <button
            onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            className="p-2 rounded-lg bg-white cursor-pointer dark:bg-gray-800 shadow-md"
          >
            {mobileMenuOpen ? (
              <X className="w-6 h-6" />
            ) : (
              <Menu className="w-6 h-6" />
            )}
          </button>
        </div>

        {/* Sidebar Navigation*/}
        <DocsSidebar />

        {/* Main Content */}
        <div className="flex-1 lg:ml-72 mt-16 lg:mt-0">
          {/* Toolbar Section */}
          <motion.div
            className="py-7 md:py-10 px-4 sm:px-6 lg:px-8"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
          >
            <div className="flex flex-col gap-4 w-full">
              <div className="lg:mt-10">
                {/* Breadcrumb */}
                <Breadcrumb>
                  <BreadcrumbList>
                    <BreadcrumbItem>
                      <BreadcrumbLink
                        href="/"
                        className="text-emerald-600 dark:text-emerald-400 hover:text-emerald-700 dark:hover:text-emerald-300"
                      >
                        <Home className="inline mr-1 h-4 w-4" />
                        Home
                      </BreadcrumbLink>
                    </BreadcrumbItem>
                    <BreadcrumbSeparator />
                    <BreadcrumbItem>
                      <BreadcrumbLink
                        href="/docs"
                        className="text-gray-700 dark:text-gray-300"
                      >
                        API Docs
                      </BreadcrumbLink>
                    </BreadcrumbItem>
                    <BreadcrumbSeparator />
                    <BreadcrumbItem>
                      <BreadcrumbLink className="text-gray-500 dark:text-gray-400">
                        Delete Chatbot Icon
                      </BreadcrumbLink>
                    </BreadcrumbItem>
                  </BreadcrumbList>
                </Breadcrumb>
              </div>
            </div>
          </motion.div>

          {/* Content Section */}
          <motion.div
            className="px-4 sm:px-6 lg:px-8 pb-12"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
          >
            <div className="bg-white px-4 py-3 dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden mb-4">
              {/* Page Header */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4 }}
                variants={itemVariants}
                whileHover={{ scale: 1.01 }}
                className="mb-10"
              >
                <motion.h1
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.3 }}
                  className="text-3xl font-bold text-gray-900 dark:text-white mb-2"
                >
                  Delete Chatbot Icon
                </motion.h1>
                <motion.p
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.4 }}
                  className="text-lg text-gray-600 dark:text-gray-300 font-medium"
                >
                  Remove the icon associated with your chatbot through our API
                </motion.p>

                <div className="my-6 h-px bg-gradient-to-r from-transparent via-emerald-500 to-transparent dark:via-emerald-400" />
              </motion.div>

              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.5 }}
                className="rounded-xl p-6 mb-8 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700"
              >
                <div className="flex items-center mb-4">
                  <Sparkles className="w-5 h-5 mr-2 text-emerald-600 dark:text-emerald-400" />
                  <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                    API Overview
                  </h2>
                </div>

                <p className="mb-4 text-gray-600 dark:text-gray-300">
                  The Delete Chatbot Icon API endpoint allows you to remove the
                  icon associated with a specific chatbot.
                </p>

                <motion.div
                  className="p-4 rounded-lg mb-4 bg-gray-100 dark:bg-gray-700"
                  whileHover={{ scale: 1.01 }}
                  variants={itemVariants}
                >
                  <div className="flex items-center justify-between mb-2">
                    <CodeBlock language="http">
                    {`POST https://www.chatzuri.com/api/v1/delete-chatbot-icon`}
                    </CodeBlock>
                    <CopyButton
                      textToCopy="POST https://www.chatzuri.com/api/v1/delete-chatbot-icon"
                      buttonId="get-conversations-api-endpoint"
                      classNames="ml-2"
                    />
                  </div>
                </motion.div>

                <p className="mb-4 text-gray-600 dark:text-gray-300">
                  Press the eye icon to {showApiKey ? "hide" : "reveal"} the
                  secret API key:
                </p>

                <motion.div
                  className="relative group"
                  whileHover={{ scale: 1.01 }}
                  variants={itemVariants}
                >
                  <div
                    className={`
    p-3 pr-12 rounded-lg flex items-center 
    transition-all duration-300
    ${
      showApiKey
        ? "bg-emerald-50 dark:bg-emerald-900/30 border border-emerald-200 dark:border-emerald-800"
        : "bg-gray-100 dark:bg-gray-800 border border-gray-200 dark:border-gray-700"
    }
    hover:shadow-sm hover:border-emerald-300 dark:hover:border-emerald-600
  `}
                  >
                    <span
                      className={`
      font-mono text-sm 
      ${
        showApiKey
          ? "text-emerald-600 dark:text-emerald-400"
          : "text-gray-600 dark:text-gray-300"
      }
      transition-all duration-300
    `}
                    >
                      {showApiKey
                        ? "Bearer sk_test_1234567890abcdef"
                        : "Bearer ****************"}
                    </span>

                    <button
                      onClick={() => setShowApiKey(!showApiKey)}
                      className={`
        absolute right-3 p-2 cursor-pointer rounded-full 
        transition-all duration-300
        ${
          showApiKey
            ? "text-emerald-600 dark:text-emerald-400 hover:bg-emerald-100 dark:hover:bg-emerald-800/50"
            : "text-gray-500 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-700"
        }
        hover:scale-110 active:scale-95
      `}
                      aria-label={showApiKey ? "Hide API key" : "Show API key"}
                    >
                      {showApiKey ? (
                        <EyeOff className="w-5 h-5" />
                      ) : (
                        <Eye className="w-5 h-5" />
                      )}
                    </button>

                    {showApiKey && (
                      <motion.div
                        initial={{ opacity: 0, y: -5 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="absolute -top-2 -right-2"
                      >
                        <span
                          className={`
          px-2 py-1 text-xs font-medium rounded-full 
          bg-emerald-500 text-white shadow-sm
          flex items-center
        `}
                        >
                          <Shield className="w-3 h-3 mr-1" />
                          Visible
                        </span>
                      </motion.div>
                    )}
                  </div>

                  {!showApiKey && (
                    <motion.div
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      className={`
        absolute inset-0 flex items-center justify-center
        bg-gradient-to-r from-transparent via-white/70 to-transparent
        dark:via-gray-900/70 pointer-events-none
      `}
                    >
                      <button
                        className={`
        pointer-events-auto cursor-pointer
        px-3 py-1.5 text-xs font-medium rounded-full 
        bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700
        shadow-sm flex items-center hover:bg-gray-50 dark:hover:bg-gray-700
        transition-all
      `}
                      >
                        <Key className="w-3.5 h-3.5 mr-1.5" />
                        Click to reveal API key
                      </button>
                    </motion.div>
                  )}
                </motion.div>
              </motion.div>

              {/* Documentation Sections */}
              <div className="space-y-12">
                {/* Authentication Section */}
                <motion.section
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.6 }}
                  className="rounded-xl p-6 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700"
                >
                  <h2 className="text-2xl font-bold mb-4 text-gray-900 dark:text-white">
                    Authentication
                  </h2>

                  <p className="mb-4 text-gray-600 dark:text-gray-300">
                    All API requests must include your secret API key in the
                    Authorization header.
                  </p>

                  <CodeBlock language="http">
                    {`Authorization: Bearer YOUR_API_KEY`}
                  </CodeBlock>
                </motion.section>
              </div>
            </div>

            {/* Next Step Card */}
            <motion.div
              whileHover={{ y: -5 }}
              variants={itemVariants}
              className="mb-8 bg-gradient-to-r from-emerald-50 to-emerald-100 dark:from-emerald-900/30 dark:to-emerald-800/30 rounded-xl p-6 text-center border border-emerald-100 dark:border-emerald-800/50 shadow-sm"
            >
              <Link
                href="/docs/upload-chatbot-icon"
                className="flex items-center justify-center gap-2 text-emerald-700 dark:text-emerald-300 font-medium"
              >
                Next::Upload Chatbot Icon
                <ArrowRight className="w-5 h-5" />
              </Link>
            </motion.div>

            {/* Social Links */}
            <SocialLinks />
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default DeleteChatbotIconPage;
