"use client";

import { motion } from "framer-motion";
import { Home, Eye, Settings, ArrowRight, Menu, X } from "lucide-react";
import Head from "next/head";
import Image from "next/image";
import Link from "next/link";
import { useState } from "react";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { DocsSidebar } from "@/components/sidebar/docs-sidebar";
import SocialLinks from "@/components/social-links/social-links";
import { CopyButton } from "@/components/copy-button/copy-button";
import { itemVariants } from "@/variants/variants";
import { CodeBlock } from "@/components/code-block/code-block";

const ApiDocsPage = () => {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  return (
    <div className="min-h-screen">
      <Head>
        <title>Getting Setup | Chatzuri API Docs</title>
      </Head>
      <div className="flex flex-col lg:flex-row">
        {/* Mobile Menu Button */}
        <div className="lg:hidden fixed top-4 right-4 z-50">
          <button
            onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            className="p-2 rounded-lg bg-white dark:bg-gray-800 shadow-md"
          >
            {mobileMenuOpen ? (
              <X className="w-6 h-6" />
            ) : (
              <Menu className="w-6 h-6" />
            )}
          </button>
        </div>

        {/* Sidebar Navigation*/}
        <DocsSidebar />

        {/* Main Content */}
        <div className="flex-1 lg:ml-72 mt-16 lg:mt-0">
          {/* Hero Section */}
          <div className="relative isolate px-6 pt-14 lg:px-8">
            <div
              className="absolute inset-x-0 -top-40 -z-10 transform-gpu overflow-hidden blur-3xl sm:-top-80"
              aria-hidden="true"
            >
              <div
                className="relative left-[calc(50%-11rem)] aspect-1155/678 w-[36.125rem] -translate-x-1/2 rotate-[30deg] bg-linear-to-tr from-[#ff80b5] to-[#00a67e] opacity-30 sm:left-[calc(50%-30rem)] sm:w-[72.1875rem]"
                style={{
                  backgroundImage: `linear-gradient(rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.3)),url('/images/affiliate-bg.jpg')`,
                  clipPath:
                    "polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)",
                  WebkitClipPath:
                    "polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)",
                }}
              ></div>
            </div>
            <div className="mx-auto max-w-2xl py-10 sm:py-12 lg:py-11">
              <div className="text-center">
                <motion.h1
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6 }}
                  className="dark:text-white text-4xl font-semibold tracking-tight text-balance text-gray-900 sm:text-7xl"
                >
                  Chatzuri API Documentation
                </motion.h1>
                <motion.p
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.2 }}
                  className="dark:text-white mt-8 text-lg font-medium text-pretty text-gray-500 sm:text-xl/8"
                >
                  Explore our comprehensive REST API to integrate Chatzuri into
                  your apps. Get started with authentication, endpoints, code
                  examples, and usage limits.
                </motion.p>
              </div>
            </div>
            <div
              className="absolute inset-x-0 top-[calc(100%-13rem)] -z-10 transform-gpu overflow-hidden blur-3xl sm:top-[calc(100%-30rem)]"
              aria-hidden="true"
            >
              <div
                className="relative left-[calc(50%+3rem)] aspect-1155/678 w-[36.125rem] -translate-x-1/2 bg-linear-to-tr from-[#ff80b5] to-[#9089fc] opacity-30 sm:left-[calc(50%+36rem)] sm:w-[72.1875rem]"
                style={{
                  clipPath:
                    "polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)",
                  WebkitClipPath:
                    "polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)",
                }}
              ></div>
            </div>
          </div>

          {/* Toolbar Section */}
          <motion.div
            className="py-7 md:py-10 px-4 sm:px-6 lg:px-8"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
          >
            <div className="flex flex-col gap-4 w-full">
              {/* Breadcrumb */}
              <Breadcrumb>
                <BreadcrumbList>
                  <BreadcrumbItem>
                    <BreadcrumbLink
                      href="/"
                      className="text-emerald-600 dark:text-emerald-400 hover:text-emerald-700 dark:hover:text-emerald-300"
                    >
                      <Home className="inline mr-1 h-4 w-4" />
                      Home
                    </BreadcrumbLink>
                  </BreadcrumbItem>
                  <BreadcrumbSeparator />
                  <BreadcrumbItem>
                    <BreadcrumbLink
                      href="/docs"
                      className="text-gray-700 dark:text-gray-300"
                    >
                      API Docs
                    </BreadcrumbLink>
                  </BreadcrumbItem>
                  <BreadcrumbSeparator />
                  <BreadcrumbItem>
                    <BreadcrumbLink className="text-gray-500 dark:text-gray-400">
                      Getting setup
                    </BreadcrumbLink>
                  </BreadcrumbItem>
                </BreadcrumbList>
              </Breadcrumb>

              <motion.h1
                variants={itemVariants}
                className="text-2xl md:text-3xl font-bold text-gray-900 dark:text-white"
              >
                Getting setup
              </motion.h1>
            </div>
          </motion.div>

          {/* Content Section */}
          <motion.div
            className="px-4 sm:px-6 lg:px-8 pb-12"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
          >
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden mb-4">
              <div className="p-6 sm:p-10">
                {/* Main Content */}
                <div className="mb-14">
                  <motion.div className="mb-8" variants={itemVariants}>
                    <h1 className="text-2xl md:text-3xl font-bold text-gray-900 dark:text-white mb-4">
                      Getting Setup
                    </h1>
                    <p className="text-lg text-gray-600 dark:text-gray-300 font-semibold">
                      This page will help you get started with Chatzuri.
                    </p>
                    <div className="my-6 h-px bg-gradient-to-r from-transparent via-emerald-500 to-transparent dark:via-emerald-400" />
                  </motion.div>

                  {/* API Key Section */}
                  <motion.div
                    className="space-y-4 mb-10"
                    variants={itemVariants}
                  >
                    <p className="text-gray-700 dark:text-gray-300">
                      Chatzuri offers an API for you to interact with your
                      chatbots if you are subscribed to a paid plan. To get an
                      API key, go to{" "}
                      <span className="inline-flex items-center gap-1 max-w-full overflow-x-auto">
                        <div className="min-w-0">
                          <CodeBlock
                            language="http"
                            className="whitespace-nowrap"
                          >
                            {`https://www.chatzuri.com/dashboard/<your-team-slug>/settings/api-keys`}
                          </CodeBlock>
                        </div>
                        <CopyButton
                          textToCopy="https://www.chatzuri.com/dashboard/<your-team-slug>/settings/api-keys"
                          buttonId="api-key-url"
                          classNames="flex-shrink-0"
                        />
                      </span>
                      .
                    </p>
                    <p className="text-gray-700 dark:text-gray-300 flex items-center">
                      <Eye className="w-5 h-5 mr-2 text-emerald-600 dark:text-emerald-400" />
                      Pressing the eye icon will reveal the secret API key. This
                      key will need to be substituted into for all API requests.
                    </p>
                    <div className="mt-6 rounded-lg overflow-hidden border border-gray-200 dark:border-gray-700">
                      <Image
                        src="/images/api-key-example.png"
                        alt="Account API key"
                        width={800}
                        height={400}
                        className="w-full h-auto"
                        placeholder="blur"
                        blurDataURL="/images/placeholder.jpg"
                      />
                    </div>
                  </motion.div>

                  {/* Chatbot ID Section */}
                  <motion.div
                    className="space-y-4 mb-10"
                    variants={itemVariants}
                  >
                    <p className="text-gray-700 dark:text-gray-300">
                      To get the ID of a created Chatbot, navigate to{" "}
                      <span className="inline-flex items-center gap-1 max-w-full overflow-x-auto">
                        <div className="min-w-0">
                          <CodeBlock language="http">
                            {`https://www.chatzuri.com/dashboard/<your-team-slug>/chatbots`}
                          </CodeBlock>
                        </div>
                        <CopyButton
                          textToCopy="https://www.chatzuri.com/dashboard/&lt;your-team-slug&gt;/chatbots"
                          buttonId="api-key-url"
                          classNames="flex-shrink-0"
                        />
                      </span>{" "}
                      and click on the chatbot you would like to get the ID for.
                    </p>
                    <p className="text-gray-700 dark:text-gray-300 flex items-center">
                      <Settings className="w-5 h-5 mr-2 text-emerald-600 dark:text-emerald-400" />
                      Select &quot;Settings&quot;, then make sure you are on the
                      &quot;General&quot; tab. You will find the Chatbot ID at
                      the top.
                    </p>
                    <div className="mt-6 rounded-lg overflow-hidden border border-gray-200 dark:border-gray-700">
                      <Image
                        src="/images/chatbot-id-example.png"
                        alt="Chatbot ID"
                        width={800}
                        height={400}
                        className="w-full h-auto"
                        placeholder="blur"
                        blurDataURL="/images/placeholder.jpg"
                      />
                    </div>
                  </motion.div>

                  {/* URL Section */}
                  <motion.div className="space-y-4" variants={itemVariants}>
                    <p className="text-gray-700 dark:text-gray-300">
                      You can also quickly get the ID by using the URL of the
                      webpage. Once you have clicked on the Chatbot, the URL
                      will contain your Chatbot ID in the form{" "}
                      <span className="inline-flex items-center gap-1 max-w-full overflow-x-auto">
                        <div className="min-w-0">
                          <CodeBlock language="http">
                            {`https://www.chatzuri.com/dashboard/<your-team-slug>/chatbot/<Your Chatbot ID>`}
                          </CodeBlock>
                        </div>

                        <CopyButton
                          textToCopy=" https://www.chatzuri.com/dashboard/&lt;your-team-slug&gt;/chatbot/&lt;Your Chatbot ID&gt;"
                          buttonId="api-key-url"
                          classNames="flex-shrink-0"
                        />
                      </span>
                      .
                    </p>
                  </motion.div>
                </div>
              </div>
            </div>
            {/* Next Step Card */}
            <motion.div
              whileHover={{ y: -5 }}
              variants={itemVariants}
              className="mb-8 bg-gradient-to-r from-emerald-50 to-emerald-100 dark:from-emerald-900/30 dark:to-emerald-800/30 rounded-xl p-6 text-center border border-emerald-100 dark:border-emerald-800/50 shadow-sm"
            >
              <Link
                href="/docs/create-chatbot"
                className="flex items-center justify-center gap-2 text-emerald-700 dark:text-emerald-300 font-medium"
              >
                Next: Create a Chatbot
                <ArrowRight className="w-5 h-5" />
              </Link>
            </motion.div>

            {/* Social Links */}
            <SocialLinks />
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default ApiDocsPage;
