"use client";
import { motion } from "framer-motion";
import { <PERSON><PERSON>, <PERSON>, BarChart2, Target } from "lucide-react";
import Head from "next/head";

export default function CookiePolicy() {
  const cookieTables = [
    {
      title: "Strictly Necessary Cookies",
      description:
        "These cookies are necessary for the website to function and cannot be switched off in our systems. They are usually only set in response to actions made by you which amount to a request for services, such as setting your privacy preferences, logging in or filling in forms. You can set your browser to block or alert you about these cookies, but that will cause some parts of the site to not work. These cookies do not store any personally identifiable information.",
      icon: <Lock className="w-5 h-5 text-emerald-500" />,
      cookies: [
        {
          subgroup: ".chatzuri.com",
          name: "tracking-preferences",
          type: "First party",
        },
        {
          subgroup: ".chatzuri.com",
          name: "sb-backend-auth-token.0",
          type: "First party",
        },
        {
          subgroup: ".chatzuri.com",
          name: "sb-backend-auth-token.1",
          type: "First party",
        },
        {
          subgroup: ".chatzuri.com",
          name: "__stripe_mid",
          type: "Third party",
        },
        {
          subgroup: ".chatzuri.com",
          name: "__stripe_sid",
          type: "Third party",
        },
      ],
    },
    {
      title: "Performance Cookies",
      description:
        "These cookies (Chatzuri uses Google Analytics) allow us to count visits and traffic sources so we can measure and improve the performance of our site. They help us to know which pages are the most and least popular and see how visitors move around the site. If you do not allow these cookies we will not know when you have visited our site and will not be able to monitor its performance.",
      icon: <BarChart2 className="w-5 h-5 text-emerald-500" />,
      links: [
        {
          text: "Google Analytics privacy overview",
          url: "https://support.google.com/analytics/answer/6004245",
        },
        {
          text: "Google Analytics opt-out",
          url: "https://tools.google.com/dlpage/gaoptout?hl=en-GB",
        },
      ],
      cookies: [
        { subgroup: ".chatzuri.com", name: "_ga", type: "Third party" },
        {
          subgroup: ".chatzuri.com",
          name: "ajs_anonymous_id",
          type: "Third party",
        },
        { subgroup: ".chatzuri.com", name: "ajs_user_id", type: "Third party" },
      ],
    },
    {
      title: "Marketing Cookies",
      description:
        "We also use a marketing database management program that deploys a cookie when a user interacts with marketing communications, such as a marketing email or a marketing-based landing page on our website. This cookie collects personal information such as your name, which pages you visit on our website, how you arrived at our website, and your purchases from Chatzuri. Collected information is used to evaluate the effectiveness of our marketing campaigns or to provide better targeting for marketing.",
      icon: <Target className="w-5 h-5 text-emerald-500" />,
      cookies: [
        { subgroup: ".chatzuri.com", name: "_ga", type: "Third party" },
        { subgroup: ".chatzuri.com", name: "_gcl_au", type: "Third party" },
        { subgroup: ".chatzuri.com", name: "_fb", type: "Third party" },
      ],
    },
  ];

  return (
    <>
      <Head>
        <title>Cookie Policy | Chatzuri</title>
        <meta
          name="description"
          content="Chatzuri's Cookie Policy - Learn how we use cookies to enhance your experience"
        />
      </Head>

      {/* Hero Section */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5 }}
        className="relative isolate px-6 pt-14 lg:px-8"
      >
        <div
          className="absolute inset-x-0 -top-40 -z-10 transform-gpu overflow-hidden blur-3xl sm:-top-80"
          aria-hidden="true"
        >
          <div
            className="relative left-[calc(50%-11rem)] aspect-1155/678 w-[36.125rem] -translate-x-1/2 rotate-[30deg] bg-linear-to-tr from-[#ff80b5] to-[#00a67e] opacity-30 sm:left-[calc(50%-30rem)] sm:w-[72.1875rem]"
            style={{
              backgroundImage: `linear-gradient(rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.3)),url('/images/affiliate-bg.jpg')`,
              clipPath:
                "polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)",
              WebkitClipPath:
                "polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)",
            }}
          ></div>
        </div>
        <div className="mx-auto max-w-2xl py-10 sm:py-12 lg:py-11">
          <div className="text-center">
            <motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="dark:text-white text-4xl font-semibold tracking-tight text-balance text-gray-900 sm:text-7xl"
            >
              We Use Cookies Responsibly.
            </motion.h1>
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="dark:text-white mt-8 text-lg font-medium text-pretty text-gray-500 sm:text-xl/8"
            >
              Chatzuri uses cookies to enhance your browsing experience, analyze
              site traffic, and personalize content. We never use cookies to
              track you across the web or sell your data. You&apos;re in control
              — customize your cookie preferences anytime.
            </motion.p>
          </div>
        </div>
        <div
          className="absolute inset-x-0 top-[calc(100%-13rem)] -z-10 transform-gpu overflow-hidden blur-3xl sm:top-[calc(100%-30rem)]"
          aria-hidden="true"
        >
          <div
            className="relative left-[calc(50%+3rem)] aspect-1155/678 w-[36.125rem] -translate-x-1/2 bg-linear-to-tr from-[#ff80b5] to-[#9089fc] opacity-30 sm:left-[calc(50%+36rem)] sm:w-[72.1875rem]"
            style={{
              clipPath:
                "polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)",
              WebkitClipPath:
                "polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)",
            }}
          ></div>
        </div>
      </motion.div>

      <motion.main
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5 }}
        className="container py-16 mx-auto px-4 sm:px-6 lg:px-8"
      >
        <motion.div
          initial={{ y: -20 }}
          animate={{ y: 0 }}
          className="max-w-4xl mx-auto"
        >
          <motion.h1
            className="text-4xl font-bold text-gray-900 dark:text-white mb-8 pb-4 border-b border-gray-200 dark:border-gray-700"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.1 }}
          >
            Cookie Policy
          </motion.h1>

          <div className="space-y-8">
            <motion.section
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300"
            >
              <div className="flex items-center mb-4">
                <Cookie className="w-5 h-5 text-emerald-500" />
                <h2 className="text-2xl font-semibold text-gray-800 dark:text-white ml-2">
                  Introduction
                </h2>
              </div>
              <div className="text-gray-600 dark:text-gray-300 space-y-4">
                <p>
                  Chatzuri believes in transparency about collection and use of
                  data. This policy provides information about how and when
                  Chatzuri uses cookies for these purposes. Capitalized terms
                  used in this policy but not defined have the meaning set forth
                  in our Privacy Policy, which also includes additional details
                  about the collection and use of information at Chatzuri.
                </p>
              </div>
            </motion.section>

            <motion.section
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300"
            >
              <h3 className="text-xl font-semibold text-gray-700 dark:text-gray-200 mb-3">
                What is a cookie?
              </h3>
              <div className="text-gray-600 dark:text-gray-300">
                <p>
                  Cookies are small text files sent by us to your computer or
                  mobile device, which enable Chatzuri features and
                  functionality. They are unique to your account or your
                  browser. Session-based cookies last only while your browser is
                  open and are automatically deleted when you close your
                  browser. Persistent cookies last until you or your browser
                  delete them or until they expire.
                </p>
              </div>
            </motion.section>

            <motion.section
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
              className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300"
            >
              <h3 className="text-xl font-semibold text-gray-700 dark:text-gray-200 mb-3">
                Does Chatzuri use cookies?
              </h3>
              <div className="text-gray-600 dark:text-gray-300">
                <p>
                  Yes. Chatzuri uses cookies and similar technologies like
                  single-pixel gifs and web beacons. Chatzuri uses both
                  session-based and persistent cookies. Chatzuri sets and
                  accesses cookies on the domains operated by Chatzuri and its
                  corporate affiliates (collectively, the &quot;Sites&quot;). In
                  addition, Chatzuri uses third party cookies, like Google
                  Analytics.
                </p>
              </div>
            </motion.section>

            <motion.section
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5 }}
              className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300"
            >
              <h3 className="text-xl font-semibold text-gray-700 dark:text-gray-200 mb-3">
                How is Chatzuri using cookies?
              </h3>
              <div className="text-gray-600 dark:text-gray-300 space-y-4">
                <p>
                  Some cookies are associated with your account and personal
                  information to remember that you are logged in and which
                  workspaces you are logged into. Other cookies are not tied to
                  your account but are unique and allow us to carry out
                  analytics and customization, among other similar things.
                </p>
                <p>
                  Cookies can be used to recognize you when you visit a Site or
                  use our Services, remember your preferences, and give you a
                  personalized experience that is consistent with your settings.
                  Cookies also make your interactions faster and more secure.
                </p>
              </div>
            </motion.section>

            <motion.section
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6 }}
              className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300"
            >
              <h3 className="text-xl font-semibold text-gray-700 dark:text-gray-200 mb-3">
                Categories of use
              </h3>
              <div className="text-gray-600 dark:text-gray-300 space-y-4">
                <ul className="list-disc pl-6 space-y-3">
                  <li>
                    <p>
                      <span className="font-semibold">Authentication:</span> If
                      you&apos;re signed into the Services, cookies help
                      Chatzuri show you the right information and personalize
                      your experience.
                    </p>
                  </li>
                  <li>
                    <p>
                      <span className="font-semibold">Security:</span> Chatzuri
                      uses cookies to enable and support security features, and
                      to help detect malicious activity.
                    </p>
                  </li>
                  <li>
                    <p>
                      <span className="font-semibold">
                        Preferences, features, and services:
                      </span>
                      Cookies denote which language you prefer and what your
                      communications preferences are. They can help fill out
                      forms on our Sites more easily. They also provide you with
                      features, insights, and customized content.
                    </p>
                  </li>
                  <li>
                    <p>
                      <span className="font-semibold">Marketing:</span> Chatzuri
                      may use cookies to help deliver marketing campaigns and
                      track their performance (e.g., a user visited chatzuri.com
                      and then made a purchase). Similarly, Chatzuri&apos;s
                      partners may use cookies to provide us with information
                      about your interactions with their services, but use of
                      those third-party cookies would be subject to the service
                      provider&apos;s policies.
                    </p>
                  </li>
                  <li>
                    <p>
                      <span className="font-semibold">
                        Performance, Analytics, and Research:
                      </span>
                      Cookies help Chatzuri learn how well the Sites and
                      Services perform. Chatzuri also uses cookies to
                      understand, improve, and research products, features, and
                      services, including to create logs and record when you
                      access our Sites and Services from different devices, such
                      as your work computer or your mobile device.
                    </p>
                  </li>
                </ul>
              </div>
            </motion.section>

            <motion.section
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.7 }}
              className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300"
            >
              <h3 className="text-xl font-semibold text-gray-700 dark:text-gray-200 mb-3">
                What third-party cookies does Chatzuri use?
              </h3>
              <div className="text-gray-600 dark:text-gray-300">
                <p>
                  You can find a list of the third-party cookies that Chatzuri
                  uses on our sites along with other relevant information in the
                  cookie tables below. Chatzuri does its best to keep this table
                  updated, but please note that the number and names of cookies,
                  pixels, and other technologies may change from time to time.
                </p>
              </div>
            </motion.section>

            {cookieTables.map((table, index) => (
              <motion.section
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.8 + index * 0.1 }}
                className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300"
              >
                <div className="flex items-center mb-4">
                  {table.icon}
                  <h3 className="text-xl font-semibold text-gray-700 dark:text-gray-200 ml-2">
                    {table.title}
                  </h3>
                </div>
                <div className="text-gray-600 dark:text-gray-300 space-y-4">
                  <p>{table.description}</p>

                  {table.links && (
                    <div className="space-y-2">
                      {table.links.map((link, i) => (
                        <p key={i}>
                          <a
                            href={link.url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-emerald-600 dark:text-emerald-400 hover:underline"
                          >
                            {link.text}
                          </a>
                        </p>
                      ))}
                    </div>
                  )}

                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                      <thead className="bg-gray-50 dark:bg-gray-700">
                        <tr>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Cookie Subgroup
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Cookies
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Cookies used
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                        {table.cookies.map((cookie, i) => (
                          <tr key={i}>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                              {cookie.subgroup}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                              {cookie.name}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                              {cookie.type}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              </motion.section>
            ))}

            <motion.section
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 1.1 }}
              className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300"
            >
              <h3 className="text-xl font-semibold text-gray-700 dark:text-gray-200 mb-3">
                How To Control and Delete Cookies
              </h3>
              <div className="text-gray-600 dark:text-gray-300 space-y-4">
                <div>
                  <h4 className="font-medium mb-2">1. Using Your Browser</h4>
                  <p>
                    Many of the cookies used on our website and through emails
                    can be enabled or disabled through our consent tool or by
                    disabling the cookies through your browser. To disable
                    cookies through your browser, follow the instructions
                    usually located within the &quot;Help,&quot;
                    &quot;Tools&quot; or &quot;Edit&quot; menus in your browser.
                    Please note that disabling a cookie or category of cookies
                    does not delete the cookie from your browser unless manually
                    completed through your browser function.
                  </p>
                </div>
                <div>
                  <h4 className="font-medium mb-2">
                    2. Cookies Set in the Past
                  </h4>
                  <p>
                    Collection of your data from our analytics cookies can be
                    deleted. If cookies are deleted, the information collected
                    prior to the preference change may still be used. However,
                    we will stop using the disabled cookie to collect any
                    further information from your user experience. For our
                    marketing cookie, when a user opts out of tracking, a new
                    cookie is placed to prevent users from being tracked.
                  </p>
                </div>
              </div>
            </motion.section>
          </div>
        </motion.div>
      </motion.main>
    </>
  );
}
