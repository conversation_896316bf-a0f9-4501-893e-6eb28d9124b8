import StatsPage from "@/components/stats/stats";
import Teams from "@/components/teams/teams";
import {
  Lightbulb,
  Users,
  Globe,
  BarChart2,
  Code,
  Rocket,
  ShieldCheck,
  MessageSquare,
  BrainCircuit,
} from "lucide-react";
import Image from "next/image";
import Link from "next/link";

export default function AboutPage() {
  return (
    <div className="">
      {/* Hero Section */}
      <section className="relative overflow-hidden bg-gradient-to-r from-emerald-600 to-emerald-800 dark:from-emerald-700 dark:to-emerald-900 text-white py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
            <div className="relative z-10">
              <h1 className="text-4xl md:text-5xl font-bold mb-6 leading-tight">
                Pioneering{" "}
                <span className="text-emerald-200">Conversational AI</span> That
                Understands
              </h1>
              <p className="text-xl mb-8 opacity-90">
                At Chatzuri, we&apos;re redefining how businesses communicate through
                intelligent, empathetic AI agents that learn and adapt.
              </p>
              <div className="flex flex-wrap gap-4">
                <Link
                  href="/help"
                  className="px-8 py-3 bg-white text-emerald-700 dark:text-emerald-900 font-medium rounded-lg hover:bg-gray-100 transition-colors flex items-center"
                >
                  Get in Touch
                  <Rocket className="w-5 h-5 ml-2" />
                </Link>
                <Link
                  href="/blogs"
                  className="px-8 py-3 border-2 border-white text-white font-medium rounded-lg hover:bg-white/10 transition-colors flex items-center"
                >
                  Our Blog
                  <MessageSquare className="w-5 h-5 ml-2" />
                </Link>
              </div>
            </div>
            <div className="relative h-80 md:h-96">
              <Image
                src="/images/ai-conversation-about.jpg"
                alt="AI Conversation"
                fill
                className="object-cover rounded-xl shadow-2xl"
                priority
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent rounded-xl"></div>
            </div>
          </div>
        </div>
        <div className="absolute -bottom-20 -right-20 w-64 h-64 rounded-full bg-emerald-500/20 blur-3xl"></div>
      </section>

      {/* Stats Section */}
      <StatsPage />

      {/* Our Story */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <span className="inline-block bg-emerald-100 dark:bg-emerald-900/50 text-emerald-600 dark:text-emerald-400 text-sm px-4 py-1 rounded-full mb-4">
              Our Journey
            </span>
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
              From Concept to{" "}
              <span className="text-emerald-600 dark:text-emerald-400">
                Conversational Revolution
              </span>
            </h2>
            <p className="max-w-3xl mx-auto text-lg text-gray-600 dark:text-gray-300">
              Founded in 2022, Chatzuri began with a simple vision: create AI
              that communicates as naturally as humans while maintaining
              accuracy and reliability.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                icon: Lightbulb,
                title: "The Spark",
                year: "2022",
                description:
                  "Recognized the gap in African-focused conversational AI solutions",
              },
              {
                icon: Code,
                title: "First Prototype",
                year: "2023 Q1",
                description:
                  "Built initial models with focus on local languages and contexts",
              },
              {
                icon: Users,
                title: "First Clients",
                year: "2023 Q2",
                description:
                  "Onboarded pioneering businesses in finance and healthcare",
              },
              {
                icon: BarChart2,
                title: "Growth Phase",
                year: "2023 Q4",
                description:
                  "Expanded team and improved accuracy metrics by 40%",
              },
              {
                icon: Globe,
                title: "Regional Expansion",
                year: "2024",
                description: "Launched services in 3 new African markets",
              },
              {
                icon: BrainCircuit,
                title: "Today",
                year: "2024+",
                description:
                  "Leading the conversation in ethical, effective AI communication",
              },
            ].map((item, index) => (
              <div
                key={index}
                className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-md hover:shadow-lg transition-shadow border border-gray-100 dark:border-gray-700"
              >
                <div className="w-12 h-12 bg-emerald-100 dark:bg-emerald-900/50 rounded-full flex items-center justify-center mb-4">
                  <item.icon className="w-6 h-6 text-emerald-600 dark:text-emerald-400" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-1">
                  {item.title}
                </h3>
                <p className="text-sm text-emerald-600 dark:text-emerald-400 mb-3">
                  {item.year}
                </p>
                <p className="text-gray-600 dark:text-gray-300">
                  {item.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="py-20 bg-emerald-50 dark:bg-emerald-900/10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
              Our{" "}
              <span className="text-emerald-600 dark:text-emerald-400">
                Core Values
              </span>
            </h2>
            <p className="max-w-3xl mx-auto text-lg text-gray-600 dark:text-gray-300">
              These principles guide every decision we make and every line of
              code we write.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              {
                icon: ShieldCheck,
                title: "Integrity",
                description:
                  "We prioritize ethical AI development and transparent practices",
              },
              {
                icon: Users,
                title: "Empathy",
                description:
                  "Our technology is designed to understand human nuance",
              },
              {
                icon: Globe,
                title: "Local Focus",
                description: "We build solutions tailored for African contexts",
              },
              {
                icon: Lightbulb,
                title: "Innovation",
                description:
                  "Continually pushing boundaries in conversational AI",
              },
            ].map((value, index) => (
              <div
                key={index}
                className="bg-white dark:bg-gray-800 p-8 rounded-xl shadow-sm text-center hover:shadow-md transition-shadow"
              >
                <div className="w-16 h-16 bg-emerald-100 dark:bg-emerald-900/30 rounded-full flex items-center justify-center mx-auto mb-6">
                  <value.icon className="w-8 h-8 text-emerald-600 dark:text-emerald-400" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
                  {value.title}
                </h3>
                <p className="text-gray-600 dark:text-gray-300">
                  {value.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <Teams />
          <div className="mt-8 text-center">
            <Link
              href="/careers"
              className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-emerald-600 hover:bg-emerald-700"
            >
              Join Our Team
              <Users className="w-5 h-5 ml-2" />
            </Link>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-emerald-600 to-emerald-800 dark:from-emerald-700 dark:to-emerald-900 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            Ready to Transform Your Customer Conversations?
          </h2>
          <p className="text-xl mb-8 max-w-3xl mx-auto opacity-90">
            Discover how Chatzuri&apos;s AI solutions can elevate your business
            communication.
          </p>
          <div className="flex flex-wrap justify-center gap-4">
            <Link
              href="/help"
              className="px-8 py-3 bg-white text-emerald-700 dark:text-emerald-900 font-medium rounded-lg hover:bg-gray-100 transition-colors flex items-center"
            >
              Get Started
              <Rocket className="w-5 h-5 ml-2" />
            </Link>
            <Link
              href="/demo"
              className="px-8 py-3 border-2 border-white text-white font-medium rounded-lg hover:bg-white/10 transition-colors flex items-center"
            >
              Request Demo
              <MessageSquare className="w-5 h-5 ml-2" />
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
}
