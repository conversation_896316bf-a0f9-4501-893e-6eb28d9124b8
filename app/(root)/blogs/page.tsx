import {
  <PERSON><PERSON><PERSON><PERSON>,
  ArrowR<PERSON>,
  User,
  CalendarDays,
  Lightbulb,
  Check,
  ChevronRight,
} from "lucide-react";
import Link from "next/link";
import Image from "next/image";
import BlogsSidebarLinks from "@/components/sidebar/blogs-sidebar-links";
import BlogsSidebar from "@/components/sidebar/blogs-sidebar";
import { CodeBlock } from "@/components/code-block/code-block";

export default function BlogPage() {
  return (
    <main className="transition-colors duration-300">
      {/* Featured Post Section */}
      <section className="relative overflow-hidden bg-gradient-to-r from-emerald-500 to-emerald-700 dark:from-emerald-600 dark:to-emerald-800 text-white py-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
            <div>
              <h1 className="text-4xl md:text-5xl font-bold mb-4 leading-tight ">
                Confronting AI Hallucinations: Turning Flaws into Features
              </h1>
              <p className="text-lg mb-6 opacity-90">
                Discover how <PERSON><PERSON><PERSON> transforms AI errors into opportunities
                for smarter conversations.
              </p>
              <div className="flex items-center space-x-4 text-sm">
                <span className="flex items-center">
                  <User className="w-4 h-4 mr-1" />
                  Lawrence
                </span>
                <span className="flex items-center">
                  <CalendarDays className="w-4 h-4 mr-1" />
                  January 20, 2024
                </span>
              </div>
              <Link
                href="#featured-post"
                className="mt-6 inline-flex items-center px-6 py-3 bg-white text-emerald-600 dark:text-emerald-800 font-medium rounded-lg hover:bg-gray-100 transition-colors"
              >
                Read More
                <ArrowRight className="w-4 h-4 ml-2" />
              </Link>
            </div>
            <div className="relative h-64 md:h-80 lg:h-96 rounded-xl overflow-hidden shadow-2xl">
              <Image
                src="/images/ai1.jpg"
                alt="AI Conversation"
                fill
                className="object-cover"
                priority
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
            </div>
          </div>
        </div>
      </section>

      {/* Quick Tags Section */}
      <BlogsSidebarLinks />

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Articles Column */}
        <div className="lg:col-span-2 space-y-12">
          {/* Featured Post */}
          <article
            id="featured-post"
            className="bg-white dark:bg-gray-800 rounded-xl shadow-md overflow-hidden transition-all hover:shadow-lg"
          >
            <div className="p-6 sm:p-8">
              <div className="flex items-center mb-4">
                <span className="bg-emerald-100 dark:bg-emerald-900/50 text-emerald-800 dark:text-emerald-200 text-xs px-3 py-1 rounded-full">
                  Featured
                </span>
                <span className="ml-3 text-sm text-gray-500 dark:text-gray-400 flex items-center">
                  <CalendarDays className="w-4 h-4 mr-1" />
                  January 20, 2024
                </span>
              </div>

              <h2 className="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white mb-4">
                Confronting AI Hallucinations: Turning Flaws into Features with
                Chatzuri
              </h2>

              <div className="prose dark:prose-invert max-w-none text-gray-600 dark:text-gray-300">
                <p className="lead">
                  Hallucinations—instances where AI chatbots generate incorrect
                  or irrelevant responses—aren&apos;t just hurdles; they&apos;re
                  opportunities for innovation. At Chatzuri, we&apos;re
                  pioneering techniques that minimize these errors, while
                  transforming occasional slips into teachable moments.
                </p>

                <p>
                  In this post, we&apos;ll explore advanced strategies like
                  human feedback loops, custom knowledge bases, and persistent
                  training, ensuring that your chatbot grows smarter with every
                  interaction.
                </p>

                <hr className="border-gray-200 dark:border-gray-700 my-6" />

                <p>
                  We&apos;ve witnessed brands turn these AI quirks into powerful
                  USPs: leveraging Chatzuri&apos;s platform to detect and
                  correct hallucinations in real-time, strengthening trust and
                  credibility. Whether you&apos;re just starting to build your
                  own agents or want to refine existing ones, let&apos;s push
                  beyond the hype and harness the true power of AI-enhanced
                  conversations.
                </p>

                <h3 className="text-xl font-semibold mt-8 mb-4 text-gray-800 dark:text-gray-200">
                  Blockquotes
                </h3>

                <blockquote className="border-l-4 border-emerald-500 pl-4 italic bg-gray-50 dark:bg-gray-700 p-4 rounded-r">
                  <p className="text-gray-700 dark:text-gray-300">
                    &quot;With Chatzuri, even our chatbot&apos;s rare missteps
                    become stepping stones to a more intelligent, empathetic
                    conversation.&quot;
                  </p>
                </blockquote>

                <p>
                  Ready to refine your chatbot&apos;s accuracy and user
                  satisfaction? Our{" "}
                  <Link
                    href="/contact"
                    className="text-emerald-600 dark:text-emerald-400 hover:underline"
                  >
                    contact page
                  </Link>{" "}
                  awaits your queries, and our{" "}
                  <Link
                    href="/pricing"
                    className="text-emerald-600 dark:text-emerald-400 hover:underline"
                  >
                    pricing
                  </Link>{" "}
                  page outlines how you can scale up without breaking the bank.
                </p>

                <h3 className="text-xl font-semibold mt-8 mb-4 text-gray-800 dark:text-gray-200">
                  Key Strategies
                </h3>

                <p>Key actions to take when addressing hallucinations:</p>

                <ul className="list-disc pl-6 space-y-2 marker:text-emerald-500">
                  <li>Integrate robust error-handling protocols</li>
                  <li>Continuously train with domain-specific data</li>
                  <li>Solicit human feedback for improved quality</li>
                </ul>

                <p className="mt-4">
                  Implement these steps, and you&apos;ll be ahead of the game:
                </p>

                <ol className="list-decimal pl-6 space-y-2 marker:font-semibold marker:text-emerald-500">
                  <li>Identify patterns in AI errors</li>
                  <li>Fine-tune models with targeted datasets</li>
                  <li>Monitor performance metrics consistently</li>
                </ol>

                <p className="mt-4">
                  By understanding the root causes, you&apos;ll transform
                  chatbot weaknesses into strengths, crafting dialogues that
                  delight rather than disappoint.
                </p>

                <div className="mt-6 grid gap-4 sm:grid-cols-2">
                  <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h4 className="font-semibold text-emerald-600 dark:text-emerald-400 flex items-center">
                      <Lightbulb className="w-5 h-5 mr-2" />
                      Natural Language Processing (NLP)
                    </h4>
                    <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
                      The core tech that enables Chatzuri to interpret and
                      respond to user queries accurately.
                    </p>
                  </div>

                  <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                    <h4 className="font-semibold text-emerald-600 dark:text-emerald-400 flex items-center">
                      <Lightbulb className="w-5 h-5 mr-2" />
                      Machine Learning
                    </h4>
                    <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
                      Adaptive algorithms that help your chatbot evolve over
                      time, mitigating hallucinations.
                    </p>
                  </div>
                </div>

                <div className="mt-4 bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                  <h4 className="font-semibold text-emerald-600 dark:text-emerald-400 flex items-center">
                    <Lightbulb className="w-5 h-5 mr-2" />
                    Knowledge Graphs
                  </h4>
                  <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
                    Structured data sources ensuring factually correct,
                    context-rich interactions.
                  </p>
                </div>

                <h3 className="text-xl font-semibold mt-8 mb-4 text-gray-800 dark:text-gray-200">
                  Best Practices
                </h3>

                <ul className="grid gap-3 sm:grid-cols-2">
                  <li className="flex items-start">
                    <span className="flex-shrink-0 bg-emerald-100 dark:bg-emerald-900/50 text-emerald-600 dark:text-emerald-400 p-1 rounded-full mr-3">
                      <Check className="w-4 h-4" />
                    </span>
                    <span className="text-gray-700 dark:text-gray-300">
                      <strong>Quality</strong> is king in AI responses
                    </span>
                  </li>
                  <li className="flex items-start">
                    <span className="flex-shrink-0 bg-emerald-100 dark:bg-emerald-900/50 text-emerald-600 dark:text-emerald-400 p-1 rounded-full mr-3">
                      <Check className="w-4 h-4" />
                    </span>
                    <span className="text-gray-700 dark:text-gray-300">
                      <em>Empathy</em> ensures user satisfaction
                    </span>
                  </li>
                  <li className="flex items-start">
                    <span className="flex-shrink-0 bg-emerald-100 dark:bg-emerald-900/50 text-emerald-600 dark:text-emerald-400 p-1 rounded-full mr-3">
                      <Check className="w-4 h-4" />
                    </span>
                    <span className="text-gray-700 dark:text-gray-300">
                      Use{" "}
                      <abbr
                        title="Artificial Intelligence"
                        className="no-underline"
                      >
                        AI
                      </abbr>{" "}
                      ethically and transparently
                    </span>
                  </li>
                  <li className="flex items-start">
                    <span className="flex-shrink-0 bg-emerald-100 dark:bg-emerald-900/50 text-emerald-600 dark:text-emerald-400 p-1 rounded-full mr-3">
                      <Check className="w-4 h-4" />
                    </span>
                    <span className="text-gray-700 dark:text-gray-300">
                      Always <cite>— Lawrence, Chatzuri</cite>
                    </span>
                  </li>
                </ul>

                <div className="mt-6 p-4 bg-emerald-50 dark:bg-emerald-900/20 border border-emerald-100 dark:border-emerald-800 rounded-lg">
                  <h4 className="font-semibold text-emerald-700 dark:text-emerald-300 mb-2">
                    Code Example
                  </h4>
                  <CodeBlock language="javascript">
                    {`const chatbot = "Chatzuri agent";
console.log("Empower your chatbot with Chatzuri!");`}
                  </CodeBlock>
                  <p className="mt-2 text-sm text-emerald-700 dark:text-emerald-300">
                    Ready to start building unstoppable agents? Our platform
                    awaits.
                  </p>
                </div>

                <div className="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700 flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="flex-shrink-0 w-[40px] h-[40px]">
                      <Image
                        src="/images/webp/female2.webp"
                        alt="Lawrence"
                        width={40}
                        height={40}
                        className="rounded-full"
                        placeholder="blur"
                        blurDataURL="/images/placeholder.jpg"
                      />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-900 dark:text-white">
                        Lawrence
                      </p>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        Chatzuri Founder
                      </p>
                    </div>
                  </div>
                  <Link
                    href="/blog/ai-hallucinations"
                    className="text-sm font-medium text-emerald-600 dark:text-emerald-400 hover:underline"
                  >
                    Read full article →
                  </Link>
                </div>
              </div>
            </div>
          </article>

          {/* Second Post */}
          <article className="bg-white dark:bg-gray-800 rounded-xl shadow-md overflow-hidden transition-all hover:shadow-lg">
            <div className="p-6 sm:p-8">
              <div className="flex items-center mb-4">
                <span className="text-sm text-gray-500 dark:text-gray-400 flex items-center">
                  <CalendarDays className="w-4 h-4 mr-1" />
                  January 18, 2024
                </span>
              </div>

              <h2 className="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white mb-4">
                Scaling Your Business with AI: Why Chatzuri Agents Are the
                Future
              </h2>

              <div className="prose dark:prose-invert max-w-none text-gray-600 dark:text-gray-300">
                <p>
                  As competition intensifies, scaling personalized customer
                  interactions becomes a monumental challenge. Chatzuri&apso;s
                  agent builder platform offers a frictionless way to handle
                  exponential growth without sacrificing quality. With robust
                  analytics, domain-specific tuning, and automated workflows,
                  you can serve thousands of users simultaneously—no human
                  burnout required.
                </p>

                <blockquote className="border-l-4 border-emerald-500 pl-4 italic bg-gray-50 dark:bg-gray-700 p-4 rounded-r mt-6">
                  <p className="text-gray-700 dark:text-gray-300">
                    &quot;Our retail clients saw conversion boosts of up to 30%
                    after integrating Chatzuri. It&apso;s not just automation;
                    it&apso;s evolution.&quot;
                  </p>
                </blockquote>

                <h3 className="text-xl font-semibold mt-8 mb-4 text-gray-800 dark:text-gray-200">
                  Performance Metrics
                </h3>

                <p>
                  Comparing user satisfaction metrics before and after Chatzuri
                  integration:
                </p>

                <div className="mt-4 overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead className="bg-gray-50 dark:bg-gray-700">
                      <tr>
                        <th
                          scope="col"
                          className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                        >
                          Metric
                        </th>
                        <th
                          scope="col"
                          className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                        >
                          Pre-Chatzuri
                        </th>
                        <th
                          scope="col"
                          className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                        >
                          Post-Chatzuri
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                      <tr>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                          Response Accuracy
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                          76%
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-emerald-600 dark:text-emerald-400 font-semibold">
                          95%
                        </td>
                      </tr>
                      <tr>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                          Customer Satisfaction
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                          68%
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-emerald-600 dark:text-emerald-400 font-semibold">
                          92%
                        </td>
                      </tr>
                      <tr>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                          Agent Load Handling
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                          500 requests/hour
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-emerald-600 dark:text-emerald-400 font-semibold">
                          5,000+ requests/hour
                        </td>
                      </tr>
                    </tbody>
                    <tfoot className="bg-gray-50 dark:bg-gray-700">
                      <tr>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                          Overall Improvement
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                          --
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-emerald-600 dark:text-emerald-400 font-semibold">
                          Significant
                        </td>
                      </tr>
                    </tfoot>
                  </table>
                </div>

                <p className="mt-6">
                  Interested in seeing how quickly you can scale? Check out our{" "}
                  <Link
                    href="/pricing"
                    className="text-emerald-600 dark:text-emerald-400 hover:underline"
                  >
                    pricing
                  </Link>{" "}
                  and get in touch via our{" "}
                  <Link
                    href="/contact"
                    className="text-emerald-600 dark:text-emerald-400 hover:underline"
                  >
                    contact
                  </Link>{" "}
                  page. Let&apos;s talk about elevating your brand&apos;s
                  conversational game.
                </p>

                <div className="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700 flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="flex-shrink-0 w-[40px] h-[40px]">
                      <Image
                        src="/images/webp/female2.webp"
                        alt="Lawrence"
                        width={40}
                        height={40}
                        className="rounded-full"
                        placeholder="blur"
                        blurDataURL="/images/placeholder.jpg"
                      />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-900 dark:text-white">
                        Lawrence
                      </p>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        Chatzuri Founder
                      </p>
                    </div>
                  </div>
                  <Link
                    href="/blog/scaling-with-ai"
                    className="flex gap-2 items-center text-sm font-medium text-emerald-600 dark:text-emerald-400 hover:underline"
                  >
                    Read full article <ChevronRight className="w-4 h-4" />
                  </Link>
                </div>
              </div>
            </div>
          </article>

          {/* Third Post */}
          <article className="bg-white dark:bg-gray-800 rounded-xl shadow-md overflow-hidden transition-all hover:shadow-lg">
            <div className="p-6 sm:p-8">
              <div className="flex items-center mb-4">
                <span className="text-sm text-gray-500 dark:text-gray-400 flex items-center">
                  <CalendarDays className="w-4 h-4 mr-1" />
                  January 17, 2024
                </span>
              </div>

              <h2 className="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white mb-4">
                Revolutionizing Customer Support: Chatzuri&apos;s Team
                Collaboration Tools
              </h2>

              <div className="prose dark:prose-invert max-w-none text-gray-600 dark:text-gray-300">
                <p>
                  Whether you&apos;re a startup or a Fortune 500 giant, cohesive
                  teamwork behind the scenes is critical. Chatzuri&apos;s
                  collaborative features allow multiple team members to design,
                  refine, and deploy conversational agents effortlessly. With
                  real-time analytics and shared workspaces, your support team
                  stays aligned and efficient.
                </p>

                <ul className="list-disc pl-6 space-y-2 marker:text-emerald-500 mt-4">
                  <li>Instantly assign team roles and permissions</li>
                  <li>
                    Share conversation transcripts for rapid feedback loops
                  </li>
                  <li>Continuously update FAQs and knowledge bases</li>
                </ul>

                <div className="mt-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600">
                  <h4 className="font-semibold text-emerald-600 dark:text-emerald-400 mb-2">
                    Team Collaboration Benefits
                  </h4>
                  <div className="grid gap-4 sm:grid-cols-2 mt-3">
                    <div className="flex items-start">
                      <span className="flex-shrink-0 bg-emerald-100 dark:bg-emerald-900/50 text-emerald-600 dark:text-emerald-400 p-1 rounded-full mr-3">
                        <Check className="w-4 h-4" />
                      </span>
                      <span className="text-sm text-gray-700 dark:text-gray-300">
                        Reduce resolution time by up to 40%
                      </span>
                    </div>
                    <div className="flex items-start">
                      <span className="flex-shrink-0 bg-emerald-100 dark:bg-emerald-900/50 text-emerald-600 dark:text-emerald-400 p-1 rounded-full mr-3">
                        <Check className="w-4 h-4" />
                      </span>
                      <span className="text-sm text-gray-700 dark:text-gray-300">
                        Improve agent onboarding speed
                      </span>
                    </div>
                    <div className="flex items-start">
                      <span className="flex-shrink-0 bg-emerald-100 dark:bg-emerald-900/50 text-emerald-600 dark:text-emerald-400 p-1 rounded-full mr-3">
                        <Check className="w-4 h-4" />
                      </span>
                      <span className="text-sm text-gray-700 dark:text-gray-300">
                        Maintain consistent brand voice
                      </span>
                    </div>
                    <div className="flex items-start">
                      <span className="flex-shrink-0 bg-emerald-100 dark:bg-emerald-900/50 text-emerald-600 dark:text-emerald-400 p-1 rounded-full mr-3">
                        <Check className="w-4 h-4" />
                      </span>
                      <span className="text-sm text-gray-700 dark:text-gray-300">
                        Centralize knowledge management
                      </span>
                    </div>
                  </div>
                </div>

                <p className="mt-6">
                  Ready to empower your support staff and delight your
                  customers? Visit our{" "}
                  <Link
                    href="/register"
                    className="text-emerald-600 dark:text-emerald-400 hover:underline"
                  >
                    registration page
                  </Link>{" "}
                  and start building your dream team with Chatzuri.
                </p>

                <div className="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700 flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="flex-shrink-0 w-[40px] h-[40px]">
                      <Image
                        src="/images/webp/female2.webp"
                        alt="Lawrence"
                        width={40}
                        height={40}
                        className="rounded-full"
                        placeholder="blur"
                        blurDataURL="/images/placeholder.jpg"
                      />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-900 dark:text-white">
                        Lawrence
                      </p>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        Chatzuri Founder
                      </p>
                    </div>
                  </div>
                  <Link
                    href="/blogs/customer-support"
                    className="flex items-center gap-2 text-sm font-medium text-emerald-600 dark:text-emerald-400 hover:underline"
                  >
                    Read full article <ChevronRight className="w-4 h-4" />
                  </Link>
                </div>
              </div>
            </div>
          </article>

          {/* Pagination */}
          <nav className="flex items-center justify-between border-t border-gray-200 dark:border-gray-700 pt-8">
            <Link
              href="#"
              className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700"
            >
              <ArrowLeft className="w-5 h-5 mr-2" />
              Older posts
            </Link>
            <span className="text-sm text-gray-500 dark:text-gray-400">
              Page 1 of 5
            </span>
            <button
              disabled
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-gray-400 bg-gray-100 dark:bg-gray-700 cursor-not-allowed"
            >
              Newer posts
              <ArrowRight className="w-5 h-5 ml-2" />
            </button>
          </nav>
        </div>

        {/* Sidebar */}
        <BlogsSidebar />
      </div>
    </main>
  );
}
