"use client";
import { motion } from "framer-motion";
import { BookOpen } from "lucide-react";

const ChatbotSettingsGuide = () => {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5,
      },
    },
  };

  return (
    <main className="bg-white dark:bg-gray-900">
      {/* Hero Section */}
      <motion.div
        className="relative isolate px-6 pt-14 lg:px-8"
        variants={itemVariants}
      >
        <div
          className="absolute inset-x-0 -top-40 -z-10 transform-gpu overflow-hidden blur-3xl sm:-top-80"
          aria-hidden="true"
        >
          <div
            className="relative left-[calc(50%-11rem)] aspect-[1155/678] w-[36.125rem] -translate-x-1/2 rotate-[30deg] bg-gradient-to-tr from-emerald-400 to-teal-600 opacity-30 sm:left-[calc(50%-30rem)] sm:w-[72.1875rem]"
            style={{
              clipPath:
                "polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)",
            }}
          ></div>
        </div>
        <div className="mx-auto max-w-2xl py-10 sm:py-12 lg:py-11">
          <div className="text-center">
            <motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-4xl font-semibold tracking-tight text-gray-900 dark:text-white sm:text-5xl md:text-6xl"
            >
              Chatbot settings
            </motion.h1>
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="mt-6 text-lg leading-8 text-gray-600 dark:text-gray-300 sm:text-xl"
            >
              Learn to navigate the Chatzuri chatbot settings, overcoming common challenges,
              improving response quality, and manage teams on Chatzuri.
            </motion.p>
            <div className="mt-6 flex justify-center">
              <BookOpen className="w-10 h-10 text-emerald-500 dark:text-emerald-400" />
            </div>
          </div>
        </div>
        <div
          className="absolute inset-x-0 top-[calc(100%-13rem)] -z-10 transform-gpu overflow-hidden blur-3xl sm:top-[calc(100%-30rem)]"
          aria-hidden="true"
        >
          <div
            className="relative left-[calc(50%+3rem)] aspect-[1155/678] w-[36.125rem] -translate-x-1/2 bg-gradient-to-tr from-emerald-400 to-teal-600 opacity-30 sm:left-[calc(50%+36rem)] sm:w-[72.1875rem]"
            style={{
              clipPath:
                "polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)",
            }}
          ></div>
        </div>
      </motion.div>

      {/* Content Section */}
      <motion.div
        initial="hidden"
        animate="visible"
        variants={containerVariants}
        className="container mx-auto px-4 py-8 max-w-4xl"
      >
        <article className="prose prose-lg dark:prose-invert prose-emerald max-w-none">
          <motion.section variants={itemVariants} className="mb-12">
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-6">General Settings</h2>
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              Your chatbotapos;s settings page is its command center, a place where you can make key adjustments to your
              bot&apos;s behavior and functionality. Letapos;s take a look at each setting and what it accomplishes.
            </p>

            <h3 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4">1. Open AI</h3>

            <div className="bg-emerald-50 dark:bg-emerald-900/20 p-4 rounded-lg mb-6">
              <h4 className="text-lg font-medium text-emerald-700 dark:text-emerald-400">Instructions</h4>
              <p className="text-gray-600 dark:text-gray-300">
                The instructions allow you to establish the nature of your chatbotapos&apos;s interactions and give your bot a
                personality. You can adjust and modify the instructions to better suit your needs.
              </p>
            </div>

            <div className="space-y-4 mb-6">
              <div>
                <h5 className="font-medium text-gray-900 dark:text-white">Modify the bot&apos;s personality</h5>
                <p className="text-gray-600 dark:text-gray-300">
                  If you&apos;d like your bot to have a casual and friendly tone, you can experiment with a phrase like this
                  in your instructions: &quot;You are a friendly and casual AI Assistant.&quot;
                </p>
              </div>
              <div>
                <h5 className="font-medium text-gray-900 dark:text-white">Change how the bot responds to unknown queries</h5>
                <p className="text-gray-600 dark:text-gray-300">
                  Instead of saying &quot;Hmm, I am not sure.&quot;, you might want it to say something like, &quot;I&apos;m sorry, I don&apos;t
                  have the information you&apos;re looking for, please contact customer support.&quot;
                </p>
              </div>
              <div>
                <h5 className="font-medium text-gray-900 dark:text-white">Direct its focus on certain topics</h5>
                <p className="text-gray-600 dark:text-gray-300">
                  If you want your bot to be a specialist in a certain area, you could add, &quot;You are an AI Assistant who
                  specializes in providing information about environmental sustainability.&quot;
                </p>
              </div>
              <div>
                <h5 className="font-medium text-gray-900 dark:text-white">Define its boundaries</h5>
                <p className="text-gray-600 dark:text-gray-300">
                  If you want to restrict your bot from providing certain types of information, you could specify, &quot;Do
                  not share financial advice or information.&quot;
                </p>
              </div>
            </div>

            <p className="text-gray-600 dark:text-gray-300 mb-6">
              By tailoring your instructions, you can ensure that your bot provides the necessary information, and does
              it in a way that aligns with your brand&apos;s voice and values. Furthermore, it can help your bot avoid
              hallucinations, and prevent it from answering questions outside the scope of the data provided.
            </p>

            <div className="bg-emerald-50 dark:bg-emerald-900/20 p-4 rounded-lg mb-6">
              <h4 className="text-lg font-medium text-emerald-700 dark:text-emerald-400">Model</h4>
              <p className="text-gray-600 dark:text-gray-300">
                This setting allows you to choose the AI model you prefer your chatbot to use. By default, it&apos;s set to
                the GPT-4o model, which uses one credit per message.
              </p>
            </div>

            <div className="grid grid-cols-2 gap-2 mb-6">
              <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
                <p className="text-gray-700 dark:text-gray-300">GPT-4</p>
              </div>
              <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
                <p className="text-gray-700 dark:text-gray-300">GPT-4 Turbo</p>
              </div>
              <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
                <p className="text-gray-700 dark:text-gray-300">GPT-3.5 Turbo</p>
              </div>
              <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
                <p className="text-gray-700 dark:text-gray-300">GPT-4o</p>
              </div>
              <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
                <p className="text-gray-700 dark:text-gray-300">GPT-4o Mini</p>
              </div>
            </div>

            <p className="text-gray-600 dark:text-gray-300 mb-6">
              If you use a GPT model but run out of credits and are on the Unlimited plan, you can use your personal
              Open AI API key. You can input your API key in the Account settings.
            </p>

            <div className="bg-emerald-50 dark:bg-emerald-900/20 p-4 rounded-lg mb-6">
              <h4 className="text-lg font-medium text-emerald-700 dark:text-emerald-400">Temperature</h4>
              <p className="text-gray-600 dark:text-gray-300">
                The temperature corresponds with the &quot;creativity&quot; of the bots responses. This value is set at zero, which
                instructs your chatbot to choose the most likely output when generating responses, resulting in more
                consistent and less random answers. You can adjust this number and experiment with the bot to fit your
                needs.
              </p>
            </div>
          </motion.section>

          {/* Other sections follow the same pattern */}
          
          <motion.nav 
            className="flex flex-wrap justify-center gap-4 mt-12"
            variants={itemVariants}
          >
            <motion.a
              href="/guides/get-started"
              className="px-6 py-2 rounded-full border border-gray-300 dark:border-gray-600 text-gray-800 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              Get started
            </motion.a>
            <motion.a
              href="/guides/response-quality"
              className="px-6 py-2 rounded-full border border-gray-300 dark:border-gray-600 text-gray-800 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              Response quality
            </motion.a>
            <motion.a
              href="/guides/custom-domains"
              className="px-6 py-2 rounded-full border border-gray-300 dark:border-gray-600 text-gray-800 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              Custom domains
            </motion.a>
            <motion.a
              href="/guides/manage-teams"
              className="px-6 py-2 rounded-full border border-gray-300 dark:border-gray-600 text-gray-800 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              Manage teams
            </motion.a>
            <motion.a
              href="/guides/integrations"
              className="px-6 py-2 rounded-full bg-emerald-500 hover:bg-emerald-600 dark:bg-emerald-600 dark:hover:bg-emerald-700 text-white"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              Integrations
            </motion.a>
          </motion.nav>
        </article>
      </motion.div>
    </main>
  );
};

export default ChatbotSettingsGuide;