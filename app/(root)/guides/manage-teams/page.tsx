"use client";
import { motion } from "framer-motion";
import { CheckCircle2, Clock, Settings, Users } from "lucide-react";
import Link from "next/link";

const TeamsGuide = () => {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5,
      },
    },
  };

  return (
    <main className="bg-white dark:bg-gray-900">
      {/* Hero Section */}
      <motion.div
        className="relative isolate px-6 pt-14 lg:px-8"
        variants={itemVariants}
      >
        <div
          className="absolute inset-x-0 -top-40 -z-10 transform-gpu overflow-hidden blur-3xl sm:-top-80"
          aria-hidden="true"
        >
          <div
            className="relative left-[calc(50%-11rem)] aspect-[1155/678] w-[36.125rem] -translate-x-1/2 rotate-[30deg] bg-gradient-to-tr from-emerald-400 to-teal-600 opacity-30 sm:left-[calc(50%-30rem)] sm:w-[72.1875rem]"
            style={{
              clipPath:
                "polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)",
            }}
          ></div>
        </div>
        <div className="mx-auto max-w-2xl py-10 sm:py-12 lg:py-11">
          <div className="text-center">
            <motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-4xl font-semibold tracking-tight text-gray-900 dark:text-white sm:text-5xl md:text-6xl"
            >
              Create and Manage Teams
            </motion.h1>
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="mt-6 text-lg leading-8 text-gray-600 dark:text-gray-300 sm:text-xl"
            >
              Organize your users in Chatzuri teams for better collaboration and
              permission control.
            </motion.p>
          </div>
        </div>
        <div
          className="absolute inset-x-0 top-[calc(100%-13rem)] -z-10 transform-gpu overflow-hidden blur-3xl sm:top-[calc(100%-30rem)]"
          aria-hidden="true"
        >
          <div
            className="relative left-[calc(50%+3rem)] aspect-[1155/678] w-[36.125rem] -translate-x-1/2 bg-gradient-to-tr from-emerald-400 to-teal-600 opacity-30 sm:left-[calc(50%+36rem)] sm:w-[72.1875rem]"
            style={{
              clipPath:
                "polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)",
            }}
          ></div>
        </div>
      </motion.div>

      {/* Content Section */}
      <motion.div
        initial="hidden"
        animate="visible"
        variants={containerVariants}
        className="container mx-auto px-4 py-8 max-w-4xl"
      >
        <article className="prose prose-lg dark:prose-invert prose-emerald max-w-none">
          <motion.section variants={itemVariants} className="mb-12">
            <h3 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4">
              1. Creating a Team
            </h3>
            <p className="text-gray-600 dark:text-gray-300 mb-4">
              Head over to your Chatzuri account dashboard, and click on the
              Settings tab at the top of the page.
            </p>
            <p className="text-gray-600 dark:text-gray-300 mb-4">
              All existing Chatzuri members have been migrated to a default
              Chatzuri team associated with their account name. You can see a
              list of your default team by clicking on the teams dropdown at the
              top left corner of the dashboard.
            </p>
            <p className="text-gray-600 dark:text-gray-300 mb-4">
              To create a new team, click on the Create Team button on the
              Select team dropdown menu.
            </p>
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              On the next page, provide a name for your team, a preferred URL
              (if you aren&apos;t comfortable with the auto-generated one), and
              then click on the Create button. The new team will be created and
              automatically added to your list of teams.
            </p>

            <h3 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4">
              2. Inviting Members to a Team
            </h3>

            <p className="text-gray-600 dark:text-gray-300 mb-4">
              From your Chatzuri account dashboard, click on the teams dropdown
              at the top left corner of the page and select the team you want to
              invite a member to.
            </p>
            <p className="text-gray-600 dark:text-gray-300 mb-4">
              On the team page, click on the Settings tab at the top of the
              page.
            </p>
            <p className="text-gray-600 dark:text-gray-300 mb-4">
              Click on Members on the left sidebar.
            </p>
            <p className="text-gray-600 dark:text-gray-300 mb-4">
              Click the Invite members button.
            </p>

            <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg my-3 italic">
              <p className="text-gray-600 dark:text-gray-300">
                Note: If the button is disabled and you get an error message
                saying: &quot;You have reached the maximum number of members for your
                plan,&quot; then you need to upgrade your plan for that Team. Each
                team on Chatzuri has its own chatbots, billing info, and billing
                plans. This means your plans are not shared across teams, and
                the privileges and limitations are unique to each team based on
                the plan. If you have any questions, please contact
                <a href="mailto:<EMAIL>" className="text-emerald-600 dark:text-emerald-800"><EMAIL>.</a>
              </p>
            </div>

            <p className="text-gray-600 dark:text-gray-300 mb-4">
              If the button is enabled, once you click on the Invite Members
              button, you will be prompted to provide an email address for the
              member to be invited.
            </p>
            <p className="text-gray-600 dark:text-gray-300 mb-4">
              Provide an email address and use the drop-down next to the email
              address input field to select the role you want the member to have
              and then click Send Invite(s).
            </p>

            <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg my-3 italic">
              <p className="text-gray-600 dark:text-gray-300">
                Note: There are distinct roles within a team – Owners and
                Members. Owners can change team settings, including billing
                info, plans, and team name, and can delete the team. They can
                also manage all the chatbots within the team. Members can ONLY
                manage the chatbots. This includes updating training data,
                seeing analytics, and deleting chatbots, but they cannot alter
                team settings.
              </p>
            </div>

            <p className="text-gray-600 dark:text-gray-300 mb-6">
              Once an invitee receives the invite email from Chatzuri,
              they&apos;ll be able to follow the provided link to join the team.
              The invited members would have to create a Chatzuri account to be
              able to log into the Chatzuri platform and access the team.
            </p>

            <div className="bg-emerald-50 dark:bg-emerald-900/20 p-4 rounded-lg mb-6">
              <h4 className="font-medium text-emerald-700 dark:text-emerald-400 mb-2">
                Remember
              </h4>
              <ul className="space-y-3 text-gray-600 dark:text-gray-300">
                <li className="flex items-start gap-3">
                  <CheckCircle2 className="w-5 h-5 mt-0.5 text-emerald-500 dark:text-emerald-400 flex-shrink-0" />
                  <span>
                    Each team has its own chatbots, billing information, and
                    plan. These are not shared between teams.
                  </span>
                </li>
                <li className="flex items-start gap-3">
                  <Settings className="w-5 h-5 mt-0.5 text-emerald-500 dark:text-emerald-400 flex-shrink-0" />
                  <span>
                    Owners can change team settings (billing, plan, name),
                    delete the team, and manage all chatbots within the team.
                  </span>
                </li>
                <li className="flex items-start gap-3">
                  <Users className="w-5 h-5 mt-0.5 text-emerald-500 dark:text-emerald-400 flex-shrink-0" />
                  <span>
                    Members can only manage chatbots (train them, see data,
                    delete them). They cannot change team settings.
                  </span>
                </li>
                <li className="flex items-start gap-3">
                  <Clock className="w-5 h-5 mt-0.5 text-emerald-500 dark:text-emerald-400 flex-shrink-0" />
                  <span>
                    Invite links expire 24 hours after it has been sent to an
                    invitee.
                  </span>
                </li>
              </ul>
            </div>
          </motion.section>

          <motion.nav
            className="flex flex-wrap justify-center gap-4 mt-12"
            variants={itemVariants}
          >
            <Link
              href="/guides/get-started"
              className="px-6 py-2 rounded-full border border-gray-300 dark:border-gray-600 text-gray-800 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700"
            >
              Get started
            </Link>
            <Link
              href="/guides/chatbot-settings"
              className="px-6 py-2 rounded-full border border-gray-300 dark:border-gray-600 text-gray-800 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700"
            >
              Chatbot settings
            </Link>
            <Link
              href="/guides/response-quality"
              className="px-6 py-2 rounded-full border border-gray-300 dark:border-gray-600 text-gray-800 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700"
            >
              Response quality
            </Link>
            <Link
              href="/guides/custom-domains"
              className="px-6 py-2 rounded-full border border-gray-300 dark:border-gray-600 text-gray-800 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700"
            >
              Custom domains
            </Link>
            <Link
              href="/guides/integrations"
              className="px-6 py-2 rounded-full bg-emerald-500 hover:bg-emerald-600 dark:bg-emerald-600 dark:hover:bg-emerald-700 text-white"
            >
              Integrations
            </Link>
          </motion.nav>
        </article>
      </motion.div>
    </main>
  );
};

export default TeamsGuide;
