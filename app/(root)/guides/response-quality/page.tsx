"use client";
import { motion } from "framer-motion";
import { BookOpen } from "lucide-react";
import Link from "next/link";

const ResponseQualityGuide = () => {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5,
      },
    },
  };

  return (
    <main className="bg-white dark:bg-gray-900">
      {/* Hero Section */}
      <motion.div
        className="relative isolate px-6 pt-14 lg:px-8"
        variants={itemVariants}
      >
        <div
          className="absolute inset-x-0 -top-40 -z-10 transform-gpu overflow-hidden blur-3xl sm:-top-80"
          aria-hidden="true"
        >
          <div
            className="relative left-[calc(50%-11rem)] aspect-[1155/678] w-[36.125rem] -translate-x-1/2 rotate-[30deg] bg-gradient-to-tr from-emerald-400 to-teal-600 opacity-30 sm:left-[calc(50%-30rem)] sm:w-[72.1875rem]"
            style={{
              clipPath:
                "polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)",
            }}
          ></div>
        </div>
        <div className="mx-auto max-w-2xl py-10 sm:py-12 lg:py-11">
          <div className="text-center">
            <motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-4xl font-semibold tracking-tight text-gray-900 dark:text-white sm:text-5xl md:text-6xl"
            >
              Response quality
            </motion.h1>
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="mt-6 text-lg leading-8 text-gray-600 dark:text-gray-300 sm:text-xl"
            >
              With Chatzuri, you have multiple ways to mitigate this issue and
              fine-tune your chatbot&apos;s responses.
            </motion.p>
            <div className="mt-6 flex justify-center">
              <BookOpen className="w-10 h-10 text-emerald-500 dark:text-emerald-400" />
            </div>
          </div>
        </div>
        <div
          className="absolute inset-x-0 top-[calc(100%-13rem)] -z-10 transform-gpu overflow-hidden blur-3xl sm:top-[calc(100%-30rem)]"
          aria-hidden="true"
        >
          <div
            className="relative left-[calc(50%+3rem)] aspect-[1155/678] w-[36.125rem] -translate-x-1/2 bg-gradient-to-tr from-emerald-400 to-teal-600 opacity-30 sm:left-[calc(50%+36rem)] sm:w-[72.1875rem]"
            style={{
              clipPath:
                "polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)",
            }}
          ></div>
        </div>
      </motion.div>

      {/* Content Section */}
      <motion.div
        initial="hidden"
        animate="visible"
        variants={containerVariants}
        className="container mx-auto px-4 py-8 max-w-4xl"
      >
        <article className="prose prose-lg dark:prose-invert prose-emerald max-w-none">
          <motion.section variants={itemVariants} className="mb-12">
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-6">
              General Settings
            </h2>
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              Your chatbot&apos;s settings page is its command center, a place where
              you can make key adjustments to your bot&apos;s behavior and
              functionality. Let&apos;s take a look at each setting and what it
              accomplishes.
            </p>

            <h3 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4">
              1. Refine the Chatbot&apos;s Instructions
            </h3>
            <p className="text-gray-600 dark:text-gray-300 mb-4">
              The instructions shape your chatbot&apos;s behavior and responses. To
              ensure your bot only answers questions about the given document,
              specify this in the instructions.
            </p>

            <div className="bg-emerald-50 dark:bg-emerald-900/20 p-4 rounded-lg mb-6 italic">
              <p className="text-gray-600 dark:text-gray-300">
                &quot;I want you to act as a support agent. Your name is &quot;AI
                Assistant&quot;. You will provide me with answers from the given
                info. If the answer is not included, say exactly &quot;Hmm, I am not
                sure.&quot; and stop after that. Refuse to answer any question not
                about the info. Never break character.&quot;
              </p>
            </div>

            <p className="text-gray-600 dark:text-gray-300 mb-6">
              You can find more information about the instructions in the
              previous article,{" "}
              <Link
                href="/guides/chatbot-settings"
                className="text-emerald-600 dark:text-emerald-400 hover:underline"
              >
                Chatbot Settings
              </Link>
            </p>

            <h3 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4">
              2. Ensure Readability of Uploaded Data Sources
            </h3>
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              The quality of your chatbot&apos;s responses largely depends on the
              quality of the data sources you provide. Chatzuri uses readable
              text to generate responses, so ensure that the websites or PDFs
              you upload contain readable text. Note that Chatzuri can&apos;t process
              images, videos, or non-textual elements in documents.
            </p>
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              Some websites are not scraper friendly, so if you see your chatbot
              is unable to answer questions on your website, this might be the
              case. You can work-around this by copy and pasting information as
              text into the data sources, or uploading it as a PDF instead.
            </p>

            <h3 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4">
              Utilize the &quot;Revise&quot; Feature and Q&A Data Type
            </h3>
            <p className="text-gray-600 dark:text-gray-300 mb-4">
              The &quot;revise&quot; feature is accessible from the dashboard in your
              conversation history. It is a tool for tweaking responses.
            </p>
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              If you&apos;re not satisfied with how your chatbot answered a
              particular query, you can use this feature to alter the response
              to fix it for the future. Additionally, using the Q&A data type
              can help your chatbot generate better answers by referring to
              pre-set questions and answers. The responses you revise will
              appear in the Q&A tab under &quot;Manage&quot;.
            </p>

            <h3 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4">
              3. Leverage the Power of GPT-4o
            </h3>
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              If you want your chatbot to generate more nuanced and
              sophisticated responses, consider using the GPT-4o model. It is
              the most sophisticated GPT model available, producing more
              accurate and contextually aware responses. You can change what
              language model you use.
            </p>

            <h3 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4">
              4. Create a Document Mapping Website URLs to Page Names
            </h3>
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              If you notice your bot produces fake URLs that lead to 404 errors,
              try to make a PDF document that maps all of the correct URLs with
              the page names. This can be very helpful if your chatbot operates
              on a website with multiple pages. Having a document that maps URLs
              to page names can help your chatbot better understand user queries
              related to different pages.
            </p>
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              Additionally, you can add these links in Q&A format, and follow
              the suggestions previously mentioned.
            </p>

            <h3 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4">
              5. Next steps
            </h3>
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              By implementing these strategies, you can significantly enhance
              your Chatzuri chatbot&apos;s ability to provide useful responses,
              leading to more successful interactions.
            </p>
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              Check out our next article on Optimizing Chatzuri Chatbot
              Responses for more tips on optimizing user experience with
              Chatzuri.
            </p>
          </motion.section>

          <motion.nav
            className="flex flex-wrap justify-center gap-4 mt-12"
            variants={itemVariants}
          >
            <Link
              href="/guides/get-started"
              className="px-6 py-2 rounded-full border border-gray-300 dark:border-gray-600 text-gray-800 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700"
            >
              Get started
            </Link>
            <Link
              href="/guides/chatbot-settings"
              className="px-6 py-2 rounded-full border border-gray-300 dark:border-gray-600 text-gray-800 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700"
            >
              Chatbot settings
            </Link>
            <Link
              href="/guides/custom-domains"
              className="px-6 py-2 rounded-full border border-gray-300 dark:border-gray-600 text-gray-800 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700"
            >
              Custom domains
            </Link>
            <Link
              href="/guides/manage-teams"
              className="px-6 py-2 rounded-full border border-gray-300 dark:border-gray-600 text-gray-800 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700"
            >
              Manage teams
            </Link>
            <Link
              href="/guides/integrations"
              className="px-6 py-2 rounded-full bg-emerald-500 hover:bg-emerald-600 dark:bg-emerald-600 dark:hover:bg-emerald-700 text-white"
            >
              Integrations
            </Link>
          </motion.nav>
        </article>
      </motion.div>
    </main>
  );
};

export default ResponseQualityGuide;
