"use client";
import { motion } from "framer-motion";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Zap } from "lucide-react";
import { useTheme } from "next-themes";
import Link from "next/link";

const GuidesSection = () => {
  const { theme } = useTheme();
  const isDark = theme === "dark";

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  };

  const cardVariants = {
    hover: {
      y: -5,
      transition: {
        type: "spring",
        stiffness: 300,
      },
    },
  };

  return (
    <div className="container mx-auto px-4 py-12">
      <motion.div
        initial="hidden"
        animate="visible"
        variants={containerVariants}
      >
        {/* Hero Section */}
        <div className="relative isolate px-6 pt-14 lg:px-8">
          <div
            className="absolute inset-x-0 -top-40 -z-10 transform-gpu overflow-hidden blur-3xl sm:-top-80"
            aria-hidden="true"
          >
            <div
              className="relative left-[calc(50%-11rem)] aspect-1155/678 w-[36.125rem] -translate-x-1/2 rotate-[30deg] bg-linear-to-tr from-[#ff80b5] to-[#00a67e] opacity-30 sm:left-[calc(50%-30rem)] sm:w-[72.1875rem]"
              style={{
                backgroundImage: `linear-gradient(rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.3)),url('/images/affiliate-bg.jpg')`,
                clipPath:
                  "polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)",
                WebkitClipPath:
                  "polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)",
              }}
            ></div>
          </div>
          <div className="mx-auto max-w-2xl py-10 sm:py-12 lg:py-11">
            <div className="text-center">
              <motion.h1
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                className="dark:text-white text-4xl font-semibold tracking-tight text-balance text-gray-900 sm:text-7xl"
              >
                Guides & Tutorials
              </motion.h1>
              <motion.p
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="dark:text-white mt-8 text-lg font-medium text-pretty text-gray-500 sm:text-xl/8"
              >
                A collection of deep-dive guides to help you make the most of
                Chatzuri.
              </motion.p>
              <div className="mt-3 flex justify-center">
                <BookOpen className="w-10 h-10 text-emerald-500" />
              </div>
            </div>
          </div>
          <div
            className="absolute inset-x-0 top-[calc(100%-13rem)] -z-10 transform-gpu overflow-hidden blur-3xl sm:top-[calc(100%-30rem)]"
            aria-hidden="true"
          >
            <div
              className="relative left-[calc(50%+3rem)] aspect-1155/678 w-[36.125rem] -translate-x-1/2 bg-linear-to-tr from-[#ff80b5] to-[#9089fc] opacity-30 sm:left-[calc(50%+36rem)] sm:w-[72.1875rem]"
              style={{
                clipPath:
                  "polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)",
                WebkitClipPath:
                  "polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)",
              }}
            ></div>
          </div>
        </div>

        {/* Cards Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Getting Started Card */}
          <motion.div
            whileHover="hover"
            variants={cardVariants}
            className="h-full p-6 rounded-2xl bg-white dark:bg-gradient-to-br dark:from-gray-800 dark:to-gray-900 border border-gray-200 dark:border-gray-700 shadow-lg"

          >
            <div className="flex items-center mb-4">
              <div
                className={`p-3 rounded-full ${
                  isDark ? "bg-emerald-900/30" : "bg-emerald-100"
                }`}
              >
                <Settings className="w-6 h-6 text-emerald-500" />
              </div>
              <h2 className="text-2xl font-bold ml-3 text-gray-900 dark:text-white">
                Getting started
              </h2>
            </div>
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              Here, you&apos;ll find helpful insights on navigating the Chatzuri
              chatbot settings, overcoming common challenges, and improving
              response quality to avoid issues like incorrect or irrelevant
              answers.
            </p>
            <Link
              href="/guides/get-started"
              className={`inline-flex items-center px-5 py-3 rounded-lg font-medium ${
                isDark
                  ? "bg-emerald-600 hover:bg-emerald-700 text-white"
                  : "bg-emerald-500 hover:bg-emerald-600 text-white"
              }`}
            >
              Let&apos;s go
              <ArrowRight className="ml-2 w-4 h-4" />
            </Link>
          </motion.div>

          {/* Integrations Card */}
          <motion.div
            whileHover="hover"
            variants={cardVariants}
            className={`h-full p-6 rounded-2xl ${
              isDark
                ? "bg-gradient-to-br from-gray-800 to-gray-900 border border-gray-700"
                : "bg-white border border-gray-200"
            } shadow-lg`}
          >
            <div className="flex items-center mb-4">
              <div
                className={`p-3 rounded-full ${
                  isDark ? "bg-emerald-900/30" : "bg-emerald-100"
                }`}
              >
                <Zap className="w-6 h-6 text-emerald-500" />
              </div>
              <h2 className="text-2xl font-bold ml-3 text-gray-900 dark:text-white">
                Integrations
              </h2>
            </div>
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              Step-by-step guides for connecting your Chatzuri chatbot with
              WhatsApp, Zapier, Slack, WordPress, and more. Easily embed and
              manage your chatbot across platforms.
            </p>
            <Link
              href="/guides/integrations"
              className={`inline-flex items-center px-5 py-3 rounded-lg font-medium ${
                isDark
                  ? "bg-emerald-600 hover:bg-emerald-700 text-white"
                  : "bg-emerald-500 hover:bg-emerald-600 text-white"
              }`}
            >
              Let&apos;s go
              <ArrowRight className="ml-2 w-4 h-4" />
            </Link>
          </motion.div>
        </div>
      </motion.div>
    </div>
  );
};

export default GuidesSection;
