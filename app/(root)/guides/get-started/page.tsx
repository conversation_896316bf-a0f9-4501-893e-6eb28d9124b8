"use client";
import { itemVariants } from "@/variants/variants";
import { motion } from "framer-motion";
import {
  <PERSON>R<PERSON>,
  BookOpen,
  Settings,
  MessageSquare,
  Users,
  Globe,
} from "lucide-react";
import Link from "next/link";

const GetStartedGuide = () => {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  };

  const cardVariants = {
    hover: {
      y: -5,
      transition: {
        type: "spring",
        stiffness: 300,
      },
    },
  };

  const mergedVariants = {
    ...itemVariants,
    ...cardVariants,
  };
  return (
    <main className="container mx-auto px-4 py-8">
      <motion.div
        initial="hidden"
        animate="visible"
        variants={containerVariants}
      >
        {/* Hero Section */}
        <motion.div
          className="relative isolate px-6 pt-14 lg:px-8"
          variants={itemVariants}
        >
          <div
            className="absolute inset-x-0 -top-40 -z-10 transform-gpu overflow-hidden blur-3xl sm:-top-80"
            aria-hidden="true"
          >
            <div
              className="relative left-[calc(50%-11rem)] aspect-1155/678 w-[36.125rem] -translate-x-1/2 rotate-[30deg] bg-linear-to-tr from-[#ff80b5] to-[#00a67e] opacity-30 sm:left-[calc(50%-30rem)] sm:w-[72.1875rem]"
              style={{
                backgroundImage: `linear-gradient(rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.3)),url('/images/affiliate-bg.jpg')`,
                clipPath:
                  "polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)",
                WebkitClipPath:
                  "polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)",
              }}
            ></div>
          </div>
          <div className="mx-auto max-w-2xl py-10 sm:py-12 lg:py-11">
            <div className="text-center">
              <motion.h1
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                className="dark:text-white text-4xl font-semibold tracking-tight text-balance text-gray-900 sm:text-7xl"
              >
                Get started
              </motion.h1>
              <motion.p
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="dark:text-white mt-8 text-lg font-medium text-pretty text-gray-500 sm:text-xl/8"
              >
                Learn to navigate the Chatzuri chatbot settings, overcoming
                common challenges, improving response quality, and manage teams
                on Chatzuri..
              </motion.p>
              <div className="mt-3 flex justify-center">
                <BookOpen className="w-10 h-10 text-emerald-500" />
              </div>
            </div>
          </div>
          <div
            className="absolute inset-x-0 top-[calc(100%-13rem)] -z-10 transform-gpu overflow-hidden blur-3xl sm:top-[calc(100%-30rem)]"
            aria-hidden="true"
          >
            <div
              className="relative left-[calc(50%+3rem)] aspect-1155/678 w-[36.125rem] -translate-x-1/2 bg-linear-to-tr from-[#ff80b5] to-[#9089fc] opacity-30 sm:left-[calc(50%+36rem)] sm:w-[72.1875rem]"
              style={{
                clipPath:
                  "polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)",
                WebkitClipPath:
                  "polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)",
              }}
            ></div>
          </div>
        </motion.div>

        {/* Cards Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          {/* Chatbot Settings Card */}
          <motion.div
            variants={mergedVariants}
            whileHover="hover"
            className="h-full p-6 rounded-2xl bg-white border border-gray-200 dark:bg-gray-800 dark:border-gray-700 shadow-lg"
          >
            <div className="flex items-center mb-4">
              <div className="p-3 rounded-full bg-emerald-100 dark:bg-emerald-900/30">
                <Settings className="w-6 h-6 text-emerald-500 dark:text-emerald-400" />
              </div>
              <h2 className="text-2xl font-bold ml-3 text-gray-900 dark:text-white">
                Chatbot settings
              </h2>
            </div>
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              This guide will help you navigate the Chatzuri chatbot settings
              page and provide insight on how to overcome some common
              challenges.
            </p>
            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <Link
                href="/guides/chatbot-settings"
                className="inline-flex items-center px-5 py-3 rounded-lg font-medium bg-emerald-500 hover:bg-emerald-600 dark:bg-emerald-600 dark:hover:bg-emerald-700 text-white"
              >
                Let&apos;s go
                <ArrowRight className="ml-2 w-4 h-4" />
              </Link>
            </motion.div>
          </motion.div>

          {/* Response Quality Card */}
          <motion.div
            variants={mergedVariants}
            whileHover="hover"
            className="h-full p-6 rounded-2xl bg-white border border-gray-200 dark:bg-gray-800 dark:border-gray-700 shadow-lg"
          >
            <div className="flex items-center mb-4">
              <div className="p-3 rounded-full bg-emerald-100 dark:bg-emerald-900/30">
                <MessageSquare className="w-6 h-6 text-emerald-500 dark:text-emerald-400" />
              </div>
              <h2 className="text-2xl font-bold ml-3 text-gray-900 dark:text-white">
                Response quality
              </h2>
            </div>
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              AI chatbots can sometimes generate incorrect or irrelevant
              responses, known as &quot;hallucinations&quot;. This guide addresses how to
              handle such issues.
            </p>
            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <Link
                href="/guides/response-quality"
                className="inline-flex items-center px-5 py-3 rounded-lg font-medium bg-emerald-500 hover:bg-emerald-600 dark:bg-emerald-600 dark:hover:bg-emerald-700 text-white"
              >
                Let&apos;s go
                <ArrowRight className="ml-2 w-4 h-4" />
              </Link>
            </motion.div>
          </motion.div>
        </div>

        {/* Second Row of Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          {/* Custom Domains Card */}
          <motion.div
            variants={mergedVariants}
            whileHover="hover"
            className="h-full p-6 rounded-2xl bg-white border border-gray-200 dark:bg-gray-800 dark:border-gray-700 shadow-lg"
          >
            <div className="flex items-center mb-4">
              <div className="p-3 rounded-full bg-emerald-100 dark:bg-emerald-900/30">
                <Globe className="w-6 h-6 text-emerald-500 dark:text-emerald-400" />
              </div>
              <h2 className="text-2xl font-bold ml-3 text-gray-900 dark:text-white">
                Custom Domains
              </h2>
            </div>
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              How to add and configure a custom domain on Chatzuri
            </p>
            <Link
              href="/guides/custom-domains"
              className="inline-flex items-center px-5 py-3 rounded-lg font-medium bg-emerald-500 hover:bg-emerald-600 dark:bg-emerald-600 dark:hover:bg-emerald-700 text-white"
            >
              Let&apos;s go
              <ArrowRight className="ml-2 w-4 h-4" />
            </Link>
          </motion.div>

          {/* Manage Teams Card */}
          <motion.div
            variants={mergedVariants}
            whileHover="hover"
            className="h-full p-6 rounded-2xl bg-white border border-gray-200 dark:bg-gray-800 dark:border-gray-700 shadow-lg"
          >
            <div className="flex items-center mb-4">
              <div className="p-3 rounded-full bg-emerald-100 dark:bg-emerald-900/30">
                <Users className="w-6 h-6 text-emerald-500 dark:text-emerald-400" />
              </div>
              <h2 className="text-2xl font-bold ml-3 text-gray-900 dark:text-white">
                Create and Manage Teams
              </h2>
            </div>
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              How to create and manage teams on Chatzuri
            </p>
            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <Link
                href="/guides/manage-teams"
                className="inline-flex items-center px-5 py-3 rounded-lg font-medium bg-emerald-500 hover:bg-emerald-600 dark:bg-emerald-600 dark:hover:bg-emerald-700 text-white"
              >
                Let&apos;s go
                <ArrowRight className="ml-2 w-4 h-4" />
              </Link>
            </motion.div>
          </motion.div>
        </div>

        {/* Pagination */}
        <motion.nav
          className="flex flex-wrap justify-center gap-4 mb-8"
          variants={itemVariants}
        >
          <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
            <Link
              href="/guides"
              className="px-6 py-2 rounded-full border border-gray-300 dark:border-gray-600 text-gray-800 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700"
            >
              All guides
            </Link>
          </motion.div>

          <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
            <Link
              href="/guides/integrations"
              className="px-6 py-2 rounded-full bg-emerald-500 hover:bg-emerald-600 dark:bg-emerald-600 dark:hover:bg-emerald-700 text-white"
            >
              Integrations
            </Link>
          </motion.div>
        </motion.nav>
      </motion.div>
    </main>
  );
};

export default GetStartedGuide;
