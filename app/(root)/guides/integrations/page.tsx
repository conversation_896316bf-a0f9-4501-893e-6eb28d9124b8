"use client";
import { motion } from "framer-motion";
import Link from "next/link";
import {
  Zap,
  MessageSquare,
  ShoppingCart,
  Wrench,
  Slack,
  Grid,
  Layout,
  Instagram,
  Mail,
} from "lucide-react";
import { FaWordpress } from "react-icons/fa";

const IntegrationsPage = () => {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5,
      },
    },
  };

  const integrations = [
    {
      title: "Zapier",
      description:
        "A Step-by-step guide on how to integrate your Chatzuri bot with Zapier",
      href: "/guides/zapier",
      icon: <Zap className="w-8 h-8" />,
      bgColor: "bg-gradient-to-br from-purple-500 to-indigo-600",
      textColor: "text-white",
      buttonVariant: "outline-light",
    },
    {
      title: "WhatsApp",
      description:
        "This guide provides a step-by-step process for integrating WhatsApp with Chatzuri",
      href: "/guides/whatsapp",
      icon: <MessageSquare className="w-8 h-8" />,
      bgColor: "bg-gradient-to-br from-green-500 to-emerald-600",
      textColor: "text-white",
      buttonVariant: "outline-light",
    },
    {
      title: "WordPress",
      description:
        "Embed a Chatzuri chatbot on your WordPress website and WooCommerce store",
      href: "/guides/wordpress",
      icon: <FaWordpress className="w-8 h-8" />,
      bgColor: "bg-gradient-to-br from-blue-500 to-sky-600",
      textColor: "text-white",
      buttonVariant: "outline-light",
    },
    {
      title: "Shopify",
      description:
        "How to add a Chatzuri chatbot to your Shopify website or store",
      href: "/guides/shopify",
      icon: <ShoppingCart className="w-8 h-8" />,
      bgColor: "bg-gradient-to-br from-amber-500 to-orange-500",
      textColor: "text-white",
      buttonVariant: "outline-light",
    },
    {
      title: "Wix",
      description:
        "A quick guide on how to add a Chatzuri chatbot on your Wix website",
      href: "/guides/wix",
      icon: <Wrench className="w-8 h-8" />,
      bgColor: "bg-gradient-to-br from-red-400 to-pink-500",
      textColor: "text-white",
      buttonVariant: "outline-light",
    },
    {
      title: "Slack",
      description:
        "Add a Chatzuri chatbot to Slack for seamless team communication",
      href: "/guides/slack",
      icon: <Slack className="w-8 h-8" />,
      bgColor: "bg-gradient-to-br from-purple-400 to-fuchsia-600",
      textColor: "text-white",
      buttonVariant: "outline-light",
    },
    {
      title: "Bubble",
      description: "How to embed a Chatzuri chatbot on your Bubble web app",
      href: "/guides/bubble",
      icon: <Grid className="w-8 h-8" />,
      bgColor: "bg-gradient-to-br from-blue-400 to-cyan-500",
      textColor: "text-white",
      buttonVariant: "outline-light",
    },
    {
      title: "Webflow",
      description: "How to add a Chatzuri chatbot to your Webflow website",
      href: "/guides/webflow",
      icon: <Layout className="w-8 h-8" />,
      bgColor: "bg-gradient-to-br from-indigo-400 to-violet-600",
      textColor: "text-white",
      buttonVariant: "outline-light",
    },
    {
      title: "Instagram",
      description:
        "Step-by-step process for integrating Instagram with Chatzuri",
      href: "/guides/instagram",
      icon: <Instagram className="w-8 h-8" />,
      bgColor: "bg-gradient-to-br from-pink-500 to-rose-600",
      textColor: "text-white",
      buttonVariant: "outline-light",
    },
    {
      title: "Messenger",
      description:
        "Step-by-step process for integrating Messenger with Chatzuri",
      href: "/guides/messenger",
      icon: <Mail className="w-8 h-8" />,
      bgColor: "bg-gradient-to-br from-blue-400 to-sky-600",
      textColor: "text-white",
      buttonVariant: "outline-light",
    },
  ];

  return (
    <main className="bg-white dark:bg-gray-900">
      {/* Hero Section */}
      <motion.div
        className="relative isolate px-6 pt-20 pb-16 lg:px-8"
        initial="hidden"
        animate="visible"
        variants={containerVariants}
      >
        <div
          className="absolute inset-x-0 -top-40 -z-10 transform-gpu overflow-hidden blur-3xl sm:-top-80"
          aria-hidden="true"
        >
          <div
            className="relative left-[calc(50%-11rem)] aspect-[1155/678] w-[36.125rem] -translate-x-1/2 rotate-[30deg] bg-gradient-to-tr from-emerald-400 to-teal-600 opacity-30 sm:left-[calc(50%-30rem)] sm:w-[72.1875rem]"
            style={{
              clipPath:
                "polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)",
            }}
          />
        </div>

        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <motion.div
            className="mx-auto max-w-2xl text-center"
            variants={itemVariants}
          >
            <motion.h1
              className="text-4xl font-bold tracking-tight text-gray-900 dark:text-white sm:text-5xl md:text-6xl"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              Integrations
            </motion.h1>
            <motion.p
              className="mt-6 text-xl leading-8 text-gray-600 dark:text-gray-300"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              Connect your Chatzuri chatbot with your favorite tools and
              platforms
            </motion.p>
          </motion.div>
        </div>
      </motion.div>

      {/* Integration Cards */}
      <motion.div
        className="container mx-auto px-4 py-8 max-w-7xl"
        initial="hidden"
        animate="visible"
        variants={containerVariants}
      >
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {integrations.map((integration, index) => (
            <motion.div
              key={index}
              variants={itemVariants}
              whileHover={{ y: -5 }}
              className="rounded-xl overflow-hidden shadow-lg transition-all duration-300 hover:shadow-xl"
            >
              <div
                className={`h-full p-6 ${integration.bgColor} ${integration.textColor} flex flex-col`}
              >
                <div className="flex items-center mb-4">
                  <div className="relative p-3 rounded-lg bg-white/10 backdrop-blur-sm mr-4 shadow-lg">
                    <div className="absolute inset-0 rounded-lg bg-gradient-to-br from-white/20 via-transparent to-transparent opacity-70"></div>
                    <div className="absolute inset-0 rounded-lg border border-white/10"></div>
                    <div className="relative z-10 text-white">
                      {integration.icon}
                    </div>
                  </div>
                  <h3 className="text-2xl font-bold">{integration.title}</h3>
                </div>
                <p className="mb-6 opacity-90 flex-grow">
                  {integration.description}
                </p>
                <div className="mt-auto">
                  <Link
                    href={integration.href}
                    className={`inline-flex items-center px-4 py-2 rounded-full border ${
                      integration.buttonVariant === "outline-light"
                        ? "border-white/30 hover:bg-white/10 text-white"
                        : "border-gray-300 hover:bg-gray-100 text-gray-800 dark:border-gray-600 dark:hover:bg-gray-700 dark:text-gray-200"
                    } transition-colors`}
                  >
                    Let&apos;s go
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-4 w-4 ml-2"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M14 5l7 7m0 0l-7 7m7-7H3"
                      />
                    </svg>
                  </Link>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Bottom Navigation */}
        <motion.nav
          className="flex flex-wrap justify-center gap-4 mt-12"
          variants={itemVariants}
        >
          <Link
            href="/guides"
            className="px-6 py-2 rounded-full border border-gray-300 dark:border-gray-600 text-gray-800 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
          >
            All guides
          </Link>
          <Link
            href="/guides/get-started"
            className="px-6 py-2 rounded-full border border-gray-300 dark:border-gray-600 text-gray-800 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
          >
            Get started
          </Link>
        </motion.nav>
      </motion.div>
    </main>
  );
};

export default IntegrationsPage;
