"use client";
import { motion } from "framer-motion";
import { Globe } from "lucide-react";
import Link from "next/link";

const CustomDomainsGuide = () => {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5,
      },
    },
  };

  return (
    <main className="bg-white dark:bg-gray-900">
      {/* Hero Section */}
      <motion.div
        className="relative isolate px-6 pt-14 lg:px-8"
        variants={itemVariants}
      >
        <div
          className="absolute inset-x-0 -top-40 -z-10 transform-gpu overflow-hidden blur-3xl sm:-top-80"
          aria-hidden="true"
        >
          <div
            className="relative left-[calc(50%-11rem)] aspect-[1155/678] w-[36.125rem] -translate-x-1/2 rotate-[30deg] bg-gradient-to-tr from-emerald-400 to-teal-600 opacity-30 sm:left-[calc(50%-30rem)] sm:w-[72.1875rem]"
            style={{
              clipPath:
                "polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)",
            }}
          ></div>
        </div>
        <div className="mx-auto max-w-2xl py-10 sm:py-12 lg:py-11">
          <div className="text-center">
            <motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-4xl font-semibold tracking-tight text-gray-900 dark:text-white sm:text-5xl md:text-6xl"
            >
              Custom Domains
            </motion.h1>
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="mt-6 text-lg leading-8 text-gray-600 dark:text-gray-300 sm:text-xl"
            >
              Configure your chatbot to appear on your own domain for a seamless
              brand experience.
            </motion.p>
            <div className="mt-6 flex justify-center">
              <Globe className="w-10 h-10 text-emerald-500 dark:text-emerald-400" />
            </div>
          </div>
        </div>
        <div
          className="absolute inset-x-0 top-[calc(100%-13rem)] -z-10 transform-gpu overflow-hidden blur-3xl sm:top-[calc(100%-30rem)]"
          aria-hidden="true"
        >
          <div
            className="relative left-[calc(50%+3rem)] aspect-[1155/678] w-[36.125rem] -translate-x-1/2 bg-gradient-to-tr from-emerald-400 to-teal-600 opacity-30 sm:left-[calc(50%+36rem)] sm:w-[72.1875rem]"
            style={{
              clipPath:
                "polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)",
            }}
          ></div>
        </div>
      </motion.div>

      {/* Content Section */}
      <motion.div
        initial="hidden"
        animate="visible"
        variants={containerVariants}
        className="container mx-auto px-4 py-8 max-w-4xl"
      >
        <article className="prose prose-lg dark:prose-invert prose-emerald max-w-none">
          <motion.section variants={itemVariants} className="mb-12">
            <h3 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4">
              1. What &quot;Custom Domain&quot; Means on Chatzuri
            </h3>
            <p className="text-gray-600 dark:text-gray-300 mb-4">
              When we say you can add a custom domain, it means you can host
              your chatbot on a subdomain that belongs to your company or brand,
              instead of Chatzuri&apos;s default domain. For example, instead of your
              chatbot appearing on a URL like chatzuri.com/yourbot, it could be
              hosted on yourbot.yourdomain.com. This feature enhances your
              brand&apos;s professionalism and trustworthiness by keeping everything
              under your domain.
            </p>

            <div className="bg-emerald-50 dark:bg-emerald-900/20 p-4 rounded-lg mb-6">
              <p className="font-medium text-emerald-700 dark:text-emerald-400 mb-2">
                By adding a custom subdomain:
              </p>
              <ul className="space-y-2 text-gray-600 dark:text-gray-300">
                <li className="flex items-start">
                  <span className="mr-2">•</span>
                  <span>
                    Your chatbot becomes an integrated part of your website
                  </span>
                </li>
                <li className="flex items-start">
                  <span className="mr-2">•</span>
                  <span>
                    Visitors&apos; traffic requests won&apos;t be redirected to an
                    external Chatzuri URL
                  </span>
                </li>
                <li className="flex items-start">
                  <span className="mr-2">•</span>
                  <span>
                    It improves the user experience with consistent branding
                    across your website and chatbot interactions
                  </span>
                </li>
              </ul>
            </div>

            <h3 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4">
              2. Step-by-Step Guide to Adding a Custom Subdomain to Your Chatbot
              on Chatzuri
            </h3>

            <div className="space-y-6">
              <div>
                <h4 className="text-xl font-medium text-gray-900 dark:text-white mb-2">
                  Log Into Your Chatzuri Account
                </h4>
              </div>

              <div>
                <h4 className="text-xl font-medium text-gray-900 dark:text-white mb-2">
                  Navigate to Your Chatbot Settings
                </h4>
              </div>

              <div>
                <h4 className="text-xl font-medium text-gray-900 dark:text-white mb-2">
                  Locate the Domain Customization Option
                </h4>
              </div>

              <div>
                <h4 className="text-xl font-medium text-gray-900 dark:text-white mb-2">
                  Enter Your Custom Subdomain
                </h4>
                <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg my-3 italic">
                  <p className="text-gray-600 dark:text-gray-300">
                    This is where you type in the subdomain (e.g.,
                    support.yourbusiness.com) that you own or have control over.
                    Chatzuri currently supports subdomains only, so you&apos;ll need
                    to ensure you&apos;re using a subdomain on your website.
                  </p>
                  <p className="text-gray-600 dark:text-gray-300 mt-2">
                    Adding a custom subdomain gives your chatbot a professional
                    look by branding it with your website&apos;s URL instead of
                    Chatzuri&apos;s default domain.
                  </p>
                </div>
              </div>

              <div>
                <h4 className="text-xl font-medium text-gray-900 dark:text-white mb-2">
                  Configure DNS Settings
                </h4>
                <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg my-3 italic">
                  <p className="text-gray-600 dark:text-gray-300">
                    You might need to configure additional DNS settings like
                    CNAME records. This step will link the custom subdomain to
                    your chatbot, ensuring it works correctly when users visit
                    the URL.
                  </p>
                  <p className="text-gray-600 dark:text-gray-300 mt-2">
                    Chatzuri will provide specific DNS instructions for setting
                    this up, which usually includes pointing the subdomain to
                    Chatzuri&apos;s servers.
                  </p>
                </div>
              </div>

              <div>
                <h4 className="text-xl font-medium text-gray-900 dark:text-white mb-2">
                  Save and Test
                </h4>
                <p className="text-gray-600 dark:text-gray-300">
                  Once you&apos;ve successfully configured the domain settings and
                  DNS records, save your changes. It may take some time for the
                  changes to propagate across the web (usually within a few
                  minutes to 24 hours). Test the custom subdomain by entering it
                  into a browser to ensure your chatbot loads correctly.
                </p>
              </div>
            </div>
          </motion.section>

          <motion.nav
            className="flex flex-wrap justify-center gap-4 mt-12"
            variants={itemVariants}
          >
            <Link
              href="/guides/get-started"
              className="px-6 py-2 rounded-full border border-gray-300 dark:border-gray-600 text-gray-800 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700"
            >
              Get started
            </Link>
            <Link
              href="/guides/chatbot-settings"
              className="px-6 py-2 rounded-full border border-gray-300 dark:border-gray-600 text-gray-800 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700"
            >
              Chatbot settings
            </Link>
            <Link
              href="/guides/response-quality"
              className="px-6 py-2 rounded-full border border-gray-300 dark:border-gray-600 text-gray-800 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700"
            >
              Response quality
            </Link>
            <Link
              href="/guides/manage-teams"
              className="px-6 py-2 rounded-full border border-gray-300 dark:border-gray-600 text-gray-800 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700"
            >
              Manage teams
            </Link>
            <Link
              href="/guides/integrations"
              className="px-6 py-2 rounded-full bg-emerald-500 hover:bg-emerald-600 dark:bg-emerald-600 dark:hover:bg-emerald-700 text-white"
            >
              Integrations
            </Link>
          </motion.nav>
        </article>
      </motion.div>
    </main>
  );
};

export default CustomDomainsGuide;
