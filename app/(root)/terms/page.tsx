"use client";

import { motion } from "framer-motion";
import {
  FileText,
  Shield,
  Lock,
  Server,
  CreditCard,
  Clock,
  AlertTriangle,
  Handshake,
} from "lucide-react";
import Head from "next/head";

export default function TermsOfService() {
  const sections = [
    {
      title: "Introduction",
      icon: <FileText className="w-5 h-5 text-emerald-500" />,
      content: (
        <>
          <p className="mb-4 lowercase first-letter:uppercase first-letter:tracking-wide">
            THESE TERMS OF SERVICE (the &quot;Agreement&quot;) GOVERN YOUR
            RECEIPT, ACCESS TO, AND USE OF THE SERVICES PROVIDED BY
            Chatzuri.com, INC. (&quot;Chatzuri&quot;). BY (A) PURCHASING ACCESS
            TO THE SERVICE THROUGH AN ONLINE ORDERING PROCESS THAT REFERENCES
            THIS AGREEMENT, (B) SIGNING UP FOR A FREE OR PAID ACCESS PLAN FOR
            THE SERVICE VIA A PLATFORM THAT REFERENCES THIS AGREEMENT, OR (C)
            CLICKING A BOX INDICATING ACCEPTANCE, YOU AGREE TO BE BOUND BY THE
            TERMS OF THIS AGREEMENT.
          </p>
          <p className="lowercase">
            THE INDIVIDUAL ACCEPTING THIS AGREEMENT DOES SO ON BEHALF OF A
            COMPANY OR OTHER LEGAL ENTITY (&quot;Customer&quot;); SUCH
            INDIVIDUAL REPRESENTS AND WARRANTS THAT THEY HAVE THE AUTHORITY TO
            BIND SUCH ENTITY AND ITS AFFILIATES TO THIS AGREEMENT. IF THE
            INDIVIDUAL ACCEPTING THIS AGREEMENT DOES NOT HAVE SUCH AUTHORITY, OR
            IF THE ENTITY DOES NOT AGREE WITH THESE TERMS AND CONDITIONS, SUCH
            INDIVIDUAL MUST NOT ACCEPT THIS AGREEMENT AND MAY NOT USE THE
            SERVICES. CAPITALIZED TERMS HAVE THE MEANINGS SET FORTH HEREIN. THE
            PARTIES AGREE AS FOLLOWS:
          </p>
        </>
      ),
    },
    {
      title: "1. The Service",
      icon: <Server className="w-5 h-5 text-emerald-500" />,
      subsections: [
        {
          title: "1.1 Service Description",
          content: (
            <p>
              Chatzuri owns and provides a cloud-based artificial intelligence
              service offering chatbots for customer support, sales, and user
              engagement (the &quot;Service&quot;). Anything the Customer
              (including Users) configures, customizes, uploads, or otherwise
              utilizes through the Service is considered a &quot;User
              Submission.&quot; Customer is solely responsible for all User
              Submissions it contributes to the Service. Additional terms
              regarding User Submissions, including ownership, are in Section
              8.2 below. The Service may include templates, scripts,
              documentation, and other materials that assist Customer in using
              the Service (&quot;Chatzuri Content&quot;). Customers will not
              receive or have access to the underlying code or software of the
              Service (collectively, the &quot;Software&quot;) nor receive a
              copy of the Software itself.
            </p>
          ),
        },
        {
          title: "1.2 Customer&apos;s Subscription",
          content: (
            <p>
              Subject to the terms of this Agreement, Customer may purchase a
              subscription to, and has the right to access and use, the Service
              as specified in one or more ordering screens agreed upon by the
              parties through Chatzuri&apos;s website or service portal that
              reference this Agreement and describe the business terms related
              to Customer&apos;s subscription (&quot;Order(s)&quot;). All
              subscriptions are for the period described in the applicable Order
              (&quot;Subscription Period&quot;). Use of and access to the
              Service is permitted only for individuals authorized by the
              Customer and solely for Customer&apos;s own internal business
              purposes, not for the benefit of any third party
              (&quot;Users&quot;).
            </p>
          ),
        },
        {
          title: "1.3 Chatzuri&apos;s Ownership",
          content: (
            <p>
              Chatzuri owns the Service, Software, Chatzuri Content,
              Documentation, and anything else provided by Chatzuri to the
              Customer (collectively, the &quot;Chatzuri Materials&quot;).
              Chatzuri retains all rights, title, and interest (including all
              intellectual property rights) in and to the Chatzuri Materials,
              all related and underlying technology, and any updates,
              enhancements, modifications, or fixes thereto, as well as all
              derivative works of or modifications to any of the foregoing. No
              implied licenses are granted under this Agreement, and any rights
              not expressly granted to the Customer are reserved by Chatzuri.
            </p>
          ),
        },
        {
          title: "1.4 Permissions",
          content: (
            <p>
              The Service includes customizable settings allowing Users to grant
              permissions to other Users to perform various tasks within the
              Service (&quot;Permissions&quot;). It is solely the
              Customer&apos;s responsibility to set and manage all Permissions,
              including determining which Users can set such Permissions.
              Accordingly, Chatzuri has no responsibility for managing
              Permissions and no liability for Permissions set by the Customer
              and its Users. The Customer may provide access to the Service to
              its Affiliates, in which case all rights granted and obligations
              incurred under this Agreement shall extend to such Affiliates. The
              Customer represents and warrants it is fully responsible for any
              breaches of this Agreement by its Affiliates and has the authority
              to negotiate this Agreement on behalf of its Affiliates. The
              Customer is also responsible for all payment obligations under
              this Agreement, regardless of whether the use of the Service is by
              the Customer or its Affiliates. Any claim by an Affiliate against
              Chatzuri must be brought by the Customer, not the Affiliate. An
              &quot;Affiliate&quot; of a party means any entity directly or
              indirectly controlling, controlled by, or under common control
              with that party, where &quot;control&quot; means the ownership of
              more than fifty percent (50%) of the voting shares or other equity
              interests.
            </p>
          ),
        },
      ],
    },
    {
      title: "2. Restrictions",
      icon: <Lock className="w-5 h-5 text-emerald-500" />,
      subsections: [
        {
          title: "2.1 Customer&apos;s Responsibilities",
          content: (
            <p>
              The Customer is responsible for all activity on its account and
              those of its Users, except where such activity results from
              unauthorized access due to vulnerabilities in the Service itself.
              The Customer will ensure its Users are aware of and comply with
              the obligations and restrictions in this Agreement, bearing
              responsibility for any breaches by a User.
            </p>
          ),
        },
        {
          title: "2.2 Use Restrictions",
          content: (
            <p>
              The Customer agrees not to, and not to permit Users or third
              parties to, directly or indirectly: (a) modify, translate, copy,
              or create derivative works based on the Service; (b) reverse
              engineer, decompile, or attempt to discover the source code or
              underlying ideas of the Service, except as permitted by law; (c)
              sublicense, sell, rent, lease, distribute, or otherwise
              commercially exploit the Service; (d) remove proprietary notices
              from the Service; (e) use the Service in violation of laws or
              regulations; (f) attempt unauthorized access to or disrupt the
              Service; (g) use the Service to support products competitive to
              Chatzuri; (h) test the Service&apos;s vulnerability without
              authorization. If the Customer&apos;s use of the Service
              significantly harms Chatzuri or the Service&apos;s security or
              integrity, Chatzuri may suspend access to the Service, taking
              reasonable steps to notify the Customer and resolve the issue
              promptly.
            </p>
          ),
        },
        {
          title: "2.3 API Access Restrictions",
          content: (
            <p>
              Chatzuri may provide access to APIs as part of the Service.
              Chatzuri reserves the right to set and enforce usage limits on the
              APIs, and the Customer agrees to comply with such limits. Chatzuri
              may also suspend or terminate API access at any time.
            </p>
          ),
        },
      ],
    },
    {
      title: "3. Third-Party Services",
      icon: <Server className="w-5 h-5 text-emerald-500" />,
      content: (
        <p>
          The Service may interface with third-party products, services, or
          applications that are not owned or controlled by Chatzuri
          (&quot;Third-Party Services&quot;). Customers have the discretion to
          utilize these Third-Party Services in conjunction with our Service.
          Should the integration of the Service with any Third-Party Service
          require, customers will be responsible for providing their login
          information to Chatzuri solely for the purpose of enabling Chatzuri to
          deliver its Service. Customers affirm that they have the authority to
          provide such information without violating any terms and conditions
          governing their use of the Third-Party Services. Chatzuri does not
          endorse any Third-Party Services. Customers acknowledge that this
          Agreement does not cover the use of Third-Party Services, and they may
          need to enter into separate agreements with the providers of these
          services. Chatzuri expressly disclaims all representations and
          warranties concerning Third-Party Services. Customers must direct any
          warranty claims or other disputes directly to the providers of the
          Third-Party Services. The use of Third-Party Services is at the
          customer&apos;s own risk. Chatzuri shall not be liable for any issues
          arising from the use or inability to use Third-Party Services.
        </p>
      ),
    },
    {
      title: "4. Financial Terms",
      icon: <CreditCard className="w-5 h-5 text-emerald-500" />,
      subsections: [
        {
          title: "4.1 Fees",
          content: (
            <p>
              Customers are required to pay for access to and use of the Service
              as detailed in the applicable order (&quot;Fees&quot;). All Fees
              will be charged in the currency stated in the order or, if no
              currency is specified, in U.S. dollars. Payment obligations are
              non-cancellable and, except as explicitly stated in this
              Agreement, Fees are non-refundable. Chatzuri reserves the right to
              modify its Fees or introduce new fees at its discretion. Customers
              have the option not to renew their subscription if they disagree
              with any revised fees.
            </p>
          ),
        },
        {
          title: "4.2 Payment",
          content: (
            <p>
              Chatzuri, either directly or through its third-party payment
              processor (&quot;Payment Processor&quot;), will bill the customer
              for the Fees using the credit card or ACH payment information
              provided by the customer. Chatzuri reserves the right to charge
              the customer&apos;s credit card or ACH payment method for any
              services provided under the order, including recurring Fees. It is
              the customer&apos;s responsibility to ensure that Chatzuri has
              current and accurate credit card or ACH payment information.
              Failure to provide accurate information may lead to a suspension
              of access to the Services. Chatzuri also reserves the right to
              offset any Fees owed by the customer. If the customer pays through
              a Payment Processor, such transactions will be subject to the
              Payment Processor&apos;s terms, conditions, and privacy policies,
              in addition to this Agreement. Chatzuri is not responsible for
              errors or omissions by the Payment Processor. Chatzuri reserves
              the right to correct any errors made by the Payment Processor,
              even if payment has already been requested or received. If the
              customer authorizes, through accepting an order, recurring charges
              will be automatically applied to the customer&apos;s payment
              method without further authorization until the customer terminates
              this Agreement or updates their payment method.
            </p>
          ),
        },
        {
          title: "4.3 Taxes",
          content: (
            <p>
              Fees do not include any taxes, levies, duties, or similar
              governmental assessments, including value-added, sales, use, or
              withholding taxes, imposed by any jurisdiction (collectively,
              &quot;Taxes&quot;). Customers are responsible for paying all Taxes
              associated with their purchases. If Chatzuri is obligated to pay
              or collect Taxes for which the customer is responsible, Chatzuri
              will invoice the customer for such Taxes unless the customer
              provides Chatzuri with a valid tax exemption certificate
              authorized by the appropriate taxing authority beforehand. For
              clarity, Chatzuri is solely responsible for taxes based on its
              income, property, and employees.
            </p>
          ),
        },
        {
          title: "4.4 Failure to Pay",
          content: (
            <p>
              If a customer fails to pay any Fees when due, Chatzuri may suspend
              access to the Service until overdue amounts are paid. Chatzuri is
              authorized to attempt charging the customer&apos;s payment method
              multiple times if an initial charge is unsuccessful. If a customer
              believes they have been incorrectly billed, they must contact
              Chatzuri within sixty (60) days from the first billing statement
              showing the error to request an adjustment or credit. Upon
              receiving a dispute notice, Chatzuri will review and provide the
              customer with a written decision, including evidence supporting
              this decision. If it is determined that the billed amounts are
              due, the customer must pay these amounts within ten (10) days of
              receiving Chatzuri&apos;s written decision.
            </p>
          ),
        },
      ],
    },
    {
      title: "5. Term and Termination",
      icon: <Clock className="w-5 h-5 text-emerald-500" />,
      subsections: [
        {
          title: "5.1 Agreement Term and Renewals",
          content: (
            <p>
              Subscriptions to access and use Chatzuri&apos;s service
              (&quot;Service&quot;) commence on the start date specified on the
              applicable Order (&quot;Subscription Start Date&quot;) and
              continue for the duration of the Subscription Period. Customers
              may opt not to renew their Subscription Period by notifying
              Chatzuri at
              <a
                href="mailto:<EMAIL>"
                className="text-emerald-600 dark:text-emerald-400 hover:underline"
              >
                {" "}
                <EMAIL>
              </a>
              (provided that Chatzuri confirms such cancellation in writing) or
              by modifying their subscription through the Customer&apos;s
              account settings within the Service. This Agreement takes effect
              on the first day of the Subscription Period and remains effective
              for the duration of the Subscription Period stated on the Order,
              including any renewals of the Subscription Period and any period
              that the Customer is using the Service, even if such use is not
              under a paid Order (&quot;Term&quot;). If this Agreement is
              terminated by either party, it will automatically terminate all
              Orders. If a Customer cancels or chooses not to renew their paid
              subscription to the Service, the Customer&apos;s subscription will
              still be accessible but will automatically be downgraded to a
              version of the Service with reduced features and functionality
              that Chatzuri offers to unpaid subscribers (&quot;Free
              Version&quot;). Should this Agreement be terminated by either
              Chatzuri or the Customer, or should the Customer delete its
              workspace within the Service, access to the Free Version will be
              revoked.
            </p>
          ),
        },
        {
          title: "5.2 Termination",
          content: (
            <p>
              Either party may terminate this Agreement with written notice to
              the other party if the other party materially breaches this
              Agreement and such breach is not cured within thirty (30) days
              after receipt of such notice. Chatzuri may terminate a
              Customer&apos;s access to the Free Version at any time upon
              notice.
            </p>
          ),
        },
        {
          title: "5.3 Effect of Termination",
          content: (
            <p>
              If the Customer terminates this Agreement due to an uncured breach
              by Chatzuri, Chatzuri will refund any unused, prepaid Fees for the
              remainder of the then-current Subscription Period. If Chatzuri
              terminates this Agreement due to an uncured breach by the
              Customer, the Customer will pay any unpaid Fees covering the
              remainder of the then-current Subscription Period after the date
              of termination. No termination will relieve the Customer of the
              obligation to pay any Fees payable to Chatzuri for the period
              prior to the effective date of termination. Upon termination, all
              rights and licenses granted by Chatzuri will cease immediately,
              and the Customer will lose access to the Service. Within thirty
              (30) days of termination for cause, upon the Customer&apos;s
              request, or if the Customer deletes its workspace within the
              Service, Chatzuri will delete the Customer&apos;s User
              Information, including passwords, files, and submissions, unless
              an earlier deletion is requested in writing. For Customers using
              the Free Version, Chatzuri may retain User Submissions and User
              Information to facilitate continued use. Chatzuri may delete all
              User Submissions and User Information if an account remains
              inactive for more than one (1) year.
            </p>
          ),
        },
        {
          title: "5.4 Survival",
          content: (
            <p>
              Sections titled &quot;Chatzuri&apos;s Ownership&quot;,
              &quot;Third-Party Services&quot;, &quot;Financial Terms&quot;,
              &quot;Term and Termination&quot;, &quot;Warranty Disclaimer&quot;,
              &quot;Limitation of Liability&quot;, &quot;Confidentiality&quot;,
              &quot;Data&quot; and &quot;General Terms&quot; will survive any
              termination or expiration of this Agreement.
            </p>
          ),
        },
      ],
    },
    {
      title: "6. Warranties and Disclaimers",
      icon: <Shield className="w-5 h-5 text-emerald-500" />,
      subsections: [
        {
          title: "6.1 Warranties",
          content: (
            <p>
              Customers represent and warrant that all User Submissions
              submitted by Users comply with all applicable laws, rules, and
              regulations.
            </p>
          ),
        },
        {
          title: "6.2 Warranty Disclaimer",
          content: (
            <p>
              EXCEPT AS EXPRESSLY STATED HEREIN, THE SERVICES AND ALL RELATED
              COMPONENTS AND INFORMATION ARE PROVIDED ON AN &quot;AS IS&quot;
              AND &quot;AS AVAILABLE&quot; BASIS WITHOUT ANY WARRANTIES OF ANY
              KIND, AND Chatzuri EXPRESSLY DISCLAIMS ANY AND ALL WARRANTIES,
              WHETHER EXPRESS OR IMPLIED, INCLUDING THE IMPLIED WARRANTIES OF
              MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE, AND
              NON-INFRINGEMENT. CUSTOMERS ACKNOWLEDGE THAT Chatzuri DOES NOT
              WARRANT THAT THE SERVICES WILL BE UNINTERRUPTED, TIMELY, SECURE,
              OR ERROR-FREE. SOME JURISDICTIONS DO NOT ALLOW THE DISCLAIMER OF
              CERTAIN WARRANTIES, SO THE FOREGOING DISCLAIMERS MAY NOT APPLY TO
              THE EXTENT PROHIBITED BY LAW.
            </p>
          ),
        },
      ],
    },
    {
      title: "7. Limitation of Liability",
      icon: <AlertTriangle className="w-5 h-5 text-emerald-500" />,
      content: (
        <p className="lowercase">
          NOTWITHSTANDING ANY PROVISION TO THE CONTRARY, Chatzuri WILL NOT BE
          LIABLE FOR ANY INDIRECT, SPECIAL, INCIDENTAL, CONSEQUENTIAL DAMAGES,
          OR DAMAGES BASED ON THE USE OR ACCESS, INTERRUPTION, DELAY, OR
          INABILITY TO USE THE SERVICE, LOST REVENUES OR PROFITS, LOSS OF
          BUSINESS OR GOODWILL, DATA CORRUPTION, OR SYSTEM FAILURES, REGARDLESS
          OF THE LEGAL THEORY. FURTHER, Chatzuri&apos;S TOTAL LIABILITY WILL NOT
          EXCEED THE TOTAL FEES PAID OR PAYABLE BY THE CUSTOMER FOR THE SERVICE
          DURING THE TWELVE (12) MONTHS PRIOR TO THE CLAIM. THESE LIMITATIONS
          APPLY REGARDLESS OF WHETHER Chatzuri HAS BEEN ADVISED OF THE
          POSSIBILITY OF SUCH DAMAGES AND NOTWITHSTANDING ANY FAILURE OF
          ESSENTIAL PURPOSE OF ANY LIMITED REMEDY.
        </p>
      ),
    },
    {
      title: "8. Confidentiality",
      icon: <Lock className="w-5 h-5 text-emerald-500" />,
      subsections: [
        {
          title: "8.1 Definition",
          content: (
            <p>
              Each party (the &quot;Receiving Party&quot;) recognizes that the
              other party (the &quot;Disclosing Party&quot;) may share business,
              technical, or financial information pertaining to the Disclosing
              Party&apos;s operations that, due to the nature of the information
              and the context of disclosure, is reasonably considered
              confidential (&quot;Confidential Information&quot;). For Chatzuri,
              Confidential Information includes non-public details about
              features, functionality, and performance of the Service. For
              Customers, Confidential Information comprises User Information and
              User Submissions. This Agreement, along with all related Orders,
              is considered Confidential Information of both parties. However,
              Confidential Information does not include information that: (a)
              becomes publicly available without breaching any duty to the
              Disclosing Party; (b) was known to the Receiving Party before
              disclosure by the Disclosing Party without breaching any duty; (c)
              is received from a third party without breaching any duty; or (d)
              was independently developed by the Receiving Party without using
              the Disclosing Party&apos;s Confidential Information.
            </p>
          ),
        },
        {
          title: "8.2 Protection and Use of Confidential Information",
          content: (
            <p>
              The Receiving Party must: (a) protect the Disclosing Party&apos;s
              Confidential Information with at least the same degree of care it
              uses for its own similar information, but no less than a
              reasonable level of care; (b) restrict access to Confidential
              Information to personnel, affiliates, subcontractors, agents,
              consultants, legal advisors, financial advisors, and contractors
              (&quot;Representatives&quot;) who need this information in
              relation to this Agreement and who are bound by confidentiality
              obligations similar to those in this Agreement; (c) not disclose
              any Confidential Information to third parties without prior
              written consent from the Disclosing Party, except as expressly
              stated herein; and (d) use the Confidential Information solely to
              fulfill obligations under this Agreement. This does not prevent
              sharing of Agreement terms or the other party&apos;s name with
              potential investors or buyers under standard confidentiality
              terms.
            </p>
          ),
        },
        {
          title: "8.3 Compelled Access or Disclosure",
          content: (
            <p>
              If required by law, the Receiving Party may access or disclose the
              Disclosing Party&apos;s Confidential Information, provided that it
              notifies the Disclosing Party in advance (when legally
              permissible) and offers reasonable help, at the Disclosing
              Party&apos;s expense, if the Disclosing Party wants to contest the
              disclosure.
            </p>
          ),
        },
        {
          title: "8.4 Feedback",
          content: (
            <p>
              Customers may occasionally offer feedback on the Service
              (&quot;Feedback&quot;). Chatzuri may choose to incorporate this
              Feedback into its services. Customers grant Chatzuri a
              royalty-free, worldwide, perpetual, irrevocable, fully
              transferable, and sublicensable license to use, disclose, modify,
              create derivative works from, distribute, display, and exploit any
              Feedback as Chatzuri sees fit, without any obligation or
              restriction, except for not identifying the Customer as the source
              of Feedback.
            </p>
          ),
        },
      ],
    },
    {
      title: "9. Data",
      icon: <Server className="w-5 h-5 text-emerald-500" />,
      subsections: [
        {
          title: "9.1 User Information",
          content: (
            <p>
              Customers and their Users must provide information like names,
              email addresses, usernames, IP addresses, browsers, and operating
              systems (&quot;User Information&quot;) to access the Service.
              Customers authorize Chatzuri and its subcontractors to store,
              process, and retrieve User Information as part of the Service
              usage. Customers guarantee they have the necessary rights to
              provide User Information to Chatzuri for processing as described
              in this Agreement. Customers are liable for their User Information
              and any unauthorized use of their credentials.
            </p>
          ),
        },
        {
          title: "9.2 User Submissions",
          content: (
            <p>
              Customers grant Chatzuri a non-exclusive, worldwide, royalty-free,
              transferable license to use, process, and display User Submissions
              solely to provide the Service. Beyond the rights granted here,
              Customers retain all rights to User Submissions, with no implied
              licenses under this Agreement.
            </p>
          ),
        },
        {
          title: "9.3 Service Data",
          content: (
            <p>
              Chatzuri collects data on Service performance and operation
              (&quot;Service Data&quot;) as Customers use the Service. Provided
              Service Data is aggregated and anonymized, without disclosing any
              personal information, Chatzuri can use this data freely. Chatzuri
              owns all rights to Service Data, but will not identify Customers
              or Users as its source.
            </p>
          ),
        },
        {
          title: "9.4 Data Protection",
          content: (
            <p>
              Chatzuri maintains reasonable security practices to protect
              Customer Data, including User Submissions and User Information.
              Nonetheless, Customers are responsible for securing their systems
              and data. Chatzuri processes all Customer Data in accordance with
              its Data Processing Agreement.
            </p>
          ),
        },
      ],
    },
    {
      title: "10. General Terms",
      icon: <Handshake className="w-5 h-5 text-emerald-500" />,
      subsections: [
        {
          title: "10.1 Publicity",
          content: (
            <p>
              With prior written consent from the Customer, Chatzuri is allowed
              to identify the Customer and use and display the Customer&apos;s
              name, logo, trademarks, or service marks on Chatzuri&apos;s
              website and in Chatzuri&apos;s marketing materials. This will help
              in demonstrating the clientele and user base of Chatzuri without
              compromising any confidential information or privacy rights of the
              Customer.
            </p>
          ),
        },
        {
          title: "10.2 Force Majeure",
          content: (
            <p>
              Chatzuri shall not be liable for any failure or delay in
              performing its obligations hereunder caused by events beyond its
              reasonable control, including but not limited to failures of
              third-party hosting or utility providers, strikes (excluding those
              involving Chatzuri&apos;s employees), riots, fires, natural
              disasters, wars, terrorism, or government actions. These
              circumstances provide a shield for Chatzuri against unforeseen
              events that prevent it from fulfilling its service obligations.
            </p>
          ),
        },
        {
          title: "10.3 Changes",
          content: (
            <p>
              Chatzuri acknowledges that its service is an evolving,
              subscription-based product. To enhance customer experience,
              Chatzuri reserves the right to make modifications to the Service.
              However, Chatzuri commits to not materially reducing the core
              functionality provided to Customers. Furthermore, Chatzuri may
              modify the terms of this Agreement unilaterally, provided that
              Customers are notified at least thirty (30) days before such
              changes take effect, with changes posted prominently, for example,
              on the Chatzuri website terms page.
            </p>
          ),
        },
        {
          title: "10.4 Relationship of the Parties",
          content: (
            <p>
              This Agreement does not create a partnership, franchise, joint
              venture, agency, fiduciary, or employment relationship between
              Chatzuri and the Customer. Both parties are independent
              contractors, maintaining their respective operations and autonomy
              while cooperating under the terms laid out in this Agreement.
            </p>
          ),
        },
        {
          title: "10.5 No Third-Party Beneficiaries",
          content: (
            <p>
              This Agreement is strictly between Chatzuri and the Customer. It
              is not intended to benefit any third party, nor shall any third
              party have the right to enforce any of its terms, directly or
              indirectly. This clause clarifies the intended scope of the
              Agreement, limiting obligations and benefits to the parties
              involved.
            </p>
          ),
        },
        {
          title: "10.6 Email Communications",
          content: (
            <p>
              Notices under this Agreement will be communicated via email,
              although Chatzuri may choose to provide notices through the
              Service instead. Notices to Chatzuri must be directed to a
              designated Chatzuri email, while notices to Customers will be sent
              to the email addresses provided by them through the Service.
              Notices are considered delivered the next business day after
              emailing or the same day if provided through the Service.
            </p>
          ),
        },
        {
          title: "10.7 Amendment and Waivers",
          content: (
            <p>
              No modifications to this Agreement will be effective unless in
              writing and signed or acknowledged by authorized representatives
              of both parties. Neither party&apos;s delay or failure to exercise
              any right under this Agreement will be deemed a waiver of that
              right. Waivers must also be in writing and signed by the party
              granting the waiver.
            </p>
          ),
        },
        {
          title: "10.8 Severability",
          content: (
            <p>
              Should any provision of this Agreement be found unlawful or
              unenforceable by a court, it will be modified to the minimum
              extent necessary to make it lawful or enforceable, while the
              remaining provisions continue in full effect. This clause ensures
              the Agreement remains operational even if parts of it are modified
              or removed.
            </p>
          ),
        },
        {
          title: "10.9 Assignment",
          content: (
            <p>
              Neither party may assign or delegate their rights or obligations
              under this Agreement without the other party&apos;s prior written
              consent, except that Chatzuri may do so without consent in cases
              of mergers, acquisitions, corporate reorganizations, or sales of
              substantially all assets. Any unauthorized assignment will be
              void. This Agreement binds and benefits the parties, their
              successors, and permitted assigns.
            </p>
          ),
        },
        {
          title: "10.10 Governing Law and Venue",
          content: (
            <p>
              This Agreement will be governed by the laws of the State of
              Delaware, USA, excluding its conflict of laws principles. Disputes
              arising under this Agreement will be resolved in the state or
              federal courts in New Castle County, Delaware, to which both
              parties consent to jurisdiction and venue. There is a waiver of
              any right to a jury trial for disputes arising under this
              Agreement. The prevailing party in any enforcement action is
              entitled to recover its reasonable costs and attorney fees.
            </p>
          ),
        },
        {
          title: "10.11 Entire Agreement",
          content: (
            <p>
              This Agreement, including any referenced documents and Orders,
              constitutes the full agreement between Chatzuri and the Customer,
              superseding all prior discussions, agreements, and understandings
              of any nature. This ensures clarity and completeness in the mutual
              expectations and obligations of the parties involved.
            </p>
          ),
        },
      ],
    },
  ];

  return (
    <>
      <Head>
        <title>Terms of Service | Chatzuri</title>
        <meta
          name="description"
          content="Chatzuri's Terms of Service - Legal agreement for using our services"
        />
      </Head>

      {/* Hero Section */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5 }}
        className="relative isolate px-6 pt-14 lg:px-8"
      >
        <div
          className="absolute inset-x-0 -top-40 -z-10 transform-gpu overflow-hidden blur-3xl sm:-top-80"
          aria-hidden="true"
        >
          <div
            className="relative left-[calc(50%-11rem)] aspect-1155/678 w-[36.125rem] -translate-x-1/2 rotate-[30deg] bg-linear-to-tr from-[#ff80b5] to-[#00a67e] opacity-30 sm:left-[calc(50%-30rem)] sm:w-[72.1875rem]"
            style={{
              backgroundImage: `linear-gradient(rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.3)),url('/images/affiliate-bg.jpg')`,
              clipPath:
                "polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)",
              WebkitClipPath:
                "polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)",
            }}
          ></div>
        </div>
        <div className="mx-auto max-w-2xl py-10 sm:py-12 lg:py-11">
          <div className="text-center">
            <motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="dark:text-white text-4xl font-semibold tracking-tight text-balance text-gray-900 sm:text-7xl"
            >
              Clear Terms. Trusted Service.
            </motion.h1>
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="dark:text-white mt-8 text-lg font-medium text-pretty text-gray-500 sm:text-xl/8"
            >
              By using Chatzuri, you agree to our Terms and Conditions designed
              to protect both you and our platform. We believe in transparency,
              fair use, and mutual respect — giving you full control and clear
              expectations when using our services.
            </motion.p>
          </div>
        </div>
        <div
          className="absolute inset-x-0 top-[calc(100%-13rem)] -z-10 transform-gpu overflow-hidden blur-3xl sm:top-[calc(100%-30rem)]"
          aria-hidden="true"
        >
          <div
            className="relative left-[calc(50%+3rem)] aspect-1155/678 w-[36.125rem] -translate-x-1/2 bg-linear-to-tr from-[#ff80b5] to-[#9089fc] opacity-30 sm:left-[calc(50%+36rem)] sm:w-[72.1875rem]"
            style={{
              clipPath:
                "polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)",
              WebkitClipPath:
                "polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)",
            }}
          ></div>
        </div>
      </motion.div>
      <motion.main
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5 }}
        className="container py-16 mx-auto px-4 sm:px-6 lg:px-8"
      >
        <motion.div
          initial={{ y: -20 }}
          animate={{ y: 0 }}
          className="max-w-4xl mx-auto"
        >
          <motion.h1
            className="text-4xl font-bold text-gray-900 dark:text-white mb-8 pb-4 border-b border-gray-200 dark:border-gray-700"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.1 }}
          >
            Terms of Service
          </motion.h1>

          <div className="space-y-12">
            {sections.map((section, index) => (
              <motion.section
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 + index * 0.1 }}
                className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300"
              >
                <div className="flex items-center mb-4">
                  {section.icon}
                  <h2 className="text-2xl font-semibold text-gray-800 dark:text-white ml-2 pb-2 border-b border-emerald-500/30">
                    {section.title}
                  </h2>
                </div>

                {section.content && (
                  <div className="text-gray-600 dark:text-gray-300">
                    {section.content}
                  </div>
                )}

                {section.subsections && (
                  <div className="space-y-6">
                    {section.subsections.map((subsection, subIndex) => (
                      <div key={subIndex} className="ml-4">
                        <h3 className="text-lg font-medium text-gray-700 dark:text-gray-200 mb-2">
                          {subsection.title}
                        </h3>
                        <div className="text-gray-600 dark:text-gray-300">
                          {subsection.content}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </motion.section>
            ))}
          </div>
        </motion.div>
      </motion.main>
    </>
  );
}
