"use client";
import { motion } from "framer-motion";
import Link from "next/link";
import { useState } from "react";
import {
  CheckCircle,
  Zap,
  BarChart2,
  Globe,
  Shield,
  Code,
  Settings,
  BookOpen,
  <PERSON><PERSON>,
  <PERSON><PERSON>te,
  <PERSON><PERSON>,
  CircleDollarSign,
} from "lucide-react";
import Image from "next/image";
import { DemoForm } from "@/components/forms/demo";

export default function Home() {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [showChatbotPreview, setShowChatbotPreview] = useState(false);
  const MotionLink = motion.create(Link);

  const features = [
    {
      icon: <CheckCircle className="w-6 h-6 text-emerald-500" />,
      title: "Trustworthy, Accurate Answers",
      description:
        "With features like 'Revise answers' and 'Confidence score,' you can be sure your agent is giving the right answers.",
    },
    {
      icon: <Zap className="w-6 h-6 text-emerald-500" />,
      title: "Lead Generation Engine",
      description:
        "Collect leads and gather your customer's data, all while providing a personalized experience.",
    },
    {
      icon: <BarChart2 className="w-6 h-6 text-emerald-500" />,
      title: "Advanced Analytics",
      description:
        "Get insights into your agent's interactions with your customers and use them to improve its performance.",
    },
    {
      icon: <Globe className="w-6 h-6 text-emerald-500" />,
      title: "Multiple Data Sources",
      description: "Import data from multiple sources to train your agent.",
    },
    {
      icon: <Palette className="w-6 h-6 text-emerald-500" />,
      title: "Customizations",
      description:
        "Customize your agent's look and feel to match your brand's style and website design.",
    },
    {
      icon: <Shield className="w-6 h-6 text-emerald-500" />,
      title: "Privacy/Security",
      description:
        "Your data is hosted on secure servers with robust encryption and access control.",
    },
    {
      icon: <Settings className="w-6 h-6 text-emerald-500" />,
      title: "Auto-Retrain",
      description:
        "Set your agent to retrain automatically and always be synced with your data.",
    },
    {
      icon: <Code className="w-6 h-6 text-emerald-500" />,
      title: "Integrations",
      description:
        "Connect your agent to your favorite tools like Slack, WhatsApp Zapier, and more.",
    },
    {
      icon: <BookOpen className="w-6 h-6 text-emerald-500" />,
      title: "Powerful AI Models",
      description:
        "Choose from a variety of AI models, including GPT-4o, Claude 3.5 Sonnet, and Gemini 1.5 Pro.",
    },
  ];

  const testimonials = [
    {
      quote:
        "Thanks to Chatzuri AI, we developed an AI-powered video recommendation tool. We're really impressed by their commitment to quality.",
      name: "S. Dino",
      role: "PM, Upesi Ltd.",
    },
    {
      quote:
        "For anyone on the fence - in just 2 hours I've tested, set up and gone live on three separate websites with Chatzuri.",
      name: "Samson K.",
      role: "CTO, Jalmark Ltd.",
    },
    {
      quote: "Website embed works like a charm even with non-English content!",
      name: "Sally J.",
      role: "HR, MyFurniture PLC",
    },
  ];

  return (
    <>
      {/* Hero Section */}
      <section className="relative overflow-hidden">
        <div className="container mx-auto px-4 py-20 md:py-32">
          <div className="flex flex-col md:flex-row items-center">
            <motion.div
              className="md:w-1/2 mb-12 md:mb-0"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 dark:text-white mb-6">
                Custom ChatGPT for your business
              </h1>
              <p className="text-xl text-gray-600 dark:text-gray-300 mb-8">
                Build a custom GPT, embed it on your website, and let it handle
                customer support, lead generation, engage with your users, and
                more!
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <MotionLink
                  href="/register"
                  className="px-8 py-3 bg-emerald-600 hover:bg-emerald-700 text-white font-medium rounded-lg shadow-lg transition-colors flex items-center justify-center gap-2"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  Build your Agent
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 25 25"
                    width="20"
                    height="20"
                    className="fill-current"
                  >
                    <path d="m17.5 5.999-.707.707 5.293 5.293H1v1h21.086l-5.294 5.295.707.707L24 12.499l-6.5-6.5z" />
                  </svg>
                </MotionLink>
              </div>
              <p className="mt-3 text-sm text-gray-500 dark:text-gray-400">
                No credit card needed
              </p>
            </motion.div>

            <motion.div
              className="md:w-1/2 relative"
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <Image
                src="/images/chatzuri-hero.png"
                alt="Chatzuri"
                width={500} // or a fixed width
                height={300} // or a fixed height
                className="w-full max-w-2xl mx-auto"
                placeholder="blur"
                blurDataURL="/images/placeholder.jpg"
              />
            </motion.div>
          </div>
        </div>
      </section>

      {/* Website Preview Section */}
      <section className="py-16 bg-gray-50 dark:bg-gray-800">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto text-center mb-12">
            <motion.h2
              className="text-4xl font-bold text-gray-900 dark:text-white mb-4"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
            >
              Experience the Future of Customer Engagement
            </motion.h2>
            <motion.p
              className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
              viewport={{ once: true }}
            >
              See how our AI agent can transform your website interactions
            </motion.p>
          </div>

          <div className="flex flex-col lg:flex-row gap-12 items-center">
            {/* Left Side - Text Content */}
            <motion.div
              className="lg:w-1/2"
              initial={{ opacity: 0, x: -20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
            >
              <div className="space-y-6">
                <div className="flex items-start gap-4">
                  <motion.div
                    initial={{ scale: 0 }}
                    whileInView={{ scale: 1 }}
                    transition={{ duration: 0.5, delay: 0.2 }}
                    viewport={{ once: true }}
                    className="p-3 bg-emerald-100 dark:bg-emerald-900 rounded-full"
                  >
                    <Zap className="w-6 h-6 text-emerald-600 dark:text-emerald-300" />
                  </motion.div>
                  <div>
                    <h3 className="text-xl font-semibold text-gray-800 dark:text-white">
                      Instant Responses
                    </h3>
                    <p className="text-gray-600 dark:text-gray-300 mt-2">
                      Your visitors get immediate answers to their questions
                      24/7, without waiting.
                    </p>
                  </div>
                </div>

                <div className="flex items-start gap-4">
                  <motion.div
                    initial={{ scale: 0 }}
                    whileInView={{ scale: 1 }}
                    transition={{ duration: 0.5, delay: 0.3 }}
                    viewport={{ once: true }}
                    className="p-3 bg-blue-100 dark:bg-blue-900 rounded-full"
                  >
                    <BarChart2 className="w-6 h-6 text-blue-600 dark:text-blue-300" />
                  </motion.div>
                  <div>
                    <h3 className="text-xl font-semibold text-gray-800 dark:text-white">
                      Boost Conversions
                    </h3>
                    <p className="text-gray-600 dark:text-gray-300 mt-2">
                      Engage visitors at the right moment and guide them through
                      your conversion funnel.
                    </p>
                  </div>
                </div>

                <div className="flex items-start gap-4">
                  <motion.div
                    initial={{ scale: 0 }}
                    whileInView={{ scale: 1 }}
                    transition={{ duration: 0.5, delay: 0.4 }}
                    viewport={{ once: true }}
                    className="p-3 bg-purple-100 dark:bg-purple-900 rounded-full"
                  >
                    <Shield className="w-6 h-6 text-purple-600 dark:text-purple-300" />
                  </motion.div>
                  <div>
                    <h3 className="text-xl font-semibold text-gray-800 dark:text-white">
                      Data Privacy First
                    </h3>
                    <p className="text-gray-600 dark:text-gray-300 mt-2">
                      We prioritize your data security with enterprise-grade
                      protection for all interactions.
                    </p>
                  </div>
                </div>

                <motion.div
                  className="mt-8 p-6 bg-white/50 dark:bg-gray-700/50 rounded-xl border border-gray-200 dark:border-gray-600"
                  initial={{ opacity: 0 }}
                  whileInView={{ opacity: 1 }}
                  transition={{ duration: 0.5, delay: 0.5 }}
                  viewport={{ once: true }}
                >
                  <div className="flex items-center gap-3">
                    <CircleDollarSign className="w-8 h-8 text-emerald-600 dark:text-emerald-400" />
                    <h4 className="text-lg font-medium text-gray-800 dark:text-white">
                      No credit card required
                    </h4>
                  </div>
                  <p className="text-gray-600 dark:text-gray-300 mt-3">
                    Start your free trial today and experience the difference AI
                    can make for your business.
                  </p>
                </motion.div>
              </div>
            </motion.div>

            {/* Right Side - Form */}
            <motion.div
              className="lg:w-1/2 w-full"
              initial={{ opacity: 0, x: 20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
            >
              <div className="bg-white dark:bg-gray-700 p-8 rounded-xl shadow-lg border border-gray-100 dark:border-gray-600">
                <div className="text-center mb-6">
                  <motion.div
                    initial={{ scale: 0 }}
                    whileInView={{ scale: 1 }}
                    transition={{ duration: 0.5 }}
                    viewport={{ once: true }}
                    className="inline-flex items-center justify-center p-4 bg-emerald-100 dark:bg-emerald-900 rounded-full mb-4"
                  >
                    <Bot className="w-8 h-8 text-emerald-600 dark:text-emerald-300" />
                  </motion.div>
                  <h3 className="text-2xl font-bold text-gray-800 dark:text-white mb-2">
                    Try it out for free
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300">
                    Enter your website URL to see the magic in action
                  </p>
                </div>

                <DemoForm />

                <div className="mt-4 text-center text-sm text-gray-500 dark:text-gray-400">
                  <p>No installation required • 100% free trial</p>
                </div>
              </div>

              {showChatbotPreview && (
                <motion.div
                  id="agent-preview"
                  className="mt-8 bg-white dark:bg-gray-700 rounded-xl shadow-lg overflow-hidden border border-gray-200 dark:border-gray-600"
                  initial={{ opacity: 0, scale: 0.95 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.5 }}
                >
                  <div className="p-3 bg-gray-50 dark:bg-gray-600 flex items-center">
                    <div className="flex gap-1.5">
                      <div className="w-3 h-3 rounded-full bg-red-500"></div>
                      <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                      <div className="w-3 h-3 rounded-full bg-green-500"></div>
                    </div>
                    <p className="text-xs text-gray-500 dark:text-gray-300 mx-auto">
                      AI Agent Preview
                    </p>
                  </div>
                  <iframe
                    src="https://www.chatzuri.com/p/iframe/6749666406ca719dec13333a"
                    className="w-full h-[500px] border-0"
                    allowFullScreen
                  />
                </motion.div>
              )}
            </motion.div>
          </div>
        </div>
      </section>

      {/* Clients Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center mb-12">
            <motion.h2
              className="text-3xl font-bold text-gray-900 dark:text-white mb-4"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
            >
              Trusted by 2000+ businesses
            </motion.h2>
            <motion.p
              className="text-lg text-gray-600 dark:text-gray-300"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
              viewport={{ once: true }}
            >
              Leading teams choose Chatzuri for their various agents needs
            </motion.p>
          </div>

          <motion.div
            className="grid grid-cols-2 md:grid-cols-4 gap-8 items-center justify-center"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ duration: 0.5, staggerChildren: 0.1 }}
            viewport={{ once: true }}
          >
            {[
              "/images/logos/everick_logo.svg",
              "/images/logos/leidos-logo.svg",
              "/images/logos/mercado-libre-logo.svg",
              "/images/logos/palo-alto-logo.svg",
              "/images/logos/scotiabank-logo.svg",
              "/images/logos/siriusxm-logo.svg",
              "/images/logos/softmizer.svg",
              "/images/logos/general-mills-logo.svg",
            ].map((logo, index) => (
              <motion.div
                key={index}
                className="relative flex items-center justify-center p-4 bg-white dark:bg-gray-700 rounded-lg shadow-sm"
                whileHover={{ scale: 1.05 }}
                transition={{ duration: 0.2 }}
              >
                <div className="relative w-full h-16">
                  {" "}
                  <Image
                    src={logo}
                    alt="Client logo"
                    fill
                    priority={index < 2}
                    className="object-contain p-2"
                    sizes="(max-width: 768px) 50vw, (max-width: 1200px) 25vw, 20vw"
                    unoptimized={logo.endsWith(".svg")}
                  />
                </div>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="py-16 bg-gray-50 dark:bg-gray-800">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center mb-12">
            <motion.h2
              className="text-3xl font-bold text-gray-900 dark:text-white mb-4"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
            >
              How it works
            </motion.h2>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[
              {
                title: "Import Your Data",
                description:
                  "Connect your data sources, upload files, or add a website for crawling, and Chatzuri will use all that data to train your agents.",
                image: "/images/import-agents-data-chatzuri.png",
              },
              {
                title: "Customize Behavior & Appearance",
                description:
                  "Make your agents look like it's part of your website with custom colors and logos.",
                image: "/images/customize-chatzuri-agents.png",
              },
              {
                title: "Embed on Your Website",
                description:
                  "Add a chat widget to any website or app with a simple embed code.",
                image: "/images/embed-chatzuri-agents-in-website.png",
              },
              {
                title: "Integrate with Your Tools",
                description:
                  "Connect your agents to your favorite tools like Slack, WhatsApp, Zapier, and more.",
                image: "/images/integrate-chatzuri-agents-tools.png",
              },
            ].map((step, index) => (
              <motion.div
                key={index}
                className="relative bg-white dark:bg-gray-700 rounded-xl shadow-sm overflow-hidden"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ y: -5 }}
              >
                <div className="relative w-full h-48">
                  <Image
                    src={step.image}
                    alt={step.title}
                    fill
                    className="object-cover"
                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                    placeholder="blur"
                    blurDataURL="/images/placeholder.jpg"
                  />
                </div>

                {/* Content */}
                <div className="p-6">
                  <h3 className="text-xl font-semibold text-gray-800 dark:text-white mb-3">
                    Step {index + 1}: {step.title}
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300">
                    {step.description}
                  </p>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center mb-12">
            <motion.h2
              className="text-3xl font-bold text-gray-900 dark:text-white mb-4"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
            >
              Features
            </motion.h2>
            <motion.p
              className="text-lg text-gray-600 dark:text-gray-300"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
              viewport={{ once: true }}
            >
              Everything you need for your no-code AI agents
            </motion.p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {features.map((feature, index) => (
              <motion.div
                key={index}
                className="bg-white dark:bg-gray-700 p-6 rounded-xl shadow-sm border border-gray-100 dark:border-gray-600"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.05 }}
                viewport={{ once: true }}
                whileHover={{ scale: 1.02 }}
              >
                <div className="flex items-center mb-4">
                  <div className="p-2 bg-emerald-100 dark:bg-emerald-900/30 rounded-full mr-4">
                    {feature.icon}
                  </div>
                  <h3 className="text-xl font-semibold text-gray-800 dark:text-white">
                    {feature.title}
                  </h3>
                </div>
                <p className="text-gray-600 dark:text-gray-300">
                  {feature.description}
                </p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-emerald-600 text-white">
        <div className="container mx-auto px-4 text-center">
          <motion.h2
            className="text-3xl md:text-4xl font-bold mb-6"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            Human-like AI approaches for hyper-personalized interactions
          </motion.h2>
          <motion.p
            className="text-xl mb-8 max-w-3xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            viewport={{ once: true }}
          >
            Our partners don&apos;t just play the AI game—they win!
          </motion.p>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            viewport={{ once: true }}
          >
            <Link
              href="/register"
              className="inline-block px-8 py-3 bg-white text-emerald-600 font-medium rounded-lg shadow-lg hover:bg-gray-100 transition-colors"
            >
              Try for FREE
            </Link>
          </motion.div>
        </div>
      </section>

      {/* Use Cases Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center mb-12">
            <motion.h2
              className="text-3xl font-bold text-gray-900 dark:text-white mb-4"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
            >
              Use Cases
            </motion.h2>
            <motion.p
              className="text-lg text-gray-600 dark:text-gray-300"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
              viewport={{ once: true }}
            >
              How businesses are using Chatzuri
            </motion.p>
          </div>

          <div className="grid md:grid-cols-2 gap-6">
            {[
              {
                title: "Customer Support",
                description:
                  "Experience swift query resolutions, available 24/7, with personalized assistance tailored to your customers' needs.",
                bgColor: "bg-gray-800",
                textColor: "text-white",
                image: "/images/abstract2.jpg",
              },
              {
                title: "AI Personas to Engage Your Users",
                description:
                  "Create AI personas about any topic and have them engage with your users and provide them with a personalized experience.",
                bgColor: "bg-emerald-100",
                textColor: "text-gray-800",
                image: "/images/abstract3.jpg",
              },
              {
                title: "Lead Generation",
                description:
                  "Set your agents to collect leads and qualify them, all while providing a personalized experience to your customers.",
                bgColor: "bg-white",
                textColor: "text-gray-800",
                image: "/images/abstract12.jpg",
              },
              {
                title: "Multi-platform Agents",
                description:
                  "Add AI agents to your website, mobile app, or any other platform with a simple embed code or with our API.",
                bgColor: "bg-blue-600",
                textColor: "text-white",
                image: "/images/abstract6.jpg",
              },
              {
                title: "Employee Training & Onboarding",
                description:
                  "Set up an AI-powered training assistant that helps new employees learn company protocols and access documentation.",
                bgColor: "bg-yellow-100",
                textColor: "text-gray-800",
                image: "/images/abstract9.jpg",
              },
              {
                title: "Virtual Product Specialist",
                description:
                  "Create an expert AI consultant that helps customers find the perfect product match by understanding their needs.",
                bgColor: "bg-white",
                textColor: "text-gray-800",
                image: "/images/abstract18.jpg",
              },
            ].map((useCase, index) => (
              <motion.div
                key={index}
                className={`${useCase.bgColor} ${useCase.textColor} rounded-xl overflow-hidden shadow-lg`}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ y: -5 }}
              >
                <div className="p-6">
                  <h3 className="text-2xl font-bold mb-3">{useCase.title}</h3>
                  <p className="mb-4">{useCase.description}</p>
                </div>
                <div className="h-48 overflow-hidden">
                  <Image
                    src={useCase.image}
                    alt={useCase.title}
                    width={500} // or a fixed width
                    height={300} // or a fixed height
                    className="w-full h-full object-cover"
                    placeholder="blur"
                    blurDataURL="/images/placeholder.jpg"
                  />
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-gray-50 dark:bg-gray-800">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-3 gap-6">
            {[
              {
                value: "+4000",
                label: "Businesses using Chatzuri",
              },
              {
                value: "+78,981",
                label: "Files & URLs uploaded for training",
              },
              {
                value: "+96%",
                label: "Accuracy of Agents Answers",
              },
            ].map((stat, index) => (
              <motion.div
                key={index}
                className="bg-white dark:bg-gray-700 p-8 rounded-xl shadow-sm text-center"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <div className="text-5xl font-bold text-emerald-600 dark:text-emerald-400 mb-3">
                  {stat.value}
                </div>
                <p className="text-gray-600 dark:text-gray-300">{stat.label}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center mb-12">
            <motion.h2
              className="text-3xl font-bold text-gray-900 dark:text-white mb-4"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
            >
              What People Are Saying
            </motion.h2>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <motion.div
                key={index}
                className="bg-white dark:bg-gray-700 p-6 rounded-xl shadow-sm"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ y: -5 }}
              >
                <div className="flex items-center mb-4">
                  <div className="w-12 h-12 rounded-full overflow-hidden mr-4">
                    <Image
                      src={`/images/person${
                        index === 0 ? "10" : index === 1 ? "14" : "16"
                      }.jpg`}
                      alt={testimonial.name}
                      width={500} // or a fixed width
                      height={300} // or a fixed height
                      className="w-full h-full object-cover"
                      placeholder="blur"
                      blurDataURL="/images/placeholder.jpg"
                    />
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-800 dark:text-white">
                      {testimonial.name}
                    </h4>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      {testimonial.role}
                    </p>
                  </div>
                </div>
                <div className="relative">
                  <Quote className="absolute -top-2 -left-2 w-5 h-5 text-gray-300 dark:text-gray-600" />
                  <p className="text-gray-600 dark:text-gray-300 pl-6 relative">
                    {testimonial.quote}
                  </p>
                  <Quote className="absolute -bottom-2 -right-2 w-5 h-5 text-gray-300 dark:text-gray-600 transform rotate-180" />
                </div>
              </motion.div>
            ))}
          </div>

          <motion.div
            className="text-center mt-12"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <Link
              href="/register"
              className="inline-block px-8 py-3 bg-emerald-600 hover:bg-emerald-700 text-white font-medium rounded-lg shadow-lg transition-colors"
            >
              Join us Today
            </Link>

            <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
              No credit card required
            </p>
          </motion.div>
        </div>
      </section>

      {/* Final CTA */}
      <section className="py-16 bg-emerald-50 dark:bg-gray-800">
        <div className="container mx-auto px-4 text-center">
          <motion.div
            className="max-w-4xl mx-auto bg-white dark:bg-gray-700 p-8 md:p-12 rounded-xl shadow-lg"
            initial={{ opacity: 0, scale: 0.95 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
              AI AgentsLoadingState built for you. To meet all your needs.
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 mb-8">
              One AI Agents partner. Endless possibilities.
            </p>
            <Link
              href="/register"
              className="inline-block px-8 py-3 bg-emerald-600 hover:bg-emerald-700 text-white font-medium rounded-lg shadow-lg transition-colors"
            >
              Try for Free
            </Link>
            <p className="mt-3 text-sm text-gray-500 dark:text-gray-400">
              No credit card required
            </p>
          </motion.div>
        </div>
      </section>
    </>
  );
}
