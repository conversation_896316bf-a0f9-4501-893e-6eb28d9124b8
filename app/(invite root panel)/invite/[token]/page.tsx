import Image from "next/image";
import dbConnect from "@/lib/db/db";
import { createHash } from "crypto";
import { TeamInvitationModel } from "@/models";
import { StatusEnum } from "@/enums/enums";
import { authOptions } from "@/lib/auth/auth";
import { getServerSession } from "next-auth";
import { Button } from "@/components/ui/button";
import { redirect } from "next/navigation";
import { Suspense } from "react";
import { Loading } from "@/components/loader/loader";
import Link from "next/link";

type Params = Promise<{ token: string }>;

export default async function InvitePage({ params }: { params: Params }) {
  const { token } = await params;
  return (
    <Suspense fallback={<Loading />}>
      <InvitePageContent token={token} />
    </Suspense>
  );
}

async function InvitePageContent({ token }: { token: string }) {
  await dbConnect();
  const tokenHash = createHash("sha256").update(token).digest("hex");
  const invitationPromise = TeamInvitationModel.findOne({
    tokenHash,
    status: StatusEnum.PENDING,
    expiresAt: { $gt: new Date() },
  }).populate<{ teamId: { name: string } }>("teamId");

  const sessionPromise = getServerSession(authOptions);

  const [invitation, session] = await Promise.all([
    invitationPromise,
    sessionPromise,
  ]);

  if (!invitation) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <div className="max-w-md w-full bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden border border-gray-200 dark:border-gray-700">
          <div className="w-full h-48 relative">
            <Image
              src="/images/expired.jpg"
              alt="Expired invitation"
              fill
              className="object-cover"
              quality={100}
              priority
              placeholder="blur"
              blurDataURL="/images/placeholder.jpg"
            />
          </div>

          {/* Content section */}
          <div className="p-8 text-center">
            <h2 className="text-2xl font-bold text-gray-800 dark:text-gray-100 mb-2">
              Invitation Expired
            </h2>
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              This invitation link is no longer valid.
            </p>
            <Button asChild variant="default" className="cursor-pointer">
              <Link href="/" className="w-full">
                Return to Home
              </Link>
            </Button>
          </div>
        </div>
      </div>
    );
  }

  const userEmail = session?.user?.email;

  // If user is logged in but email doesn't match
  if (session && userEmail && invitation.email !== userEmail) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <div className="max-w-md w-full bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden border border-gray-200 dark:border-gray-700">
          <div className="p-8 text-center relative">
            <div className="w-full h-48 relative mb-6 rounded-lg overflow-hidden">
              <Image
                src="/images/warning.jpg"
                alt="Wrong account"
                fill
                className="object-cover"
                quality={80}
                priority
                placeholder="blur"
                blurDataURL="/images/placeholder.jpg"
              />
            </div>

            <h2 className="text-2xl font-bold text-gray-800 dark:text-gray-100 mb-2">
              Account Mismatch
            </h2>
            <p className="text-gray-600 dark:text-gray-300 mb-4">
              You&apos;re signed in as{" "}
              <span className="font-medium text-gray-900 dark:text-gray-100">
                {userEmail}
              </span>{" "}
              but this invitation is for{" "}
              <span className="font-medium text-gray-900 dark:text-gray-100">
                {invitation.email}
              </span>
              .
            </p>

            <div className="flex flex-col space-y-3">
              <form action="/api/auth/signout" method="POST">
                <input
                  type="hidden"
                  name="callbackUrl"
                  value={`/invite/${token}`}
                />
                <Button
                  type="submit"
                  variant="outline"
                  className="cursor-pointer w-full border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700"
                >
                  Sign Out and Use Correct Account
                </Button>
              </form>
              <Button asChild variant="default" className="cursor-pointer">
                <Link href="/" className="w-full">
                  Return to Home
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // If user is logged in with correct email
  if (session && userEmail && invitation.email === userEmail) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <div className="max-w-md w-full bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden border border-gray-200 dark:border-gray-700">
          <div className="w-full h-48 relative">
            {" "}
            <Image
              src="/images/success.jpg"
              alt="Success"
              fill
              className="object-cover"
              quality={100}
              priority
              placeholder="blur"
              blurDataURL="/images/placeholder.jpg"
            />
          </div>
          <div className="p-8 text-center">
            <h1 className="text-3xl font-bold text-gray-800 dark:text-gray-100 mb-2">
              Join {invitation.teamId.name}
            </h1>
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              You&apos;re now signed in as{" "}
              <span className="font-medium text-gray-900 dark:text-gray-100">
                {userEmail}
              </span>
            </p>
            <form action={`/api/invite/${token}`} method="POST">
              <input type="hidden" name="token" value={token} />
              <Button
                type="submit"
                className="w-full mb-3 cursor-pointer"
                size="lg"
                variant="default"
              >
                Accept Invitation
              </Button>
            </form>
          </div>
        </div>
      </div>
    );
  }

  // If user is not logged in
  return redirect(
    `/login?callbackUrl=${encodeURIComponent(`/invite/${token}`)}`
  );
}
