import "../globals.css";
import { Providers } from "@/providers/provider";
import { Loading } from "@/components/loader/loader";
import { Suspense } from "react";
import { Toaster } from "react-hot-toast";
import { BackToTop } from "@/components/back-to-top/back-to-top";
import { OfflineBanner } from "@/components/offline-barner/offline-barner";
import Consent from "@/components/cookie-consent/consent";
import AuthFooter from "@/components/footer/auth-footer";
import { AuthHeader } from "@/components/header/auth-header";
import { geistMono, geistSans } from "@/fonts/fonts";
import { viewportConfig } from "@/viewport/viewport";
import { getDefaultMetadata } from "@/utils/site/defaults";

export const viewport = viewportConfig;
export const metadata = getDefaultMetadata();

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={`${geistSans.variable} ${geistMono.variable} relative antialiased`}
      >
        <Providers>
          <Suspense fallback={<Loading />}>
            <AuthHeader />
            {children}
            <AuthFooter />
            <BackToTop />
            <Consent />
            <OfflineBanner />
            <Toaster
              position="top-center"
              toastOptions={{
                className: "rounded-full",
                duration: 4000,
                success: {
                  duration: 3000,
                  className: "rounded-full",
                },
                error: {
                  duration: 5000,
                  className: "rounded-full",
                },
              }}
            />
          </Suspense>
        </Providers>
      </body>
    </html>
  );
}
