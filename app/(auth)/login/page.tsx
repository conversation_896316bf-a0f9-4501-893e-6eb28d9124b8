"use client";

import { motion } from "framer-motion";
import Image from "next/image";
import Link from "next/link";
import { containerVariants, itemVariants } from "@/variants/variants";
import LoginForm from "@/components/forms/login-from";
import GoogleSigning from "@/components/sign-in/google";

export default function SignInPage() {
  return (
    <div className="min-h-screen flex flex-col">
      <div className="h-12"></div>
      <main className="flex flex-1 flex-col lg:flex-row items-center justify-center p-4 gap-8">
        {/* Left Side - Welcome Section */}
        <motion.div
          initial={{ opacity: 0, x: -50 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.8 }}
          className="relative w-full  lg:w-1/2 min-h-[500px] lg:min-h-screen flex items-center justify-center"
        >
          {/* Background Image */}
          <div className="absolute inset-0 overflow-hidden rounded-2xl">
            <Image
              src="/images/welcome-illustration.jpg"
              alt="Background"
              fill
              priority
              className="object-cover object-center"
              quality={100}
              placeholder="blur"
              blurDataURL="/images/placeholder.jpg"
            />
            {/* Overlay */}
            <div className="absolute inset-0 bg-black/40 dark:bg-black/60 backdrop-blur-sm" />
          </div>

          {/* Content */}
          <div className="relative z-10 flex flex-col items-center text-center max-w-md px-8 py-12 text-white">
            {/* Title */}
            <motion.h1
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.3, duration: 0.5 }}
              className="text-4xl lg:text-5xl font-bold mb-6 text-white drop-shadow-lg"
            >
              Welcome Back
            </motion.h1>

            {/* Description */}
            <motion.p
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.4, duration: 0.5 }}
              className="text-lg lg:text-xl mb-8 text-gray-100 dark:text-gray-200 drop-shadow-md"
            >
              Build a custom GPT, embed it on your website and let it handle
              customer support, lead generation, engage with your users, and
              more.
            </motion.p>

            {/* Animated Dots */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.6, duration: 0.5 }}
              className="hidden lg:flex gap-4"
            >
              <div
                className="w-4 h-4 rounded-full bg-emerald-400 animate-pulse"
                style={{ animationDelay: "0s" }}
              />
              <div
                className="w-4 h-4 rounded-full bg-emerald-300 animate-pulse"
                style={{ animationDelay: "0.2s" }}
              />
              <div
                className="w-4 h-4 rounded-full bg-emerald-200 animate-pulse"
                style={{ animationDelay: "0.4s" }}
              />
            </motion.div>
          </div>
        </motion.div>

        {/* Right Side - Sign In Form */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="w-full max-w-md bg-white dark:bg-gray-800 rounded-2xl shadow-xl overflow-hidden"
        >
          <div className="p-8">
            <motion.div
              variants={containerVariants}
              initial="hidden"
              animate="visible"
            >
              <motion.div variants={itemVariants} className="text-center mb-8">
                <h2 className="text-2xl font-bold text-gray-800 dark:text-white">
                  Sign In
                </h2>
                <p className="text-gray-500 dark:text-gray-400 mt-2">
                  Access your account to continue
                </p>
              </motion.div>

              {/* Google Sign In */}
              <motion.div variants={itemVariants} className="mb-6">
                <GoogleSigning />
              </motion.div>

              {/* Divider */}
              <motion.div
                variants={itemVariants}
                className="relative flex items-center justify-center my-6"
              >
                <div className="absolute inset-0 flex items-center">
                  <div className="w-full border-t border-gray-200 dark:border-gray-700" />
                </div>
                <div className="relative px-2 bg-white dark:bg-gray-800 text-sm text-gray-500 dark:text-gray-400">
                  Or with email
                </div>
              </motion.div>

              {/* Form */}
              <LoginForm />

              <motion.div
                variants={itemVariants}
                className="mt-6 text-center text-sm text-gray-600 dark:text-gray-400"
              >
                Don&apos;t have an account?{" "}
                <Link
                  href="/register"
                  className="font-medium text-emerald-600 dark:text-emerald-400 hover:text-emerald-800 dark:hover:text-emerald-300"
                >
                  Sign up
                </Link>
              </motion.div>
            </motion.div>
          </div>
        </motion.div>
      </main>
    </div>
  );
}
