import { NextResponse } from "next/server";
import crypto from "crypto";
import dbConnect from "@/lib/db/db";
import { UserModel } from "@/models";
import { ForgotPasswordSchema } from "@/validations/validations";
import { sendEmail } from "@/utils/mail/mail";
import { generatePasswordResetTemplate } from "@/utils/mail/templates/reset-password";

export async function POST(req: Request) {
  try {
    const body = await req.json();
    const { email } = ForgotPasswordSchema.parse(body);

    await dbConnect();

    const user = await UserModel.findOne({ email });
    if (!user) {
      return NextResponse.json(
        { message: "If the email exists, a reset link will be sent." },
        { status: 200 }
      );
    }

    // Generate reset token and expiry
    const token = crypto.randomBytes(32).toString("hex");
    const tokenExpiry = new Date(Date.now() + 1000 * 60 * 30); // 30 min

    user.resetPasswordToken = token;
    user.resetPasswordExpires = tokenExpiry;
    await user.save();

    const resetUrl = `${process.env.NEXT_PUBLIC_SITE_URL}/reset-password?token=${token}`;

    const html = generatePasswordResetTemplate({
      name: user.email.split("@")[0],
      resetUrl,
    });

    await sendEmail({
      sender: {
        name: "Chatzuri Support",
        address: process.env.MAIL_USER!,
      },
      recipients: [{ name: user.email.split("@")[0], address: user.email }],
      subject: "Reset your password",
      message: "Click the button below to reset your password",
      html,
    });

    return NextResponse.json({ message: "Reset link sent" }, { status: 200 });
    //eslint-disable-next-line @typescript-eslint/no-explicit-any
  } catch (error: any) {
    return NextResponse.json({ message: error.message }, { status: 500 });
  }
}
