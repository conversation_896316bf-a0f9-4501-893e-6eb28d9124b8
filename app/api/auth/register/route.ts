import { NextResponse } from "next/server";
import { RegisterSchema } from "@/validations/validations";
import crypto from "crypto";
import { UserModel } from "@/models";
import dbConnect from "@/lib/db/db";
import { hashPassword } from "@/utils/hash-password";
import { adminEmails } from "@/config/admin-emails";
import { generateVerificationEmailTemplate } from "@/utils/mail/templates/verify";
import { sendEmail } from "@/utils/mail/mail";

export async function POST(req: Request) {
  try {
    const body = await req.json();
    const token = crypto.randomBytes(32).toString("hex");
    const tokenExpires = new Date(Date.now() + 30 * 60 * 1000); // 30 mins

    // Validate with Zod schema
    const { email, password, mobile } = RegisterSchema._def.schema
      .pick({
        email: true,
        password: true,
        mobile: true,
      })
      .parse(body);

    await dbConnect();

    // Check if user exists
    const existingUser = await UserModel.findOne({ email })
      .select("_id")
      .lean();
    if (existingUser) {
      return NextResponse.json(
        {
          message: "User already exists",
          errorType: "USER_EXISTS",
        },
        { status: 409 }
      );
    }

    // Create new user
    const hashedPassword = await hashPassword(password);
    const isAdmin = adminEmails.includes(email);

    await UserModel.create({
      email,
      billingEmail:email,
      password: hashedPassword,
      mobile,
      emailVerified: isAdmin,
      role: isAdmin ? "admin" : "user",
      canLogin: true,
      isAdmin,
      ...(!isAdmin && {
        emailVerificationToken: token,
        emailVerificationExpires: tokenExpires,
      }),
    });

    if (!isAdmin) {
      // Send verification email
      const verifyUrl = `${process.env.NEXT_PUBLIC_SITE_URL}/verify-email?token=${token}`;
      const html = generateVerificationEmailTemplate({
        name: email.split("@")[0],
        verifyUrl,
      });

      await sendEmail({
        sender: {
          name: "Chatzuri Support",
          address: process.env.MAIL_USER!,
        },
        recipients: [{ name: email.split("@")[0], address: email }],
        subject: "Verify Your Email - Chatzuri",
        message: "Please verify your email to activate your account.",
        html,
      });
    }

    return NextResponse.json(
      { message: "Account created successfully" },
      { status: 201 }
    );
    //eslint-disable-next-line @typescript-eslint/no-explicit-any
  } catch (error: any) {
    console.error("Registration error:", error);

    // Handle Zod validation errors
    if (error.name === "ZodError") {
      return NextResponse.json(
        {
          message: "Validation failed",
          errors: error.errors,
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        message:
          process.env.NODE_ENV === "development"
            ? error.message
            : "Registration failed. Please try again.",
      },
      { status: 500 }
    );
  }
}
