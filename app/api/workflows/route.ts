import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth/auth";
import dbConnect from "@/lib/db/db";
import { WorkflowModel, TeamModel, MemberModel } from "@/models";
import { redis } from "@/lib/redis/redis";

const CACHE_TTL = 300; // 5 minutes
const WORKFLOWS_CACHE_PREFIX = "workflows:team:";

interface WorkflowApiResponse {
  success: boolean;
  data?: any;
  error?: string;
  pagination?: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

interface CreateWorkflowDto {
  name: string;
  description?: string;
  teamId: string;
  template?: string;
  triggers: string[];
  nodes?: any[];
  edges?: any[];
}

// GET /api/workflows - List workflows for team
export async function GET(request: Request) {
  const session = await getServerSession(authOptions);

  if (!session?.user?.id) {
    return NextResponse.json<WorkflowApiResponse>(
      { success: false, error: "Unauthorized" },
      { status: 401 }
    );
  }

  try {
    await dbConnect();
    const url = new URL(request.url);
    const teamId = url.searchParams.get("teamId");
    const page = parseInt(url.searchParams.get("page") || "1");
    const limit = Math.min(parseInt(url.searchParams.get("limit") || "10"), 50);
    const search = url.searchParams.get("search") || "";

    const userId = session.user.id;

    if (!teamId) {
      return NextResponse.json<WorkflowApiResponse>(
        { success: false, error: "Team ID is required" },
        { status: 400 }
      );
    }

    const cacheKey = `${WORKFLOWS_CACHE_PREFIX}${teamId}:user:${userId}:page:${page}:limit:${limit}:search:${search}`;

    // Try to get cached result
    const cachedResult = await redis.get(cacheKey);
    if (cachedResult) {
      return NextResponse.json<WorkflowApiResponse>(JSON.parse(cachedResult));
    }

    // Check if user has access to the team
    const team = await TeamModel.findById(teamId);
    if (!team) {
      return NextResponse.json<WorkflowApiResponse>(
        { success: false, error: "Team not found" },
        { status: 404 }
      );
    }

    // Check if user is owner or member
    const isOwner = team.ownerId.toString() === userId;
    const isMember = !isOwner && await MemberModel.findOne({
      teamId: teamId,
      userId: userId,
      status: "accepted"
    });

    if (!isOwner && !isMember) {
      return NextResponse.json<WorkflowApiResponse>(
        { success: false, error: "Access denied to this team" },
        { status: 403 }
      );
    }

    // Build query for workflows in this team
    const query: any = { teamId: teamId, isActive: true };
    
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: "i" } },
        { description: { $regex: search, $options: "i" } }
      ];
    }

    const skip = (page - 1) * limit;
    const [workflows, total] = await Promise.all([
      WorkflowModel.find(query)
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .populate("userId", "name email")
        .lean(),
      WorkflowModel.countDocuments(query)
    ]);

    const response = {
      success: true,
      data: workflows.map(workflow => ({
        ...workflow,
        _id: workflow._id.toString(),
        userId: workflow.userId.toString(),
        teamId: workflow.teamId.toString(),
      })),
      pagination: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    };

    // Cache the result
    await redis.set(
      cacheKey,
      JSON.stringify(response),
      "EX",
      CACHE_TTL
    );

    return NextResponse.json<WorkflowApiResponse>(response);

  } catch (error) {
    console.error("Error fetching workflows:", error);
    return NextResponse.json<WorkflowApiResponse>(
      { success: false, error: "Failed to fetch workflows" },
      { status: 500 }
    );
  }
}

// POST /api/workflows - Create new workflow
export async function POST(request: Request) {
  const session = await getServerSession(authOptions);

  if (!session?.user?.id) {
    return NextResponse.json<WorkflowApiResponse>(
      { success: false, error: "Unauthorized" },
      { status: 401 }
    );
  }

  try {
    await dbConnect();
    const userId = session.user.id;
    const body: CreateWorkflowDto = await request.json();

    // Validate required fields
    if (!body.name || !body.teamId) {
      return NextResponse.json<WorkflowApiResponse>(
        { success: false, error: "Missing required fields: name, teamId" },
        { status: 400 }
      );
    }

    // Check if user has access to the team
    const team = await TeamModel.findById(body.teamId);
    if (!team) {
      return NextResponse.json<WorkflowApiResponse>(
        { success: false, error: "Team not found" },
        { status: 404 }
      );
    }

    // Check if user is owner or member
    const isOwner = team.ownerId.toString() === userId;
    const isMember = !isOwner && await MemberModel.findOne({
      teamId: body.teamId,
      userId: userId,
      status: "accepted"
    });

    if (!isOwner && !isMember) {
      return NextResponse.json<WorkflowApiResponse>(
        { success: false, error: "Access denied to this team" },
        { status: 403 }
      );
    }

    // Create the workflow
    const newWorkflow = await WorkflowModel.create({
      ...body,
      userId: userId,
      teamId: body.teamId,
      triggers: body.triggers || [],
      nodes: body.nodes || [],
      edges: body.edges || [],
      version: "1.0.0",
      status: "draft",
    });

    // Invalidate cache for this team
    const cachePattern = `${WORKFLOWS_CACHE_PREFIX}${body.teamId}:*`;
    const keys = await redis.keys(cachePattern);
    if (keys.length > 0) {
      await redis.del(...keys);
    }

    const response = {
      success: true,
      data: {
        ...newWorkflow.toObject(),
        _id: newWorkflow._id.toString(),
        userId: newWorkflow.userId.toString(),
        teamId: newWorkflow.teamId.toString(),
      }
    };

    return NextResponse.json<WorkflowApiResponse>(response, { status: 201 });

  } catch (error) {
    console.error("Error creating workflow:", error);
    return NextResponse.json<WorkflowApiResponse>(
      { success: false, error: "Failed to create workflow" },
      { status: 500 }
    );
  }
}
