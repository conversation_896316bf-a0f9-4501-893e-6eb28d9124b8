import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import { ObjectId } from 'mongodb';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/auth';
import { getDirectExecutor } from '@/lib/execution/direct-executor';
import { getQueueManager } from '@/lib/execution/queue-manager';

// Execute workflow manually
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ workflowId: string }> }
) {
  try {
    // Get user session.
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { workflowId } = await params;
    const {
      input = {},
      variables = {},
      mode = 'async',
      startNodeId = null
    } = await request.json();

    console.log(`Manual execution requested for workflow: ${workflowId}`);
    console.log(`🔧 Execution mode received: ${mode}`);

    // Validate workflow access
    const { db } = await connectToDatabase();

    // First, find the workflow
    const workflow = await db.collection('workflows').findOne({
      _id: new ObjectId(workflowId)
    });

    if (!workflow) {
      return NextResponse.json(
        { error: 'Workflow not found' },
        { status: 404 }
      );
    }

    // Then check access more flexibly 
    const userId = session.user.id?.toString();
    const userTeamId = session.user.teamId?.toString();
    const workflowUserId = workflow.userId?.toString();
    const workflowTeamId = workflow.teamId?.toString();

    console.log('Execute access check:', {
      userId,
      userTeamId,
      workflowUserId,
      workflowTeamId
    });

    // Allow access if user owns the workflow or is in the same team
    const ownsWorkflow = workflowUserId === userId;
    const sameTeam = workflowTeamId && userTeamId && workflowTeamId === userTeamId;

    if (!ownsWorkflow && !sameTeam) {
      return NextResponse.json(
        { error: 'Access denied' },
        { status: 403 }
      );
    }

    // Validate workflow has nodes
    if (!workflow.nodes || workflow.nodes.length === 0) {
      return NextResponse.json(
        { error: 'Workflow has no nodes to execute' },
        { status: 400 }
      );
    }

    // If startNodeId is specified, validate it exists
    if (startNodeId) {
      const nodeExists = workflow.nodes.some((node: any) => node.id === startNodeId);
      if (!nodeExists) {
        return NextResponse.json(
          { error: `Start node ${startNodeId} not found in workflow` },
          { status: 400 }
        );
      }
    }

    const executionId = `manual_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // Prepare trigger data
    const triggerData = {
      input,
      variables,
      startNodeId,
      triggeredBy: session.user.email,
      timestamp: new Date().toISOString()
    };

    // Prepare initial variables
    const initialVariables = {
      ...variables,
      manual_input: input,
      triggered_by: session.user.email,
      trigger_timestamp: new Date().toISOString()
    };

    console.log(`🚀 Starting LangGraph workflow execution: ${workflow.name}`);
    console.log(`📋 Execution ID: ${executionId}`);
    console.log(`👤 User ID: ${userId}`);
    console.log(`⚙️ Mode: ${mode}`);
    console.log(`🔧 Input:`, input);
    console.log(`📊 Variables:`, initialVariables);

    // Note: Initial execution record will be saved by the direct executor
    console.log(`📋 Execution will be tracked with ID: ${executionId}`);

    // Get direct executor and execute the workflow
    const directExecutor = getDirectExecutor();

    console.log(`🚀 Starting direct workflow execution...`);

    // Execute in background for async mode
    if (mode === 'async') {
      try {
        // Get queue manager and queue the workflow execution
        console.log(`📋 Queueing workflow execution for async processing...`);
        const queueManager = await getQueueManager();

        const job = await queueManager.queueWorkflowExecution(
          workflowId,
          executionId,
          userId,
          { ...input, ...initialVariables },
          'async'
        );

        console.log(`✅ Workflow queued successfully with job ID: ${job.id}`);

        // Return immediate response
        return NextResponse.json({
          success: true,
          executionId,
          jobId: job.id,
          mode: 'async',
          message: 'Workflow execution queued successfully with Redis/BullMQ',
          timestamp: new Date().toISOString(),
          statusUrl: `/api/workflows/${workflowId}/executions/${executionId}/status`,
          logs: [
            `🚀 Queue Manager initialized`,
            `📋 Job queued with ID: ${job.id}`,
            `🔄 Processing via Redis/BullMQ...`,
            `📊 Monitor progress via status URL`
          ]
        });
      } catch (error) {
        console.error(`❌ Failed to queue workflow execution:`, error);

        // Fallback to direct execution if queue fails
        console.log(`🔄 Falling back to direct execution...`);
        directExecutor.executeWorkflow(workflowId, executionId, userId, { ...input, ...initialVariables })
          .then(result => {
            console.log(`✅ Fallback execution completed: ${executionId}`);
          })
          .catch(error => {
            console.error(`❌ Fallback execution failed: ${executionId}`, error);
          });

        return NextResponse.json({
          success: true,
          executionId,
          mode: 'async',
          message: 'Workflow execution started (fallback to direct execution)',
          timestamp: new Date().toISOString(),
          statusUrl: `/api/workflows/${workflowId}/executions/${executionId}/status`,
          logs: [
            `⚠️ Queue unavailable, using fallback`,
            `📋 Direct execution started`,
            `🔄 Processing in background...`,
            `📊 Monitor progress via status URL`
          ]
        });
      }
    } else {
      // Synchronous execution
      try {
        const result = await directExecutor.executeWorkflow(workflowId, executionId, userId, { ...input, ...initialVariables });

        return NextResponse.json({
          success: true,
          executionId,
          mode: 'sync',
          result: {
            status: result.status,
            startTime: result.startTime,
            endTime: result.endTime,
            nodeResults: result.nodeResults,
            variables: result.variables,
            messages: result.messages?.map((msg: any) => ({
              type: msg._getType(),
              content: msg.content
            }))
          },
          timestamp: new Date().toISOString()
        });
      } catch (error) {
        console.error(`❌ Synchronous workflow execution failed:`, error);

        return NextResponse.json({
          success: false,
          executionId,
          error: error instanceof Error ? error.message : 'Execution failed',
          mode: 'sync',
          timestamp: new Date().toISOString()
        }, { status: 500 });
      }
    }

  } catch (error) {
    console.error('Manual workflow execution API error:', error);
    
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// Get workflow execution info/schema
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ workflowId: string }> }
) {
  try {
    // Get user session
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { workflowId } = await params;

    // Validate workflow access
    const { db } = await connectToDatabase();

    // First, find the workflow
    const workflow = await db.collection('workflows').findOne({
      _id: new ObjectId(workflowId)
    });

    if (!workflow) {
      return NextResponse.json(
        { error: 'Workflow not found' },
        { status: 404 }
      );
    }

    // Then check access more flexibly
    const userId = session.user.id?.toString();
    const userTeamId = session.user.teamId?.toString();
    const workflowUserId = workflow.userId?.toString();
    const workflowTeamId = workflow.teamId?.toString();

    // Allow access if user owns the workflow or is in the same team
    const ownsWorkflow = workflowUserId === userId;
    const sameTeam = workflowTeamId && userTeamId && workflowTeamId === userTeamId;

    if (!ownsWorkflow && !sameTeam) {
      return NextResponse.json(
        { error: 'Access denied' },
        { status: 403 }
      );
    }

    // Get workflow execution info
    const triggerNodes = workflow.nodes.filter((node: any) => 
      ['webhook', 'schedule', 'message'].includes(node.type)
    );

    const entryNodes = workflow.nodes.filter((node: any) => {
      const hasIncomingEdge = workflow.edges.some((edge: any) => edge.target === node.id);
      return !hasIncomingEdge || ['webhook', 'schedule', 'message'].includes(node.type);
    });

    // Get recent executions
    const recentExecutions = await db.collection('workflow_executions')
      .find({ workflowId })
      .sort({ startTime: -1 })
      .limit(10)
      .toArray();

    // Calculate execution stats
    const totalExecutions = await db.collection('workflow_executions')
      .countDocuments({ workflowId });

    const successfulExecutions = await db.collection('workflow_executions')
      .countDocuments({ workflowId, status: 'completed' });

    const failedExecutions = await db.collection('workflow_executions')
      .countDocuments({ workflowId, status: 'failed' });

    return NextResponse.json({
      workflowId,
      name: workflow.name,
      description: workflow.description,
      nodeCount: workflow.nodes.length,
      edgeCount: workflow.edges.length,
      triggerNodes: triggerNodes.map((node: any) => ({
        id: node.id,
        type: node.type,
        label: node.data.label,
        config: node.data.config
      })),
      entryNodes: entryNodes.map((node: any) => ({
        id: node.id,
        type: node.type,
        label: node.data.label
      })),
      executionSchema: {
        input: {
          type: 'object',
          description: 'Input data for workflow execution',
          properties: {
            message: { type: 'string', description: 'Input message' },
            data: { type: 'object', description: 'Input data object' }
          }
        },
        variables: {
          type: 'object',
          description: 'Initial variables for workflow',
          properties: {
            user_id: { type: 'string', description: 'User identifier' },
            session_id: { type: 'string', description: 'Session identifier' }
          }
        }
      },
      stats: {
        totalExecutions,
        successfulExecutions,
        failedExecutions,
        successRate: totalExecutions > 0 ? (successfulExecutions / totalExecutions) * 100 : 0,
        lastExecution: recentExecutions[0]?.startTime || null
      },
      recentExecutions: recentExecutions.map(exec => ({
        executionId: exec.executionId,
        status: exec.status,
        triggerType: exec.triggerType,
        startTime: exec.startTime,
        endTime: exec.endTime,
        nodeCount: exec.nodeExecutions?.length || 0
      })),
      examples: {
        basicExecution: {
          input: { message: "Hello, world!" },
          variables: { user_name: "John Doe" }
        },
        advancedExecution: {
          input: { 
            message: "Process this data",
            data: { items: [1, 2, 3] }
          },
          variables: { 
            user_id: "user_123",
            session_id: "session_456"
          }
        }
      }
    });

  } catch (error) {
    console.error('Workflow execution info API error:', error);
    
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
