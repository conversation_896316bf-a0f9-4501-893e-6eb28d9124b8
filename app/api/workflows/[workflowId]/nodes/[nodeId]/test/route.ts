import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import { ObjectId } from 'mongodb';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/auth';
import { getDirectExecutor } from '@/lib/execution/direct-executor';

// Test single node execution
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ workflowId: string; nodeId: string }> }
) {
  try {
    // Get user session
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { workflowId, nodeId } = await params;
    const { input = {}, variables = {}, mode = 'async' } = await request.json();

    console.log(`Testing node: ${nodeId} in workflow: ${workflowId}`);

    // Validate workflow access
    const { db } = await connectToDatabase();

    // First, find the workflow
    const workflow = await db.collection('workflows').findOne({
      _id: new ObjectId(workflowId)
    });

    if (!workflow) {
      return NextResponse.json(
        { error: 'Workflow not found' },
        { status: 404 }
      );
    }

    // Then check access more flexibly
    const userId = session.user.id?.toString();
    const userTeamId = session.user.teamId?.toString();
    const workflowUserId = workflow.userId?.toString();
    const workflowTeamId = workflow.teamId?.toString();

    console.log('Node test access check:', {
      userId,
      userTeamId,
      workflowUserId,
      workflowTeamId
    });

    // Allow access if user owns the workflow or is in the same team
    const ownsWorkflow = workflowUserId === userId;
    const sameTeam = workflowTeamId && userTeamId && workflowTeamId === userTeamId;

    if (!ownsWorkflow && !sameTeam) {
      return NextResponse.json(
        { error: 'Access denied' },
        { status: 403 }
      );
    }

    // Validate node exists
    const node = workflow.nodes.find((n: any) => n.id === nodeId);
    if (!node) {
      return NextResponse.json(
        { error: 'Node not found in workflow' },
        { status: 404 }
      );
    }

    const executionId = `test_${Date.now()}_${nodeId}_${Math.random().toString(36).substr(2, 6)}`;

    console.log(`🔧 Starting LangGraph node test: ${node.data.label} (${node.type})`);
    console.log(`📋 Execution ID: ${executionId}`);
    console.log(`🔧 Node ID: ${nodeId}`);
    console.log(`⚙️ Mode: ${mode}`);
    console.log(`🔧 Input:`, input);
    console.log(`📊 Variables:`, variables);

    // Get direct executor and test the node
    const directExecutor = getDirectExecutor();

    console.log(`🚀 Starting direct node test...`);

    // Execute in background for async mode
    if (mode === 'async') {
      // Start execution in background
      directExecutor.testNode(workflowId, nodeId, userId, input, variables)
        .then(result => {
          console.log(`✅ Background node test completed: ${nodeId}`);
        })
        .catch(error => {
          console.error(`❌ Background node test failed: ${nodeId}`, error);
        });

      // Return immediate response
      return NextResponse.json({
        success: true,
        executionId,
        mode: 'async',
        message: 'Node test started successfully with LangGraph',
        timestamp: new Date().toISOString(),
        statusUrl: `/api/workflows/${workflowId}/nodes/${nodeId}/test/${executionId}/status`,
        logs: [
          `🔧 LangGraph node test engine initialized`,
          `📋 Direct node test started`,
          `🔄 Processing in background...`,
          `📊 Monitor progress via status URL`
        ]
      });
    } else {
      // Synchronous execution
      try {
        const result = await directExecutor.testNode(workflowId, nodeId, userId, input, variables);

        return NextResponse.json({
          success: true,
          executionId,
          result: {
            success: result.success,
            output: result.result,
            executionTime: result.executionTime,
            timestamp: result.timestamp,
            variables: {
              ...variables,
              [`${nodeId}_test_output`]: result.result,
              test_execution_time: result.executionTime,
              test_timestamp: result.timestamp
            }
          },
          mode: 'sync',
          timestamp: new Date().toISOString()
        });
      } catch (error) {
        console.error(`❌ Synchronous node test failed:`, error);

        return NextResponse.json({
          success: false,
          executionId,
          error: error instanceof Error ? error.message : 'Node test failed',
          mode: 'sync',
          timestamp: new Date().toISOString()
        }, { status: 500 });
      }
    }

  } catch (error) {
    console.error('Node test API error:', error);
    
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// Get node test schema/info
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ workflowId: string; nodeId: string }> }
) {
  try {
    // Get user session
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { workflowId, nodeId } = await params;

    // Validate workflow access
    const { db } = await connectToDatabase();

    // First, find the workflow
    const workflow = await db.collection('workflows').findOne({
      _id: new ObjectId(workflowId)
    });

    if (!workflow) {
      return NextResponse.json(
        { error: 'Workflow not found' },
        { status: 404 }
      );
    }

    // Then check access more flexibly
    const userId = session.user.id?.toString();
    const userTeamId = session.user.teamId?.toString();
    const workflowUserId = workflow.userId?.toString();
    const workflowTeamId = workflow.teamId?.toString();

    // Allow access if user owns the workflow or is in the same team
    const ownsWorkflow = workflowUserId === userId;
    const sameTeam = workflowTeamId && userTeamId && workflowTeamId === userTeamId;

    if (!ownsWorkflow && !sameTeam) {
      return NextResponse.json(
        { error: 'Access denied' },
        { status: 403 }
      );
    }

    // Find the node
    const node = workflow.nodes.find((n: any) => n.id === nodeId);
    if (!node) {
      return NextResponse.json(
        { error: 'Node not found in workflow' },
        { status: 404 }
      );
    }

    // Return node information for testing
    return NextResponse.json({
      nodeId: node.id,
      nodeType: node.type,
      nodeLabel: node.data.label,
      config: node.data.config,
      schema: {
        input: getNodeInputSchema(node.type),
        output: getNodeOutputSchema(node.type),
        variables: getNodeVariableSchema(node.type)
      },
      examples: getNodeExamples(node.type),
      description: getNodeDescription(node.type)
    });

  } catch (error) {
    console.error('Node info API error:', error);
    
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// Helper functions for node schemas and examples
function getNodeInputSchema(nodeType: string): any {
  const schemas: Record<string, any> = {
    webhook: {
      type: 'object',
      properties: {
        body: { type: 'object', description: 'Webhook request body' },
        headers: { type: 'object', description: 'HTTP headers' },
        params: { type: 'object', description: 'Query parameters' }
      }
    },
    ai_agent: {
      type: 'object',
      properties: {
        message: { type: 'string', description: 'Input message for the AI agent' },
        context: { type: 'object', description: 'Additional context' }
      }
    },
    web_search: {
      type: 'object',
      properties: {
        query: { type: 'string', description: 'Search query' },
        limit: { type: 'number', description: 'Number of results', default: 10 }
      }
    },
    calculator: {
      type: 'object',
      properties: {
        expression: { type: 'string', description: 'Mathematical expression to evaluate' }
      }
    },
    condition: {
      type: 'object',
      properties: {
        value: { type: 'any', description: 'Value to evaluate' }
      }
    }
  };

  return schemas[nodeType] || {
    type: 'object',
    description: 'Generic input object'
  };
}

function getNodeOutputSchema(nodeType: string): any {
  const schemas: Record<string, any> = {
    ai_agent: {
      type: 'object',
      properties: {
        response: { type: 'string', description: 'AI agent response' },
        tokens_used: { type: 'number', description: 'Tokens consumed' }
      }
    },
    web_search: {
      type: 'object',
      properties: {
        results: { 
          type: 'array', 
          items: {
            type: 'object',
            properties: {
              title: { type: 'string' },
              url: { type: 'string' },
              snippet: { type: 'string' }
            }
          }
        }
      }
    },
    calculator: {
      type: 'object',
      properties: {
        result: { type: 'number', description: 'Calculation result' },
        expression: { type: 'string', description: 'Original expression' }
      }
    }
  };

  return schemas[nodeType] || {
    type: 'object',
    description: 'Generic output object'
  };
}

function getNodeVariableSchema(nodeType: string): any {
  return {
    type: 'object',
    description: 'Variables available to this node type',
    properties: {
      [`${nodeType}_output`]: {
        type: 'any',
        description: `Output from ${nodeType} node`
      }
    }
  };
}

function getNodeExamples(nodeType: string): any {
  const examples: Record<string, any> = {
    ai_agent: {
      input: { message: "Hello, how are you?" },
      variables: { user_name: "John" }
    },
    web_search: {
      input: { query: "latest AI news", limit: 5 },
      variables: {}
    },
    calculator: {
      input: { expression: "2 + 2 * 3" },
      variables: {}
    }
  };

  return examples[nodeType] || {
    input: {},
    variables: {}
  };
}

function getNodeDescription(nodeType: string): string {
  const descriptions: Record<string, string> = {
    webhook: 'Receives HTTP webhook requests and triggers workflow execution',
    ai_agent: 'Processes input using AI models with configurable tools and memory',
    web_search: 'Searches the web using configured search providers',
    calculator: 'Evaluates mathematical expressions and formulas',
    email: 'Sends email messages using configured SMTP settings',
    condition: 'Evaluates conditions and routes workflow based on results',
    filter: 'Filters data based on specified criteria',
    transform: 'Transforms data using mapping or custom functions',
    response: 'Sends responses back to the user or external systems'
  };

  return descriptions[nodeType] || 'Generic workflow node';
}
