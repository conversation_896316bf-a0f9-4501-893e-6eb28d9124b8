import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import { ObjectId } from 'mongodb';

// Get chat configuration for public embedding
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ workflowId: string }> }
) {
  try {
    const { workflowId } = await params;

    console.log(`🔧 Getting chat config for workflow: ${workflowId}`);

    const { db } = await connectToDatabase();
    
    const workflow = await db.collection('workflows').findOne({
      _id: new ObjectId(workflowId)
    });

    if (!workflow) {
      return NextResponse.json(
        { error: 'Workflow not found' },
        { status: 404 }
      );
    }

    // Find message trigger in workflow
    const messageTrigger = workflow.nodes?.find((node: any) => 
      node.type === 'message'
    );

    if (!messageTrigger) {
      return NextResponse.json(
        { error: 'No message trigger found in workflow' },
        { status: 400 }
      );
    }

    const config = messageTrigger.data?.config || {};

    // Build chat configuration
    const chatConfig = {
      // General Settings
      initialMessages: config.initialMessages || ["Hello! How can I help you today?"],
      suggestedMessages: config.suggestedMessages || [],
      messagePlaceholder: config.messagePlaceholder || "Type your message...",
      footer: config.footer || "Powered by AI Assistant",
      theme: config.theme || "light",
      isPublic: config.isPublic !== false, // Default to true for backward compatibility
      allowedDomains: config.allowedDomains || ["*"],
      ipLimit: config.ipLimit || 100,
      ipLimitTimeframe: config.ipLimitTimeframe || 3600,
      ipLimitMessage: config.ipLimitMessage || "Rate limit exceeded. Please try again later.",
      displayName: config.displayName || "AI Assistant",
      iconPath: config.iconPath,
      loadPreviousSession: config.loadPreviousSession !== false,

      // Interface Settings
      customerMessageColor: config.customerMessageColor || "#007bff",
      customerMessageColorAsChatbotHeader: config.customerMessageColorAsChatbotHeader || false,
      hasChatBubbleToggle: config.hasChatBubbleToggle || false,
      chatBubbleColor: config.chatBubbleColor || "#007bff",
      alignChatBubble: config.alignChatBubble || "right",
      autoShowInitialDelay: config.autoShowInitialDelay || 0,

      // Workflow metadata
      workflowName: workflow.name,
      workflowId: workflow._id.toString(),
      isActive: workflow.isActive !== false
    };

    // Check if workflow is active
    if (!chatConfig.isActive) {
      return NextResponse.json(
        { error: 'Chat is currently inactive' },
        { status: 503 }
      );
    }

    console.log(`✅ Chat config loaded successfully`);

    return NextResponse.json(chatConfig);

  } catch (error) {
    console.error('Chat config API error:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
