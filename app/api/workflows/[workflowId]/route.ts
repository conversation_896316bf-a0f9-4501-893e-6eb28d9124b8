import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth/auth";
import dbConnect from "@/lib/db/db";
import { WorkflowModel, TeamModel, MemberModel } from "@/models";
import mongoose from "mongoose";

// GET - Fetch single workflow
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ workflowId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    const { workflowId } = await params;

    // Validate ObjectId
    if (!mongoose.Types.ObjectId.isValid(workflowId)) {
      return NextResponse.json(
        { success: false, error: "Invalid workflow ID" },
        { status: 400 }
      );
    }

    await dbConnect();

    // Find workflow
    const workflow = await WorkflowModel.findById(workflowId).lean();

    if (!workflow) {
      return NextResponse.json(
        { success: false, error: "Workflow not found" },
        { status: 404 }
      );
    }

    // Check if user has access to this workflow's team
    const team = await TeamModel.findById(workflow.teamId);
    if (!team) {
      return NextResponse.json(
        { success: false, error: "Associated team not found" },
        { status: 404 }
      );
    }

    // Check team access
    const workflowTeamId = workflow.teamId?.toString();
    const userId = session.user.id?.toString();

    // Check if user is owner or member of the team
    const isOwner = team.ownerId.toString() === userId;
    const isMember = !isOwner && await MemberModel.findOne({
      teamId: workflowTeamId,
      userId: userId,
      status: "accepted"
    });

    if (!isOwner && !isMember) {
      return NextResponse.json(
        { success: false, error: "Access denied to this team" },
        { status: 403 }
      );
    }

    return NextResponse.json({
      success: true,
      data: workflow
    });

  } catch (error) {
    console.error("Error fetching workflow:", error);
    return NextResponse.json(
      { success: false, error: "Internal server error" },
      { status: 500 }
    );
  }
}

// PUT - Update workflow
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ workflowId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    const { workflowId } = await params;
    const updateData = await request.json();

    // Validate ObjectId
    if (!mongoose.Types.ObjectId.isValid(workflowId)) {
      return NextResponse.json(
        { success: false, error: "Invalid workflow ID" },
        { status: 400 }
      );
    }

    await dbConnect();

    // Find workflow and check access
    const workflow = await WorkflowModel.findById(workflowId);
    if (!workflow) {
      return NextResponse.json(
        { success: false, error: "Workflow not found" },
        { status: 404 }
      );
    }

    // Check if user has access to this workflow's team
    const team = await TeamModel.findById(workflow.teamId);
    if (!team) {
      return NextResponse.json(
        { success: false, error: "Associated team not found" },
        { status: 404 }
      );
    }

    // Check team access
    const workflowTeamId = workflow.teamId?.toString();
    const userId = session.user.id?.toString();

    // Check if user is owner or member of the team
    const isOwner = team.ownerId.toString() === userId;
    const isMember = !isOwner && await MemberModel.findOne({
      teamId: workflowTeamId,
      userId: userId,
      status: "accepted"
    });

    if (!isOwner && !isMember) {
      return NextResponse.json(
        { success: false, error: "Access denied to this team" },
        { status: 403 }
      );
    }

    // Update workflow
    const updatedWorkflow = await WorkflowModel.findByIdAndUpdate(
      workflowId,
      {
        ...updateData,
        updatedAt: new Date()
      },
      { new: true, runValidators: true }
    ).lean();

    return NextResponse.json({
      success: true,
      data: updatedWorkflow
    });

  } catch (error) {
    console.error("Error updating workflow:", error);
    return NextResponse.json(
      { success: false, error: "Internal server error" },
      { status: 500 }
    );
  }
}

// DELETE - Delete workflow
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ workflowId: string }> }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    const { workflowId } = await params;

    // Validate ObjectId
    if (!mongoose.Types.ObjectId.isValid(workflowId)) {
      return NextResponse.json(
        { success: false, error: "Invalid workflow ID" },
        { status: 400 }
      );
    }

    await dbConnect();

    // Find workflow and check access
    const workflow = await WorkflowModel.findById(workflowId);
    if (!workflow) {
      return NextResponse.json(
        { success: false, error: "Workflow not found" },
        { status: 404 }
      );
    }

    // Check if user has access to this workflow's team
    const team = await TeamModel.findById(workflow.teamId);
    if (!team) {
      return NextResponse.json(
        { success: false, error: "Associated team not found" },
        { status: 404 }
      );
    }

    // Check team access
    const workflowTeamId = workflow.teamId?.toString();
    const userId = session.user.id?.toString();

    // Check if user is owner or member of the team
    const isOwner = team.ownerId.toString() === userId;
    const isMember = !isOwner && await MemberModel.findOne({
      teamId: workflowTeamId,
      userId: userId,
      status: "accepted"
    });

    if (!isOwner && !isMember) {
      return NextResponse.json(
        { success: false, error: "Access denied to this team" },
        { status: 403 }
      );
    }

    // Delete workflow
    await WorkflowModel.findByIdAndDelete(workflowId);

    return NextResponse.json({
      success: true,
      message: "Workflow deleted successfully"
    });

  } catch (error) {
    console.error("Error deleting workflow:", error);
    return NextResponse.json(
      { success: false, error: "Internal server error" },
      { status: 500 }
    );
  }
}
