import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import { ObjectId } from 'mongodb';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/auth';
import { getDirectExecutor } from '@/lib/execution/direct-executor';
import { getQueueManager } from '@/lib/execution/queue-manager';

// Get execution status
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ workflowId: string; executionId: string }> }
) {
  try {
    // Get user session
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { workflowId, executionId } = await params;

    // Validate workflow access
    const { db } = await connectToDatabase();

    // First, find the workflow
    const workflow = await db.collection('workflows').findOne({
      _id: new ObjectId(workflowId)
    });

    if (!workflow) {
      return NextResponse.json(
        { error: 'Workflow not found' },
        { status: 404 }
      );
    }

    // Then check access more flexibly
    const userId = session.user.id?.toString();
    const userTeamId = session.user.teamId?.toString();
    const workflowUserId = workflow.userId?.toString();
    const workflowTeamId = workflow.teamId?.toString();

    // Allow access if user owns the workflow or is in the same team
    const ownsWorkflow = workflowUserId === userId;
    const sameTeam = workflowTeamId && userTeamId && workflowTeamId === userTeamId;

    if (!ownsWorkflow && !sameTeam) {
      return NextResponse.json(
        { error: 'Access denied' },
        { status: 403 }
      );
    }

    // Get execution status from database
    const execution = await db.collection('workflow_executions').findOne({
      executionId
    });

    if (!execution) {
      return NextResponse.json(
        { error: 'Execution not found' },
        { status: 404 }
      );
    }

    // Calculate simple execution metrics
    const totalNodes = execution.nodeExecutions?.length || 0;
    const completedNodes = execution.nodeExecutions?.filter((n: any) => n.status === 'completed').length || 0;
    const failedNodes = execution.nodeExecutions?.filter((n: any) => n.status === 'failed').length || 0;

    const executionTime = execution.endTime
      ? new Date(execution.endTime).getTime() - new Date(execution.startTime).getTime()
      : Date.now() - new Date(execution.startTime).getTime();

    const metrics = {
      totalNodes,
      completedNodes,
      failedNodes,
      executionTime,
      successRate: totalNodes > 0 ? (completedNodes / totalNodes) * 100 : 0,
      isComplete: ['completed', 'failed', 'cancelled'].includes(execution.status)
    };

    return NextResponse.json({
      executionId: execution.executionId,
      workflowId: execution.workflowId,
      status: execution.status,
      triggerType: execution.triggerType,
      startTime: execution.startTime,
      endTime: execution.endTime,
      currentNodeId: execution.currentNodeId,
      nodeExecutions: (execution.nodeExecutions || []).map((nodeExec: any) => ({
        nodeId: nodeExec.nodeId,
        status: nodeExec.status,
        startTime: nodeExec.startTime,
        endTime: nodeExec.endTime,
        executionTime: nodeExec.executionTime,
        error: nodeExec.error,
        hasOutput: !!nodeExec.output
      })),
      metrics,
      error: execution.error,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Execution status API error:', error);
    
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// Cancel execution
export async function DELETE(
  request: NextRequest,
  { params }: { params: { workflowId: string; executionId: string } }
) {
  try {
    // Get user session
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { workflowId, executionId } = params;

    // Validate workflow access
    const { db } = await connectToDatabase();
    const workflow = await db.collection('workflows').findOne({
      _id: new ObjectId(workflowId),
      $or: [
        { userId: new ObjectId(session.user.id) },
        { teamId: new ObjectId(session.user.teamId) }
      ]
    });

    if (!workflow) {
      return NextResponse.json(
        { error: 'Workflow not found or access denied' },
        { status: 404 }
      );
    }

    // Try to cancel the queued job first (most executions are queued)
    const queueManager = getQueueManager();
    const jobCancelled = await queueManager.cancelJob(executionId);

    // Also mark execution as cancelled in database
    await db.collection('workflow_executions').updateOne(
      { executionId },
      {
        $set: {
          status: 'cancelled',
          endTime: new Date(),
          error: { message: 'Execution cancelled by user', code: 'USER_CANCELLED' }
        }
      }
    );

    if (!jobCancelled) {
      // If no job found in queue, check if execution exists in database
      const execution = await db.collection('workflow_executions').findOne({ executionId });
      if (!execution) {
        return NextResponse.json(
          { error: 'Execution not found' },
          { status: 404 }
        );
      }
    }

    return NextResponse.json({
      success: true,
      executionId,
      message: 'Execution cancelled successfully',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Execution cancellation API error:', error);
    
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// Get detailed execution data (including outputs)
export async function POST(
  request: NextRequest,
  { params }: { params: { workflowId: string; executionId: string } }
) {
  try {
    // Get user session
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { workflowId, executionId } = params;
    const { includeOutputs = false, includeVariables = false } = await request.json();

    // Validate workflow access
    const { db } = await connectToDatabase();
    const workflow = await db.collection('workflows').findOne({
      _id: new ObjectId(workflowId),
      $or: [
        { userId: new ObjectId(session.user.id) },
        { teamId: new ObjectId(session.user.teamId) }
      ]
    });

    if (!workflow) {
      return NextResponse.json(
        { error: 'Workflow not found or access denied' },
        { status: 404 }
      );
    }

    // Get execution from database
    const execution = await db.collection('workflow_executions').findOne({
      executionId
    });

    if (!execution) {
      return NextResponse.json(
        { error: 'Execution not found' },
        { status: 404 }
      );
    }

    // Build detailed response
    const response: any = {
      executionId: execution.executionId,
      workflowId: execution.workflowId,
      status: execution.status,
      triggerType: execution.triggerType,
      triggerData: execution.triggerData,
      startTime: execution.startTime,
      endTime: execution.endTime,
      currentNodeId: execution.currentNodeId,
      error: execution.error,
      metrics: calculateExecutionMetrics(execution)
    };

    // Include node executions with optional outputs
    response.nodeExecutions = execution.nodeExecutions.map(nodeExec => {
      const nodeData: any = {
        nodeId: nodeExec.nodeId,
        status: nodeExec.status,
        startTime: nodeExec.startTime,
        endTime: nodeExec.endTime,
        executionTime: nodeExec.executionTime,
        error: nodeExec.error
      };

      if (includeOutputs) {
        nodeData.input = nodeExec.input;
        nodeData.output = nodeExec.output;
      }

      if (includeVariables) {
        nodeData.variablesSnapshot = nodeExec.variablesSnapshot;
      }

      return nodeData;
    });

    // Include final variables if requested
    if (includeVariables) {
      response.variables = execution.variables;
    }

    return NextResponse.json(response);

  } catch (error) {
    console.error('Detailed execution API error:', error);
    
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// Calculate execution metrics
function calculateExecutionMetrics(execution: any): any {
  const totalNodes = execution.nodeExecutions.length;
  const completedNodes = execution.nodeExecutions.filter((n: any) => n.status === 'completed').length;
  const failedNodes = execution.nodeExecutions.filter((n: any) => n.status === 'failed').length;
  const skippedNodes = execution.nodeExecutions.filter((n: any) => n.status === 'skipped').length;
  
  const executionTime = execution.endTime 
    ? new Date(execution.endTime).getTime() - new Date(execution.startTime).getTime()
    : Date.now() - new Date(execution.startTime).getTime();

  const nodeExecutionTimes = execution.nodeExecutions
    .filter((n: any) => n.executionTime)
    .map((n: any) => n.executionTime);

  const averageNodeTime = nodeExecutionTimes.length > 0
    ? nodeExecutionTimes.reduce((a: number, b: number) => a + b, 0) / nodeExecutionTimes.length
    : 0;

  return {
    totalNodes,
    completedNodes,
    failedNodes,
    skippedNodes,
    executionTime,
    averageNodeTime,
    successRate: totalNodes > 0 ? (completedNodes / totalNodes) * 100 : 0,
    isComplete: execution.status === 'completed' || execution.status === 'failed' || execution.status === 'cancelled'
  };
}
