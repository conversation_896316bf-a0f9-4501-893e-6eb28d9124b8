import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth/auth";
import dbConnect from "@/lib/db/db";
import { UserModel } from "@/models";

export async function POST(request: Request) {
  const session = await getServerSession(authOptions);

  if (!session?.user?.id) {
    return NextResponse.json(
      { success: false, error: "Unauthorized" },
      { status: 401 }
    );
  }

  try {
    await dbConnect();
    const { billingEmail } = await request.json();
    const userId = session.user.id;

    // Update user's billing email
    const updatedUser = await UserModel.findByIdAndUpdate(
      userId,
      { $set: { billingEmail } },
      { new: true }
    ).select("billingEmail");

    if (!updatedUser) {
      return NextResponse.json(
        { success: false, error: "User not found" },
        { status: 404 }
      );
    }

    return NextResponse.json(
      {
        success: true,
        data: updatedUser,
        message: "Billing email updated successfully",
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error updating billing email:", error);
    return NextResponse.json(
      { success: false, error: "Failed to update billing email" },
      { status: 500 }
    );
  }
}

export async function GET() {
  const session = await getServerSession(authOptions);

  if (!session?.user?.id) {
    return NextResponse.json(
      { success: false, error: "Unauthorized" },
      { status: 401 }
    );
  }

  try {
    await dbConnect();
    const userId = session.user.id;
    const user = await UserModel.findById(userId).select("billingEmail email");

    if (!user) {
      return NextResponse.json(
        { success: false, error: "User not found" },
        { status: 404 }
      );
    }

    return NextResponse.json(
      {
        success: true,
        data: {
          billingEmail: user.billingEmail || user.email,
        },
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error fetching billing email:", error);
    return NextResponse.json(
      { success: false, error: "Failed to fetch billing email" },
      { status: 500 }
    );
  }
}
