import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth/auth";
import dbConnect from "@/lib/db/db";
import { UserModel } from "@/models";
import { CACHE_TTL, TAX_INFO_CACHE_PREFIX } from "@/config/redis";
import { redis } from "@/lib/redis/redis";


export async function GET() {
  const session = await getServerSession(authOptions);

  if (!session?.user?.id) {
    return NextResponse.json(
      { success: false, error: "Unauthorized" },
      { status: 401 }
    );
  }

  try {
    await dbConnect();
    const userId = session.user.id;
    const cacheKey = `${TAX_INFO_CACHE_PREFIX}${userId}`;

    // Try to get cached tax info first
    const cachedTaxInfo = await redis.get(cacheKey);
    if (cachedTaxInfo) {
      return NextResponse.json(
        {
          success: true,
          data: JSON.parse(cachedTaxInfo),
        },
        { status: 200 }
      );
    }

    const user = await UserModel.findById(userId).select("taxType taxId");

    if (!user) {
      return NextResponse.json(
        { success: false, error: "User not found" },
        { status: 404 }
      );
    }

    const taxData = {
      taxType: user.taxType || "",
      taxId: user.taxId || "",
    };

    // Cache the tax information
    await redis.set(
      cacheKey,
      JSON.stringify(taxData),
      "EX", // Set expire
      CACHE_TTL
    );

    return NextResponse.json(
      {
        success: true,
        data: taxData,
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error fetching tax information:", error);
    return NextResponse.json(
      { success: false, error: "Failed to fetch tax information" },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  const session = await getServerSession(authOptions);

  if (!session?.user?.id) {
    return NextResponse.json(
      { success: false, error: "Unauthorized" },
      { status: 401 }
    );
  }

  try {
    await dbConnect();
    const { type, id } = await request.json();
    const userId = session.user.id;
    const cacheKey = `${TAX_INFO_CACHE_PREFIX}${userId}`;

    // Update user's tax information
    const updatedUser = await UserModel.findByIdAndUpdate(
      userId,
      { $set: { taxType: type, taxId: id } },
      { new: true }
    ).select("taxType taxId");

    if (!updatedUser) {
      return NextResponse.json(
        { success: false, error: "User not found" },
        { status: 404 }
      );
    }

    // Invalidate the cached tax information
    await redis.del(cacheKey);

    return NextResponse.json(
      {
        success: true,
        data: {
          type: updatedUser.taxType,
          id: updatedUser.taxId,
        },
        message: "Tax information updated successfully",
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error updating tax information:", error);
    return NextResponse.json(
      { success: false, error: "Failed to update tax information" },
      { status: 500 }
    );
  }
}
