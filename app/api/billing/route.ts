import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/auth';
import dbConnect from '@/lib/db/db';
import { BillingFormValues } from '@/types/types';
import { UserModel } from '@/models';
import { redis } from '@/lib/redis/redis';
import { BILLING_INFO_CACHE_PREFIX, CACHE_TTL } from '@/config/redis';


export async function GET() {
  const session = await getServerSession(authOptions);

  if (!session?.user?.id) {
    return NextResponse.json(
      { success: false, error: 'Unauthorized' },
      { status: 401 }
    );
  }

  try {
    await dbConnect();

    const userId = session.user.id;
    const cacheKey = `${BILLING_INFO_CACHE_PREFIX}${userId}`;

    // Try to get cached billing info first
    const cachedBillingInfo = await redis.get(cacheKey);
    if (cachedBillingInfo) {
      return NextResponse.json(
        {
          success: true,
          data: JSON.parse(cachedBillingInfo),
        },
        { status: 200 }
      );
    }

    const user = await UserModel.findById(userId).select(
      'name address addressLine2 city state postal_code country'
    );

    if (!user) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      );
    }

    // Map the user data to the billing form format
    const billingData = {
      organizationName: user.name || '',
      countryOrRegion: user.country || '',
      addressLine1: user.address || '',
      addressLine2: user.addressLine2 || '',
      city: user.city || '',
      state: user.state || '',
      postalCode: user.postal_code || '',
    };

    // Cache the billing information
    await redis.set(
      cacheKey,
      JSON.stringify(billingData),
      "EX", // Set expire
      CACHE_TTL
    );

    return NextResponse.json(
      {
        success: true,
        data: billingData,
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('Error fetching billing information:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch billing information' },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  const session = await getServerSession(authOptions);

  if (!session?.user?.id) {
    return NextResponse.json(
      { success: false, error: 'Unauthorized' },
      { status: 401 }
    );
  }

  try {
    await dbConnect();

    const body: BillingFormValues = await request.json();
    const userId = session.user.id;
    const cacheKey = `${BILLING_INFO_CACHE_PREFIX}${userId}`;

    // Update user's billing information
    const updatedUser = await UserModel.findByIdAndUpdate(
      userId,
      {
        $set: {
          name: body.organizationName,
          address: body.addressLine1,
          addressLine2: body.addressLine2 || undefined,
          city: body.city,
          state: body.state,
          postal_code: body.postalCode,
          country: body.countryOrRegion,
        },
      },
      { new: true }
    ).select('-password -resetPasswordToken -apiKey -otp');

    if (!updatedUser) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      );
    }

    // Invalidate the cached billing information
    await redis.del(cacheKey);

    return NextResponse.json(
      {
        success: true,
        data: updatedUser,
        message: 'Billing information updated successfully',
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('Error updating billing information:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to update billing information' },
      { status: 500 }
    );
  }
}
