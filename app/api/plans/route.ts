import { NextResponse } from "next/server";
import { PlanModel } from "@/models";
import dbConnect from "@/lib/db/db";
import { checkIfAdmin } from "@/lib/auth/checkAdmin";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth/auth";
import { redis } from "@/lib/redis/redis";
import { CACHE_TTL, PLANS_CACHE_KEY } from "@/config/redis";

// export async function GET(request: Request) {
//   const session = await getServerSession(authOptions);
//   const userId = session?.user?.id;
//   if (!userId) {
//     return NextResponse.json(
//       { success: false, error: "Unauthorized" },
//       { status: 401 }
//     );
//   }
//   await dbConnect();

//   // Verify admin status
//   const user = await checkIfAdmin(userId);
//   if (!user) {
//     return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
//   }

//   try {
//     const { searchParams } = new URL(request.url);
//     const activeOnly = searchParams.get("activeOnly");
//     const filter = activeOnly === "true" ? { isActive: true } : {};

//     const plans = await PlanModel.find(filter).sort({ priceMonthly: 1 });
//     return NextResponse.json(plans);
//   } catch (error) {
//     console.error("Error fetching plans:", error);
//     return NextResponse.json(
//       { error: "Failed to fetch plans" },
//       { status: 500 }
//     );
//   }
// }

export async function GET() {
  try {
    await dbConnect();

    // Try to get cached plans first
    const cachedPlans = await redis.get(PLANS_CACHE_KEY);
    if (cachedPlans) {
      return NextResponse.json(JSON.parse(cachedPlans), { status: 200 });
    }

    // If not in cache, fetch from database
    const plans = await PlanModel.find({ isActive: true }).lean();

    // Cache the result
    await redis.set(
      PLANS_CACHE_KEY,
      JSON.stringify(plans),
      "EX", // Set expire
      CACHE_TTL
    );

    return NextResponse.json(plans, { status: 200 });
  } catch (error) {
    console.error("GET plans error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  const session = await getServerSession(authOptions);
  const userId = session?.user?.id;
  if (!userId) {
    return NextResponse.json(
      { success: false, error: "Unauthorized" },
      { status: 401 }
    );
  }
  await dbConnect();

  // Verify admin status
  const user = await checkIfAdmin(userId);
  if (!user) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const planData = await request.json();

    // Validate required fields
    if (!planData.name || !planData.priceMonthly || !planData.priceYearly) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }

    const newPlan = new PlanModel(planData);
    await newPlan.save();

    // Invalidate the cache when a new plan is added
    await redis.del(PLANS_CACHE_KEY);

    return NextResponse.json(newPlan, { status: 201 });
  } catch (error) {
    console.error("Error creating plan:", error);
    return NextResponse.json(
      { error: "Failed to create plan" },
      { status: 500 }
    );
  }
}

export async function PUT(request: Request) {
  const session = await getServerSession(authOptions);
  const userId = session?.user?.id;
  if (!userId) {
    return NextResponse.json(
      { success: false, error: "Unauthorized" },
      { status: 401 }
    );
  }
  await dbConnect();

  // Verify admin status
  const user = await checkIfAdmin(userId);
  if (!user) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const { id, ...updateData } = await request.json();

    if (!id) {
      return NextResponse.json(
        { error: "Plan ID is required" },
        { status: 400 }
      );
    }

    const updatedPlan = await PlanModel.findByIdAndUpdate(id, updateData, {
      new: true,
    });

    if (!updatedPlan) {
      return NextResponse.json({ error: "Plan not found" }, { status: 404 });
    }
    await redis.del(PLANS_CACHE_KEY);
    return NextResponse.json(updatedPlan);
  } catch (error) {
    console.error("Error updating plan:", error);
    return NextResponse.json(
      { error: "Failed to update plan" },
      { status: 500 }
    );
  }
}
