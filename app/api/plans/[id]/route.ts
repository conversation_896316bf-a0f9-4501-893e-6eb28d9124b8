import { NextResponse } from "next/server";
import { PlanModel } from "@/models";
import dbConnect from "@/lib/db/db";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth/auth";
import { checkIfAdmin } from "@/lib/auth/checkAdmin";
import { CACHE_TTL, PLANS_LIST_CACHE_KEY, SINGLE_PLAN_CACHE_PREFIX } from "@/config/redis";
import { redis } from "@/lib/redis/redis";



interface Props {
  params: Promise<{
    id: string;
  }>;
}

export async function GET(request: Request, props: Props) {
  const session = await getServerSession(authOptions);
  const params = await props.params;
  const userId = session?.user?.id;
  if (!userId) {
    return NextResponse.json(
      { success: false, error: "Unauthorized" },
      { status: 401 }
    );
  }
  await dbConnect();

  // Verify admin status
  const user = await checkIfAdmin(userId);
  if (!user) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const cacheKey = `${SINGLE_PLAN_CACHE_PREFIX}${params.id}`;
    
    // Try to get cached plan first
    const cachedPlan = await redis.get(cacheKey);
    if (cachedPlan) {
      return NextResponse.json(JSON.parse(cachedPlan));
    }

    // If not in cache, fetch from database
    const plan = await PlanModel.findById(params.id);

    if (!plan) {
      return NextResponse.json({ error: "Plan not found" }, { status: 404 });
    }

    // Cache the result
    await redis.set(
      cacheKey,
      JSON.stringify(plan),
      "EX", // Set expire
      CACHE_TTL
    );

    return NextResponse.json(plan);
  } catch (error) {
    console.error("Error fetching plan:", error);
    return NextResponse.json(
      { error: "Failed to fetch plan" },
      { status: 500 }
    );
  }
}

export async function PUT(request: Request, props: Props) {
  const session = await getServerSession(authOptions);
  const params = await props.params;
  const userId = session?.user?.id;
  if (!userId) {
    return NextResponse.json(
      { success: false, error: "Unauthorized" },
      { status: 401 }
    );
  }
  await dbConnect();

  // Verify admin status
  const user = await checkIfAdmin(userId);
  if (!user) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const updateData = await request.json();
    const updatedPlan = await PlanModel.findByIdAndUpdate(
      params.id,
      updateData,
      { new: true }
    );

    if (!updatedPlan) {
      return NextResponse.json({ error: "Plan not found" }, { status: 404 });
    }

    // Invalidate both single plan cache and plans list cache
    const cacheKey = `${SINGLE_PLAN_CACHE_PREFIX}${params.id}`;
    await redis.del(cacheKey, PLANS_LIST_CACHE_KEY);

    return NextResponse.json(updatedPlan);
  } catch (error) {
    console.error("Error updating plan:", error);
    return NextResponse.json(
      { error: "Failed to update plan" },
      { status: 500 }
    );
  }
}

export async function DELETE(request: Request, props: Props) {
  const session = await getServerSession(authOptions);
  const params = await props.params;
  const userId = session?.user?.id;
  if (!userId) {
    return NextResponse.json(
      { success: false, error: "Unauthorized" },
      { status: 401 }
    );
  }
  await dbConnect();

  // Verify admin status
  const user = await checkIfAdmin(userId);
  if (!user) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    // Check if this is the default plan
    const plan = await PlanModel.findById(params.id);
    if (plan?.isDefault) {
      return NextResponse.json(
        { error: "Cannot delete the default plan" },
        { status: 400 }
      );
    }

    const deletedPlan = await PlanModel.findByIdAndDelete(params.id);

    if (!deletedPlan) {
      return NextResponse.json({ error: "Plan not found" }, { status: 404 });
    }

    // Invalidate both single plan cache and plans list cache
    const cacheKey = `${SINGLE_PLAN_CACHE_PREFIX}${params.id}`;
    await redis.del(cacheKey, PLANS_LIST_CACHE_KEY);

    return NextResponse.json({ message: "Plan deleted successfully" });
  } catch (error) {
    console.error("Error deleting plan:", error);
    return NextResponse.json(
      { error: "Failed to delete plan" },
      { status: 500 }
    );
  }
}