import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth/auth";
import dbConnect from "@/lib/db/db";
import { UserModel } from "@/models";
import Stripe from "stripe";

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: "2025-05-28.basil",
});


export async function GET() {
  const session = await getServerSession(authOptions);

  if (!session?.user?.id) {
    return NextResponse.json(
      { success: false, error: "Unauthorized" },
      { status: 401 }
    );
  }

  try {
    await dbConnect();
    const user = await UserModel.findById(session.user.id).select(
      "paymentMethods"
    );

    if (!user) {
      return NextResponse.json(
        { success: false, error: "User not found" },
        { status: 404 }
      );
    }

    return NextResponse.json(
      {
        success: true,
        paymentMethods: user.paymentMethods || [],
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error fetching payment methods:", error);
    return NextResponse.json(
      { success: false, error: "Failed to fetch payment methods" },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  const session = await getServerSession(authOptions);

  if (!session?.user?.id) {
    return NextResponse.json(
      { success: false, error: "Unauthorized" },
      { status: 401 }
    );
  }

  try {
    await dbConnect();
    const { paymentMethodId, cardName } = await request.json();
    const userId = session.user.id;

    // Get or create Stripe customer
    const user = await UserModel.findById(userId);
    if (!user) {
      return NextResponse.json(
        { success: false, error: "User not found" },
        { status: 404 }
      );
    }

    let customerId = user.stripeCustomerId;
    if (!customerId) {
      const customer = await stripe.customers.create({
        email: user.email,
        name: user.name,
        metadata: {
          userId: userId.toString(),
        },
      });
      customerId = customer.id;
      await UserModel.findByIdAndUpdate(userId, {
        stripeCustomerId: customerId,
      });
    }

    // Attach payment method to customer
    await stripe.paymentMethods.attach(paymentMethodId, {
      customer: customerId,
    });

    // Set as default payment method if none exists
    const customerObj = (await stripe.customers.retrieve(
      customerId
    )) as Stripe.Customer;
    if (
      !("deleted" in customerObj) &&
      !customerObj.invoice_settings.default_payment_method
    ) {
      await stripe.customers.update(customerId, {
        invoice_settings: {
          default_payment_method: paymentMethodId,
        },
      });
    }

    // Save payment method to database
    const paymentMethod = await stripe.paymentMethods.retrieve(paymentMethodId);
    const card = paymentMethod.card;

    await UserModel.findByIdAndUpdate(userId, {
      $push: {
        paymentMethods: {
          id: paymentMethodId,
          brand: card?.brand,
          last4: card?.last4,
          expMonth: card?.exp_month,
          expYear: card?.exp_year,
          name: cardName,
        },
      },
    });

    return NextResponse.json(
      {
        success: true,
        message: "Payment method added successfully",
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error adding payment method:", error);
    return NextResponse.json(
      { success: false, error: "Failed to add payment method" },
      { status: 500 }
    );
  }
}

