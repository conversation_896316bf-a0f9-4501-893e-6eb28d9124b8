import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth/auth";
import dbConnect from "@/lib/db/db";
import { UserModel } from "@/models";
import Stripe from "stripe";

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: "2025-05-28.basil",
});

interface Props {
  params: Promise<{
    id: string;
  }>;
}

export async function DELETE(request: Request, props: Props) {
  const session = await getServerSession(authOptions);
  const params = await props.params;

  if (!session?.user?.id) {
    return NextResponse.json(
      { success: false, error: "Unauthorized" },
      { status: 401 }
    );
  }

  try {
    await dbConnect();
    const paymentMethodId = params.id;
    const userId = session.user.id;

    // First detach from Stripe customer
    const user = await UserModel.findById(userId);
    if (!user) {
      return NextResponse.json(
        { success: false, error: "User not found" },
        { status: 404 }
      );
    }

    if (user.stripeCustomerId) {
      try {
        await stripe.paymentMethods.detach(paymentMethodId);
      } catch (stripeError) {
        console.warn("Stripe detach error:", stripeError);
      }
    }

    // Remove from database
    await UserModel.findByIdAndUpdate(userId, {
      $pull: { paymentMethods: { id: paymentMethodId } },
    });

    return NextResponse.json(
      {
        success: true,
        message: "Payment method deleted successfully",
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error deleting payment method:", error);
    return NextResponse.json(
      { success: false, error: "Failed to delete payment method" },
      { status: 500 }
    );
  }
}



export async function PATCH(request: Request, props: Props) {
  const session = await getServerSession(authOptions);
  const params = await props.params;

  if (!session?.user?.id) {
    return NextResponse.json(
      { success: false, error: "Unauthorized" },
      { status: 401 }
    );
  }

  try {
    await dbConnect();

    const userId = session.user.id;
    const paymentMethodId = params.id;

    const body = await request.json();
    const { status } = body;


    if (!["active", "inactive"].includes(status)) {
      return NextResponse.json(
        { success: false, error: "Invalid status value" },
        { status: 400 }
      );
    }

    const updatedUser = await UserModel.findOneAndUpdate(
      { _id: userId, "paymentMethods.id": paymentMethodId },
      { $set: { "paymentMethods.$.status": status } },
      { new: true }
    );

    if (!updatedUser) {
      return NextResponse.json(
        { success: false, error: "Payment method not found" },
        { status: 404 }
      );
    }    
    return NextResponse.json(
      {
        success: true,
        message: "Payment method status updated successfully",
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error updating payment method status:", error);
    return NextResponse.json(
      { success: false, error: "Failed to update payment method status" },
      { status: 500 }
    );
  }
}
