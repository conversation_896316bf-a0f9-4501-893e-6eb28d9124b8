import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    // Get base URL from environment or request
    const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 
                   process.env.VERCEL_URL ? `https://${process.env.VERCEL_URL}` :
                   `${request.nextUrl.protocol}//${request.nextUrl.host}`;

    // Generate widget configuration script
    const configScript = `
window.CHAT_WIDGET_CONFIG = {
  baseUrl: '${baseUrl}',
  version: '1.0.0',
  timestamp: ${Date.now()}
};
`;

    return new NextResponse(configScript, {
      status: 200,
      headers: {
        'Content-Type': 'application/javascript',
        'Cache-Control': 'public, max-age=300', // Cache for 5 minutes
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET',
        'Access-Control-Allow-Headers': 'Content-Type',
      },
    });

  } catch (error) {
    console.error('Error generating widget config:', error);
    return new NextResponse('Failed to generate widget config', { status: 500 });
  }
}
