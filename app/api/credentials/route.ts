// Credentials API Routes

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/auth';
import { CredentialManager } from '@/lib/integrations/core/credential-manager';
import { CreateCredentialRequest } from '@/lib/integrations/types/integration-types';

const credentialManager = CredentialManager.getInstance();

// GET /api/credentials - List credentials for team
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const teamId = searchParams.get('teamId');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '50');
    const appType = searchParams.get('appType') as any;

    if (!teamId) {
      return NextResponse.json({ error: 'Team ID is required' }, { status: 400 });
    }

    // TODO: Verify user has access to team

    const result = await credentialManager.listCredentials(teamId, page, limit, appType);
    
    // Remove encrypted data from response
    const sanitizedCredentials = result.credentials.map(cred => ({
      ...cred,
      encryptedCredentials: undefined
    }));

    return NextResponse.json({
      ...result,
      credentials: sanitizedCredentials
    });

  } catch (error) {
    console.error('Failed to list credentials:', error);
    return NextResponse.json(
      { error: 'Failed to list credentials' },
      { status: 500 }
    );
  }
}

// POST /api/credentials - Create new credential
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { teamId, ...credentialRequest }: { teamId: string } & CreateCredentialRequest = body;

    if (!teamId) {
      return NextResponse.json({ error: 'Team ID is required' }, { status: 400 });
    }

    // TODO: Verify user has admin access to team

    // Validate request
    if (!credentialRequest.name || !credentialRequest.appType || !credentialRequest.credentials) {
      return NextResponse.json(
        { error: 'Name, appType, and credentials are required' },
        { status: 400 }
      );
    }

    const credential = await credentialManager.createCredential(
      teamId,
      session.user.id,
      credentialRequest
    );

    // Remove encrypted data from response
    const sanitizedCredential = {
      ...credential,
      encryptedCredentials: undefined
    };

    return NextResponse.json(sanitizedCredential, { status: 201 });

  } catch (error) {
    console.error('Failed to create credential:', error);
    
    if (error instanceof Error && error.message.includes('Invalid credentials')) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to create credential' },
      { status: 500 }
    );
  }
}

// PUT /api/credentials - Update credential
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { credentialId, teamId, ...updateRequest } = body;

    if (!credentialId || !teamId) {
      return NextResponse.json(
        { error: 'Credential ID and Team ID are required' },
        { status: 400 }
      );
    }

    // TODO: Verify user has admin access to team

    const credential = await credentialManager.updateCredential(
      credentialId,
      teamId,
      updateRequest
    );

    if (!credential) {
      return NextResponse.json(
        { error: 'Credential not found' },
        { status: 404 }
      );
    }

    // Remove encrypted data from response
    const sanitizedCredential = {
      ...credential,
      encryptedCredentials: undefined
    };

    return NextResponse.json(sanitizedCredential);

  } catch (error) {
    console.error('Failed to update credential:', error);
    return NextResponse.json(
      { error: 'Failed to update credential' },
      { status: 500 }
    );
  }
}

// DELETE /api/credentials - Delete credential
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const credentialId = searchParams.get('credentialId');
    const teamId = searchParams.get('teamId');

    if (!credentialId || !teamId) {
      return NextResponse.json(
        { error: 'Credential ID and Team ID are required' },
        { status: 400 }
      );
    }

    // TODO: Verify user has admin access to team

    const success = await credentialManager.deleteCredential(credentialId, teamId);

    if (!success) {
      return NextResponse.json(
        { error: 'Credential not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({ success: true });

  } catch (error) {
    console.error('Failed to delete credential:', error);
    
    if (error instanceof Error && error.message.includes('in use')) {
      return NextResponse.json(
        { error: error.message },
        { status: 409 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to delete credential' },
      { status: 500 }
    );
  }
}
