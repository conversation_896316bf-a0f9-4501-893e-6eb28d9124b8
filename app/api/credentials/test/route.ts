// Credential Test API Route

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/auth';
import { CredentialManager } from '@/lib/integrations/core/credential-manager';
import { IntegrationRegistry } from '@/lib/integrations/core/integration-registry';
import { CredentialTestRequest } from '@/lib/integrations/types/integration-types';

const credentialManager = CredentialManager.getInstance();

// POST /api/credentials/test - Test credential connection
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    
    // Test existing credential
    if (body.credentialId && body.teamId) {
      const { credentialId, teamId } = body;
      
      // TODO: Verify user has access to team
      
      const result = await credentialManager.testCredential(credentialId, teamId);
      return NextResponse.json(result);
    }
    
    // Test new credentials
    if (body.appType && body.credentials) {
      const { appType, credentials }: CredentialTestRequest = body;
      
      const integration = IntegrationRegistry.getIntegration(appType);
      if (!integration) {
        return NextResponse.json(
          { error: `Integration not found for ${appType}` },
          { status: 400 }
        );
      }

      const result = await integration.validateCredentials(credentials);
      return NextResponse.json(result);
    }

    return NextResponse.json(
      { error: 'Either credentialId/teamId or appType/credentials are required' },
      { status: 400 }
    );

  } catch (error) {
    console.error('Failed to test credential:', error);
    return NextResponse.json(
      { 
        isValid: false,
        errors: ['Failed to test credential connection'],
        warnings: []
      },
      { status: 500 }
    );
  }
}
