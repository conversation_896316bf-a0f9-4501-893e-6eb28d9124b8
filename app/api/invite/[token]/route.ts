import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth/auth";
import dbConnect from "@/lib/db/db";
import TeamInvitationModel from "@/models/team-invitation";
import MemberModel from "@/models/member";
import UserModel from "@/models/user";
import { StatusEnum, RoleEnum } from "@/enums/enums";
import { TeamModel } from "@/models";
import { createHash } from "crypto";
import { revalidatePath } from "next/cache";
import mongoose from "mongoose";
import {
  ITEAM_CACHE_PREFIX,
  TEAM_CACHE_PREFIX,
  TEAM_MEMBERS_CACHE_PREFIX,
  TEAMS_CACHE_PREFIX,
  TEAMS_COUNT_CACHE_PREFIX,
  USER_TEAMS_CACHE_PREFIX,
} from "@/config/redis";
import { redis } from "@/lib/redis/redis";

interface Props {
  params: Promise<{
    token: string;
  }>;
}

export async function GET(req: Request, props: Props) {
  await dbConnect();
  const { headers } = req;
  const host = headers.get("host");
  const protocol = headers.get("x-forwarded-proto") || "http";

  const baseUrl = `${protocol}://${host}`;
  const session = await getServerSession(authOptions);
  const params = await props.params;
  try {
    // 1. Find the invitation
    const invitation = await TeamInvitationModel.findOne({
      token: params.token,
      status: StatusEnum.PENDING,
    }).populate({
      path: "teamId",
      model: "Team",
      select: "url name",
    });

    if (!invitation) {
      return NextResponse.json(
        { success: false, message: "Invitation not found or already accepted" },
        { status: 404 }
      );
    }

    // 2. Check if invitation is expired
    if (new Date() > new Date(invitation.expiresAt)) {
      return NextResponse.json(
        { success: false, message: "Invitation has expired" },
        { status: 410 } // 410 Gone
      );
    }

    // 3. Check if user is logged in
    if (!session?.user) {
      return NextResponse.json(
        {
          success: false,
          message: "Authentication required",
          redirect: `${baseUrl}/login?callbackUrl=${baseUrl}/invite/${params.token}`,
        },
        { status: 401 }
      );
    }

    // 4. Verify the logged in user matches the invite email
    const user = await UserModel.findOne({ email: invitation.email });
    if (!user || user._id.toString() !== session.user.id) {
      return NextResponse.json(
        {
          success: false,
          message: "This invitation is for a different email address",
        },
        { status: 403 }
      );
    }

    // 5. Check if user is already a member
    const existingMember = await MemberModel.findOne({
      teamId: invitation.teamId,
      userId: user._id,
    });

    if (existingMember) {
      // Clean up the invitation if user is already a member
      await TeamInvitationModel.deleteOne({ _id: invitation._id });
      return NextResponse.json(
        {
          success: false,
          message: "You are already a member of this team",
          redirect: `${baseUrl}/dashboard/team/${
            //eslint-disable-next-line @typescript-eslint/no-explicit-any
            (invitation.teamId as any).url
          }`,
        },
        { status: 400 }
      );
    }

    // 6. Create new member record
    const newMember = new MemberModel({
      teamId: invitation.teamId,
      userId: user._id,
      email: user.email,
      name: user.name,
      role: RoleEnum.MEMBER, // Or use role from invitation if you store it
      invitedBy: invitation.invitedBy,
      status: StatusEnum.ACCEPTED,
      acceptedAt: new Date(),
    });

    await newMember.save();

    // 7. Update team members count
    await TeamModel.updateOne(
      { _id: invitation.teamId },
      { $inc: { membersCount: 1 } }
    );

    // 8. Delete the invitation
    await TeamInvitationModel.deleteOne({ _id: invitation._id });

    return NextResponse.json({
      success: true,
      message: "Successfully joined the team",
      //eslint-disable-next-line @typescript-eslint/no-explicit-any
      redirect: `${baseUrl}/dashboard/team/${(invitation.teamId as any).url}`,
    });
  } catch (error) {
    console.error("[INVITE_ACCEPT_ERROR]", error);
    return NextResponse.json(
      { success: false, message: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function POST(req: Request, props: Props) {
  try {
    await dbConnect();
    const session = await getServerSession(authOptions);
    const user = session?.user;
    const params = await props.params;

    if (!user) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    const tokenHash = createHash("sha256").update(params.token).digest("hex");

    // Start a database session for atomic operations
    const dbsession = await mongoose.startSession();
    dbsession.startTransaction();

    try {
      // 1. Find and validate the invitation
      const invitation = await TeamInvitationModel.findOne({
        tokenHash,
        status: StatusEnum.PENDING,
      }).session(dbsession);

      if (!invitation) {
        await dbsession.abortTransaction();
        return NextResponse.json(
          { error: "Invalid or expired invitation" },
          { status: 404 }
        );
      }

      // 2. Verify email match
      if (invitation.email !== user.email) {
        await dbsession.abortTransaction();
        return NextResponse.json(
          { error: "This invitation belongs to a different email" },
          { status: 403 }
        );
      }

      // 3. Check if user is already a member
      const existingMember = await MemberModel.findOne({
        teamId: invitation.teamId,
        $or: [{ userId: user.id }, { email: user.email }],
      }).session(dbsession);

      if (existingMember) {
        await dbsession.abortTransaction();
        return NextResponse.json(
          { error: "You are already a member of this team" },
          { status: 409 }
        );
      }

      // 4. Create new member record
      await MemberModel.create(
        [
          {
            teamId: invitation.teamId,
            userId: user.id,
            role: RoleEnum.MEMBER,
            name: user.name,
            email: user.email,
            invitedBy: invitation.invitedBy,
            status: StatusEnum.ACCEPTED,
            acceptedAt: new Date(),
          },
        ],
        { dbsession }
      );

      // 5. Update team members count
      await TeamModel.findByIdAndUpdate(
        invitation.teamId,
        { $inc: { membersCount: 1 } },
        { dbsession }
      );

      // 6. Delete the invitation
      await TeamInvitationModel.deleteOne({ _id: invitation._id }).session(
        dbsession
      );

      // Commit the transaction
      await dbsession.commitTransaction();

      // Revalidate any cached paths
      revalidatePath(`/dashboard/${invitation.teamId}`);
      revalidatePath("/dashboard");

      // Redirect to team dashboard
      const team = await TeamModel.findById(invitation.teamId);

      if (team) {
        const cacheKeysToInvalidate = [
          `${TEAM_CACHE_PREFIX}${team.url}:*`,
          `${ITEAM_CACHE_PREFIX}${team._id}:*`,
          `${TEAMS_CACHE_PREFIX}${user.id}:*`,
          `${USER_TEAMS_CACHE_PREFIX}${user.id}:*`,
          `${TEAMS_COUNT_CACHE_PREFIX}${user.id}`,
          `${TEAM_MEMBERS_CACHE_PREFIX}${team._id}:*`,
          invitation.invitedBy && invitation.invitedBy.toString() !== user.id
            ? `${TEAMS_CACHE_PREFIX}${invitation.invitedBy}:*`
            : null,
          invitation.invitedBy && invitation.invitedBy.toString() !== user.id
            ? `${USER_TEAMS_CACHE_PREFIX}${invitation.invitedBy}:*`
            : null,
        ].filter((key): key is string => typeof key === "string" && !!key);

        // Use EVAL for atomic pattern deletion
        await Promise.all(
          cacheKeysToInvalidate.map((key) =>
            redis.eval(
              `local keys = redis.call('keys', ARGV[1]) 
               for i,k in ipairs(keys) do 
                 redis.call('del', k) 
               end 
               return keys`,
              0,
              key
            )
          )
        );
      }

      return NextResponse.redirect(
        new URL(`/dashboard/team/${team?.url}`, req.url)
      );
    } catch (error) {
      await dbsession.abortTransaction();
      throw error;
    } finally {
      dbsession.endSession();
    }
  } catch (error) {
    console.error("[INVITE_ACCEPT_ERROR]", error);
    return NextResponse.json(
      { error: "Failed to process invitation" },
      { status: 500 }
    );
  }
}
