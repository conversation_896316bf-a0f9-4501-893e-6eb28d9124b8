import { NextRequest } from 'next/server'
import { GET, POST } from '../route'
import { CredentialManager } from '@/lib/integrations/core/credential-manager'
import { getServerSession } from 'next-auth'

// Mock dependencies
jest.mock('@/lib/integrations/core/credential-manager')
jest.mock('next-auth')

const mockCredentialManager = {
  listCredentials: jest.fn(),
  createCredential: jest.fn(),
}

const mockSession = {
  user: {
    id: 'user-123',
    email: '<EMAIL>'
  }
}

describe('/api/teams/[teamId]/credentials', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    
    // Mock CredentialManager singleton
    ;(CredentialManager.getInstance as jest.Mock).mockReturnValue(mockCredentialManager)
    
    // Mock session
    ;(getServerSession as jest.Mock).mockResolvedValue(mockSession)
  })

  describe('GET /api/teams/[teamId]/credentials', () => {
    const createRequest = (params: { teamId: string }, searchParams?: Record<string, string>) => {
      const url = new URL(`http://localhost/api/teams/${params.teamId}/credentials`)
      if (searchParams) {
        Object.entries(searchParams).forEach(([key, value]) => {
          url.searchParams.set(key, value)
        })
      }
      
      return new NextRequest(url, {
        method: 'GET',
      })
    }

    it('should return credentials list successfully', async () => {
      const mockCredentials = [
        {
          id: 'cred-1',
          name: 'Test Credential',
          appType: 'openai',
          isActive: true
        }
      ]

      mockCredentialManager.listCredentials.mockResolvedValue({
        credentials: mockCredentials,
        total: 1,
        page: 1,
        limit: 50
      })

      const request = createRequest({ teamId: 'team-123' })
      const response = await GET(request, { params: { teamId: 'team-123' } })
      
      expect(response.status).toBe(200)
      
      const data = await response.json()
      expect(data).toEqual({
        credentials: mockCredentials,
        total: 1,
        page: 1,
        limit: 50
      })

      expect(mockCredentialManager.listCredentials).toHaveBeenCalledWith(
        'team-123',
        1,
        50,
        undefined
      )
    })

    it('should handle pagination parameters', async () => {
      mockCredentialManager.listCredentials.mockResolvedValue({
        credentials: [],
        total: 0,
        page: 2,
        limit: 10
      })

      const request = createRequest(
        { teamId: 'team-123' },
        { page: '2', limit: '10' }
      )
      
      await GET(request, { params: { teamId: 'team-123' } })

      expect(mockCredentialManager.listCredentials).toHaveBeenCalledWith(
        'team-123',
        2,
        10,
        undefined
      )
    })

    it('should handle appType filter', async () => {
      mockCredentialManager.listCredentials.mockResolvedValue({
        credentials: [],
        total: 0,
        page: 1,
        limit: 50
      })

      const request = createRequest(
        { teamId: 'team-123' },
        { appType: 'openai' }
      )
      
      await GET(request, { params: { teamId: 'team-123' } })

      expect(mockCredentialManager.listCredentials).toHaveBeenCalledWith(
        'team-123',
        1,
        50,
        'openai'
      )
    })

    it('should return 401 when not authenticated', async () => {
      ;(getServerSession as jest.Mock).mockResolvedValue(null)

      const request = createRequest({ teamId: 'team-123' })
      const response = await GET(request, { params: { teamId: 'team-123' } })

      expect(response.status).toBe(401)
      
      const data = await response.json()
      expect(data.error).toBe('Unauthorized')
    })

    it('should handle credential manager errors', async () => {
      mockCredentialManager.listCredentials.mockRejectedValue(
        new Error('Database connection failed')
      )

      const request = createRequest({ teamId: 'team-123' })
      const response = await GET(request, { params: { teamId: 'team-123' } })

      expect(response.status).toBe(500)
      
      const data = await response.json()
      expect(data.error).toBe('Failed to load credentials')
    })
  })

  describe('POST /api/teams/[teamId]/credentials', () => {
    const createRequest = (teamId: string, body: any) => {
      return new NextRequest(`http://localhost/api/teams/${teamId}/credentials`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(body),
      })
    }

    const validCreateRequest = {
      name: 'Test Credential',
      appType: 'openai',
      credentials: {
        api_key: 'sk-1234567890abcdef'
      }
    }

    it('should create credential successfully', async () => {
      const mockCreatedCredential = {
        id: 'cred-new',
        name: 'Test Credential',
        appType: 'openai',
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      }

      mockCredentialManager.createCredential.mockResolvedValue(mockCreatedCredential)

      const request = createRequest('team-123', validCreateRequest)
      const response = await POST(request, { params: { teamId: 'team-123' } })

      expect(response.status).toBe(201)
      
      const data = await response.json()
      expect(data).toEqual(mockCreatedCredential)

      expect(mockCredentialManager.createCredential).toHaveBeenCalledWith(
        'team-123',
        'user-123',
        validCreateRequest
      )
    })

    it('should validate required fields', async () => {
      const invalidRequest = {
        name: '',
        appType: 'openai',
        credentials: {}
      }

      const request = createRequest('team-123', invalidRequest)
      const response = await POST(request, { params: { teamId: 'team-123' } })

      expect(response.status).toBe(400)
      
      const data = await response.json()
      expect(data.error).toContain('Name is required')
    })

    it('should validate appType', async () => {
      const invalidRequest = {
        ...validCreateRequest,
        appType: 'invalid-app-type'
      }

      const request = createRequest('team-123', invalidRequest)
      const response = await POST(request, { params: { teamId: 'team-123' } })

      expect(response.status).toBe(400)
      
      const data = await response.json()
      expect(data.error).toContain('Invalid app type')
    })

    it('should handle credential creation errors', async () => {
      mockCredentialManager.createCredential.mockRejectedValue(
        new Error('Invalid credentials: API key is invalid')
      )

      const request = createRequest('team-123', validCreateRequest)
      const response = await POST(request, { params: { teamId: 'team-123' } })

      expect(response.status).toBe(400)
      
      const data = await response.json()
      expect(data.error).toBe('Invalid credentials: API key is invalid')
    })

    it('should return 401 when not authenticated', async () => {
      ;(getServerSession as jest.Mock).mockResolvedValue(null)

      const request = createRequest('team-123', validCreateRequest)
      const response = await POST(request, { params: { teamId: 'team-123' } })

      expect(response.status).toBe(401)
    })

    it('should handle malformed JSON', async () => {
      const request = new NextRequest('http://localhost/api/teams/team-123/credentials', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: 'invalid json',
      })

      const response = await POST(request, { params: { teamId: 'team-123' } })

      expect(response.status).toBe(400)
      
      const data = await response.json()
      expect(data.error).toContain('Invalid JSON')
    })
  })

  describe('error handling', () => {
    it('should handle unexpected errors gracefully', async () => {
      // Mock an unexpected error
      mockCredentialManager.listCredentials.mockImplementation(() => {
        throw new Error('Unexpected error')
      })

      const request = new NextRequest('http://localhost/api/teams/team-123/credentials')
      const response = await GET(request, { params: { teamId: 'team-123' } })

      expect(response.status).toBe(500)
      
      const data = await response.json()
      expect(data.error).toBe('Internal server error')
    })

    it('should sanitize error messages in production', async () => {
      const originalEnv = process.env.NODE_ENV
      process.env.NODE_ENV = 'production'

      mockCredentialManager.listCredentials.mockRejectedValue(
        new Error('Database password is wrong')
      )

      const request = new NextRequest('http://localhost/api/teams/team-123/credentials')
      const response = await GET(request, { params: { teamId: 'team-123' } })

      expect(response.status).toBe(500)
      
      const data = await response.json()
      expect(data.error).toBe('Failed to load credentials')
      expect(data.error).not.toContain('password')

      process.env.NODE_ENV = originalEnv
    })
  })
})
