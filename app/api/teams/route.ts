import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth/auth";
import { CreateTeamDto, Team, TeamsApiResponse } from "@/types/types";
import dbConnect from "@/lib/db/db";
import { TeamModel } from "@/models";
import { generateSecureApiKey } from "@/utils/generate-api-key";
import {
  CACHE_TTL,
  TEAMS_CACHE_PREFIX,
  TEAMS_COUNT_CACHE_PREFIX,
} from "@/config/redis";
import { redis } from "@/lib/redis/redis";

export async function GET(request: Request) {
  const session = await getServerSession(authOptions);
  const { searchParams } = new URL(request.url);

  // Pagination parameters
  const page = parseInt(searchParams.get("page") || "1");
  const limit = parseInt(searchParams.get("limit") || "10");

  if (!session?.user?.id) {
    return NextResponse.json<Pick<TeamsApiResponse, "success" | "error">>(
      { success: false, error: "Unauthorized" },
      { status: 401 }
    );
  }

  await dbConnect();

  try {
    const userId = session.user.id;
    const cacheKey = `${TEAMS_CACHE_PREFIX}${userId}:page:${page}:limit:${limit}`;
    const countCacheKey = `${TEAMS_COUNT_CACHE_PREFIX}${userId}`;

    // Try to get cached teams first
    const [cachedTeams, cachedTotal] = await Promise.all([
      redis.get(cacheKey),
      redis.get(countCacheKey),
    ]);

    if (cachedTeams && cachedTotal) {
      return NextResponse.json<TeamsApiResponse>(
        {
          success: true,
          data: JSON.parse(cachedTeams),
          pagination: {
            total: parseInt(cachedTotal),
            page,
            limit,
            totalPages: Math.ceil(parseInt(cachedTotal) / limit),
          },
        },
        { status: 200 }
      );
    }

    // Get total count of teams for pagination metadata
    const total = await TeamModel.countDocuments({ ownerId: userId });

    const teams = (await TeamModel.find({ ownerId: userId })
      .populate({
        path: "members",
        select: "role status email invitedAt acceptedAt",
        populate: {
          path: "userId",
          select: "name image email",
          model: "User",
        },
      })
      .skip((page - 1) * limit)
      .limit(limit)
      .lean()
      //eslint-disable-next-line  @typescript-eslint/no-explicit-any
      .exec()) as unknown as (Team & { members: any[] })[];

    const formattedTeams = teams.map((team) => {
      const membersArray = Array.isArray(team.members)
        ? team.members
        : team.members
        ? [team.members]
        : [];

      return {
        ...team,
        _id: team._id.toString(),
        members:
          //eslint-disable-next-line  @typescript-eslint/no-explicit-any
          membersArray.map((member: any) => ({
            ...member,
            _id: member._id.toString(),
            userId: member.userId
              ? {
                  _id: member.userId._id.toString(),
                  name: member.userId.name,
                  avatar: member.userId.avatar || member.userId.image,
                  email: member.userId.email,
                }
              : null,
          })) || [],
      };
    });

    // Cache the results
    await Promise.all([
      redis.set(
        cacheKey,
        JSON.stringify(formattedTeams),
        "EX", // Set expire
        CACHE_TTL
      ),
      redis.set(
        countCacheKey,
        total.toString(),
        "EX", // Set expire
        CACHE_TTL
      ),
    ]);

    return NextResponse.json<TeamsApiResponse>(
      {
        success: true,
        data: formattedTeams,
        pagination: {
          total,
          page,
          limit,
          totalPages: Math.ceil(total / limit),
        },
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error fetching teams:", error);
    return NextResponse.json<Pick<TeamsApiResponse, "success" | "error">>(
      { success: false, error: "Failed to fetch teams" },
      { status: 500 }
    );
  }
}

export async function POST(request: Request) {
  const session = await getServerSession(authOptions);

  if (!session?.user?.id) {
    return NextResponse.json<Pick<TeamsApiResponse, "success" | "error">>(
      { success: false, error: "Unauthorized" },
      { status: 401 }
    );
  }

  try {
    const userId = session.user.id;
    const body: CreateTeamDto = await request.json();

    // Check team limit (50 max)
    const countCacheKey = `${TEAMS_COUNT_CACHE_PREFIX}${userId}`;
    const cachedCount = await redis.get(countCacheKey);
    let userTeamCount = cachedCount ? parseInt(cachedCount) : null;

    if (userTeamCount === null) {
      userTeamCount = await TeamModel.countDocuments({ ownerId: userId });
    }

    if (userTeamCount >= 50) {
      return NextResponse.json<Pick<TeamsApiResponse, "success" | "error">>(
        { success: false, error: "Maximum team limit reached (50 teams)" },
        { status: 400 }
      );
    }

    // Generate a secure API key
    const apiKey = generateSecureApiKey();

    const newTeamDoc = await TeamModel.create({
      ...body,
      ownerId: userId,
      apiKey: apiKey,
    });

    const newTeamObj = newTeamDoc.toObject();
    const newTeam = {
      ...newTeamObj,
      _id: (newTeamObj._id as unknown as { toString: () => string }).toString(),
    };

    // Invalidate the teams cache for this user
    const pattern = `${TEAMS_CACHE_PREFIX}${userId}:*`;
    const keys = await redis.keys(pattern);
    if (keys.length > 0) {
      await redis.del(...keys);
    }

    // Update the count cache
    await redis.set(
      countCacheKey,
      (userTeamCount + 1).toString(),
      "EX", // Set expire
      CACHE_TTL
    );

    return NextResponse.json<Omit<TeamsApiResponse, "pagination">>(
      {
        success: true,
        data: newTeam,
        message: "Team created successfully",
      },
      { status: 201 }
    );
  //eslint-disable-next-line  @typescript-eslint/no-explicit-any
  } catch (error: any) {
    if (error.code === 11000) {
      return NextResponse.json<Pick<TeamsApiResponse, "success" | "error">>(
        { success: false, error: "Team URL already exists" },
        { status: 400 }
      );
    }
    console.error("Error creating team:", error);
    return NextResponse.json<Pick<TeamsApiResponse, "success" | "error">>(
      { success: false, error: "Failed to create team" },
      { status: 500 }
    );
  }
}
