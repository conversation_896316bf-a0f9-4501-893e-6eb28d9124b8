import { NextResponse } from "next/server";
import {
  Team,
  TeamApiResponse,
  TeamInvitationMember,
  TeamsApiResponse,
  UpdateTeamDto,
} from "@/types/types";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth/auth";
import dbConnect from "@/lib/db/db";
import { MemberModel, TeamInvitationModel, TeamModel } from "@/models";
import { redis } from "@/lib/redis/redis";
import {
  CACHE_TTL,
  TEAM_CACHE_PREFIX,
  TEAM_INVITATIONS_CACHE_PREFIX,
  TEAM_MEMBERS_CACHE_PREFIX,
  TEAMS_CACHE_PREFIX,
  TEAMS_COUNT_CACHE_PREFIX,
  USER_TEAMS_CACHE_PREFIX,
} from "@/config/redis";
import { StatusEnum } from "@/enums/enums";
import { IMember, PopulatedMember } from "@/models/member";

interface Props {
  params: Promise<{
    slug: string;
  }>;
}

// GET single team by slug
export async function GET(request: Request, props: Props) {
  const session = await getServerSession(authOptions);

  if (!session?.user?.id) {
    return NextResponse.json<TeamApiResponse>(
      { success: false, error: "Unauthorized" },
      { status: 401 }
    );
  }

  await dbConnect();
  const params = await props.params;
  const cacheKey = `${TEAM_CACHE_PREFIX}${params.slug}:user:${session.user.id}`;

  try {
    // Try to get cached team first
    const cachedTeam = await redis.get(cacheKey);
    if (cachedTeam) {
      return NextResponse.json<TeamApiResponse>(JSON.parse(cachedTeam), {
        status: 200,
      });
    }

    // First get the team without populating members to get the _id
    const team = await TeamModel.findOne({
      url: params.slug,
    }).lean();

    if (!team) {
      return NextResponse.json<TeamApiResponse>(
        { success: false, error: "Team not found" },
        { status: 404 }
      );
    }

    // Get ALL members for this team (not filtered by userId)
    const members = await MemberModel.find({
      teamId: team._id,
    })
      .populate("userId")
      .lean();

    // Get pending invitations for this team
    const pendingInvitations = await TeamInvitationModel.find({
      teamId: team._id,
      status: StatusEnum.PENDING,
      expiresAt: { $gt: new Date() },
    }).lean();

    // Get all teams where user is a member (including owner)
    const userMemberships = await MemberModel.find({
      userId: session.user.id,
      teamId: { $ne: team._id },
    })
      .populate({
        path: "teamId",
        select: "name url description ownerId membersCount createdAt",
      })
      .lean();

    const relatedTeams = userMemberships
      .map((membership) => {
        const team = membership.teamId as unknown as Team;
        if (!team) return null;
        return {
          ...team,
          _id: team._id.toString(),
        };
      })
      .filter(Boolean) as Team[];

    const finalResponse: TeamApiResponse = {
      success: true,
      data: {
        currentTeam: {
          ...team,
          _id: team._id.toString(),
          membersCount: members.length + pendingInvitations.length,
          members: [
            ...members.map((member) => ({
              ...member,
              _id: member._id.toString(),
              teamId:
                member.teamId &&
                typeof member.teamId === "object" &&
                "toString" in member.teamId
                  ? member.teamId.toString()
                  : String(member.teamId),
              invitedBy:
                member.invitedBy &&
                typeof member.invitedBy === "object" &&
                "toString" in member.invitedBy
                  ? member.invitedBy.toString()
                  : member.invitedBy
                  ? String(member.invitedBy)
                  : undefined,
              userId: member.userId
                ? typeof member.userId === "object" && "_id" in member.userId
                  ? {
                      ...(member.userId as Record<string, unknown>),
                      _id:
                        (member.userId as { _id: unknown })._id?.toString?.() ??
                        String((member.userId as { _id: unknown })._id),
                    }
                  : { _id: String(member.userId) }
                : null,
            })),
            ...pendingInvitations.map((invite) => ({
              _id: invite._id.toString(),
              userId: null,
              teamId: invite.teamId, // keep as ObjectId for type compatibility
              role: "member" as const,
              status: "pending" as const,
              name: invite.email.split("@")[0],
              email: invite.email,
              invitedAt: invite.createdAt,
              invitedBy: invite.invitedBy, // keep as ObjectId for type compatibility
              type: "invitation" as const,
            })),
          ] as unknown as (IMember | PopulatedMember | TeamInvitationMember)[],
        },
        relatedTeams,
      },
    };

    // Cache the result
    await redis.set(
      cacheKey,
      JSON.stringify(finalResponse),
      "EX", // Set expire
      CACHE_TTL
    );

    return NextResponse.json<TeamApiResponse>(finalResponse, { status: 200 });
  } catch (error) {
    console.error("Error fetching team:", error);
    return NextResponse.json<TeamApiResponse>(
      { success: false, error: "Failed to fetch team" },
      { status: 500 }
    );
  }
}

// PUT update team by slug
export async function PUT(request: Request, props: Props) {
  const session = await getServerSession(authOptions);

  if (!session?.user?.id) {
    return NextResponse.json<Pick<TeamsApiResponse, "success" | "error">>(
      { success: false, error: "Unauthorized" },
      { status: 401 }
    );
  }

  await dbConnect();
  const params = await props.params;

  try {
    const body: UpdateTeamDto = await request.json();
    const userId = session.user.id;

    // First find the current team to get its _id
    const currentTeam = await TeamModel.findOne({
      url: params.slug,
      ownerId: userId,
    });

    if (!currentTeam) {
      return NextResponse.json<Pick<TeamsApiResponse, "success" | "error">>(
        { success: false, error: "Team not found" },
        { status: 404 }
      );
    }

    // Prevent changing the slug to an existing one
    if (body.url && body.url !== params.slug) {
      const existingTeam = await TeamModel.findOne({
        url: body.url,
        _id: { $ne: currentTeam._id },
      });

      if (existingTeam) {
        return NextResponse.json<Pick<TeamsApiResponse, "success" | "error">>(
          { success: false, error: "Team URL already exists" },
          { status: 400 }
        );
      }
    }

    const updatedTeam = await TeamModel.findOneAndUpdate(
      { _id: currentTeam._id, ownerId: userId },
      body,
      { new: true }
    ).lean();

    if (!updatedTeam) {
      return NextResponse.json<Pick<TeamsApiResponse, "success" | "error">>(
        { success: false, error: "Team not found" },
        { status: 404 }
      );
    }

    const cacheKeys = [
      `${TEAM_MEMBERS_CACHE_PREFIX}${updatedTeam._id}:*`,
      `${TEAM_INVITATIONS_CACHE_PREFIX}${updatedTeam._id}:*`,
      `${TEAM_CACHE_PREFIX}${params.slug}:*`, // All team data caches (including user-specific)
      `${USER_TEAMS_CACHE_PREFIX}${userId}:*`,
      `${TEAMS_CACHE_PREFIX}${userId}:*`,
      `${TEAMS_COUNT_CACHE_PREFIX}${userId}`,
    ];

    // Use pipeline for atomic operations
    const pipeline = redis.pipeline();

    // Delete all matching keys
    cacheKeys.forEach((pattern) => {
      pipeline.eval(
        `local keys = redis.call('keys', ARGV[1]) 
         for i,k in ipairs(keys) do 
           redis.call('del', k) 
         end 
         return keys`,
        0,
        pattern
      );
    });

    await pipeline.exec();

    return NextResponse.json<TeamApiResponse>(
      {
        success: true,
        data: updatedTeam
          ? { currentTeam: { ...updatedTeam, _id: updatedTeam._id.toString() } }
          : undefined,
        message: "Team updated successfully",
      },
      { status: 200 }
    );
    //eslint-disable-next-line  @typescript-eslint/no-explicit-any
  } catch (error: any) {
    console.error("Error updating team:", error);
    return NextResponse.json<Pick<TeamsApiResponse, "success" | "error">>(
      { success: false, error: "Failed to update team" },
      { status: 500 }
    );
  }
}

// DELETE team by slug
export async function DELETE(request: Request, props: Props) {
  const session = await getServerSession(authOptions);

  if (!session?.user?.id) {
    return NextResponse.json<Pick<TeamsApiResponse, "success" | "error">>(
      { success: false, error: "Unauthorized" },
      { status: 401 }
    );
  }

  await dbConnect();
  const params = await props.params;
  const userId = session.user.id;

  try {
    const deletedTeam = await TeamModel.findOneAndDelete({
      url: params.slug,
      ownerId: userId,
    }).lean();

    if (!deletedTeam) {
      return NextResponse.json<Pick<TeamsApiResponse, "success" | "error">>(
        { success: false, error: "Team not found" },
        { status: 404 }
      );
    }

    // Invalidate caches
    const cacheKeys = [
      `${TEAM_CACHE_PREFIX}${params.slug}:*`, // All versions of this team
      `${USER_TEAMS_CACHE_PREFIX}${userId}:*`, // User's teams listings
      `${TEAMS_CACHE_PREFIX}${userId}:*`, // Main teams listing cache
      `${TEAMS_COUNT_CACHE_PREFIX}${userId}`, // Teams count cache
    ];

    // Use EVAL for atomic pattern deletion
    await Promise.all(
      cacheKeys.map((key) =>
        redis.eval(
          `local keys = redis.call('keys', ARGV[1]) 
           for i,k in ipairs(keys) do 
             redis.call('del', k) 
           end 
           return keys`,
          0,
          key
        )
      )
    );

    return NextResponse.json<
      Pick<TeamsApiResponse, "success" | "error" | "message">
    >({ success: true, message: "Team deleted successfully" }, { status: 200 });
  } catch (error) {
    console.error("Error deleting team:", error);
    return NextResponse.json<Pick<TeamsApiResponse, "success" | "error">>(
      { success: false, error: "Failed to delete team" },
      { status: 500 }
    );
  }
}
