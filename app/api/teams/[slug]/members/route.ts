import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth/auth";
import dbConnect from "@/lib/db/db";
import { MemberModel, TeamInvitationModel, TeamModel } from "@/models";
import { StatusEnum } from "@/enums/enums";
import { ITeam } from "@/models/teams";
import {
  CACHE_TTL,
  TEAM_INVITATIONS_CACHE_PREFIX,
  TEAM_MEMBERS_CACHE_PREFIX,
} from "@/config/redis";
import { redis } from "@/lib/redis/redis";

interface Props {
  params: Promise<{
    slug: string;
  }>;
}

export async function GET(req: Request, props: Props) {
  try {
    await dbConnect();
    const session = await getServerSession(authOptions);
    const userId = session?.user?.id;
    if (!userId) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const params = await props.params;
    const { searchParams } = new URL(req.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const skip = (page - 1) * limit;

    // Generate cache keys
    const membersCacheKey = `${TEAM_MEMBERS_CACHE_PREFIX}${params.slug}:page:${page}:limit:${limit}`;
    const invitationsCacheKey = `${TEAM_INVITATIONS_CACHE_PREFIX}${params.slug}:page:${page}:limit:${limit}`;
    const countsCacheKey = `team:counts:${params.slug}`;

    // Try to get cached data first
    const [cachedMembers, cachedInvitations, cachedCounts] = await Promise.all([
      redis.get(membersCacheKey),
      redis.get(invitationsCacheKey),
      redis.get(countsCacheKey),
    ]);

    if (cachedMembers && cachedInvitations && cachedCounts) {
      const counts = JSON.parse(cachedCounts);
      return NextResponse.json({
        success: true,
        data: [...JSON.parse(cachedMembers), ...JSON.parse(cachedInvitations)],
        pagination: {
          page,
          limit,
          totalMembers: counts.totalMembers,
          totalInvitations: counts.totalInvitations,
          totalItems: counts.totalMembers + counts.totalInvitations,
          totalPages: Math.ceil(
            (counts.totalMembers + counts.totalInvitations) / limit
          ),
        },
      });
    }

    // First fetch the team to get its ID
    const team: ITeam | null = await TeamModel.findOne({ url: params.slug })
      .populate({
        path: "members",
        populate: {
          path: "userId",
          select: "name email image",
        },
      })
      .exec();

    if (!team) {
      return new NextResponse("Team not found", { status: 404 });
    }

    // Fetch data in parallel
    const [members, invitations, totalMembers, totalInvitations] =
      await Promise.all([
        MemberModel.find({ teamId: team._id })
          .skip(skip)
          .limit(limit)
          .populate({
            path: "teamId",
            select: "ownerId",
          })
          .sort({ createdAt: -1 })
          .exec(),
        TeamInvitationModel.find({
          teamId: team._id,
          status: StatusEnum.PENDING,
          expiresAt: { $gt: new Date() },
        })
          .populate({
            path: "teamId",
            select: "ownerId",
          })
          .skip(skip)
          .limit(limit)
          .sort({ createdAt: -1 })
          .exec(),
        MemberModel.countDocuments({ teamId: team._id }),
        TeamInvitationModel.countDocuments({
          teamId: team._id,
          status:StatusEnum.PENDING,
        }),
      ]);

    // Transform members
    //eslint-disable-next-line  @typescript-eslint/no-explicit-any
    const membersData = members.map((member: any) => ({
      ...member.toObject(),
      type: "member",
    }));

    const invitationsData = invitations.map((invitation) => {
      const emailName = invitation.email.split("@")[0];
      return {
        _id: invitation._id,
        userId: null,
        avatar: "/avatars/default-avatar.png",
        teamId: invitation.teamId,
        role: "member",
        name: emailName,
        email: invitation.email,
        status: "pending",
        invitedAt: invitation.createdAt,
        createdAt: invitation.createdAt,
        updatedAt: invitation.updatedAt,
        __v: invitation.__v,
        expiresAt: invitation.expiresAt,
        invitedBy: invitation.invitedBy,
        type: "invitation",
      };
    });

    // Cache the results
    const countsData = { totalMembers, totalInvitations };
    await Promise.all([
      redis.set(membersCacheKey, JSON.stringify(membersData), "EX", CACHE_TTL),
      redis.set(
        invitationsCacheKey,
        JSON.stringify(invitationsData),
        "EX",
        CACHE_TTL
      ),
      redis.set(countsCacheKey, JSON.stringify(countsData), "EX", CACHE_TTL),
    ]);

    // Combine both arrays
    return NextResponse.json({
      success: true,
      data: [...membersData, ...invitationsData],
      pagination: {
        page,
        limit,
        totalMembers,
        totalInvitations,
        totalItems: totalMembers + totalInvitations,
        totalPages: Math.ceil((totalMembers + totalInvitations) / limit),
      },
    });
  } catch (error) {
    console.error("[TEAM_MEMBERS_ERROR]", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}