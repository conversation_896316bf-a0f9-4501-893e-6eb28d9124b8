import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth/auth";
import dbConnect from "@/lib/db/db";
import { MemberModel, TeamModel } from "@/models";
import { RoleEnum } from "@/enums/enums";
import { sendEmail } from "@/utils/mail/mail";
import { generateRemovalEmail } from "@/utils/mail/templates/member-removal";
import {
  TEAM_CACHE_PREFIX,
  TEAM_INVITATIONS_CACHE_PREFIX,
  TEAM_MEMBERS_CACHE_PREFIX,
  TEAMS_CACHE_PREFIX,
  USER_TEAMS_CACHE_PREFIX,
} from "@/config/redis";
import { redis } from "@/lib/redis/redis";

interface Props {
  params: Promise<{
    slug: string;
    id: string;
  }>;
}

export async function DELETE(req: Request, props: Props) {
  try {
    const session = await getServerSession(authOptions);
    const params = await props.params;
    const userId = session?.user?.id;

    if (!userId) {
      return NextResponse.json(
        {
          message: "Unauthorized",
        },
        { status: 401 }
      );
    }

    await dbConnect();

    // Verify team exists and user has permission (owner or admin)
    const team = await TeamModel.findOne({
      url: params.slug,
      $or: [
        { ownerId: userId }, // Team owner
        { "members.userId": userId, "members.role": RoleEnum.ADMIN }, // Team admin
      ],
    }).populate("ownerId");

    if (!team) {
      return NextResponse.json(
        {
          message: "Team not found or unauthorized",
        },
        { status: 404 }
      );
    }

    const ownerEmail =
      team.ownerId &&
      typeof team.ownerId === "object" &&
      "email" in team.ownerId
        //eslint-disable-next-line  @typescript-eslint/no-explicit-any
        ? (team.ownerId as any).email
        : "<EMAIL>"; // fallback if not found

    // Find the member to be removed
    const memberToRemove = await MemberModel.findOne({
      _id: params.id,
      teamId: team._id,
    });

    if (!memberToRemove) {
      return NextResponse.json(
        {
          message: "Member not found",
        },
        { status: 404 }
      );
    }

    // Prevent removing owners
    if (memberToRemove.role === RoleEnum.OWNER) {
      return NextResponse.json(
        {
          message: "Cannot remove team owner",
        },
        { status: 403 }
      );
    }

    // Prevent removing yourself
    if (memberToRemove.userId?.toString() === userId) {
      return NextResponse.json(
        {
          message: "Cannot remove yourself",
        },
        { status: 403 }
      );
    }

    // Delete the member
    await MemberModel.deleteOne({
      _id: params.id,
      teamId: team._id,
    });

    // Update team members count
    await TeamModel.updateOne(
      { _id: team._id },
      { $inc: { membersCount: -1 } }
    );

    // Direct Redis cache invalidation
    const removedUserId = memberToRemove.userId?._id?.toString();
    const cachePatterns = [
      `${TEAM_MEMBERS_CACHE_PREFIX}${params.slug}:*`,
      `${TEAM_CACHE_PREFIX}${params.slug}:*`,
      `${TEAM_INVITATIONS_CACHE_PREFIX}${params.slug}:*`,
      `team:counts:${params.slug}`,
      `${USER_TEAMS_CACHE_PREFIX}${userId}:*`,
      `${TEAMS_CACHE_PREFIX}${userId}:*`,
      ...(removedUserId
        ? [
            `${USER_TEAMS_CACHE_PREFIX}${removedUserId}:*`,
            `${TEAMS_CACHE_PREFIX}${removedUserId}:*`,
          ]
        : []),
    ];

    await Promise.all(
      cachePatterns.map((pattern) =>
        redis.eval(
          `local keys = redis.call('keys', ARGV[1])
           for i,k in ipairs(keys) do
             redis.call('del', k)
           end
           return keys`,
          0,
          pattern
        )
      )
    );

    // Send removal email (fire and forget)
    try {
      await sendEmail({
        sender: {
          name: `${team.name} (via Chatzuri)`,
          address: process.env.MAIL_USER!,
        },
        recipients: [
          {
            name:
              memberToRemove.userId &&
              typeof memberToRemove.userId === "object" &&
              "name" in memberToRemove.userId
                //eslint-disable-next-line  @typescript-eslint/no-explicit-any
                ? (memberToRemove.userId as any).name
                : memberToRemove.name || "Member",
            address:
              memberToRemove.userId &&
              typeof memberToRemove.userId === "object" &&
              "email" in memberToRemove.userId
                //eslint-disable-next-line  @typescript-eslint/no-explicit-any
                ? (memberToRemove.userId as any).email
                : memberToRemove.email || "",
          },
        ],
        subject: `You've been removed from ${team.name}`,
        message: `You've been removed from ${team.name}.`,
        html: generateRemovalEmail(
          { ...team.toObject(), _id: (team._id as string).toString() },
          ownerEmail
        ),
      });
    } catch (emailError) {
      console.error("[MEMBER_REMOVAL_EMAIL_ERROR]", emailError);
      // Continue even if email fails
    }

    return NextResponse.json({
      success: true,
      message: "Member removed successfully",
    });
  } catch (error) {
    console.error("[MEMBER_REMOVE_ERROR]", error);
    return NextResponse.json(
      {
        message: "Internal Server Error",
      },
      { status: 500 }
    );
  }
}
