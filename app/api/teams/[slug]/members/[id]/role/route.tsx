import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth/auth";
import dbConnect from "@/lib/db/db";
import MemberModel from "@/models/member";
import { RoleEnum } from "@/enums/enums";
import { TeamModel } from "@/models";
import { redis } from "@/lib/redis/redis";
import {
  TEAM_CACHE_PREFIX,
  TEAM_INVITATIONS_CACHE_PREFIX,
  TEAM_MEMBERS_CACHE_PREFIX,
  TEAMS_CACHE_PREFIX,
  USER_TEAMS_CACHE_PREFIX,
} from "@/config/redis";

interface Props {
  params: Promise<{
    slug: string;
    id: string;
  }>;
}

export async function PATCH(req: Request, props: Props) {
  try {
    await dbConnect();
    const session = await getServerSession(authOptions);
    const userId = session?.user?.id;
    const params = await props.params;

    if (!userId) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
    }

    const { role } = await req.json();

    // Validate role
    if (!Object.values(RoleEnum).includes(role)) {
      return NextResponse.json({ message: "Invalid role" }, { status: 400 });
    }

    // Verify team exists and user is owner/admin
    const team = await TeamModel.findOne({
      url: params.slug,
      $or: [
        { ownerId: userId }, // Team owner
        { "members.userId": userId, "members.role": RoleEnum.ADMIN }, // Team admin
      ],
    });

    if (!team) {
      return NextResponse.json(
        { message: "Team not found or unauthorized" },
        { status: 404 }
      );
    }

    // Prevent modifying owners
    if (role === RoleEnum.OWNER) {
      return NextResponse.json(
        { message: "Cannot assign owner role" },
        { status: 403 }
      );
    }

    // Update member role
    const updatedMember = await MemberModel.findOneAndUpdate(
      {
        _id: params.id,
        teamId: team._id,
        role: { $ne: RoleEnum.OWNER }, // Prevent modifying owners
      },
      { role },
      { new: true }
    );

    if (!updatedMember) {
      return NextResponse.json(
        {
          message:
            "Member not found or unauthorized or has not accepted the invite",
        },
        { status: 404 }
      );
    }

    // Invalidate relevant caches
    const cachePatterns = [
      `${TEAM_MEMBERS_CACHE_PREFIX}${params.slug}:*`, // All paginated member lists
      `${TEAM_CACHE_PREFIX}${params.slug}:*`, // Team details cache (all variations)
      `${TEAM_INVITATIONS_CACHE_PREFIX}${params.slug}:*`, // Team invitations
      `${USER_TEAMS_CACHE_PREFIX}${userId}:*`, // Current user's teams
      `${TEAMS_CACHE_PREFIX}${userId}:*`, // Main teams listing
      ...(updatedMember.userId
        ? [
            `${USER_TEAMS_CACHE_PREFIX}${updatedMember.userId._id}:*`, // Updated member's teams
            `${TEAMS_CACHE_PREFIX}${updatedMember.userId._id}:*`, // Updated member's main listing
          ]
        : []),
    ];

    await Promise.all(
      cachePatterns.map((pattern) =>
        redis.eval(
          `local keys = redis.call('keys', ARGV[1])
           for i,k in ipairs(keys) do
             redis.call('del', k)
           end
           return keys`,
          0,
          pattern
        )
      )
    );

    return NextResponse.json({
      success: true,
      member: {
        id: updatedMember._id,
        role: updatedMember.role,
      },
    });
  } catch (error) {
    console.error("[MEMBER_ROLE_UPDATE_ERROR]", error);
    return NextResponse.json(
      {
        message: "Internal Server Error",
      },
      { status: 500 }
    );
  }
}
