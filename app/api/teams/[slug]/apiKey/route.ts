import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth/auth";
import dbConnect from "@/lib/db/db";
import { TeamModel } from "@/models";
import { generateSecureApiKey } from "@/utils/generate-api-key";
import { redis } from "@/lib/redis/redis";
import { TEAMS_CACHE_PREFIX } from "@/config/redis";

interface Props {
  params: Promise<{
    slug: string;
  }>;
}

export async function POST(request: Request, props: Props) {
  const session = await getServerSession(authOptions);
  const params = await props.params;
  if (!session?.user?.id) {
    return NextResponse.json(
      { success: false, error: "Unauthorized" },
      { status: 401 }
    );
  }

  try {
    await dbConnect();
    const teamUrl = params.slug;

    // Find the team and verify ownership
    const team = await TeamModel.findOne({ url: teamUrl });

    if (!team) {
      return NextResponse.json(
        { success: false, error: "Team not found" },
        { status: 404 }
      );
    }

    if (team.ownerId.toString() !== session.user.id) {
      return NextResponse.json(
        { success: false, error: "Unauthorized - Not team owner" },
        { status: 403 }
      );
    }

    // Generate new API key
    const newApiKey = generateSecureApiKey();

    // Update the team with new API key
    const updatedTeam = await TeamModel.findOneAndUpdate(
      { url: teamUrl },
      { apiKey: newApiKey },
      { new: true }
    ).select("apiKey");

    if (!updatedTeam) {
      return NextResponse.json(
        { success: false, error: "Failed to update team with new API key" },
        { status: 500 }
      );
    }

    // Invalidate the teams cache for this user
    const userId = session.user.id;
    const pattern = `${TEAMS_CACHE_PREFIX}${userId}:*`;
    const keys = await redis.keys(pattern);
    if (keys.length > 0) {
      await redis.del(...keys);
    }

    return NextResponse.json(
      {
        success: true,
        apiKey: updatedTeam.apiKey,
        message: "API key generated successfully",
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error generating API key:", error);
    return NextResponse.json(
      { success: false, error: "Failed to generate API key" },
      { status: 500 }
    );
  }
}
