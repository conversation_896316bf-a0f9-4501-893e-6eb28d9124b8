import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth/auth";
import dbConnect from "@/lib/db/db";
import { TeamModel } from "@/models";
import { openAiKeySchema } from "@/validations/validations";
import {
  TEAM_CACHE_PREFIX,
  TEAM_CREDENTIALS_CACHE_PREFIX,
} from "@/config/redis";
import { redis } from "@/lib/redis/redis";

interface Props {
  params: Promise<{
    slug: string;
  }>;
}

export async function POST(request: Request, props: Props) {
  const session = await getServerSession(authOptions);
  const params = await props.params;
  if (!session?.user?.id) {
    return NextResponse.json(
      { success: false, error: "Unauthorized" },
      { status: 401 }
    );
  }

  try {
    await dbConnect();
    const slug = params.slug;
    const userId = session.user.id;

    // Validate request body
    const body = await request.json();
    const validation = openAiKeySchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json(
        { success: false, error: validation.error.errors },
        { status: 400 }
      );
    }

    const { openAiOrg, openAiKey } = validation.data;

    // Check if user has permission to update this team
    const team = await TeamModel.findOne({
      url: slug,
      ownerId: userId,
    });

    if (!team) {
      return NextResponse.json(
        {
          success: false,
          error: "Team not found or you do not have permission",
        },
        { status: 404 }
      );
    }

    // Update the team with new OpenAI credentials
    const updatedTeam = await TeamModel.findByIdAndUpdate(
      team._id,
      {
        $set: {
          openAiKey,
          openAiOrg,
          metaData: JSON.stringify({
            ...(team.metaData ? JSON.parse(team.metaData) : {}),
            openAiOrg,
          }),
        },
      },
      { new: true }
    );

    if (!updatedTeam) {
      return NextResponse.json(
        {
          success: false,
          error: "Failed to update team credentials",
        },
        { status: 500 }
      );
    }

    // Invalidate relevant caches
    const cacheKeys = [
      `${TEAM_CACHE_PREFIX}${slug}`,
      `${TEAM_CREDENTIALS_CACHE_PREFIX}${slug}`,
    ];

    await redis.del(...cacheKeys);

    // Optionally encrypt sensitive data before returning
    const responseData = {
      ...updatedTeam.toObject(),
      openAiKey: "••••••••••••" + (openAiKey ? openAiKey.slice(-4) : ""),
    };

    return NextResponse.json(
      {
        success: true,
        message: "OpenAI credentials saved successfully",
        team: responseData,
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error saving OpenAI credentials:", error);
    return NextResponse.json(
      { success: false, error: "Failed to save OpenAI credentials" },
      { status: 500 }
    );
  }
}
