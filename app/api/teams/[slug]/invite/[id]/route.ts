import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth/auth";
import TeamInvitationModel from "@/models/team-invitation";
import dbConnect from "@/lib/db/db";
import { StatusEnum } from "@/enums/enums";
import { redis } from "@/lib/redis/redis";
import {
  TEAM_MEMBERS_CACHE_PREFIX,
  TEAM_INVITATIONS_CACHE_PREFIX,
  TEAM_CACHE_PREFIX,
} from "@/config/redis";
import { TeamModel } from "@/models";

interface Props {
  params: Promise<{
    slug: string;
    id: string;
  }>;
}

export async function DELETE(req: Request, props: Props) {
  try {
    await dbConnect();
    const params = await props.params;
    const session = await getServerSession(authOptions);
    const userId = session?.user?.id;

    if (!userId) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const team = await TeamModel.findOne({
      url: params.slug,
      ownerId: userId,
    }).exec();

    if (!team) {
      return new NextResponse("Team not found", { status: 404 });
    }

    const deletedInvitation = await TeamInvitationModel.findOneAndDelete({
      _id: params.id,
      teamId: team._id,
      status: StatusEnum.PENDING,
    }).exec();

    if (!deletedInvitation) {
      return new NextResponse("Invitation not found or already cancelled", {
        status: 404,
      });
    }

    // Invalidate relevant Redis caches
    const cacheKeys = [
      `${TEAM_MEMBERS_CACHE_PREFIX}${team._id}`,
      `${TEAM_INVITATIONS_CACHE_PREFIX}${team._id}`,
      `${TEAM_CACHE_PREFIX}${params.slug}:*`,
    ];

    const pipeline = redis.pipeline();
    cacheKeys.forEach((pattern) => {
      pipeline.eval(
        `local keys = redis.call('keys', ARGV[1]) 
         for i,k in ipairs(keys) do 
           redis.call('del', k) 
         end 
         return keys`,
        0,
        pattern
      );
    });
    await pipeline.exec();

    return NextResponse.json({
      success: true,
      message: "Invitation cancelled successfully",
    });
  } catch (error) {
    console.error("[INVITATION_CANCEL_ERROR]", error);
    return new NextResponse(
      typeof error === "object" && error !== null && "message" in error
        ? (error as { message?: string }).message || "Internal Server Error"
        : "Internal Server Error",
      { status: 500 }
    );
  }
}
