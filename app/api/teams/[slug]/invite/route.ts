import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth/auth";
import TeamInvitationModel from "@/models/team-invitation";
import MemberModel from "@/models/member";
import { generateInviteToken } from "@/lib/tokens/tokens";
import { sendEmail } from "@/utils/mail/mail";
import dbConnect from "@/lib/db/db";
import { TeamModel } from "@/models";
import { StatusEnum } from "@/enums/enums";
import { generateInviteEmail } from "@/utils/mail/templates/member-invite";
import { createHash } from "crypto";
import { redis } from "@/lib/redis/redis";
import {
  TEAM_MEMBERS_CACHE_PREFIX,
  TEAM_INVITATIONS_CACHE_PREFIX,
  TEAM_CACHE_PREFIX,
} from "@/config/redis";

interface Props {
  params: Promise<{
    slug: string;
  }>;
}

export async function POST(req: Request, props: Props) {
  try {
    await dbConnect();

    const session = await getServerSession(authOptions);
    const userId = session?.user?.id;

    if (!userId) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const { email } = await req.json();

    if (!email) {
      return new NextResponse("Email is required", { status: 400 });
    }

    // Validate email format
    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      return new NextResponse("Invalid email format", { status: 400 });
    }

    const params = await props.params;
    const team = (await TeamModel.findOne({
      url: params.slug,
      ownerId: userId,
    }).exec()) as (typeof TeamModel)["prototype"] | null;

    if (!team) {
      return new NextResponse("Team not found", { status: 404 });
    }

    // Check membership and invitations in a single query for better performance
    const [existingMember, existingInvite] = await Promise.all([
      MemberModel.findOne({
        teamId: team._id,
        email,
      }).exec(),

      TeamInvitationModel.findOne({
        teamId: team._id,
        email,
        status: StatusEnum.PENDING,
        expiresAt: { $gt: new Date() },
      }).exec(),
    ]);

    if (existingMember) {
      return NextResponse.json(
        { error: "User is already a team member" },
        { status: 409 }
      );
    }

    if (existingInvite) {
      // Calculate when the existing invite expires (in days)
      const expiresInDays = Math.ceil(
        (existingInvite.expiresAt.getTime() - Date.now()) /
          (1000 * 60 * 60 * 24)
      );

      return NextResponse.json(
        {
          error: "Invitation already sent",
          expiresIn: `${expiresInDays} days`,
          invitedAt: existingInvite.createdAt,
        },
        { status: 409 }
      );
    }

    // Create and send invitation
    const inviteToken = generateInviteToken(email, team._id.toString());
    const tokenHash = createHash("sha256").update(inviteToken).digest("hex");

    const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 7 days

    const invitation = new TeamInvitationModel({
      teamId: team._id,
      email,
      token: inviteToken,
      tokenHash,
      expiresAt,
      invitedBy: userId,
      status: StatusEnum.PENDING,
    });

    await invitation.save();

    // Invalidate relevant Redis caches
    const cacheKeys = [
      `${TEAM_MEMBERS_CACHE_PREFIX}${team._id}`, // Team members list
      `${TEAM_INVITATIONS_CACHE_PREFIX}${team._id}`, // Team invitations list
      `${TEAM_CACHE_PREFIX}${params.slug}:*`, // Team data cache
    ];

    // Delete all matching keys using pipeline for better performance
    const pipeline = redis.pipeline();
    cacheKeys.forEach((pattern) => {
      pipeline.eval(
        `local keys = redis.call('keys', ARGV[1]) 
         for i,k in ipairs(keys) do 
           redis.call('del', k) 
         end 
         return keys`,
        0,
        pattern
      );
    });
    await pipeline.exec();

    const { headers } = req;
    const host = headers.get("host");
    const protocol = headers.get("x-forwarded-proto") || "http";

    const baseUrl = `${protocol}://${host}`;
    const inviteLink = `${baseUrl}/invite/${inviteToken}`;
    const html = generateInviteEmail(team.name, inviteLink);

    try {
      await sendEmail({
        sender: {
          name: `${team.name} (via Chatzuri)`,
          address: process.env.MAIL_USER!,
        },
        recipients: [{ name: email.split("@")[0], address: email }],
        subject: `You've been invited to join ${team.name}`,
        message: `You've been invited to join ${team.name}. Please use the link provided to accept the invitation.`,
        html,
      });

      // Log successful email sending
      await TeamInvitationModel.updateOne(
        { _id: invitation._id },
        { $set: { emailSent: true, sentAt: new Date() } }
      );
    } catch (error) {
      console.error("Failed to send invitation email:", error);
      // Mark invitation as failed to send
      await TeamInvitationModel.updateOne(
        { _id: invitation._id },
        {
          $set: {
            emailSent: false,
            sendError: error instanceof Error ? error.message : String(error),
          },
        }
      );
      throw new Error("Failed to send invitation email");
    }

    return NextResponse.json({
      success: true,
      expiresAt,
      inviteLink,
    });
  } catch (error) {
    console.error("[TEAM_INVITE_ERROR]", error);
    return new NextResponse(
      typeof error === "object" && error !== null && "message" in error
        ? (error as { message?: string }).message || "Internal Server Error"
        : "Internal Server Error",
      { status: 500 }
    );
  }
}
