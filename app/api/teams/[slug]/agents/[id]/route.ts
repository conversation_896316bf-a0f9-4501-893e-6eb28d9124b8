import { NextResponse } from "next/server";
import { AgentModel, TeamModel } from "@/models";
import dbConnect from "@/lib/db/db";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth/auth";
import { redis } from "@/lib/redis/redis";
import { AGENT_CACHE_KEY } from "@/config/redis";
import { checkTeamAccess } from "@/lib/auth/check-team-access";

interface Props {
  params: Promise<{
    slug: string;
    id: string;
  }>;
}

export async function GET(req: Request, props: Props) {
  const session = await getServerSession(authOptions);
  const params= await props.params;
  const slug=params.slug
  const agentId=params.id
  const userId = session?.user?.id;

  if (!userId) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  await dbConnect();

  try {
    if (!slug) {
      return NextResponse.json(
        { error: "Team URL query parameter is required" },
        { status: 400 }
      );
    }

    const currentTeam = await TeamModel.findOne({
      url: slug,
      ownerId: userId,
    });

    if (!currentTeam || !currentTeam._id) {
      return NextResponse.json({ error: "Team not found" }, { status: 404 });
    }

    const teamId = currentTeam._id;

    // Verify team access
    const hasAccess = await checkTeamAccess(userId, String(currentTeam._id));
    if (!hasAccess) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 403 });
    }

    // Check cache first
    const cacheKey = `${AGENT_CACHE_KEY}:${agentId}`;
    const cachedAgent = await redis.get(cacheKey);

    if (cachedAgent) {
      return NextResponse.json(JSON.parse(cachedAgent));
    }

    // Fetch from database
    const agent = await AgentModel.findOne({
      _id: agentId,
      teamId,
      isActive: true,
    }).lean();

    if (!agent) {
      return NextResponse.json({ error: "Agent not found" }, { status: 404 });
    }

    // Cache the result
    await redis.set(cacheKey, JSON.stringify(agent), "EX", 60 * 60);

    return NextResponse.json(agent);
  } catch (error) {
    console.error("Error fetching agent:", error);
    return NextResponse.json(
      { error: "Failed to fetch agent" },
      { status: 500 }
    );
  }
}

export async function PATCH(req: Request, props: Props) {
  const session = await getServerSession(authOptions);
  const params = await props.params;
  const userId = session?.user?.id;
  if (!userId) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }
  await dbConnect();

  try {
    const body = await req.json();
    const { name, teamId } = body;

    // Verify team access
    const hasAccess = await checkTeamAccess(userId, teamId);
    if (!hasAccess) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 403 });
    }

    const updatedAgent = await AgentModel.findByIdAndUpdate(
      params.id,
      { name },
      { new: true }
    );

    if (!updatedAgent) {
      return NextResponse.json({ error: "Agent not found" }, { status: 404 });
    }

    // Invalidate all cache entries for this team
    const cachePattern = `${AGENT_CACHE_KEY}:${teamId}:*`;
    const keys = await redis.keys(cachePattern);
    if (keys.length > 0) {
      await redis.del(...keys);
    }

    return NextResponse.json(updatedAgent);
  } catch (error) {
    console.error("Error updating agent:", error);
    return NextResponse.json(
      { error: "Failed to update agent" },
      { status: 500 }
    );
  }
}

export async function DELETE(req: Request, props: Props) {
  const session = await getServerSession(authOptions);
  const params = await props.params;
  const userId = session?.user?.id;
  if (!userId) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }
  await dbConnect();

  try {
    const { searchParams } = new URL(req.url);
    const teamId = searchParams.get("teamId");

    if (!teamId) {
      return NextResponse.json(
        { error: "teamId is required" },
        { status: 400 }
      );
    }

    // Verify team access
    const hasAccess = await checkTeamAccess(userId, teamId);
    if (!hasAccess) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 403 });
    }

    // Soft delete
    const deletedAgent = await AgentModel.findByIdAndUpdate(
      params.id,
      { isActive: false },
      { new: true }
    );

    if (!deletedAgent) {
      return NextResponse.json({ error: "Agent not found" }, { status: 404 });
    }

    // Invalidate the cache
    await redis.del(`${AGENT_CACHE_KEY}:${teamId}`);

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error deleting agent:", error);
    return NextResponse.json(
      { error: "Failed to delete agent" },
      { status: 500 }
    );
  }
}
