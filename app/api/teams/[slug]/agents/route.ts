import { NextResponse } from "next/server";
import { AgentModel } from "@/models";
import dbConnect from "@/lib/db/db";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth/auth";
import { redis } from "@/lib/redis/redis";
import { CACHE_TTL, AGENT_CACHE_KEY } from "@/config/redis";
import { checkTeamAccess } from "@/lib/auth/check-team-access";
import mongoose from "mongoose";

export async function GET(req: Request) {
  try {
    await dbConnect();
    const { searchParams } = new URL(req.url);
    const teamId = searchParams.get("teamId");
    const limit = parseInt(searchParams.get("limit") || "10");
    const cursor = searchParams.get("cursor");

    if (!teamId) {
      return NextResponse.json(
        { error: "teamId is required" },
        { status: 400 }
      );
    }

    // Try to get cached agents first
    const cacheKey = `${AGENT_CACHE_KEY}:${teamId}:${limit}:${
      cursor || "first"
    }`;
    const cachedAgents = await redis.get(cacheKey);
    if (cachedAgents) {
      return NextResponse.json(JSON.parse(cachedAgents));
    }

    // Verify team access
    const session = await getServerSession(authOptions);
    const userId = session?.user?.id;
    if (!userId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const hasAccess = await checkTeamAccess(userId, teamId);
    if (!hasAccess) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 403 });
    }

    // Build query
    const query: Record<string, unknown> = {
      teamId: new mongoose.Types.ObjectId(teamId),
      isActive: true,
    };

    if (cursor) {
      query._id = { $lt: new mongoose.Types.ObjectId(cursor) };
    }

    // Debug: Log the query and teamId
    console.log("🔍 Fetching agents with query:", query);
    console.log("🔍 TeamId type:", typeof teamId, "Value:", teamId);

    // Fetch from database with pagination
    const agents = await AgentModel.find(query)
      .sort({ createdAt: -1 })
      .limit(limit + 1)
      .lean();

    console.log("🔍 Found agents:", agents.length, "agents");
    console.log("🔍 First agent (if any):", agents[0] ? { _id: agents[0]._id, teamId: agents[0].teamId, name: agents[0].name } : "No agents found");

    let nextCursor = null;
    if (agents.length > limit) {
      nextCursor = agents[limit - 1]._id.toString();
      agents.pop();
    }

    const result = {
      data: agents,
      nextCursor,
    };

    // Cache the result
    await redis.set(
      cacheKey,
      JSON.stringify(result),
      "EX", // Set expire
      CACHE_TTL
    );

    return NextResponse.json(result);
  } catch (error) {
    console.error("Error fetching agents:", error);
    return NextResponse.json(
      { error: "Failed to fetch agents" },
      { status: 500 }
    );
  }
}

export async function POST(req: Request) {
  const session = await getServerSession(authOptions);
  const userId = session?.user?.id;
  if (!userId) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }
  await dbConnect();

  try {
    const body = await req.json();
    const { name, teamId } = body;

    // Verify team access - now checks for owner/admin role
    const hasAccess = await checkTeamAccess(userId, teamId);
    if (!hasAccess) {
      return NextResponse.json(
        { error: "You don't have permission to create agents for this team" },
        { status: 403 }
      );
    }

    console.log("🚀 Creating agent with data:", { name, teamId, userId });
    console.log("🚀 TeamId type:", typeof teamId, "Value:", teamId);

    const newAgent = await AgentModel.create({
      name,
      teamId: new mongoose.Types.ObjectId(teamId),
      userId: new mongoose.Types.ObjectId(userId),
    });

    console.log("✅ Agent created:", {
      _id: newAgent._id,
      teamId: newAgent.teamId,
      name: newAgent.name,
      teamIdType: typeof newAgent.teamId
    });

    // Invalidate all cache entries for this team
    const cachePattern = `${AGENT_CACHE_KEY}:${teamId}:*`;
    const keys = await redis.keys(cachePattern);
    if (keys.length > 0) {
      await redis.del(...keys);
    }
    console.log("🗑️ Invalidated cache keys:", keys.length, "keys");

    return NextResponse.json(newAgent, { status: 201 });
  } catch (error) {
    console.error("Error creating agent:", error);
    return NextResponse.json(
      { error: "Failed to create agent" },
      { status: 500 }
    );
  }
}
