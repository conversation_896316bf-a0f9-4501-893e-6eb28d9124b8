import { NextRequest, NextResponse } from 'next/server'
import { connectToDatabase } from '@/lib/mongodb'

interface HealthCheck {
  status: 'healthy' | 'unhealthy' | 'degraded'
  timestamp: string
  version: string
  uptime: number
  environment: string
  checks: {
    database: HealthCheckResult
    redis: HealthCheckResult
    external_services: HealthCheckResult
    memory: HealthCheckResult
    disk: HealthCheckResult
  }
}

interface HealthCheckResult {
  status: 'pass' | 'fail' | 'warn'
  responseTime?: number
  message?: string
  details?: any
}

export async function GET(request: NextRequest) {
  const startTime = Date.now()
  
  try {
    // Initialize health check response
    const healthCheck: HealthCheck = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: process.env.npm_package_version || '1.0.0',
      uptime: process.uptime(),
      environment: process.env.NODE_ENV || 'development',
      checks: {
        database: await checkDatabase(),
        redis: await checkRedis(),
        external_services: await checkExternalServices(),
        memory: checkMemory(),
        disk: checkDisk()
      }
    }

    // Determine overall health status
    const failedChecks = Object.values(healthCheck.checks).filter(check => check.status === 'fail')
    const warnChecks = Object.values(healthCheck.checks).filter(check => check.status === 'warn')

    if (failedChecks.length > 0) {
      healthCheck.status = 'unhealthy'
    } else if (warnChecks.length > 0) {
      healthCheck.status = 'degraded'
    }

    // Set appropriate HTTP status code
    const httpStatus = healthCheck.status === 'healthy' ? 200 : 
                      healthCheck.status === 'degraded' ? 200 : 503

    return NextResponse.json(healthCheck, { 
      status: httpStatus,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'X-Response-Time': `${Date.now() - startTime}ms`
      }
    })

  } catch (error) {
    console.error('Health check failed:', error)
    
    return NextResponse.json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: 'Health check failed',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { 
      status: 503,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'X-Response-Time': `${Date.now() - startTime}ms`
      }
    })
  }
}

async function checkDatabase(): Promise<HealthCheckResult> {
  const startTime = Date.now()
  
  try {
    const { db } = await connectToDatabase()
    
    // Simple ping to check database connectivity
    await db.admin().ping()
    
    // Check if we can perform a basic operation
    const collections = await db.listCollections().toArray()
    
    return {
      status: 'pass',
      responseTime: Date.now() - startTime,
      message: 'Database connection successful',
      details: {
        collections: collections.length,
        connected: true
      }
    }
  } catch (error) {
    return {
      status: 'fail',
      responseTime: Date.now() - startTime,
      message: 'Database connection failed',
      details: {
        error: error instanceof Error ? error.message : 'Unknown database error'
      }
    }
  }
}

async function checkRedis(): Promise<HealthCheckResult> {
  const startTime = Date.now()
  
  try {
    // Only check Redis if URL is configured
    if (!process.env.REDIS_URL) {
      return {
        status: 'warn',
        message: 'Redis not configured',
        details: { configured: false }
      }
    }

    // Import Redis client dynamically to avoid errors if not installed
    const { createClient } = await import('redis')
    const client = createClient({ url: process.env.REDIS_URL })
    
    await client.connect()
    await client.ping()
    await client.disconnect()
    
    return {
      status: 'pass',
      responseTime: Date.now() - startTime,
      message: 'Redis connection successful'
    }
  } catch (error) {
    return {
      status: 'fail',
      responseTime: Date.now() - startTime,
      message: 'Redis connection failed',
      details: {
        error: error instanceof Error ? error.message : 'Unknown Redis error'
      }
    }
  }
}

async function checkExternalServices(): Promise<HealthCheckResult> {
  const startTime = Date.now()
  const services = []
  
  try {
    // Check OpenAI API if configured
    if (process.env.OPENAI_API_KEY) {
      try {
        const response = await fetch('https://api.openai.com/v1/models', {
          headers: {
            'Authorization': `Bearer ${process.env.OPENAI_API_KEY}`,
            'User-Agent': 'Chatzuri-HealthCheck/1.0'
          },
          signal: AbortSignal.timeout(5000)
        })
        
        services.push({
          name: 'OpenAI',
          status: response.ok ? 'pass' : 'fail',
          responseTime: Date.now() - startTime
        })
      } catch (error) {
        services.push({
          name: 'OpenAI',
          status: 'fail',
          error: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    }

    // Check other external services as needed
    // Add more service checks here...

    const failedServices = services.filter(s => s.status === 'fail')
    
    return {
      status: failedServices.length === 0 ? 'pass' : failedServices.length === services.length ? 'fail' : 'warn',
      responseTime: Date.now() - startTime,
      message: `${services.length - failedServices.length}/${services.length} external services healthy`,
      details: { services }
    }
  } catch (error) {
    return {
      status: 'warn',
      responseTime: Date.now() - startTime,
      message: 'External service check failed',
      details: {
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }
}

function checkMemory(): HealthCheckResult {
  try {
    const memUsage = process.memoryUsage()
    const totalMem = memUsage.heapTotal
    const usedMem = memUsage.heapUsed
    const memoryUsagePercent = (usedMem / totalMem) * 100

    // Warn if memory usage is above 80%, fail if above 95%
    const status = memoryUsagePercent > 95 ? 'fail' : 
                  memoryUsagePercent > 80 ? 'warn' : 'pass'

    return {
      status,
      message: `Memory usage: ${memoryUsagePercent.toFixed(2)}%`,
      details: {
        heapUsed: Math.round(usedMem / 1024 / 1024), // MB
        heapTotal: Math.round(totalMem / 1024 / 1024), // MB
        external: Math.round(memUsage.external / 1024 / 1024), // MB
        rss: Math.round(memUsage.rss / 1024 / 1024), // MB
        usagePercent: memoryUsagePercent
      }
    }
  } catch (error) {
    return {
      status: 'warn',
      message: 'Memory check failed',
      details: {
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }
}

function checkDisk(): HealthCheckResult {
  try {
    // Basic disk space check (simplified for container environments)
    const fs = require('fs')
    const stats = fs.statSync('.')
    
    return {
      status: 'pass',
      message: 'Disk accessible',
      details: {
        accessible: true,
        // In container environments, disk space monitoring is typically handled at the orchestration level
        note: 'Detailed disk metrics available via container orchestration'
      }
    }
  } catch (error) {
    return {
      status: 'fail',
      message: 'Disk check failed',
      details: {
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }
}
