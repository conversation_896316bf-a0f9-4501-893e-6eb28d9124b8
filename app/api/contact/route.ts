import { NextResponse } from "next/server";
import dbConnect from "@/lib/db/db";
import { ContactFormData } from "@/types/types";
import { ContactModel } from "@/models";
import { contactFormSchema } from "@/validations/validations";
import { nameFromEmail } from "@/utils/functions";
import { sendEmail } from "@/utils/mail/mail";

export async function POST(request: Request) {
  try {
    const body: ContactFormData = await request.json();

    // Validate with Zod
    const validatedData = contactFormSchema.parse(body);

    await dbConnect();

    const sender = {
      name: nameFromEmail(validatedData.email),
      address: process.env.MAIL_USER as string,
    };

    const replyTo = validatedData.email;

    const recipients = [
      {
        name: process.env.MAIL_NAME as string,
        address: process.env.MAIL_USER as string,
      },
    ];
    
    //eslint-disable-next-line @typescript-eslint/no-unused-vars
    const [_, contact] = await Promise.all([
      sendEmail({
        sender,
        recipients,
        subject: validatedData.title,
        message: validatedData.message,
        chatbotId: validatedData.chatbotId,
        replyTo,
      }),
      ContactModel.create({
        ...validatedData,
        status: "pending",
      }),
    ]);

    return NextResponse.json(
      {
        success: true,
        ticketId: contact._id.toString(),
        message: "Contact form submitted successfully",
      },
      { status: 201 }
    );
    //eslint-disable-next-line @typescript-eslint/no-explicit-any
  } catch (error: any) {
    console.error("Contact submission error:", error);

    if (error.name === "ZodError") {
      return NextResponse.json(
        { success: false, message: error.errors[0].message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        message:
          process.env.NODE_ENV === "development"
            ? error.message
            : "Failed to submit contact form",
      },
      { status: 500 }
    );
  }
}
