import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import { ObjectId } from 'mongodb';
import { getDirectExecutor } from '@/lib/execution/direct-executor';
import { getQueueManager } from '@/lib/execution/queue-manager';

// Chat message endpoint for message triggers
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ workflowId: string }> }
) {
  try {
    const { workflowId } = await params;
    const formData = await request.formData();
    
    const message = formData.get('message') as string;
    const sessionId = formData.get('sessionId') as string;
    const files = formData.getAll('files') as File[];

    console.log(`💬 Chat message received for workflow: ${workflowId}`);
    console.log(`📋 Session ID: ${sessionId}`);
    console.log(`💬 Message: ${message}`);
    console.log(`📎 Files: ${files.length}`);

    // Get workflow and validate message trigger
    const { db } = await connectToDatabase();
    const workflow = await db.collection('workflows').findOne({
      _id: new ObjectId(workflowId)
    });

    if (!workflow) {
      return NextResponse.json(
        { error: 'Workflow not found' },
        { status: 404 }
      );
    }

    // Find message trigger in workflow
    const messageTrigger = workflow.nodes?.find((node: any) => 
      node.type === 'message'
    );

    if (!messageTrigger) {
      return NextResponse.json(
        { error: 'No message trigger found in workflow' },
        { status: 400 }
      );
    }

    const config = messageTrigger.data?.config || {};

    // Check if workflow is public or requires authentication
    if (!config.isPublic) {
      // For non-public workflows, you might want to add authentication
      // For now, we'll allow all messages
    }

    // Rate limiting check
    const clientIP = getClientIP(request);
    const rateLimitResult = await checkRateLimit(clientIP, workflowId, config);
    if (!rateLimitResult.allowed) {
      return NextResponse.json(
        { error: config.ipLimitMessage || 'Rate limit exceeded' },
        { status: 429 }
      );
    }

    // Validate message against trigger conditions
    const triggerMatches = validateMessageTrigger(message, config);
    if (!triggerMatches) {
      return NextResponse.json({
        success: true,
        response: "I didn't understand that. Could you please rephrase?",
        sessionId
      });
    }

    // Generate execution ID
    const executionId = `chat_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // Process uploaded files
    const fileData = await processUploadedFiles(files);

    // Prepare chat input data
    const chatInput = {
      message: {
        content: message,
        sessionId,
        timestamp: new Date().toISOString(),
        files: fileData,
        ip: clientIP
      },
      session: {
        id: sessionId,
        messages: await getSessionMessages(sessionId, workflowId)
      },
      ...fileData.reduce((acc, file, index) => {
        acc[`file_${index}`] = file;
        return acc;
      }, {} as Record<string, any>)
    };

    // Prepare initial variables
    const initialVariables = {
      message_content: message,
      session_id: sessionId,
      user_ip: clientIP,
      file_count: files.length,
      triggered_by: 'message',
      trigger_timestamp: new Date().toISOString(),
      chat_history: await getSessionMessages(sessionId, workflowId)
    };

    console.log(`🚀 Starting chat workflow execution: ${workflow.name}`);
    console.log(`📋 Execution ID: ${executionId}`);
    console.log(`⚙️ Mode: ${workflow.executionMode || 'async'}`);

    // Save message to session history
    await saveMessageToSession(sessionId, workflowId, {
      content: message,
      sender: 'user',
      timestamp: new Date(),
      files: fileData
    });

    // Execute based on workflow execution mode
    const executionMode = workflow.executionMode || 'async';
    
    if (executionMode === 'direct') {
      // Direct/synchronous execution for real-time chat
      try {
        const directExecutor = getDirectExecutor();
        const result = await directExecutor.executeWorkflow(
          workflowId, 
          executionId, 
          `chat_user_${sessionId}`, // Special user ID for chat sessions
          { ...chatInput, ...initialVariables }
        );

        // Extract response from workflow result
        const response = extractChatResponse(result);

        // Save bot response to session history
        await saveMessageToSession(sessionId, workflowId, {
          content: response,
          sender: 'bot',
          timestamp: new Date(),
          executionId
        });

        return NextResponse.json({
          success: true,
          response,
          sessionId,
          executionId,
          timestamp: new Date().toISOString()
        });

      } catch (error) {
        console.error(`❌ Direct chat execution failed:`, error);
        
        const errorResponse = "I'm sorry, I encountered an error. Please try again.";
        
        // Save error response to session
        await saveMessageToSession(sessionId, workflowId, {
          content: errorResponse,
          sender: 'bot',
          timestamp: new Date(),
          error: true
        });

        return NextResponse.json({
          success: false,
          response: errorResponse,
          sessionId,
          error: error instanceof Error ? error.message : 'Execution failed',
          timestamp: new Date().toISOString()
        });
      }

    } else {
      // Async execution - return immediate response and process in background
      try {
        const queueManager = await getQueueManager();
        const job = await queueManager.queueWorkflowExecution(
          workflowId,
          executionId,
          `chat_user_${sessionId}`,
          { ...chatInput, ...initialVariables },
          'async'
        );

        // Return immediate response for async processing
        const immediateResponse = "I'm processing your message. Please wait a moment...";
        
        await saveMessageToSession(sessionId, workflowId, {
          content: immediateResponse,
          sender: 'bot',
          timestamp: new Date(),
          executionId,
          jobId: job.id
        });

        return NextResponse.json({
          success: true,
          response: immediateResponse,
          sessionId,
          executionId,
          jobId: job.id,
          mode: 'async',
          timestamp: new Date().toISOString()
        });

      } catch (error) {
        console.error(`❌ Async chat execution failed:`, error);
        
        const errorResponse = "I'm sorry, I encountered an error. Please try again.";
        
        await saveMessageToSession(sessionId, workflowId, {
          content: errorResponse,
          sender: 'bot',
          timestamp: new Date(),
          error: true
        });

        return NextResponse.json({
          success: false,
          response: errorResponse,
          sessionId,
          error: error instanceof Error ? error.message : 'Failed to queue execution',
          timestamp: new Date().toISOString()
        });
      }
    }

  } catch (error) {
    console.error('Chat message API error:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// Helper functions
function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for');
  const realIP = request.headers.get('x-real-ip');
  
  if (forwarded) {
    return forwarded.split(',')[0].trim();
  }
  
  if (realIP) {
    return realIP;
  }
  
  return 'unknown';
}

async function checkRateLimit(ip: string, workflowId: string, config: any): Promise<{ allowed: boolean }> {
  // Basic rate limiting implementation
  // In production, use Redis for distributed rate limiting
  const limit = config.ipLimit || 100;
  const timeframe = config.ipLimitTimeframe || 3600; // 1 hour
  
  // For now, allow all requests - implement Redis-based rate limiting later
  return { allowed: true };
}

function validateMessageTrigger(message: string, config: any): boolean {
  const triggerType = config.trigger_type || 'any';
  
  switch (triggerType) {
    case 'keyword':
      const keywords = config.keywords || [];
      const caseSensitive = config.case_sensitive || false;
      const exactMatch = config.exact_match || false;
      
      return keywords.some((keyword: string) => {
        const messageText = caseSensitive ? message : message.toLowerCase();
        const keywordText = caseSensitive ? keyword : keyword.toLowerCase();
        
        if (exactMatch) {
          return messageText === keywordText;
        } else {
          return messageText.includes(keywordText);
        }
      });
      
    case 'regex':
      try {
        const regex = new RegExp(config.regex_pattern || '.*');
        return regex.test(message);
      } catch {
        return false;
      }
      
    case 'any':
    default:
      return true;
  }
}

async function processUploadedFiles(files: File[]): Promise<any[]> {
  const fileData = [];
  
  for (const file of files) {
    // Convert file to base64 or save to storage
    const buffer = await file.arrayBuffer();
    const base64 = Buffer.from(buffer).toString('base64');
    
    fileData.push({
      name: file.name,
      type: file.type,
      size: file.size,
      data: base64 // In production, save to cloud storage and store URL
    });
  }
  
  return fileData;
}

async function getSessionMessages(sessionId: string, workflowId: string): Promise<any[]> {
  try {
    const { db } = await connectToDatabase();
    const session = await db.collection('chat_sessions').findOne({
      sessionId,
      workflowId
    });
    
    return session?.messages || [];
  } catch (error) {
    console.error('Error getting session messages:', error);
    return [];
  }
}

async function saveMessageToSession(sessionId: string, workflowId: string, message: any): Promise<void> {
  try {
    const { db } = await connectToDatabase();
    
    await db.collection('chat_sessions').updateOne(
      { sessionId, workflowId },
      {
        $push: { messages: message },
        $set: { 
          lastActivity: new Date(),
          workflowId 
        },
        $setOnInsert: { 
          sessionId,
          createdAt: new Date() 
        }
      },
      { upsert: true }
    );
  } catch (error) {
    console.error('Error saving message to session:', error);
  }
}

function extractChatResponse(result: any): string {
  // Extract the final response from workflow execution result
  if (result.nodeResults) {
    const nodeResults = Object.values(result.nodeResults);
    const lastResult = nodeResults[nodeResults.length - 1] as any;
    
    if (lastResult && typeof lastResult.result === 'string') {
      return lastResult.result;
    }
    
    if (lastResult && lastResult.output) {
      return lastResult.output.toString();
    }
  }
  
  return "I've processed your message successfully.";
}
