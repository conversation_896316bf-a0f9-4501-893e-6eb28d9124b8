import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';

// Submit feedback for chat messages
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ workflowId: string }> }
) {
  try {
    const { workflowId } = await params;
    const { messageId, feedback, sessionId, comment } = await request.json();

    console.log(`👍 Feedback received for workflow: ${workflowId}`);
    console.log(`📋 Message ID: ${messageId}`);
    console.log(`💬 Feedback: ${feedback}`);
    console.log(`📝 Comment: ${comment || 'None'}`);

    const { db } = await connectToDatabase();

    // Save feedback to database
    const feedbackRecord = {
      workflowId,
      sessionId,
      messageId,
      feedback, // 'positive' or 'negative'
      comment: comment || null,
      timestamp: new Date(),
      ip: getClientIP(request)
    };

    await db.collection('chat_feedback').insertOne(feedbackRecord);

    // Update the message in the session with feedback
    await db.collection('chat_sessions').updateOne(
      { 
        sessionId, 
        workflowId,
        'messages.id': messageId 
      },
      { 
        $set: { 
          'messages.$.feedback': feedback,
          'messages.$.feedbackComment': comment,
          'messages.$.feedbackTimestamp': new Date()
        }
      }
    );

    console.log(`✅ Feedback saved successfully`);

    return NextResponse.json({
      success: true,
      message: 'Feedback saved successfully',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error saving feedback:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// Get feedback analytics for a workflow
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ workflowId: string }> }
) {
  try {
    const { workflowId } = await params;
    const { searchParams } = new URL(request.url);
    const days = parseInt(searchParams.get('days') || '30');

    console.log(`📊 Getting feedback analytics for workflow: ${workflowId}`);

    const { db } = await connectToDatabase();

    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    // Aggregate feedback data
    const feedbackStats = await db.collection('chat_feedback').aggregate([
      {
        $match: {
          workflowId,
          timestamp: { $gte: startDate }
        }
      },
      {
        $group: {
          _id: '$feedback',
          count: { $sum: 1 },
          comments: { 
            $push: {
              $cond: [
                { $ne: ['$comment', null] },
                {
                  comment: '$comment',
                  timestamp: '$timestamp',
                  sessionId: '$sessionId'
                },
                '$$REMOVE'
              ]
            }
          }
        }
      }
    ]).toArray();

    // Calculate total feedback and percentages
    const totalFeedback = feedbackStats.reduce((sum, stat) => sum + stat.count, 0);
    
    const analytics = {
      totalFeedback,
      period: `${days} days`,
      breakdown: feedbackStats.map(stat => ({
        type: stat._id,
        count: stat.count,
        percentage: totalFeedback > 0 ? Math.round((stat.count / totalFeedback) * 100) : 0,
        recentComments: stat.comments.slice(-5) // Last 5 comments
      })),
      generatedAt: new Date().toISOString()
    };

    return NextResponse.json(analytics);

  } catch (error) {
    console.error('Error getting feedback analytics:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for');
  const realIP = request.headers.get('x-real-ip');
  
  if (forwarded) {
    return forwarded.split(',')[0].trim();
  }
  
  if (realIP) {
    return realIP;
  }
  
  return 'unknown';
}
