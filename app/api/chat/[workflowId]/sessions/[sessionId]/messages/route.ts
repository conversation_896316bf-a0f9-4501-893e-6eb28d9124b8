import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';

// Get session messages
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ workflowId: string; sessionId: string }> }
) {
  try {
    const { workflowId, sessionId } = await params;

    console.log(`📋 Getting messages for session: ${sessionId} in workflow: ${workflowId}`);

    const { db } = await connectToDatabase();
    
    const session = await db.collection('chat_sessions').findOne({
      sessionId,
      workflowId
    });

    if (!session) {
      return NextResponse.json([]);
    }

    // Convert messages to the expected format
    const messages = (session.messages || []).map((msg: any) => ({
      id: msg._id || `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      content: msg.content,
      sender: msg.sender,
      timestamp: msg.timestamp,
      type: msg.type || 'text',
      fileUrl: msg.fileUrl,
      fileName: msg.fileName,
      feedback: msg.feedback
    }));

    return NextResponse.json(messages);

  } catch (error) {
    console.error('Error getting session messages:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// Clear session messages
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ workflowId: string; sessionId: string }> }
) {
  try {
    const { workflowId, sessionId } = await params;

    console.log(`🗑️ Clearing messages for session: ${sessionId} in workflow: ${workflowId}`);

    const { db } = await connectToDatabase();
    
    await db.collection('chat_sessions').updateOne(
      { sessionId, workflowId },
      { 
        $set: { 
          messages: [],
          lastActivity: new Date()
        }
      }
    );

    return NextResponse.json({ success: true });

  } catch (error) {
    console.error('Error clearing session messages:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
