import { NextResponse } from "next/server";
import { FAQModel } from "@/models";
import dbConnect from "@/lib/db/db";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth/auth";
import { checkIfAdmin } from "@/lib/auth/checkAdmin";
import { redis } from "@/lib/redis/redis";
import { CACHE_TTL, FAQ_CACHE_KEY } from "@/config/redis";

export async function GET() {
  try {
    await dbConnect();

    // Try to get cached FAQs first
    const cachedFAQs = await redis.get(FAQ_CACHE_KEY);
    if (cachedFAQs) {
      return NextResponse.json(JSON.parse(cachedFAQs));
    }

    // If not in cache, fetch from database
    const faqs = await FAQModel.find({ isActive: true }).sort({
      createdAt: -1,
    });

    // Cache the result
    await redis.set(
      FAQ_CACHE_KEY,
      JSON.stringify(faqs),
      "EX", // Set expire
      CACHE_TTL
    );

    return NextResponse.json(faqs);
  } catch (error) {
    console.error("Error fetching FAQs:", error);
    return NextResponse.json(
      { error: "Failed to fetch FAQs" },
      { status: 500 }
    );
  }
}

export async function POST(req: Request) {
  const session = await getServerSession(authOptions);
  const userId = session?.user?.id;
  if (!userId) {
    return NextResponse.json(
      { success: false, error: "Unauthorized" },
      { status: 401 }
    );
  }
  await dbConnect();

  // Verify admin status
  const user = await checkIfAdmin(userId);
  if (!user) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const body = await req.json();
    const newFAQ = await FAQModel.create(body);

    // Invalidate the FAQs cache when a new FAQ is added
    await redis.del(FAQ_CACHE_KEY);

    return NextResponse.json(newFAQ, { status: 201 });
  } catch (error) {
    console.error("Error creating FAQ:", error);
    return NextResponse.json(
      { error: "Failed to create FAQ" },
      { status: 500 }
    );
  }
}