import { NextResponse } from "next/server";
import { FAQModel } from "@/models";
import dbConnect from "@/lib/db/db";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth/auth";
import { checkIfAdmin } from "@/lib/auth/checkAdmin";
import { FAQ_CACHE_KEY } from "@/config/redis";
import { redis } from "@/lib/redis/redis";

interface Props {
  params: Promise<{
    id: string;
  }>;
}

export async function PATCH(req: Request, props: Props) {
  const session = await getServerSession(authOptions);
  const params = await props.params;
  const userId = session?.user?.id;

  if (!userId) {
    return NextResponse.json(
      { success: false, error: "Unauthorized" },
      { status: 401 }
    );
  }

  await dbConnect();

  // Verify admin status
  const user = await checkIfAdmin(userId);
  if (!user) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const body = await req.json();
    const updatedFAQ = await FAQModel.findByIdAndUpdate(params.id, body, {
      new: true,
    });

    if (!updatedFAQ) {
      return NextResponse.json({ error: "FAQ not found" }, { status: 404 });
    }
    await redis.del(FAQ_CACHE_KEY);
    return NextResponse.json(updatedFAQ);
  } catch (error) {
    console.error("Error updating FAQ:", error);
    return NextResponse.json(
      { error: "Failed to update FAQ" },
      { status: 500 }
    );
  }
}

export async function DELETE(req: Request, props: Props) {
  const session = await getServerSession(authOptions);
  const params = await props.params;
  const userId = session?.user?.id;

  if (!userId) {
    return NextResponse.json(
      { success: false, error: "Unauthorized" },
      { status: 401 }
    );
  }

  await dbConnect();

  // Verify admin status
  const user = await checkIfAdmin(userId);
  if (!user) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const deletedFAQ = await FAQModel.findByIdAndDelete(params.id);

    if (!deletedFAQ) {
      return NextResponse.json({ error: "FAQ not found" }, { status: 404 });
    }

    await redis.del(FAQ_CACHE_KEY);
    return NextResponse.json({
      success: true,
      message: "FAQ deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting FAQ:", error);
    return NextResponse.json(
      { error: "Failed to delete FAQ" },
      { status: 500 }
    );
  }
}
