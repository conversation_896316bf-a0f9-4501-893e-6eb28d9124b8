import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import { getDirectExecutor } from '@/lib/execution/direct-executor';
import { getQueueManager } from '@/lib/execution/queue-manager';
import * as cron from 'node-cron';

// Cron job endpoint to check and execute scheduled workflows
export async function GET(request: NextRequest) {
  try {
    console.log('⏰ Running scheduled workflow check...');

    const { db } = await connectToDatabase();
    const currentTime = new Date();

    // Find all workflows with schedule triggers
    const workflows = await db.collection('workflows').find({
      'nodes.type': 'schedule',
      status: 'active'
    }).toArray();

    console.log(`📋 Found ${workflows.length} workflows with schedule triggers`);

    const executionResults = [];

    for (const workflow of workflows) {
      try {
        const scheduleNode = workflow.nodes.find((node: any) => node.type === 'schedule');
        if (!scheduleNode) continue;

        const config = scheduleNode.data?.config || {};
        
        // Skip if schedule is disabled
        if (config.enabled === false) {
          console.log(`⏭️ Skipping disabled schedule for workflow: ${workflow.name}`);
          continue;
        }

        // Check if schedule should execute now
        const shouldExecute = await shouldExecuteSchedule(config, currentTime, workflow._id.toString());
        
        if (shouldExecute) {
          console.log(`🚀 Executing scheduled workflow: ${workflow.name}`);
          
          const executionId = `schedule_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
          
          // Prepare schedule input data
          const scheduleInput = {
            schedule: {
              type: config.schedule_type || 'cron',
              expression: config.cron_expression || config.interval_value,
              timezone: config.timezone || 'UTC',
              timestamp: currentTime.toISOString(),
              executionCount: await getExecutionCount(workflow._id.toString())
            }
          };

          // Prepare initial variables
          const initialVariables = {
            scheduled_time: currentTime.toISOString(),
            schedule_type: config.schedule_type || 'cron',
            schedule_expression: config.cron_expression || config.interval_value,
            triggered_by: 'schedule',
            trigger_timestamp: currentTime.toISOString()
          };

          // Execute based on workflow execution mode
          const executionMode = workflow.executionMode || 'async';
          
          if (executionMode === 'direct') {
            // Direct execution
            try {
              const directExecutor = getDirectExecutor();
              const result = await directExecutor.executeWorkflow(
                workflow._id.toString(),
                executionId,
                'schedule_system', // Special user ID for scheduled executions
                { ...scheduleInput, ...initialVariables }
              );

              executionResults.push({
                workflowId: workflow._id.toString(),
                workflowName: workflow.name,
                executionId,
                mode: 'direct',
                status: 'completed',
                result: result.status
              });

              console.log(`✅ Direct scheduled execution completed: ${workflow.name}`);

            } catch (error) {
              console.error(`❌ Direct scheduled execution failed: ${workflow.name}`, error);
              executionResults.push({
                workflowId: workflow._id.toString(),
                workflowName: workflow.name,
                executionId,
                mode: 'direct',
                status: 'failed',
                error: error instanceof Error ? error.message : 'Unknown error'
              });
            }

          } else {
            // Async execution via queue
            try {
              const queueManager = await getQueueManager();
              const job = await queueManager.queueWorkflowExecution(
                workflow._id.toString(),
                executionId,
                'schedule_system',
                { ...scheduleInput, ...initialVariables },
                'async'
              );

              executionResults.push({
                workflowId: workflow._id.toString(),
                workflowName: workflow.name,
                executionId,
                jobId: job.id,
                mode: 'async',
                status: 'queued'
              });

              console.log(`✅ Async scheduled execution queued: ${workflow.name}`);

            } catch (error) {
              console.error(`❌ Async scheduled execution failed: ${workflow.name}`, error);
              executionResults.push({
                workflowId: workflow._id.toString(),
                workflowName: workflow.name,
                executionId,
                mode: 'async',
                status: 'failed',
                error: error instanceof Error ? error.message : 'Unknown error'
              });
            }
          }

          // Update last execution time
          await updateLastExecution(workflow._id.toString(), currentTime);

          // Check max executions limit
          if (config.max_executions && config.max_executions > 0) {
            const executionCount = await getExecutionCount(workflow._id.toString());
            if (executionCount >= config.max_executions) {
              console.log(`🛑 Max executions reached for workflow: ${workflow.name}`);
              await disableSchedule(workflow._id.toString());
            }
          }

          // Check end date
          if (config.end_date && new Date(config.end_date) <= currentTime) {
            console.log(`🛑 End date reached for workflow: ${workflow.name}`);
            await disableSchedule(workflow._id.toString());
          }
        }

      } catch (error) {
        console.error(`❌ Error processing scheduled workflow: ${workflow.name}`, error);
        executionResults.push({
          workflowId: workflow._id.toString(),
          workflowName: workflow.name,
          status: 'error',
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    console.log(`✅ Schedule check completed. Executed ${executionResults.length} workflows.`);

    return NextResponse.json({
      success: true,
      timestamp: currentTime.toISOString(),
      workflowsChecked: workflows.length,
      executionsTriggered: executionResults.length,
      results: executionResults
    });

  } catch (error) {
    console.error('Schedule check API error:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// Helper functions
async function shouldExecuteSchedule(config: any, currentTime: Date, workflowId: string): Promise<boolean> {
  const scheduleType = config.schedule_type || 'cron';
  
  // Check start date
  if (config.start_date && new Date(config.start_date) > currentTime) {
    return false;
  }

  // Check end date
  if (config.end_date && new Date(config.end_date) <= currentTime) {
    return false;
  }

  // Get last execution time
  const lastExecution = await getLastExecution(workflowId);
  
  switch (scheduleType) {
    case 'cron':
      return shouldExecuteCron(config.cron_expression || '0 9 * * *', currentTime, lastExecution);
      
    case 'interval':
      return shouldExecuteInterval(config.interval_value || 60, config.interval_unit || 'minutes', currentTime, lastExecution);
      
    case 'once':
      const executeAt = new Date(config.execute_at);
      const timeDiff = Math.abs(currentTime.getTime() - executeAt.getTime());
      return timeDiff < 60000 && !lastExecution; // Within 1 minute and never executed
      
    default:
      return false;
  }
}

function shouldExecuteCron(expression: string, currentTime: Date, lastExecution: Date | null): boolean {
  try {
    // Simple cron validation - in production, use a proper cron library
    const isValid = cron.validate(expression);
    if (!isValid) return false;

    // Check if we should execute based on cron expression
    // This is a simplified check - use a proper cron library for production
    const task = cron.schedule(expression, () => {}, { scheduled: false });
    
    // If no last execution, check if current time matches cron
    if (!lastExecution) {
      return true; // Simplified - implement proper cron matching
    }

    // Check if enough time has passed since last execution
    const timeSinceLastExecution = currentTime.getTime() - lastExecution.getTime();
    return timeSinceLastExecution >= 60000; // At least 1 minute

  } catch (error) {
    console.error('Error validating cron expression:', error);
    return false;
  }
}

function shouldExecuteInterval(value: number, unit: string, currentTime: Date, lastExecution: Date | null): boolean {
  if (!lastExecution) return true;

  const intervalMs = convertToMilliseconds(value, unit);
  const timeSinceLastExecution = currentTime.getTime() - lastExecution.getTime();
  
  return timeSinceLastExecution >= intervalMs;
}

function convertToMilliseconds(value: number, unit: string): number {
  switch (unit) {
    case 'seconds': return value * 1000;
    case 'minutes': return value * 60 * 1000;
    case 'hours': return value * 60 * 60 * 1000;
    case 'days': return value * 24 * 60 * 60 * 1000;
    default: return value * 60 * 1000; // Default to minutes
  }
}

async function getLastExecution(workflowId: string): Promise<Date | null> {
  try {
    const { db } = await connectToDatabase();
    const schedule = await db.collection('workflow_schedules').findOne({ workflowId });
    return schedule?.lastExecution || null;
  } catch (error) {
    console.error('Error getting last execution:', error);
    return null;
  }
}

async function updateLastExecution(workflowId: string, executionTime: Date): Promise<void> {
  try {
    const { db } = await connectToDatabase();
    await db.collection('workflow_schedules').updateOne(
      { workflowId },
      { 
        $set: { lastExecution: executionTime },
        $inc: { executionCount: 1 },
        $setOnInsert: { workflowId, createdAt: new Date() }
      },
      { upsert: true }
    );
  } catch (error) {
    console.error('Error updating last execution:', error);
  }
}

async function getExecutionCount(workflowId: string): Promise<number> {
  try {
    const { db } = await connectToDatabase();
    const schedule = await db.collection('workflow_schedules').findOne({ workflowId });
    return schedule?.executionCount || 0;
  } catch (error) {
    console.error('Error getting execution count:', error);
    return 0;
  }
}

async function disableSchedule(workflowId: string): Promise<void> {
  try {
    const { db } = await connectToDatabase();
    await db.collection('workflows').updateOne(
      { _id: workflowId, 'nodes.type': 'schedule' },
      { $set: { 'nodes.$.data.config.enabled': false } }
    );
    console.log(`🛑 Schedule disabled for workflow: ${workflowId}`);
  } catch (error) {
    console.error('Error disabling schedule:', error);
  }
}
