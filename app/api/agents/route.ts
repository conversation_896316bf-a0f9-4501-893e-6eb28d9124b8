import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth/auth";
import dbConnect from "@/lib/db/db";
import { AgentModel, TeamModel, MemberModel } from "@/models";
import { redis } from "@/lib/redis/redis";

const CACHE_TTL = 300; // 5 minutes
const AGENTS_CACHE_PREFIX = "agents:team:";

interface AgentApiResponse {
  success: boolean;
  data?: any;
  error?: string;
  pagination?: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

interface CreateAgentDto {
  name: string;
  description?: string;
  model: string;
  temperature: number;
  systemPrompt: string;
  tools: string[];
  capabilities?: string[];
  maxTokens?: number;
  topP?: number;
  frequencyPenalty?: number;
  presencePenalty?: number;
  teamId: string;
}

// GET /api/agents - List agents for user's teams
export async function GET(request: Request) {
  const session = await getServerSession(authOptions);

  if (!session?.user?.id) {
    return NextResponse.json<AgentApiResponse>(
      { success: false, error: "Unauthorized" },
      { status: 401 }
    );
  }

  try {
    await dbConnect();
    const url = new URL(request.url);
    const teamId = url.searchParams.get("teamId");
    const page = parseInt(url.searchParams.get("page") || "1");
    const limit = Math.min(parseInt(url.searchParams.get("limit") || "10"), 50);
    const search = url.searchParams.get("search") || "";

    const userId = session.user.id;

    // If teamId is provided, check if user has access to that team
    if (teamId) {
      const cacheKey = `${AGENTS_CACHE_PREFIX}${teamId}:user:${userId}:page:${page}:limit:${limit}:search:${search}`;
      
      // Try to get cached result
      const cachedResult = await redis.get(cacheKey);
      if (cachedResult) {
        return NextResponse.json<AgentApiResponse>(JSON.parse(cachedResult));
      }

      // Check if user has access to the team
      const team = await TeamModel.findById(teamId);
      if (!team) {
        return NextResponse.json<AgentApiResponse>(
          { success: false, error: "Team not found" },
          { status: 404 }
        );
      }

      // Check if user is owner or member
      const isOwner = team.ownerId.toString() === userId;
      const isMember = !isOwner && await MemberModel.findOne({
        teamId: teamId,
        userId: userId,
        status: "accepted"
      });

      if (!isOwner && !isMember) {
        return NextResponse.json<AgentApiResponse>(
          { success: false, error: "Access denied to this team" },
          { status: 403 }
        );
      }

      // Build query for agents in this team
      const query: any = { teamId: teamId, isActive: true };
      
      if (search) {
        query.$or = [
          { name: { $regex: search, $options: "i" } },
          { description: { $regex: search, $options: "i" } }
        ];
      }

      const skip = (page - 1) * limit;
      const [agents, total] = await Promise.all([
        AgentModel.find(query)
          .sort({ createdAt: -1 })
          .skip(skip)
          .limit(limit)
          .populate("userId", "name email")
          .lean(),
        AgentModel.countDocuments(query)
      ]);

      const response = {
        success: true,
        data: agents.map(agent => ({
          ...agent,
          _id: agent._id.toString(),
          userId: agent.userId.toString(),
          teamId: agent.teamId.toString(),
        })),
        pagination: {
          total,
          page,
          limit,
          totalPages: Math.ceil(total / limit),
        },
      };

      // Cache the result
      await redis.set(
        cacheKey,
        JSON.stringify(response),
        "EX",
        CACHE_TTL
      );

      return NextResponse.json<AgentApiResponse>(response);
    }

    // If no teamId provided, get agents from all user's teams
    const userTeams = await TeamModel.find({ ownerId: userId }).select("_id");
    const memberTeams = await MemberModel.find({ 
      userId: userId, 
      status: "accepted" 
    }).select("teamId");

    const allTeamIds = [
      ...userTeams.map(team => team._id),
      ...memberTeams.map(member => member.teamId)
    ];

    if (allTeamIds.length === 0) {
      return NextResponse.json<AgentApiResponse>({
        success: true,
        data: [],
        pagination: { total: 0, page, limit, totalPages: 0 }
      });
    }

    const query: any = { 
      teamId: { $in: allTeamIds }, 
      isActive: true 
    };

    if (search) {
      query.$or = [
        { name: { $regex: search, $options: "i" } },
        { description: { $regex: search, $options: "i" } }
      ];
    }

    const skip = (page - 1) * limit;
    const [agents, total] = await Promise.all([
      AgentModel.find(query)
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .populate("userId", "name email")
        .populate("teamId", "name url")
        .lean(),
      AgentModel.countDocuments(query)
    ]);

    const response = {
      success: true,
      data: agents.map(agent => ({
        ...agent,
        _id: agent._id.toString(),
        userId: agent.userId.toString(),
        teamId: agent.teamId.toString(),
      })),
      pagination: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    };

    return NextResponse.json<AgentApiResponse>(response);

  } catch (error) {
    console.error("Error fetching agents:", error);
    return NextResponse.json<AgentApiResponse>(
      { success: false, error: "Failed to fetch agents" },
      { status: 500 }
    );
  }
}

// POST /api/agents - Create new agent
export async function POST(request: Request) {
  const session = await getServerSession(authOptions);

  if (!session?.user?.id) {
    return NextResponse.json<AgentApiResponse>(
      { success: false, error: "Unauthorized" },
      { status: 401 }
    );
  }

  try {
    await dbConnect();
    const userId = session.user.id;
    const body: CreateAgentDto = await request.json();

    // Validate required fields
    if (!body.name || !body.model || !body.systemPrompt || !body.teamId) {
      return NextResponse.json<AgentApiResponse>(
        { success: false, error: "Missing required fields: name, model, systemPrompt, teamId" },
        { status: 400 }
      );
    }

    // Check if user has access to the team
    const team = await TeamModel.findById(body.teamId);
    if (!team) {
      return NextResponse.json<AgentApiResponse>(
        { success: false, error: "Team not found" },
        { status: 404 }
      );
    }

    // Check if user is owner or member
    const isOwner = team.ownerId.toString() === userId;
    const isMember = !isOwner && await MemberModel.findOne({
      teamId: body.teamId,
      userId: userId,
      status: "accepted"
    });

    if (!isOwner && !isMember) {
      return NextResponse.json<AgentApiResponse>(
        { success: false, error: "Access denied to this team" },
        { status: 403 }
      );
    }

    // Create the agent
    const newAgent = await AgentModel.create({
      ...body,
      userId: userId,
      teamId: body.teamId,
      tools: body.tools || [],
      capabilities: body.capabilities || [],
      temperature: body.temperature || 0.7,
      maxTokens: body.maxTokens || 4000,
      topP: body.topP || 1,
      frequencyPenalty: body.frequencyPenalty || 0,
      presencePenalty: body.presencePenalty || 0,
    });

    // Invalidate cache for this team
    const cachePattern = `${AGENTS_CACHE_PREFIX}${body.teamId}:*`;
    const keys = await redis.keys(cachePattern);
    if (keys.length > 0) {
      await redis.del(...keys);
    }

    const response = {
      success: true,
      data: {
        ...newAgent.toObject(),
        _id: newAgent._id.toString(),
        userId: newAgent.userId.toString(),
        teamId: newAgent.teamId.toString(),
      }
    };

    return NextResponse.json<AgentApiResponse>(response, { status: 201 });

  } catch (error) {
    console.error("Error creating agent:", error);
    return NextResponse.json<AgentApiResponse>(
      { success: false, error: "Failed to create agent" },
      { status: 500 }
    );
  }
}
