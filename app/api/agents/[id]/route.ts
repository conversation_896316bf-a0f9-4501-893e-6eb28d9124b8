import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth/auth";
import dbConnect from "@/lib/db/db";
import { AgentModel, TeamModel, MemberModel } from "@/models";
import { redis } from "@/lib/redis/redis";
import mongoose from "mongoose";

const CACHE_TTL = 300; // 5 minutes
const AGENT_CACHE_PREFIX = "agent:";
const AGENTS_CACHE_PREFIX = "agents:team:";

interface AgentApiResponse {
  success: boolean;
  data?: any;
  error?: string;
}

interface UpdateAgentDto {
  name?: string;
  description?: string;
  model?: string;
  temperature?: number;
  systemPrompt?: string;
  tools?: string[];
  capabilities?: string[];
  maxTokens?: number;
  topP?: number;
  frequencyPenalty?: number;
  presencePenalty?: number;
  isActive?: boolean;
}

interface Props {
  params: Promise<{
    id: string;
  }>;
}

// GET /api/agents/[id] - Get specific agent
export async function GET(request: Request, props: Props) {
  const session = await getServerSession(authOptions);

  if (!session?.user?.id) {
    return NextResponse.json<AgentApiResponse>(
      { success: false, error: "Unauthorized" },
      { status: 401 }
    );
  }

  try {
    await dbConnect();
    const params = await props.params;
    const agentId = params.id;
    const userId = session.user.id;

    // Validate MongoDB ObjectId
    if (!mongoose.Types.ObjectId.isValid(agentId)) {
      return NextResponse.json<AgentApiResponse>(
        { success: false, error: "Invalid agent ID" },
        { status: 400 }
      );
    }

    const cacheKey = `${AGENT_CACHE_PREFIX}${agentId}:user:${userId}`;
    
    // Try to get cached result
    const cachedResult = await redis.get(cacheKey);
    if (cachedResult) {
      return NextResponse.json<AgentApiResponse>(JSON.parse(cachedResult));
    }

    // Find the agent
    const agent = await AgentModel.findById(agentId)
      .populate("userId", "name email")
      .populate("teamId", "name url")
      .lean();

    if (!agent) {
      return NextResponse.json<AgentApiResponse>(
        { success: false, error: "Agent not found" },
        { status: 404 }
      );
    }

    // Check if user has access to this agent's team
    const team = await TeamModel.findById(agent.teamId);
    if (!team) {
      return NextResponse.json<AgentApiResponse>(
        { success: false, error: "Team not found" },
        { status: 404 }
      );
    }

    // Check if user is owner or member
    const isOwner = team.ownerId.toString() === userId;
    const isMember = !isOwner && await MemberModel.findOne({
      teamId: agent.teamId,
      userId: userId,
      status: "accepted"
    });

    if (!isOwner && !isMember) {
      return NextResponse.json<AgentApiResponse>(
        { success: false, error: "Access denied to this agent" },
        { status: 403 }
      );
    }

    const response = {
      success: true,
      data: {
        ...agent,
        _id: agent._id.toString(),
        userId: agent.userId.toString(),
        teamId: agent.teamId.toString(),
      }
    };

    // Cache the result
    await redis.set(
      cacheKey,
      JSON.stringify(response),
      "EX",
      CACHE_TTL
    );

    return NextResponse.json<AgentApiResponse>(response);

  } catch (error) {
    console.error("Error fetching agent:", error);
    return NextResponse.json<AgentApiResponse>(
      { success: false, error: "Failed to fetch agent" },
      { status: 500 }
    );
  }
}

// PUT /api/agents/[id] - Update agent
export async function PUT(request: Request, props: Props) {
  const session = await getServerSession(authOptions);

  if (!session?.user?.id) {
    return NextResponse.json<AgentApiResponse>(
      { success: false, error: "Unauthorized" },
      { status: 401 }
    );
  }

  try {
    await dbConnect();
    const params = await props.params;
    const agentId = params.id;
    const userId = session.user.id;
    const body: UpdateAgentDto = await request.json();



    // Validate MongoDB ObjectId
    if (!mongoose.Types.ObjectId.isValid(agentId)) {
      return NextResponse.json<AgentApiResponse>(
        { success: false, error: "Invalid agent ID" },
        { status: 400 }
      );
    }

    // Find the agent
    const agent = await AgentModel.findById(agentId);
    if (!agent) {
      return NextResponse.json<AgentApiResponse>(
        { success: false, error: "Agent not found" },
        { status: 404 }
      );
    }

    // Check if user has access to this agent's team
    const team = await TeamModel.findById(agent.teamId);
    if (!team) {
      return NextResponse.json<AgentApiResponse>(
        { success: false, error: "Team not found" },
        { status: 404 }
      );
    }

    // Check if user is owner or member
    const isOwner = team.ownerId.toString() === userId;
    const isMember = !isOwner && await MemberModel.findOne({
      teamId: agent.teamId,
      userId: userId,
      status: "accepted"
    });

    if (!isOwner && !isMember) {
      return NextResponse.json<AgentApiResponse>(
        { success: false, error: "Access denied to this agent" },
        { status: 403 }
      );
    }

    // Filter out fields that shouldn't be updated
    const allowedFields = [
      'name', 'description', 'model', 'temperature', 'systemPrompt', 'tools',
      'capabilities', 'maxTokens', 'topP', 'frequencyPenalty', 'presencePenalty',
      'language', 'initialMessages', 'suggestedMessages', 'messagePlaceholder',
      'footer', 'theme', 'isPublic', 'isActive', 'isTrained', 'allowedDomains',
      'ipLimit', 'ipLimitTimeframe', 'ipLimitMessage', 'displayName', 'iconPath',
      'profilePicturePath', 'leadTitle', 'leadTitleEnabled', 'leadEmail',
      'leadEmailEnabled', 'leadName', 'leadNameEnabled', 'leadPhone',
      'leadPhoneEnabled', 'leadMessage', 'leadMessageEnabled',
      'leadsNotificationEmail', 'leadsNotificationEmailEnabled',
      'conversationsNotificationEmail', 'conversationsNotificationEmailEnabled',
      'customDomains', 'customDomainsEnabled', 'webhooks', 'webhooksEnabled'
    ];

    const updateData: any = {};
    for (const field of allowedFields) {
      if (body.hasOwnProperty(field)) {
        updateData[field] = (body as any)[field];
      }
    }
    updateData.updatedAt = new Date();

    // Update the agent

    const updatedAgent = await AgentModel.findByIdAndUpdate(
      agentId,
      updateData,
      { new: true, runValidators: true }
    ).populate("userId", "name email")
     .populate("teamId", "name url");



    if (!updatedAgent) {
      return NextResponse.json<AgentApiResponse>(
        { success: false, error: "Agent not found or update failed" },
        { status: 404 }
      );
    }

    // Invalidate caches
    const cacheKeys = [
      `${AGENT_CACHE_PREFIX}${agentId}:user:${userId}`,
      `${AGENTS_CACHE_PREFIX}${agent.teamId}:*`
    ];
    
    // Delete specific cache
    await redis.del(cacheKeys[0]);
    
    // Delete team agents cache pattern
    const teamCacheKeys = await redis.keys(cacheKeys[1]);
    if (teamCacheKeys.length > 0) {
      await redis.del(...teamCacheKeys);
    }

    const response = {
      success: true,
      data: {
        ...updatedAgent.toObject(),
        _id: updatedAgent._id.toString(),
        userId: updatedAgent.userId.toString(),
        teamId: updatedAgent.teamId.toString(),
      }
    };

    return NextResponse.json<AgentApiResponse>(response);

  } catch (error) {
    console.error("Error updating agent:", error);

    // Handle validation errors
    if (error instanceof Error && error.name === 'ValidationError') {
      return NextResponse.json<AgentApiResponse>(
        { success: false, error: `Validation error: ${error.message}` },
        { status: 400 }
      );
    }

    return NextResponse.json<AgentApiResponse>(
      { success: false, error: error instanceof Error ? error.message : "Failed to update agent" },
      { status: 500 }
    );
  }
}

// DELETE /api/agents/[id] - Delete agent
export async function DELETE(request: Request, props: Props) {
  const session = await getServerSession(authOptions);

  if (!session?.user?.id) {
    return NextResponse.json<AgentApiResponse>(
      { success: false, error: "Unauthorized" },
      { status: 401 }
    );
  }

  try {
    await dbConnect();
    const params = await props.params;
    const agentId = params.id;
    const userId = session.user.id;

    // Validate MongoDB ObjectId
    if (!mongoose.Types.ObjectId.isValid(agentId)) {
      return NextResponse.json<AgentApiResponse>(
        { success: false, error: "Invalid agent ID" },
        { status: 400 }
      );
    }

    // Find the agent
    const agent = await AgentModel.findById(agentId);
    if (!agent) {
      return NextResponse.json<AgentApiResponse>(
        { success: false, error: "Agent not found" },
        { status: 404 }
      );
    }

    // Check if user has access to this agent's team
    const team = await TeamModel.findById(agent.teamId);
    if (!team) {
      return NextResponse.json<AgentApiResponse>(
        { success: false, error: "Team not found" },
        { status: 404 }
      );
    }

    // Check if user is owner (only owners can delete agents)
    const isOwner = team.ownerId.toString() === userId;
    if (!isOwner) {
      return NextResponse.json<AgentApiResponse>(
        { success: false, error: "Only team owners can delete agents" },
        { status: 403 }
      );
    }

    // Soft delete the agent (set isActive to false)
    await AgentModel.findByIdAndUpdate(agentId, { 
      isActive: false,
      updatedAt: new Date()
    });

    // Invalidate caches
    const cacheKeys = [
      `${AGENT_CACHE_PREFIX}${agentId}:user:${userId}`,
      `${AGENTS_CACHE_PREFIX}${agent.teamId}:*`
    ];
    
    // Delete specific cache
    await redis.del(cacheKeys[0]);
    
    // Delete team agents cache pattern
    const teamCacheKeys = await redis.keys(cacheKeys[1]);
    if (teamCacheKeys.length > 0) {
      await redis.del(...teamCacheKeys);
    }

    return NextResponse.json<AgentApiResponse>({
      success: true,
      data: { message: "Agent deleted successfully" }
    });

  } catch (error) {
    console.error("Error deleting agent:", error);
    return NextResponse.json<AgentApiResponse>(
      { success: false, error: "Failed to delete agent" },
      { status: 500 }
    );
  }
}
