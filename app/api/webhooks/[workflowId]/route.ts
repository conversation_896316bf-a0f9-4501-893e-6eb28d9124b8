import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import { ObjectId } from 'mongodb';
import { getDirectExecutor } from '@/lib/execution/direct-executor';
import { getQueueManager } from '@/lib/execution/queue-manager';

// Webhook trigger endpoint
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ workflowId: string }> }
) {
  try {
    const { workflowId } = await params;
    const body = await request.json();
    const headers = Object.fromEntries(request.headers.entries());
    const method = request.method;
    const url = request.url;

    console.log(`🔗 Webhook triggered for workflow: ${workflowId}`);
    console.log(`📋 Method: ${method}`);
    console.log(`🔧 Headers:`, headers);
    console.log(`📊 Body:`, body);

    // Get workflow and validate webhook trigger
    const { db } = await connectToDatabase();
    const workflow = await db.collection('workflows').findOne({
      _id: new ObjectId(workflowId)
    });

    if (!workflow) {
      return NextResponse.json(
        { error: 'Workflow not found' },
        { status: 404 }
      );
    }

    // Find webhook trigger in workflow
    const webhookTrigger = workflow.nodes?.find((node: any) => 
      node.type === 'webhook'
    );

    if (!webhookTrigger) {
      return NextResponse.json(
        { error: 'No webhook trigger found in workflow' },
        { status: 400 }
      );
    }

    const config = webhookTrigger.data?.config || {};

    // Validate HTTP method
    const allowedMethod = config.method || 'POST';
    if (method !== allowedMethod) {
      return NextResponse.json(
        { error: `Method ${method} not allowed. Expected ${allowedMethod}` },
        { status: 405 }
      );
    }

    // Validate authentication if required
    if (config.authentication && config.authentication !== 'none') {
      const authResult = validateAuthentication(headers, config);
      if (!authResult.valid) {
        return NextResponse.json(
          { error: authResult.error },
          { status: 401 }
        );
      }
    }

    // Validate request format
    if (config.responseFormat === 'json' && !isValidJSON(body)) {
      return NextResponse.json(
        { error: 'Invalid JSON payload' },
        { status: 400 }
      );
    }

    // Rate limiting (basic IP-based)
    const clientIP = getClientIP(request);
    const rateLimitResult = await checkRateLimit(clientIP, workflowId);
    if (!rateLimitResult.allowed) {
      return NextResponse.json(
        { error: 'Rate limit exceeded' },
        { status: 429 }
      );
    }

    // Generate execution ID
    const executionId = `webhook_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // Prepare webhook input data
    const webhookInput = {
      webhook: {
        method,
        headers,
        body,
        url,
        timestamp: new Date().toISOString(),
        ip: clientIP
      },
      ...body // Merge body data as top-level input
    };

    // Prepare initial variables
    const initialVariables = {
      webhook_payload: body,
      webhook_headers: headers,
      webhook_method: method,
      webhook_ip: clientIP,
      triggered_by: 'webhook',
      trigger_timestamp: new Date().toISOString()
    };

    console.log(`🚀 Starting webhook workflow execution: ${workflow.name}`);
    console.log(`📋 Execution ID: ${executionId}`);
    console.log(`⚙️ Mode: ${workflow.executionMode || 'async'}`);

    // Execute based on workflow execution mode
    const executionMode = workflow.executionMode || 'async';
    
    if (executionMode === 'direct') {
      // Direct/synchronous execution
      try {
        const directExecutor = getDirectExecutor();
        const result = await directExecutor.executeWorkflow(
          workflowId, 
          executionId, 
          'webhook_user', // Special user ID for webhook triggers
          { ...webhookInput, ...initialVariables }
        );

        return NextResponse.json({
          success: true,
          executionId,
          mode: 'direct',
          result: {
            status: result.status,
            output: result.nodeResults,
            executionTime: result.endTime ? 
              new Date(result.endTime).getTime() - new Date(result.startTime).getTime() : 0
          },
          timestamp: new Date().toISOString()
        });

      } catch (error) {
        console.error(`❌ Direct webhook execution failed:`, error);
        return NextResponse.json({
          success: false,
          error: error instanceof Error ? error.message : 'Execution failed',
          executionId,
          timestamp: new Date().toISOString()
        }, { status: 500 });
      }

    } else {
      // Async execution via queue
      try {
        const queueManager = await getQueueManager();
        const job = await queueManager.queueWorkflowExecution(
          workflowId,
          executionId,
          'webhook_user',
          { ...webhookInput, ...initialVariables },
          'async'
        );

        return NextResponse.json({
          success: true,
          executionId,
          jobId: job.id,
          mode: 'async',
          message: 'Workflow execution queued successfully',
          timestamp: new Date().toISOString(),
          statusUrl: `/api/workflows/${workflowId}/executions/${executionId}/status`
        });

      } catch (error) {
        console.error(`❌ Async webhook execution failed:`, error);
        return NextResponse.json({
          success: false,
          error: error instanceof Error ? error.message : 'Failed to queue execution',
          executionId,
          timestamp: new Date().toISOString()
        }, { status: 500 });
      }
    }

  } catch (error) {
    console.error('Webhook API error:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// Also support GET for testing
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ workflowId: string }> }
) {
  const { workflowId } = await params;
  
  return NextResponse.json({
    message: 'Webhook endpoint is active',
    workflowId,
    methods: ['POST'],
    timestamp: new Date().toISOString()
  });
}

// Helper functions
function validateAuthentication(headers: Record<string, string>, config: any) {
  if (config.authentication === 'api_key') {
    const apiKey = headers['x-api-key'] || headers['authorization']?.replace('Bearer ', '');
    if (!apiKey || apiKey !== config.apiKey) {
      return { valid: false, error: 'Invalid API key' };
    }
  }
  
  if (config.authentication === 'bearer') {
    const token = headers['authorization']?.replace('Bearer ', '');
    if (!token || token !== config.bearerToken) {
      return { valid: false, error: 'Invalid bearer token' };
    }
  }
  
  return { valid: true };
}

function isValidJSON(data: any): boolean {
  try {
    if (typeof data === 'object' && data !== null) {
      return true;
    }
    JSON.parse(JSON.stringify(data));
    return true;
  } catch {
    return false;
  }
}

function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for');
  const realIP = request.headers.get('x-real-ip');
  
  if (forwarded) {
    return forwarded.split(',')[0].trim();
  }
  
  if (realIP) {
    return realIP;
  }
  
  return 'unknown';
}

async function checkRateLimit(ip: string, workflowId: string): Promise<{ allowed: boolean }> {
  // Basic rate limiting - can be enhanced with Redis
  // For now, allow all requests
  return { allowed: true };
}
