import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import { ObjectId } from 'mongodb';
import crypto from 'crypto';

// Webhook trigger endpoint
export async function POST(
  request: NextRequest,
  { params }: { params: { workflowId: string; endpoint: string } }
) {
  try {
    const { workflowId, endpoint } = params;
    
    // Parse request data
    const body = await request.json().catch(() => ({}));
    const headers = Object.fromEntries(request.headers.entries());
    const url = new URL(request.url);
    const searchParams = Object.fromEntries(url.searchParams.entries());

    console.log(`Webhook received: ${workflowId}/${endpoint}`);

    // Load workflow to validate webhook configuration
    const { db } = await connectToDatabase();
    const workflow = await db.collection('workflows').findOne({
      _id: new ObjectId(workflowId)
    });

    if (!workflow) {
      return NextResponse.json(
        { error: 'Workflow not found' },
        { status: 404 }
      );
    }

    // Find webhook trigger node
    const webhookNode = workflow.nodes.find((node: any) => 
      node.type === 'webhook' && 
      (node.data.config.path === `/${endpoint}` || node.data.config.path === endpoint)
    );

    if (!webhookNode) {
      return NextResponse.json(
        { error: 'Webhook endpoint not found' },
        { status: 404 }
      );
    }

    // Validate webhook authentication
    const isValid = await validateWebhookAuth(webhookNode.data.config, headers, body);
    if (!isValid) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Validate HTTP method
    const allowedMethod = webhookNode.data.config.method || 'POST';
    if (request.method !== allowedMethod) {
      return NextResponse.json(
        { error: `Method ${request.method} not allowed. Expected ${allowedMethod}` },
        { status: 405 }
      );
    }

    // Create execution ID
    const executionId = `webhook_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // Prepare trigger data
    const triggerData = {
      body,
      headers,
      searchParams,
      endpoint,
      method: request.method,
      timestamp: new Date().toISOString()
    };

    // For now, just save the webhook execution to database (mock execution)
    const mockExecution = {
      workflowId,
      executionId,
      status: 'completed',
      triggerType: 'webhook',
      triggerData,
      startTime: new Date(),
      endTime: new Date(),
      variables: {
        webhook_body: body,
        webhook_headers: headers,
        webhook_params: searchParams
      },
      nodeExecutions: [],
      userId: workflow.userId,
      teamId: workflow.teamId
    };

    await db.collection('workflow_executions').insertOne(mockExecution);

    console.log(`Workflow queued for execution: ${executionId}`);

    // Return success response
    return NextResponse.json({
      success: true,
      executionId,
      message: 'Webhook received and workflow queued for execution',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Webhook processing error:', error);
    
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// Handle other HTTP methods
export async function GET(
  request: NextRequest,
  { params }: { params: { workflowId: string; endpoint: string } }
) {
  return handleWebhookRequest(request, params, 'GET');
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { workflowId: string; endpoint: string } }
) {
  return handleWebhookRequest(request, params, 'PUT');
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: { workflowId: string; endpoint: string } }
) {
  return handleWebhookRequest(request, params, 'PATCH');
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { workflowId: string; endpoint: string } }
) {
  return handleWebhookRequest(request, params, 'DELETE');
}

// Generic webhook handler for different HTTP methods
async function handleWebhookRequest(
  request: NextRequest,
  params: { workflowId: string; endpoint: string },
  method: string
) {
  // For non-POST methods, we still process them the same way
  // but we need to handle body parsing differently
  
  let body = {};
  if (['POST', 'PUT', 'PATCH'].includes(method)) {
    try {
      body = await request.json();
    } catch {
      // If JSON parsing fails, try to get text
      try {
        const text = await request.text();
        body = { data: text };
      } catch {
        body = {};
      }
    }
  }

  // Create a new request object with the parsed body for the POST handler
  const modifiedRequest = new NextRequest(request.url, {
    method: 'POST',
    headers: request.headers,
    body: JSON.stringify(body)
  });

  return POST(modifiedRequest, { params });
}

// Validate webhook authentication
async function validateWebhookAuth(
  config: any,
  headers: Record<string, string>,
  body: any
): Promise<boolean> {
  const authType = config.authentication || 'none';

  switch (authType) {
    case 'none':
      return true;

    case 'api_key':
      const apiKey = config.api_key;
      const headerKey = config.api_key_header || 'x-api-key';
      return headers[headerKey.toLowerCase()] === apiKey;

    case 'bearer_token':
      const bearerToken = config.bearer_token;
      const authHeader = headers.authorization || '';
      return authHeader === `Bearer ${bearerToken}`;

    case 'basic_auth':
      const username = config.username;
      const password = config.password;
      const authHeader2 = headers.authorization || '';
      const expectedAuth = `Basic ${Buffer.from(`${username}:${password}`).toString('base64')}`;
      return authHeader2 === expectedAuth;

    case 'hmac':
      const secret = config.hmac_secret;
      const algorithm = config.hmac_algorithm || 'sha256';
      const headerName = config.hmac_header || 'x-signature';
      
      const signature = headers[headerName.toLowerCase()];
      if (!signature) return false;

      const expectedSignature = crypto
        .createHmac(algorithm, secret)
        .update(JSON.stringify(body))
        .digest('hex');
      
      // Support both 'sha256=...' and raw hex formats
      const cleanSignature = signature.replace(/^(sha256=|sha1=|sha512=)/, '');
      return crypto.timingSafeEqual(
        Buffer.from(cleanSignature, 'hex'),
        Buffer.from(expectedSignature, 'hex')
      );

    default:
      console.warn(`Unknown authentication type: ${authType}`);
      return false;
  }
}

// Health check endpoint
export async function HEAD(
  request: NextRequest,
  { params }: { params: { workflowId: string; endpoint: string } }
) {
  return new NextResponse(null, { status: 200 });
}
