// Integrations API Route

import { NextRequest, NextResponse } from 'next/server';
import { IntegrationRegistry } from '@/lib/integrations/core/integration-registry';

// GET /api/integrations - List available integrations
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const category = searchParams.get('category');
    const search = searchParams.get('search');

    let integrations = IntegrationRegistry.getIntegrationMetadata();

    // Filter by category
    if (category) {
      integrations = integrations.filter(integration => 
        integration.category === category
      );
    }

    // Search by name or description
    if (search) {
      const lowerSearch = search.toLowerCase();
      integrations = integrations.filter(integration =>
        integration.name.toLowerCase().includes(lowerSearch) ||
        integration.description.toLowerCase().includes(lowerSearch)
      );
    }

    return NextResponse.json({
      integrations,
      total: integrations.length,
      categories: ['ai_services', 'business_apps', 'databases', 'communication', 'storage']
    });

  } catch (error) {
    console.error('Failed to list integrations:', error);
    return NextResponse.json(
      { error: 'Failed to list integrations' },
      { status: 500 }
    );
  }
}

// GET /api/integrations/[appType] - Get specific integration details
export async function GET_SPECIFIC(request: NextRequest, { params }: { params: { appType: string } }) {
  try {
    const { appType } = params;
    
    const integration = IntegrationRegistry.getIntegration(appType as any);
    if (!integration) {
      return NextResponse.json(
        { error: 'Integration not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      id: integration.id,
      name: integration.name,
      description: integration.description,
      category: integration.category,
      icon: integration.icon,
      color: integration.color,
      website: integration.website,
      documentation: integration.documentation,
      requiredFields: integration.getRequiredFields(),
      actions: integration.getAvailableActions()
    });

  } catch (error) {
    console.error('Failed to get integration:', error);
    return NextResponse.json(
      { error: 'Failed to get integration' },
      { status: 500 }
    );
  }
}
