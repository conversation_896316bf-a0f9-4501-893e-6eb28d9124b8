"use client";

import { useTheme } from "next-themes";
import { Prism as SyntaxHighlighter } from "react-syntax-highlighter";
import { atomDark, tomorrow } from "react-syntax-highlighter/dist/cjs/styles/prism";

type CodeBlockProps = {
  language?: string;
  children: string;
  className?: string;
  showLineNumbers?: boolean;
  wrapLines?: boolean;
};

export const CodeBlock = ({
  language = "json",
  children,
  className = "",
  showLineNumbers = false,
  wrapLines = true,
}: CodeBlockProps) => {
  const { resolvedTheme } = useTheme();

  return (
    <div className={`relative rounded-lg overflow-hidden ${className}`}>
      <SyntaxHighlighter
        language={language}
        style={resolvedTheme === "dark" ? atomDark : tomorrow}
        customStyle={{
          margin: 0,
          padding: "0.5rem",
          borderRadius: "0.5rem",
          fontSize: "0.875rem",
          lineHeight: "1.5",
          wordBreak: "break-word",
          overflowX: "auto",
          width: "100%",
        }}
        lineNumberStyle={{
          minWidth: "2.25em",
          color: resolvedTheme === "dark" ? "#6b7280" : "#9ca3af",
          paddingRight: "1em",
        }}
        showLineNumbers={showLineNumbers}
        wrapLines={wrapLines}
        PreTag="div"
        codeTagProps={{
          style: {
            display: "block",
            fontFamily: "ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, monospace",
          },
        }}
      >
        {children.trim()}
      </SyntaxHighlighter>
    </div>
  );
};