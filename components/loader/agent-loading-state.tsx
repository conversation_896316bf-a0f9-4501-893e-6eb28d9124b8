"use client";

import { Skeleton } from "@/components/ui/skeleton";
import { motion } from "framer-motion";

export function AgentsLoadingState() {
  return (
    <div className="min-h-screen transition-colors duration-300">
     
      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex flex-col lg:flex-row">
          {/* Main Content Area */}
          <main className="flex-1">
            {/* Title and Button Loading */}
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-8">
              <div className="flex items-center">
                <Skeleton className="h-8 w-32 rounded-full mr-2" />
                <Skeleton className="h-6 w-10 rounded-full" />
              </div>
              <Skeleton className="h-10 w-36 rounded-full" />
            </div>

            {/* agents Grid Loading */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
              {[...Array(9)].map((_, i) => (
                <motion.div
                  key={i}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: i * 0.1 }}
                >
                  <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden h-full flex flex-col">
                    <div className="p-6 flex-grow flex flex-col items-center text-center">
                      <Skeleton className="w-20 h-20 rounded-full mb-4" />
                      <Skeleton className="h-5 w-3/4 mb-2" />
                      <Skeleton className="h-4 w-full mb-1" />
                      <Skeleton className="h-4 w-5/6" />
                    </div>
                    <div className="px-4 py-3 bg-gray-50 dark:bg-gray-700/50 border-t border-gray-200 dark:border-gray-700 flex justify-between items-center">
                      <Skeleton className="h-3 w-24" />
                      <Skeleton className="h-4 w-4 rounded-full" />
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </main>

          {/* Sidebar Loading */}
          <aside className="hidden lg:block lg:w-72 xl:w-80 pl-8">
            <div className="sticky top-20 space-y-6">
              {/* Related Teams Card Loading */}
              <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg border border-gray-200 dark:border-gray-700 p-5">
                <div className="flex items-center justify-between mb-6">
                  <Skeleton className="h-6 w-32" />
                  <Skeleton className="h-4 w-10" />
                </div>
                <div className="space-y-3">
                  {[...Array(3)].map((_, i) => (
                    <div key={i} className="flex items-center p-3 rounded-lg bg-gray-50 dark:bg-gray-700/30">
                      <Skeleton className="w-10 h-10 rounded-full" />
                      <div className="ml-3 flex-1">
                        <Skeleton className="h-4 w-3/4 mb-1" />
                        <Skeleton className="h-3 w-1/2" />
                      </div>
                      <Skeleton className="h-4 w-4" />
                    </div>
                  ))}
                </div>
              </div>

              {/* Team Stats Card Loading */}
              <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg border border-gray-200 dark:border-gray-700 p-5">
                <div className="flex items-center justify-between mb-6">
                  <Skeleton className="h-6 w-24" />
                  <Skeleton className="h-8 w-8 rounded-full" />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="p-3 rounded-lg bg-gray-50 dark:bg-gray-700/30">
                    <Skeleton className="h-4 w-16 mb-2" />
                    <Skeleton className="h-6 w-10 mb-1" />
                    <Skeleton className="h-1.5 w-full" />
                  </div>
                  <div className="p-3 rounded-lg bg-gray-50 dark:bg-gray-700/30">
                    <Skeleton className="h-4 w-16 mb-2" />
                    <Skeleton className="h-6 w-10 mb-1" />
                    <Skeleton className="h-1.5 w-full" />
                  </div>
                </div>
                <div className="mt-6">
                  <Skeleton className="h-3 w-24 mb-2" />
                  <Skeleton className="h-10 w-full rounded-md" />
                </div>
              </div>

              {/* Quick Actions Card Loading */}
              <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg border border-gray-200 dark:border-gray-700 p-5">
                <Skeleton className="h-6 w-32 mb-4" />
                <div className="space-y-3">
                  <Skeleton className="h-10 w-full rounded-lg" />
                  <Skeleton className="h-10 w-full rounded-lg" />
                  <Skeleton className="h-10 w-full rounded-lg" />
                </div>
              </div>
            </div>
          </aside> 
        </div>
      </div>
    </div>
  );
}