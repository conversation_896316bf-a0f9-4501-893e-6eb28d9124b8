"use client";

import { Skeleton } from "@/components/ui/skeleton";
import { motion } from "framer-motion";

export function ChatbotsLoadingState() {
  return (
    <div className="min-h-screen transition-colors duration-300">
     
      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex flex-col lg:flex-row">
          {/* Main Content Area */}
          <main className="flex-1">
            {/* Title and Button Loading */}
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-8">
              <div className="flex items-center">
                <Skeleton className="h-8 w-32 rounded-full mr-2" />
                <Skeleton className="h-6 w-10 rounded-full" />
              </div>
              <Skeleton className="h-10 w-36 rounded-full" />
            </div>

            {/* Chatbots Grid Loading */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
              {[...Array(6)].map((_, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6 shadow-sm"
                >
                  {/* Header */}
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-3">
                      <Skeleton className="h-10 w-10 rounded-full" />
                      <div>
                        <Skeleton className="h-4 w-24 mb-1" />
                        <Skeleton className="h-3 w-16" />
                      </div>
                    </div>
                    <Skeleton className="h-6 w-6 rounded" />
                  </div>

                  {/* Description */}
                  <div className="mb-4">
                    <Skeleton className="h-3 w-full mb-2" />
                    <Skeleton className="h-3 w-3/4" />
                  </div>

                  {/* Stats */}
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center space-x-1">
                        <Skeleton className="h-4 w-4 rounded" />
                        <Skeleton className="h-3 w-8" />
                      </div>
                      <div className="flex items-center space-x-1">
                        <Skeleton className="h-4 w-4 rounded" />
                        <Skeleton className="h-3 w-12" />
                      </div>
                    </div>
                    <Skeleton className="h-5 w-16 rounded-full" />
                  </div>

                  {/* Actions */}
                  <div className="flex items-center justify-between">
                    <Skeleton className="h-8 w-20 rounded" />
                    <div className="flex items-center space-x-2">
                      <Skeleton className="h-8 w-8 rounded" />
                      <Skeleton className="h-8 w-8 rounded" />
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>

            {/* Load More Button Loading */}
            <div className="flex justify-center mt-8">
              <Skeleton className="h-10 w-32 rounded-full" />
            </div>
          </main>

          {/* Sidebar Loading */}
          <aside className="lg:w-80 lg:ml-8 mt-8 lg:mt-0">
            <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6">
              {/* Sidebar Header */}
              <div className="flex items-center justify-between mb-4">
                <Skeleton className="h-5 w-24" />
                <Skeleton className="h-4 w-4 rounded" />
              </div>

              {/* Sidebar Content */}
              <div className="space-y-4">
                {[...Array(4)].map((_, index) => (
                  <div key={index} className="flex items-center space-x-3">
                    <Skeleton className="h-8 w-8 rounded-full" />
                    <div className="flex-1">
                      <Skeleton className="h-3 w-20 mb-1" />
                      <Skeleton className="h-2 w-16" />
                    </div>
                  </div>
                ))}
              </div>

              {/* Sidebar Footer */}
              <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
                <Skeleton className="h-8 w-full rounded" />
              </div>
            </div>
          </aside>
        </div>
      </div>
    </div>
  );
}

// Export as default for backward compatibility
export default ChatbotsLoadingState;
