import React from "react";
import { Skeleton } from "../ui/skeleton";

export default function LoadingState() {
  return (
    <div className="p-6 max-w-7xl mx-auto">
      <div className="flex justify-between items-center mb-8">
        <Skeleton className="h-9 w-48 rounded-lg" />
        <Skeleton className="h-10 w-10 rounded-lg" />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {/* Create Team Skeleton */}
        <div className="rounded-xl border-2 border-dashed border-gray-300 dark:border-gray-700 h-full min-h-[200px] flex flex-col items-center justify-center p-6">
          <Skeleton className="w-14 h-14 rounded-full mb-4" />
          <Skeleton className="h-6 w-32 mb-2" />
          <Skeleton className="h-4 w-40" />
        </div>

        {/* Team Card Skeletons */}
        {[...Array(11)].map((_, i) => (
          <div
            key={i}
            className="rounded-xl bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 overflow-hidden"
          >
            <Skeleton className="w-full h-2" />
            <div className="p-5">
              <div className="flex justify-between mb-3">
                <Skeleton className="h-6 w-32" />
                <Skeleton className="h-5 w-5 rounded-full" />
              </div>
              <Skeleton className="h-4 w-full mb-3" />
              <Skeleton className="h-4 w-3/4 mb-4" />
              <Skeleton className="h-4 w-24" />
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}