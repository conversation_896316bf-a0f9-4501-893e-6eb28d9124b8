"use client";

import { <PERSON>, <PERSON><PERSON>ard, Key, <PERSON><PERSON>, Co<PERSON>, Bo<PERSON> } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { <PERSON>, <PERSON><PERSON><PERSON>er, <PERSON><PERSON>ontent, CardFooter } from "@/components/ui/card";

export function TeamSettingsLoading() {
  const tabs = [
    { id: "general", name: "General", icon: <Cog className="h-4 w-4" /> },
    { id: "members", name: "Members", icon: <Users className="h-4 w-4" /> },
    { id: "plans", name: "Plans", icon: <Zap className="h-4 w-4" /> },
    { id: "billing", name: "Bill<PERSON>", icon: <CreditCard className="h-4 w-4" /> },
    { id: "api-keys", name: "API Keys", icon: <Key className="h-4 w-4" /> },
    { id: "open-ai", name: "OpenAI", icon: <Bot className="h-4 w-4" /> },
  ];

  return (
    <div className="flex flex-col h-[90vh] w-full animate-pulse">
      {/* Header */}
      <div className="px-6 pt-6 pb-4 border-b">
        <div className="flex items-center space-x-4">
          <div className="flex-shrink-0 h-12 w-12 rounded-full bg-gray-200 dark:bg-gray-700"></div>
          <div className="space-y-2">
            <div className="h-7 w-48 bg-gray-200 dark:bg-gray-700 rounded"></div>
            <div className="h-4 w-64 bg-gray-200 dark:bg-gray-700 rounded"></div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex flex-1 overflow-hidden">
        {/* Sidebar Navigation */}
        <div className="w-64 border-r p-4 overflow-y-auto">
          <nav className="space-y-1">
            {tabs.map((tab) => (
              <div key={tab.id} className="w-full text-left">
                <div className="group flex items-center px-3 py-3 rounded-lg">
                  <div className="h-4 w-4 mr-3 bg-gray-200 dark:bg-gray-700 rounded"></div>
                  <div className="flex-1 space-y-1">
                    <div className="flex items-center justify-between">
                      <div className="h-4 w-20 bg-gray-200 dark:bg-gray-700 rounded"></div>
                      {tab.id === "members" && (
                        <Badge className="h-5 w-10 bg-gray-200 dark:bg-gray-700"></Badge>
                      )}
                    </div>
                    <div className="h-3 w-40 bg-gray-200 dark:bg-gray-700 rounded"></div>
                  </div>
                </div>
              </div>
            ))}
          </nav>
        </div>

        {/* Content Area */}
        <div className="flex-1 p-6 overflow-y-auto space-y-6">
          {/* Desktop tabs selector */}
          <div className="grid w-full grid-cols-6 gap-2">
            {tabs.map((tab) => (
              <div
                key={tab.id}
                className="h-10 bg-gray-200 dark:bg-gray-700 rounded-md"
              ></div>
            ))}
          </div>
          <Separator />

          {/* Tab content */}
          <Card>
            <CardHeader className="space-y-2">
              <div className="h-6 w-48 bg-gray-200 dark:bg-gray-700 rounded"></div>
              <div className="h-4 w-64 bg-gray-200 dark:bg-gray-700 rounded"></div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4">
                {/* Form fields */}
                <div className="space-y-2">
                  <div className="h-5 w-24 bg-gray-200 dark:bg-gray-700 rounded"></div>
                  <div className="h-10 w-full bg-gray-200 dark:bg-gray-700 rounded-md"></div>
                </div>
                <div className="space-y-2">
                  <div className="h-5 w-24 bg-gray-200 dark:bg-gray-700 rounded"></div>
                  <div className="h-10 w-full bg-gray-200 dark:bg-gray-700 rounded-md"></div>
                </div>
                <div className="space-y-2">
                  <div className="h-5 w-24 bg-gray-200 dark:bg-gray-700 rounded"></div>
                  <div className="h-20 w-full bg-gray-200 dark:bg-gray-700 rounded-md"></div>
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-end border-t px-6 py-4">
              <div className="h-10 w-32 bg-gray-200 dark:bg-gray-700 rounded-md"></div>
            </CardFooter>
          </Card>
        </div>
      </div>

      {/* Footer */}
      <div className="border-t p-4 flex justify-between items-center">
        <div className="h-4 w-48 bg-gray-200 dark:bg-gray-700 rounded"></div>
        <div className="h-9 w-20 bg-gray-200 dark:bg-gray-700 rounded-md"></div>
      </div>
    </div>
  );
}

export function TeamSettingsMobileLoading() {
  const tabs = [
    { id: "general", name: "General", icon: <Cog className="h-4 w-4" /> },
    { id: "members", name: "Members", icon: <Users className="h-4 w-4" /> },
    { id: "plans", name: "Plans", icon: <Zap className="h-4 w-4" /> },
    { id: "billing", name: "Billing", icon: <CreditCard className="h-4 w-4" /> },
    { id: "api-keys", name: "API Keys", icon: <Key className="h-4 w-4" /> },
    { id: "open-ai", name: "OpenAI", icon: <Bot className="h-4 w-4" /> },
  ];

  return (
    <div className="flex flex-col h-screen w-full animate-pulse">
      {/* Header */}
      <div className="px-6 pt-6 pb-4 border-b">
        <div className="flex items-center space-x-4">
          <div className="h-6 w-6 bg-gray-200 dark:bg-gray-700 rounded-full"></div>
          <div className="flex-shrink-0 h-12 w-12 rounded-full bg-gray-200 dark:bg-gray-700"></div>
          <div className="space-y-2">
            <div className="h-7 w-48 bg-gray-200 dark:bg-gray-700 rounded"></div>
            <div className="h-4 w-64 bg-gray-200 dark:bg-gray-700 rounded"></div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 p-6 overflow-y-auto">
        {/* Mobile tabs selector */}
        <div className="mb-6">
          {/* Active tab name display */}
          <div className="flex items-center justify-center mb-3 px-4 py-2 bg-gray-100 dark:bg-gray-800 rounded-lg">
            <div className="h-5 w-24 bg-gray-200 dark:bg-gray-700 rounded"></div>
          </div>

          {/* Icon-only tab list */}
          <div className="flex w-full overflow-x-auto space-x-1 justify-center">
            {tabs.map((tab) => (
              <div key={tab.id} className="flex flex-col items-center p-1">
                <div className="p-3 rounded-full h-11 w-11 bg-gray-200 dark:bg-gray-700"></div>
              </div>
            ))}
          </div>
        </div>

        {/* Tab content */}
        <Card>
          <CardHeader className="space-y-2">
            <div className="h-6 w-48 bg-gray-200 dark:bg-gray-700 rounded"></div>
            <div className="h-4 w-64 bg-gray-200 dark:bg-gray-700 rounded"></div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-4">
              {/* Form fields */}
              <div className="space-y-2">
                <div className="h-5 w-24 bg-gray-200 dark:bg-gray-700 rounded"></div>
                <div className="h-10 w-full bg-gray-200 dark:bg-gray-700 rounded-md"></div>
              </div>
              <div className="space-y-2">
                <div className="h-5 w-24 bg-gray-200 dark:bg-gray-700 rounded"></div>
                <div className="h-10 w-full bg-gray-200 dark:bg-gray-700 rounded-md"></div>
              </div>
              <div className="space-y-2">
                <div className="h-5 w-24 bg-gray-200 dark:bg-gray-700 rounded"></div>
                <div className="h-20 w-full bg-gray-200 dark:bg-gray-700 rounded-md"></div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}