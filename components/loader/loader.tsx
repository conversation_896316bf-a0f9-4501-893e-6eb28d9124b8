import { MessageCircle } from "lucide-react";

export const Loading = () => {
  return (
    <div className="fixed inset-0 flex items-center justify-center pointer-events-none z-0">
      <div className="relative">
        {/* Main chat icon */}
        <MessageCircle
          className="w-32 h-32 text-emerald-300 dark:text-emerald-600 opacity-80 
                    animate-float animate-duration-[4s] animate-ease-in-out animate-infinite"
          strokeWidth={1.5}
        />

        {/* Subtle floating dots animation */}
        <div className="absolute inset-0 flex items-center justify-center gap-1">
          {[...Array(3)].map((_, i) => (
            <span
              key={i}
              className="w-2 h-2 pulse-dot rounded-full bg-emerald-300 dark:bg-emerald-600 opacity-80 
                         animate-float animate-duration-[2s] animate-ease-in-out animate-infinite"
              style={{
                animationDelay: `${i * 0.2}s`,
                transform: `translateY(${i % 2 === 0 ? -5 : 5}px)`,
              }}
            />
          ))}
        </div>
        <div className="absolute inset-0 rounded-full opacity-0 animate-ping animate-duration-[3s] animate-infinite bg-gray-200 dark:bg-gray-700" />
      </div>
    </div>
  );
};
