"use client";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Loader2, CreditCard } from "lucide-react";
import toast from "react-hot-toast";
import { CardFormValues } from "@/types/types";
import { cardFormSchema } from "@/validations/validations";
import { CardElement, useStripe, useElements } from "@stripe/react-stripe-js";
import { useState } from "react";
import Image from "next/image";
import { usePaymentMethods } from "@/hooks/use-payment-method";
import { cardBrands } from "../cards/payment-cards";

interface CardFormProps {
  onSuccess?: () => void;
  onCancel?: () => void;
}



export function CardForm({ onSuccess, onCancel }: CardFormProps) {
  const stripe = useStripe();
  const elements = useElements();
  const [cardError, setCardError] = useState<string | null>(null);
  const [isCardComplete, setIsCardComplete] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { addPaymentMethod, isAdding } = usePaymentMethods();
  const [cardBrand, setCardBrand] = useState<string | null>(null);
  const form = useForm<CardFormValues>({
    resolver: zodResolver(cardFormSchema),
    defaultValues: {
      name: "",
    },
  });

  // Handle card element changes
  //eslint-disable-next-line @typescript-eslint/no-explicit-any
  const handleCardChange = (event: any) => {
    setCardError(event.error?.message || null);
    setIsCardComplete(event.complete);

    // Detect card brand
    if (event.brand) {
      setCardBrand(event.brand);
    } else {
      setCardBrand(null);
    }
  };

  const onSubmit = async (values: CardFormValues) => {
    if (!stripe || !elements) {
      toast.error("Payment system not ready");
      return;
    }

    const cardElement = elements.getElement(CardElement);
    if (!cardElement) {
      toast.error("Card element not found");
      return;
    }

    setIsSubmitting(true);

    try {
      const { error: stripeError, paymentMethod } =
        await stripe.createPaymentMethod({
          type: "card",
          card: cardElement,
          billing_details: {
            name: values.name,
          },
        });

      if (stripeError) {
        setCardError(stripeError.message || null);
        throw new Error(stripeError.message);
      }

      await addPaymentMethod({
        paymentMethodId: paymentMethod.id,
        cardName: values.name,
      });

      onSuccess?.();
    } catch (error) {
      if (error instanceof Error) {
        toast.error(error.message || "Failed to add payment method");
      } else {
        toast.error("Failed to add payment method");
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  // Disable button when form is invalid or card is incomplete
  const isSubmitDisabled =
    !stripe ||
    isSubmitting ||
    !form.formState.isValid ||
    !isCardComplete || isAdding ||
    !!cardError;

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Name on Card</FormLabel>
              <FormControl>
                <Input placeholder="John Doe" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <FormLabel>Card Details</FormLabel>
            {cardBrand && cardBrands[cardBrand] && (
              <div className="h-6 w-10 relative">
                <Image
                  src={cardBrands[cardBrand]}
                  alt={cardBrand}
                  fill
                  className="object-contain"
                />
              </div>
            )}
          </div>

          <div className="rounded-md border border-input p-3">
            <CardElement
              options={{
                style: {
                  base: {
                    fontSize: "16px",
                    color: "#424770",
                    "::placeholder": {
                      color: "#aab7c4",
                    },
                  },
                  invalid: {
                    color: "#9e2146",
                  },
                },
              }}
              onChange={handleCardChange}
            />
          </div>
          {cardError && (
            <p className="text-sm font-medium text-destructive">{cardError}</p>
          )}
        </div>

        <div className="flex justify-end gap-2 pt-2">
          <Button
            type="button"
            variant="outline"
            className="cursor-pointer"
            onClick={onCancel}
            disabled={isSubmitting}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={isSubmitDisabled}
            className="cursor-pointer"
          >
            {isSubmitting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Adding...
              </>
            ) : (
              <>
                <CreditCard className="mr-2 h-4 w-4" />
                Add Card
              </>
            )}
          </Button>
        </div>
      </form>
    </Form>
  );
}
