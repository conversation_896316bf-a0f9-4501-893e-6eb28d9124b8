import {
  BookOpen,
  Briefcase,
  Globe,
  HeartPulse,
  Lightbulb,
  MessageSquare,
  Scale,
  User,
} from "lucide-react";
import <PERSON> from "next/link";
import React from "react";

interface BlogsLinks {
  name: string;
  icon: React.ElementType;
  href: string;
}
export default function BlogsSidebarLinks() {
  const blogsLinks: BlogsLinks[] = [
    { name: "Chatbots", icon: MessageSquare, href: "/blogs/chatbot" },
    { name: "AI Agents", icon: User, href: "/blogs/agent" },
    { name: "Africa", icon: Globe, href: "/blogs/africa" },
    { name: "Governance", icon: Scale, href: "/blogs/governance" },
    { name: "Design", icon: BookOpen, href: "/blogs/design" },
    { name: "Culture", icon: HeartPulse, href: "/blogs/culture" },
    { name: "Business", icon: Briefcase, href: "/blogs/business" },
    { name: "Politics", icon: Scale, href: "/blogs/politics" },
    { name: "<PERSON>", icon: HeartPulse, href: "/blogs/health" },
    { name: "AGI", icon: Lightbulb, href: "/blogs/agi" },
  ];

  return (
    <section className="py-8 bg-white dark:bg-gray-800 shadow-sm">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex flex-col md:flex-row items-start md:items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white flex items-center">
            <Lightbulb className="w-5 h-5 mr-2 text-emerald-500" />
            Explore Topics
          </h2>
          <p className="text-sm text-gray-500 dark:text-gray-400 mt-1 md:mt-0">
            Discover content tailored to your interests
          </p>
        </div>

        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-5 lg:grid-cols-10 gap-3">
          {blogsLinks.map(({ name, icon: Icon, href }) => (
            <Link
              key={name}
              href={href}
              className="flex flex-col items-center justify-center p-3 rounded-lg bg-gray-100 dark:bg-gray-700 hover:bg-emerald-100 dark:hover:bg-emerald-900/30 transition-colors group"
            >
              <Icon className="w-5 h-5 text-emerald-500 group-hover:text-emerald-600 dark:group-hover:text-emerald-400 mb-1.5" />
              <span className="text-xs font-medium text-gray-700 dark:text-gray-300 group-hover:text-emerald-700 dark:group-hover:text-emerald-400 text-center">
                {name}
              </span>
            </Link>
          ))}
        </div>
      </div>
    </section>
  );
}
