"use client";

import { motion, AnimatePresence } from "framer-motion";
import Link from "next/link";
import { useState, useEffect, useRef } from "react";
import { Menu, X } from "lucide-react";
import { usePathname } from "next/navigation";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

// Animation variants
const sidebarVariants = {
  hidden: { x: -50, opacity: 0 },
  visible: {
    x: 0,
    opacity: 1,
    transition: {
      type: "spring",
      stiffness: 100,
      damping: 20,
      when: "beforeChildren",
      staggerChildren: 0.05,
    },
  },
  mobileHidden: { x: -300, opacity: 0 },
  mobileVisible: {
    x: 0,
    opacity: 1,
    transition: {
      type: "spring",
      stiffness: 300,
      damping: 30,
    },
  },
  mobileExit: {
    x: -300,
    opacity: 0,
    transition: { duration: 0.2 },
  },
};

const menuItemVariants = {
  hidden: { x: -20, opacity: 0 },
  visible: {
    x: 0,
    opacity: 1,
    transition: {
      type: "spring",
      stiffness: 200,
    },
  },
  hover: {
    x: 5,
    transition: { duration: 0.2 },
  },
};

const docsMenu = [
  { title: "Getting Setup", path: "/docs" },
  { title: "Create a Chatbot", path: "/docs/create-chatbot" },
  { title: "Message a Chatbot", path: "/docs/message-chatbot" },
  { title: "Update a Chatbot", path: "/docs/update-chatbot" },
  { title: "Delete a Chatbot", path: "/docs/delete-chatbot" },
  { title: "Stream Messages", path: "/docs/stream-messages" },
  { title: "Update Chatbot Settings", path: "/docs/update-chatbot-settings" },
  { title: "Get Leads", path: "/docs/get-leads" },
  { title: "Get Conversations", path: "/docs/get-conversations" },
  { title: "Delete Chatbot Icon", path: "/docs/delete-chatbot-icon" },
  { title: "Upload Chatbot Icon", path: "/docs/upload-chatbot-icon" },
  {
    title: "Delete Chatbot Profile Picture",
    path: "/docs/delete-chatbot-profile-picture",
  },
  {
    title: "Upload Chatbot Profile Picture",
    path: "/docs/upload-chatbot-profile-picture",
  },
  { title: "Get Chatbots", path: "/docs/get-chatbots" },
  { title: "Webhooks API", path: "/docs/webhooks-api" },
];

export const DocsSidebar = () => {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const pathname = usePathname();
  const activeLinkRef = useRef<HTMLLIElement>(null);
  const sidebarRef = useRef<HTMLDivElement>(null);

  // Scroll active link into view
  useEffect(() => {
    if (activeLinkRef.current && sidebarRef.current) {
      const sidebar = sidebarRef.current;
      const activeLink = activeLinkRef.current;

      // Calculate positions
      const sidebarTop = sidebar.scrollTop;
      const sidebarBottom = sidebarTop + sidebar.clientHeight;
      const linkTop = activeLink.offsetTop;
      const linkBottom = linkTop + activeLink.clientHeight;

      // Only scroll if the link is not already visible
      if (linkTop < sidebarTop || linkBottom > sidebarBottom) {
        sidebar.scrollTo({
          top: linkTop - sidebar.clientHeight / 3,
          behavior: "smooth",
        });
      }
    }
  }, [pathname]);

  return (
    <>
      {/* Mobile Menu Button */}
      <div className="lg:hidden fixed top-4 right-4 z-50">
        <motion.button
          onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
          className="p-2 rounded-lg bg-white dark:bg-gray-800 shadow-md"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          {mobileMenuOpen ? (
            <X className="w-6 h-6" />
          ) : (
            <Menu className="w-6 h-6" />
          )}
        </motion.button>
      </div>

      {/* Desktop Sidebar */}
      <motion.div
        ref={sidebarRef}
        className="hidden lg:mt-20 lg:block w-72 fixed h-[calc(100vh-5rem)] overflow-y-auto bg-white dark:bg-gray-800 shadow-sm z-40 rounded-tr-xl rounded-br-xl"
        initial="hidden"
        animate="visible"
        variants={sidebarVariants}
      >
        <div className="p-6 h-full">
          <motion.h2
            className="sticky top-0 z-10 bg-white dark:bg-gray-800 text-xl font-bold text-emerald-600 dark:text-emerald-400 mb-6 px-4 py-2"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.2 }}
          >
            API Documentation
            <div className="my-2 h-px bg-gradient-to-r from-transparent via-emerald-500 to-transparent dark:via-emerald-400" />
          </motion.h2>

          <nav>
            <ul className="space-y-2">
              {docsMenu.map((item) => {
                const isActive = pathname === item.path;
                return (
                  <motion.li
                    key={item.path}
                    variants={menuItemVariants}
                    whileHover="hover"
                    ref={isActive ? activeLinkRef : null}
                    className={isActive ? "active-link" : ""}
                  >
                    <Link
                      href={item.path}
                      className={`block px-4 py-2 rounded-md transition-colors ${
                        isActive
                          ? "bg-emerald-100 dark:bg-emerald-900/50 text-emerald-700 dark:text-emerald-300 font-medium"
                          : "text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                      }`}
                    >
                      <motion.span className="flex items-center">
                        {item.title}
                      </motion.span>
                    </Link>
                  </motion.li>
                );
              })}
            </ul>
          </nav>
        </div>
      </motion.div>

      {/* Mobile Sidebar */}
      <AnimatePresence>
        {mobileMenuOpen && (
          <>
            {/* Overlay */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 0.5 }}
              exit={{ opacity: 0 }}
              onClick={() => setMobileMenuOpen(false)}
              className="fixed inset-0 z-40 bg-black lg:hidden"
            />
            {/* Sidebar */}
            <motion.div
              initial="mobileHidden"
              animate="mobileVisible"
              exit="mobileExit"
              variants={sidebarVariants}
              className="fixed inset-y-0 left-0 mt-12 z-40 w-72 bg-white dark:bg-gray-800 shadow-xl lg:hidden"
            >
              <div className="h-full overflow-y-auto p-6">
                <motion.h2
                  className="sticky top-0 z-10 bg-white dark:bg-gray-800 text-xl font-bold text-emerald-600 dark:text-emerald-400 mb-6 px-4 py-2"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.2 }}
                >
                  API Documentation
                  <div className="my-2 h-px bg-gradient-to-r from-transparent via-emerald-500 to-transparent dark:via-emerald-400" />
                </motion.h2>

                {/* Mobile dropdown select */}
                <div className="mb-6 lg:hidden">
                  <Select
                    onValueChange={(value) => {
                      if (value) {
                        window.location.href = value;
                      }
                    }}
                  >
                    <SelectTrigger className="w-full cursor-pointer">
                      <SelectValue placeholder="Select a Menu Option" />
                    </SelectTrigger>
                    <SelectContent>
                      {docsMenu.map((item) => (
                        <SelectItem key={item.path} value={item.path} className="cursor-pointer">
                          {item.title}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Mobile menu list */}
                <nav>
                  <ul className="space-y-2">
                    {docsMenu.map((item) => {
                      const isActive = pathname === item.path;
                      return (
                        <motion.li
                          key={item.path}
                          variants={menuItemVariants}
                          whileHover="hover"
                        >
                          <Link
                            href={item.path}
                            onClick={() => setMobileMenuOpen(false)}
                            className={`block px-4 py-2 rounded-md transition-colors ${
                              isActive
                                ? "bg-emerald-100 dark:bg-emerald-900/50 text-emerald-700 dark:text-emerald-300 font-medium"
                                : "text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                            }`}
                          >
                            {item.title}
                          </Link>
                        </motion.li>
                      );
                    })}
                  </ul>
                </nav>
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </>
  );
};
