"use client";
import Link from "next/link";
import {
  ChevronRight,
  Star,
  Users,
  Bot,
  Zap,
  BarChart2,
  Plus,
  Settings,
} from "lucide-react";
import { Team } from "@/types/types";
import { motion } from "framer-motion";
import { containerVariants, itemVariants } from "@/variants/variants";
import { useState } from "react";
import { TeamSettingsModal } from "../modals/team-settings-modal";
import { InviteMemberModal } from "../modals/invite-member-modal";
import { CreateAgentModal } from "../modals/create-agent-modal";

interface TeamSidebarProps {
  currentTeam: Team;
  relatedTeams: Team[];
}

export const TeamSidebar = ({
  currentTeam,
  relatedTeams,
}: TeamSidebarProps) => {
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [isInviteModalOpen, setIsInviteModalOpen] = useState<boolean>(false);
  const [isCreatingAgent, setIsCreatingAgent] = useState(false);

  return (
    <aside className="hidden lg:block lg:w-72 xl:w-80 pl-8">
      <div className="space-y-6">
        {/* Quick Actions Card */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="bg-gradient-to-br from-white/80 to-white/60 dark:from-gray-800/90 dark:to-gray-800/70 backdrop-blur-sm rounded-xl shadow-lg border border-gray-200/70 dark:border-gray-700/50 overflow-hidden p-6"
        >
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
            <Zap className="h-5 w-5 text-yellow-500 mr-2" />
            Quick Actions
          </h3>
          <div className="space-y-3">
            <motion.button
              whileHover={{ x: 3 }}
              whileTap={{ scale: 0.98 }}
              onClick={() => setIsCreatingAgent(true)}
              className="w-full flex cursor-pointer items-center justify-between p-3 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100/70 dark:bg-gray-700/50 hover:bg-gray-200/50 dark:hover:bg-gray-600/50 rounded-lg transition-colors"
            >
              <span>Create New Agent</span>
              <Plus className="h-4 w-4" />
            </motion.button>
            <motion.button
              whileHover={{ x: 3 }}
              onClick={() => setIsInviteModalOpen(true)}
              whileTap={{ scale: 0.98 }}
              className="w-full flex cursor-pointer items-center justify-between p-3 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100/70 dark:bg-gray-700/50 hover:bg-gray-200/50 dark:hover:bg-gray-600/50 rounded-lg transition-colors"
            >
              <span>Invite Members</span>
              <Users className="h-4 w-4" />
            </motion.button>
            <motion.button
              whileHover={{ x: 3 }}
              whileTap={{ scale: 0.98 }}
              onClick={() => setIsModalOpen(true)}
              className="w-full flex cursor-pointer items-center justify-between p-3 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100/70 dark:bg-gray-700/50 hover:bg-gray-200/50 dark:hover:bg-gray-600/50 rounded-lg transition-colors"
            >
              <span>Team Settings</span>
              <Settings className="h-4 w-4" />
            </motion.button>

            <TeamSettingsModal
              team={currentTeam}
              isOpen={isModalOpen}
              onOpenChange={setIsModalOpen}
            />
          </div>
        </motion.div>

        {/* Related Teams Card */}
        {/* Compact Related Teams Card */}
        <motion.div
          initial="hidden"
          animate="visible"
          variants={containerVariants}
          className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden"
        >
          <div className="p-4">
            <div className="flex items-center justify-between mb-3">
              <h3 className="text-sm font-semibold text-gray-900 dark:text-white flex items-center">
                <Users className="h-4 w-4 mr-2 text-emerald-500" />
                Related Teams
              </h3>
              {relatedTeams.length > 3 && (
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {relatedTeams.length} total
                </span>
              )}
            </div>

            <motion.div className="space-y-2">
              {relatedTeams.slice(0, 3).map((team) => (
                <motion.div
                  key={team._id.toString()}
                  variants={itemVariants}
                  whileHover={{ scale: 1.01 }}
                >
                  <Link
                    href={`/dashboard/team/${team.url}`}
                    className={`flex items-center p-2 rounded-lg text-sm ${
                      team._id.toString() === currentTeam._id.toString()
                        ? "bg-emerald-50 dark:bg-emerald-900/20"
                        : "hover:bg-gray-50 dark:hover:bg-gray-700/50"
                    }`}
                  >
                    <div className="flex-shrink-0 w-8 h-8 rounded-lg bg-emerald-100 dark:bg-emerald-900/30 flex items-center justify-center text-emerald-600 dark:text-emerald-300 text-xs font-bold">
                      {team.name.charAt(0).toUpperCase()}
                    </div>

                    <div className="ml-3 flex-1 min-w-0">
                      <div className="flex items-center">
                        <p className="font-medium text-gray-900 dark:text-white truncate">
                          {team.name}
                        </p>
                        {team.isFavorite && (
                          <Star className="h-3 w-3 ml-1 text-yellow-400 fill-current" />
                        )}
                      </div>
                      <p className="text-xs text-gray-500 dark:text-gray-400 mt-0.5">
                        {team.membersCount} member
                        {team.membersCount !== 1 ? "s" : ""}
                      </p>
                    </div>

                    <ChevronRight className="h-4 w-4 text-gray-400 dark:text-gray-500" />
                  </Link>
                </motion.div>
              ))}
            </motion.div>

            {relatedTeams.length === 0 && (
              <div className="text-center py-4">
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  No related teams found
                </p>
              </div>
            )}

            {relatedTeams.length > 3 && (
              <div className="mt-2">
                <Link
                  href="/dashboard/teams"
                  className="text-xs text-emerald-600 dark:text-emerald-400 hover:underline flex items-center justify-center"
                >
                  View all teams
                  <ChevronRight className="h-3 w-3 ml-1" />
                </Link>
              </div>
            )}
          </div>
        </motion.div>

        {/* Team Stats Card */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-gradient-to-br from-white/80 to-white/60 dark:from-gray-800/90 dark:to-gray-800/70 backdrop-blur-sm rounded-xl shadow-lg border border-gray-200/70 dark:border-gray-700/50 overflow-hidden relative"
        >
          {/* Decorative elements */}
          <div className="absolute -top-5 -right-5 w-20 h-20 bg-blue-300/10 dark:bg-blue-500/10 rounded-full blur-xl"></div>

          <div className="relative z-10 p-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
                <BarChart2 className="h-5 w-5 mr-2 text-blue-500" />
                Team Stats
              </h3>
              <div className="w-8 h-8 rounded-full bg-blue-100/50 dark:bg-blue-900/30 flex items-center justify-center">
                <Zap className="h-4 w-4 text-blue-500 dark:text-blue-400" />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <motion.div
                whileHover={{ y: -3 }}
                className="bg-gradient-to-br from-gray-50/70 to-gray-100/50 dark:from-gray-700/50 dark:to-gray-700/30 p-4 rounded-lg border border-gray-200/50 dark:border-gray-600/30 shadow-sm"
              >
                <div className="flex items-center">
                  <Bot className="h-4 w-4 text-emerald-500 mr-2" />
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Chatbots
                  </p>
                </div>
                <p className="text-2xl font-bold text-gray-900 dark:text-white mt-2">
                  12
                </p>
                <div className="h-1.5 w-full bg-gray-200 dark:bg-gray-700 rounded-full mt-2 overflow-hidden">
                  <motion.div
                    className="h-full bg-gradient-to-r from-emerald-400 to-emerald-500 rounded-full"
                    initial={{ width: 0 }}
                    animate={{ width: "75%" }}
                    transition={{ duration: 1, delay: 0.3 }}
                  />
                </div>
              </motion.div>

              <motion.div
                whileHover={{ y: -3 }}
                className="bg-gradient-to-br from-gray-50/70 to-gray-100/50 dark:from-gray-700/50 dark:to-gray-700/30 p-4 rounded-lg border border-gray-200/50 dark:border-gray-600/30 shadow-sm"
              >
                <div className="flex items-center">
                  <Users className="h-4 w-4 text-blue-500 mr-2" />
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Members
                  </p>
                </div>
                <p className="text-2xl font-bold text-gray-900 dark:text-white mt-2">
                  5
                </p>
                <div className="h-1.5 w-full bg-gray-200 dark:bg-gray-700 rounded-full mt-2 overflow-hidden">
                  <motion.div
                    className="h-full bg-gradient-to-r from-blue-400 to-blue-500 rounded-full"
                    initial={{ width: 0 }}
                    animate={{ width: "45%" }}
                    transition={{ duration: 1, delay: 0.5 }}
                  />
                </div>
              </motion.div>
            </div>

            {/* Activity Sparkline */}
            <div className="mt-6">
              <div className="flex items-center justify-between mb-2">
                <p className="text-xs font-medium text-gray-500 dark:text-gray-400">
                  Weekly Activity
                </p>
                <span className="text-xs text-emerald-500">+12%</span>
              </div>
              <div className="h-10 w-full">
                <svg viewBox="0 0 100 30" className="w-full h-full">
                  <motion.path
                    d="M0,15 C10,5 20,25 30,10 C40,20 50,5 60,15 C70,25 80,10 90,20 C100,10"
                    stroke="url(#activityGradient)"
                    strokeWidth="2"
                    fill="none"
                    strokeLinecap="round"
                    initial={{ pathLength: 0 }}
                    animate={{ pathLength: 1 }}
                    transition={{ duration: 1.5 }}
                  />
                  <defs>
                    <linearGradient
                      id="activityGradient"
                      x1="0%"
                      y1="0%"
                      x2="100%"
                      y2="0%"
                    >
                      <stop offset="0%" stopColor="#10b981" />
                      <stop offset="100%" stopColor="#3b82f6" />
                    </linearGradient>
                  </defs>
                </svg>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
      {/* Invite Member Modal */}
      <InviteMemberModal
        isOpen={isInviteModalOpen}
        onOpenChange={setIsInviteModalOpen}
        onInviteSuccess={() => setIsInviteModalOpen(false)}
        teamUrl={currentTeam.url}
      />

      <CreateAgentModal
        isOpen={isCreatingAgent}
        onClose={() => setIsCreatingAgent(false)}
        teamId={currentTeam?._id?.toString() ?? ""}
      />
    </aside>
  );
};
