import { ArrowR<PERSON>, BookOpen, Mail, MessageSquare, Tag } from "lucide-react";
import <PERSON> from "next/link";
import React from "react";

interface PopularPost {
  title: string;
  date: string;
  href: string;
}
export default function BlogsSidebar() {
  const popularPosts: PopularPost[] = [
    {
      title: "The Future of AI in African Markets",
      date: "Dec 15, 2023",
      href: "#",
    },
    {
      title: "Designing Empathetic Chatbots",
      date: "Nov 28, 2023",
      href: "#",
    },
    {
      title: "AI Governance Best Practices",
      date: "Jan 5, 2024",
      href: "#",
    },
    {
      title: "Multilingual Chatbot Strategies",
      date: "Dec 2, 2023",
      href: "#",
    },
  ];
  return (
    <div className="space-y-8 sticky top-10">
      {/* About Card */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md overflow-hidden p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
          <MessageSquare className="w-5 h-5 text-emerald-500 mr-2" />
          About Chatzuri
        </h3>
        <p className="text-gray-600 dark:text-gray-400 mb-4">
          Chatzuri is revolutionizing conversational AI with cutting-edge
          technology that learns from every interaction.
        </p>
        <div className="space-y-3">
          <Link
            href="/about"
            className="flex items-center text-emerald-600 dark:text-emerald-400 hover:underline"
          >
            <ArrowRight className="w-4 h-4 mr-2" />
            Learn more about us
          </Link>
          <Link
            href="/help"
            className="flex items-center text-emerald-600 dark:text-emerald-400 hover:underline"
          >
            <ArrowRight className="w-4 h-4 mr-2" />
            Get in touch
          </Link>
        </div>
      </div>

      {/* Popular Posts */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md overflow-hidden p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
          <BookOpen className="w-5 h-5 text-emerald-500 mr-2" />
          Popular Posts
        </h3>
        <div className="space-y-4">
          {popularPosts.map((post, index) => (
            <Link key={index} href={post.href} className="block group">
              <h4 className="text-sm font-medium text-gray-900 dark:text-white group-hover:text-emerald-600 dark:group-hover:text-emerald-400">
                {post.title}
              </h4>
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                {post.date}
              </p>
              {index < 3 && (
                <hr className="border-gray-200 dark:border-gray-700 mt-3" />
              )}
            </Link>
          ))}
        </div>
      </div>

      {/* Newsletter */}
      <div className="bg-gradient-to-br from-emerald-50 to-emerald-100 dark:from-emerald-900/30 dark:to-emerald-800/30 rounded-xl shadow-md overflow-hidden p-6 border border-emerald-100 dark:border-emerald-800/50">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2 flex items-center">
          <Mail className="w-5 h-5 text-emerald-600 dark:text-emerald-400 mr-2" />
          Stay Updated
        </h3>
        <p className="text-sm text-gray-600 dark:text-gray-300 mb-4">
          Get the latest AI insights and Chatzuri news delivered to your inbox.
        </p>
        <form className="space-y-3">
          <div>
            <label htmlFor="email" className="sr-only">
              Email address
            </label>
            <input
              type="email"
              id="email"
              placeholder="Your email"
              className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-emerald-500 focus:border-emerald-500 dark:bg-gray-700 dark:text-white"
            />
          </div>
          <button
            type="submit"
            className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-emerald-600 hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500"
          >
            Subscribe
          </button>
        </form>
        <p className="mt-3 text-xs text-gray-500 dark:text-gray-400">
          We respect your privacy. Unsubscribe at any time.
        </p>
      </div>

      {/* Tags */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md overflow-hidden p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
          <Tag className="w-5 h-5 text-emerald-500 mr-2" />
          Topics
        </h3>
        <div className="flex flex-wrap gap-2">
          {[
            "AI Agents",
            "Chatbots",
            "NLP",
            "Machine Learning",
            "Africa Tech",
            "Customer Experience",
            "Automation",
            "Business Growth",
            "Support",
            "Design",
          ].map((tag) => (
            <Link
              key={tag}
              href="#"
              className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 hover:bg-emerald-100 dark:hover:bg-emerald-900/30 hover:text-emerald-800 dark:hover:text-emerald-400"
            >
              {tag}
            </Link>
          ))}
        </div>
      </div>
    </div>
  );
}
