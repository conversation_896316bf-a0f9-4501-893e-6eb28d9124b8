"use client";

import { useState } from "react";
import { AnimatePresence, motion } from "framer-motion";
import { <PERSON><PERSON><PERSON>, Users, CreditCard, <PERSON>, <PERSON><PERSON>, Cog, Bot } from "lucide-react";
import { <PERSON><PERSON>, DialogContent } from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
  CardFooter,
} from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON>List, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { CreateTeamForm } from "@/components/forms/create-team-form";
import { Team } from "@/types/types";
import { useUpdateTeam } from "@/hooks/use-teams";
import toast from "react-hot-toast";
import { MembersSettings } from "@/components/teams/settings/members-settings";
import { PlansSettings } from "@/components/teams/settings/plans-settings";
import { ApiKeysSettings } from "@/components/teams/settings/api-settings";
import { OpenAiKeysSettings } from "@/components/teams/settings/openai-settings";
import Link from "next/link";
import { ChevronLeft } from "lucide-react";
import BillingSettings from "./billing-settings";
import React from "react";
import { useTeamMembers } from "@/hooks/use-team-members";

interface TeamSettingsContentProps {
  team: Team;
  isMobile?: boolean;
  isOpen?: boolean;
  onOpenChange?: (open: boolean) => void;
}

type TabComponent = React.ReactNode;

interface TeamTab {
  id: string;
  name: string;
  icon: React.ReactNode;
  description: string;
  badge?: string;
  showButtons?: boolean;
  buttonText?: string;
  component?: TabComponent;
}

export function TeamSettingsContent({
  team,
  isMobile = false,
  isOpen = true,
  onOpenChange,
}: TeamSettingsContentProps) {
  const [activeTab, setActiveTab] = useState<string>("general");
  const updateTeam = useUpdateTeam();
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0,
    totalItems:0
  });

  const {
    members,

    isLoading: isMemberLoading,
  } = useTeamMembers({
    slug: team.url,
    initialPagination: pagination,
  });

  // useEffect(() => {
  //   if (membersPagination) {
  //     setPagination((prev) => ({
  //       ...prev,
  //       total: membersPagination.total,
  //       totalPages: membersPagination.totalPages,
  //     }));
  //   }
  // }, [membersPagination]);

  const handlePageChange = (newPage: number) => {
    setPagination((prev) => ({
      ...prev,
      page: newPage,
    }));
  };

  const subscription = {
    planName: "Pro Plan",
    monthlyMessageCredits: 1000,
    end: "2023-12-31T00:00:00.000Z",
  };

  const teamBilling = {
    taxType: "VAT",
    taxID: "GB123456789",
  };

  async function handleUpdateTeam(values: {
    name: string;
    url: string;
    description: string;
    color?: string;
    openAiKey?: string | null;
    metaData?: string | null;
    isFavorite?: boolean;
  }): Promise<void> {
    try {
      await toast.promise(
        updateTeam.mutateAsync({
          slug: team.url,
          data: values,
        }),
        {
          loading: "Updating team...",
          success: "Team updated successfully!",
          error: (err) => `Failed to update team: ${err.message}`,
        }
      );
    } catch (error) {
      console.error("Error occurred during form submission:", error);
    }
  }

  // Default tabs with components
  const tabs: TeamTab[] = [
    {
      id: "general",
      name: "General",
      icon: <Cog className="h-4 w-4" />,
      showButtons: true,
      buttonText: "Save Changes",
      description: "Configure basic team settings and preferences",
      component: (
        <CreateTeamForm
          onSubmit={handleUpdateTeam}
          isPending={updateTeam.isPending}
          hideDefaultSubmitButton={true}
          isEditing
          initialData={{
            name: team.name,
            url: team.url,
            description: team.description,
            color: team.color,
            openAiKey: team.openAiKey,
            metaData: team.metaData,
            isFavorite: team.isFavorite,
          }}
        />
      ),
    },
    {
      id: "members",
      name: "Members",
      icon: <Users className="h-4 w-4" />,
      description: "Manage team members and permissions",
      badge: members.length.toString(),
      showButtons: false,
      component: (
        <MembersSettings
          team={team}
          pagination={pagination}
          onPageChange={handlePageChange}
        />
      ),
    },
    {
      id: "plans",
      name: "Plans",
      icon: <Zap className="h-4 w-4" />,
      description: "View and upgrade your subscription plan",
      component: <PlansSettings team={team} />,
    },
    {
      id: "billing",
      name: "Billing",
      icon: <CreditCard className="h-4 w-4" />,
      description: "Manage payment methods and invoices",
      component: (
        <BillingSettings
          team={team}
          subscription={subscription}
          teamBilling={teamBilling}
        />
      ),
    },
    {
      id: "api-keys",
      name: "API Keys",
      icon: <Key className="h-4 w-4" />,
      description: "Create and manage API access keys",
      component: <ApiKeysSettings team={team} />,
    },
    {
      id: "open-ai",
      name: "OpenAI",
      icon: <Bot className="h-4 w-4" />,
      description: "Configure OpenAI API settings and models",
      component: <OpenAiKeysSettings team={team} />,
    },
  ];

  const content = (
    <div
      className={`flex flex-col ${isMobile ? "h-screen" : "h-[90vh]"} w-full`}
    >
      {/* Header */}
      <div className="px-6 pt-6 pb-4 border-b">
        <div className="flex items-center space-x-4">
          {isMobile && (
            <Link href={`/teams/${team.url}`} className="mr-2">
              <ChevronLeft className="h-6 w-6" />
            </Link>
          )}
          <div className="flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-gradient-to-br from-emerald-100 to-emerald-200 dark:from-emerald-900/30 dark:to-emerald-800/40 text-emerald-600 dark:text-emerald-400">
            <Settings className="h-6 w-6" />
          </div>
          <div>
            <h1 className="text-2xl font-bold">Team Settings</h1>
            <p className="text-sm text-muted-foreground">
              Manage your team&apos;s configuration and preferences
            </p>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex flex-1 overflow-hidden">
        {/* Sidebar Navigation - Hidden on mobile */}
        {!isMobile && (
          <div className="w-64 border-r p-4 overflow-y-auto">
            <nav className="space-y-1">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className="w-full text-left"
                >
                  <motion.div
                    whileHover={{ x: 3 }}
                    className={`group flex items-center px-3 py-3 rounded-lg transition-colors ${
                      activeTab === tab.id
                        ? "bg-emerald-50 dark:bg-emerald-900/20 text-emerald-700 dark:text-emerald-400"
                        : "text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700/50"
                    }`}
                  >
                    <div
                      className={`mr-3 ${
                        activeTab === tab.id
                          ? "text-emerald-500 dark:text-emerald-400"
                          : "text-gray-400 dark:text-gray-500 group-hover:text-emerald-500 dark:group-hover:text-emerald-400"
                      }`}
                    >
                      {tab.icon}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">{tab.name}</span>
                        {isMemberLoading ? (
                          <div className="ml-2 h-5 w-10 rounded bg-muted animate-pulse" />
                        ) : (
                          tab.badge && (
                            <Badge variant="secondary" className="ml-2">
                              {tab.badge}
                            </Badge>
                          )
                        )}
                      </div>
                      <p className="text-xs text-muted-foreground">
                        {tab.description}
                      </p>
                    </div>
                  </motion.div>
                </button>
              ))}
            </nav>
          </div>
        )}

        {/* Content Area */}
        <div className="flex-1 p-4 flex flex-col relative overflow-y-auto">
          <div className="sticky top-0 z-10 bg-background pt-2">
            <Tabs
              value={activeTab}
              onValueChange={setActiveTab}
              className="space-y-6"
            >
              {/* Mobile tabs selector */}
              {isMobile && (
                <div className="mb-6">
                  {/* Active tab name display */}
                  <div className="flex items-center justify-center mb-3 px-4 py-2 bg-gray-100 dark:bg-gray-800 rounded-lg">
                    <span className="font-medium text-sm">
                      {tabs.find((t) => t.id === activeTab)?.name}
                    </span>
                  </div>

                  {/* Icon-only tab list */}
                  <TabsList className="flex w-full overflow-x-auto space-x-1 justify-center [&_[data-state=active]]:border-none">
                    {tabs.map((tab) => (
                      <TabsTrigger
                        key={tab.id}
                        value={tab.id}
                        className="flex-shrink-0 p-2 rounded-full"
                        asChild
                      >
                        <div className="flex flex-col items-center p-1">
                          <div
                            className={`
                  p-3 rounded-full flex border-none items-center justify-center
                  transition-colors duration-200
                  ${
                    activeTab === tab.id
                      ? "bg-emerald-500/10 text-emerald-600 dark:text-emerald-400"
                      : "text-gray-500 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-800/50"
                  }
                `}
                          >
                            {React.isValidElement(tab.icon) &&
                            typeof tab.icon.type !== "string"
                              ? React.cloneElement(
                                  //eslint-disable-next-line @typescript-eslint/no-explicit-any
                                  tab.icon as React.ReactElement<any>,
                                  {
                                    className: "h-5 w-5",
                                  }
                                )
                              : tab.icon}
                          </div>
                        </div>
                      </TabsTrigger>
                    ))}
                  </TabsList>
                </div>
              )}

              {/* Desktop tabs selector */}
              {!isMobile && (
                <>
                  <TabsList className="grid w-full grid-cols-6">
                    {tabs.map((tab) => (
                      <TabsTrigger
                        key={tab.id}
                        value={tab.id}
                        className="cursor-pointer"
                      >
                        <div className="flex items-center space-x-2">
                          {tab.icon}
                          <span>{tab.name}</span>
                        </div>
                      </TabsTrigger>
                    ))}
                  </TabsList>
                  <Separator />
                </>
              )}

              {/* Tab content */}
              {tabs.map((tab) => (
                <TabsContent key={tab.id} value={tab.id} className="space-y-4">
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-xl flex items-center space-x-2">
                        {tab.icon}
                        <span>{tab.name} Settings</span>
                      </CardTitle>
                      <CardDescription>{tab.description}</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div className="flex-1">
                          <AnimatePresence mode="wait">
                            <motion.div
                              key={activeTab}
                              initial={{ opacity: 0, x: 10 }}
                              animate={{ opacity: 1, x: 0 }}
                              exit={{ opacity: 0, x: -10 }}
                              transition={{ duration: 0.2 }}
                              className="h-full"
                            >
                              {tabs.find((tab) => tab.id === activeTab)
                                ?.component || (
                                <div className="flex items-center justify-center h-full text-gray-500">
                                  No component found for this tab
                                </div>
                              )}
                            </motion.div>
                          </AnimatePresence>
                        </div>
                      </div>
                    </CardContent>
                    {tab.showButtons && (
                      <CardFooter className="flex justify-end border-t px-6 py-4">
                        <Button
                          disabled={updateTeam.isPending}
                          onClick={() => {
                            const form = document.querySelector(
                              `form[data-tab="${tab.id}"]`
                            );
                            if (form) {
                              const submitButton = form.querySelector(
                                'button[type="submit"]'
                              );
                              if (submitButton) {
                                (submitButton as HTMLButtonElement).click();
                              }
                            }
                          }}
                          className="cursor-pointer"
                        >
                          {updateTeam.isPending
                            ? "Processing..."
                            : tab.buttonText || "Save Changes"}
                        </Button>
                      </CardFooter>
                    )}
                  </Card>
                </TabsContent>
              ))}
            </Tabs>
          </div>
        </div>
      </div>

      {/* Footer */}
      {!isMobile && (
        <div className="border-t p-4 text-sm text-muted-foreground flex justify-end items-center">
          <Button
            variant="ghost"
            onClick={() => onOpenChange?.(false)}
            className="cursor-pointer"
          >
            Close
          </Button>
        </div>
      )}
    </div>
  );

  if (isMobile) {
    return content;
  }

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-8xl p-0 overflow-hidden">
        {content}
      </DialogContent>
    </Dialog>
  );
}
