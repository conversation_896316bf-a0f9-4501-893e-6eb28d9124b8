"use client";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON> } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  <PERSON>,
  CardHeader,
  <PERSON>T<PERSON><PERSON>,
  <PERSON><PERSON>ontent,
  <PERSON>Footer,
} from "@/components/ui/card";
import { useState } from "react";
import toast from "react-hot-toast";
import { Team } from "@/types/types";
import { CodeBlock } from "@/components/code-block/code-block";
import { CopyButton } from "@/components/copy-button/copy-button";

interface ApiKeysSettingsProps {
  team: Team;
  initialApiKey?: string;
}

export function ApiKeysSettings({ team }: ApiKeysSettingsProps) {
  const [apiKey, setApiKey] = useState(team.apiKey);
  const [isGenerating, setIsGenerating] = useState<boolean>(false);
  const [copied, setCopied] = useState<boolean>(false);
  const [showKey, setShowKey] = useState<boolean>(false);

  const apiRequestExample = `fetch('https://api.chatzuri.com/v1/chatbots', {
  method: 'GET',
  headers: {
    'Authorization': 'Bearer ${apiKey || "your_api_key_here"}',
    'Content-Type': 'application/json'
  }
})
.then(response => response.json())
.then(data => console.log(data));`;

  const generateNewApiKey = async () => {
    if (apiKey) {
      const confirm = window.confirm(
        "You already have an API key. Generating a new one will invalidate the old key. Continue?"
      );
      if (!confirm) return;
    }

    setIsGenerating(true);
    try {
      const response = await fetch(`/api/teams/${team.url}/apiKey`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to generate API key");
      }

      const data = await response.json();
      setApiKey(data.apiKey);
      toast.success(data.message || "New API key generated successfully");
    } catch (error) {
      toast.error(
        error instanceof Error ? error.message : "Failed to generate API key"
      );
    } finally {
      setIsGenerating(false);
    }
  };

  const copyToClipboard = () => {
    if (!apiKey) return;
    navigator.clipboard.writeText(apiKey);
    setCopied(true);
    toast.success("API key copied to clipboard");
    setTimeout(() => setCopied(false), 2000);
  };

  const toggleVisibility = () => {
    setShowKey(!showKey);
  };

  const displayKey = apiKey
    ? showKey
      ? apiKey
      : "•".repeat(Math.min(apiKey.length, 32)) // Show dots for first 32 chars
    : "";

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-3">
        <Key className="h-6 w-6 text-emerald-500" />
        <h2 className="text-2xl font-bold">Chatzuri API Keys</h2>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Manage your API keys</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid w-full items-center gap-4">
            <div className="flex flex-col space-y-1.5">
              <Label htmlFor="api-key">Your API Key</Label>
              <div className="flex items-center gap-2">
                <div className="relative flex-1">
                  <Input
                    id="api-key"
                    value={displayKey}
                    readOnly
                    placeholder="No API key generated yet"
                    className="font-mono pr-10" // Add padding for eye icon
                  />
                  {apiKey && (
                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      className="absolute cursor-pointer right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                      onClick={toggleVisibility}
                    >
                      {showKey ? (
                        <EyeOff className="h-4 w-4 text-gray-500" />
                      ) : (
                        <Eye className="h-4 w-4  text-emerald-500" />
                      )}
                    </Button>
                  )}
                </div>
                <Button
                  variant="outline"
                  size="icon"
                  className="cursor-pointer"
                  onClick={copyToClipboard}
                  disabled={!apiKey}
                >
                  {copied ? (
                    <Check className="h-4 w-4 text-emerald-500" />
                  ) : (
                    <Copy className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </div>
            <p className="text-sm text-muted-foreground">
              Keep your API key secure and don&apos;t share it publicly
            </p>
          </div>
        </CardContent>
        <CardFooter className="flex justify-start">
          <Button
            onClick={generateNewApiKey}
            disabled={isGenerating}
            className="cursor-pointer"
          >
            {isGenerating ? (
              <>
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                Generating...
              </>
            ) : (
              <>
                <Key className="h-4 w-4 mr-2" />
                Generate New API Key
              </>
            )}
          </Button>
        </CardFooter>
      </Card>

      {/* Documentation Section */}
      <Card>
        <CardHeader>
          <CardTitle>API Documentation</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <h3 className="font-medium">Base URL</h3>
              <div className="inline-flex gap-2">
                <CodeBlock language="http">
                  https://api.chatzuri.com/v1
                </CodeBlock>
                <CopyButton
                  textToCopy="https://api.chatzuri.com/v1"
                  buttonId="hhhggtffrredj"
                />
              </div>
            </div>
            <div>
              <h3 className="font-medium">Authentication</h3>
              <p className="text-sm text-muted-foreground">
                Include your API key in the Authorization header:
              </p>
              <code className="text-sm bg-muted rounded px-2 py-1 font-mono block mt-1">
                <div className="inline-flex gap-2">
                  <CodeBlock language="http">
                    {`Authorization: Bearer ${apiKey || "your_api_key_here"}`}
                  </CodeBlock>

                  <CopyButton
                    textToCopy={`Authorization: Bearer ${
                      apiKey || "your_api_key_here"
                    }`}
                    buttonId="hhhggtffrredjkkkkkkyytrdwetyu"
                  />
                </div>
              </code>
            </div>
            <div className="relative">
              <h3 className="font-medium">Example Request</h3>
              <CodeBlock language="javascript">{apiRequestExample}</CodeBlock>
              <CopyButton
                textToCopy={apiRequestExample}
                buttonId="hhhggtffrredj88765rgiojk90"
                classNames="absolute top-8 right-2"
              />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
