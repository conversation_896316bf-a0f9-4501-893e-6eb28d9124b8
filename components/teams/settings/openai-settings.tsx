"use client";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardHeader,
  CardTitle,
  CardContent,
  CardFooter,
} from "@/components/ui/card";
import { OpenAiKeyFormValues, Team } from "@/types/types";
import { openAiKeySchema } from "@/validations/validations";
import toast from "react-hot-toast";
import { AlertCircle, ArrowUpRight, Building2, Eye, EyeOff, Key } from "lucide-react";
import { useState } from "react";

export function OpenAiKeysSettings({ team }: { team: Team }) {
  const [showOpenAiKey, setShowOpenAiKey] = useState<boolean>(false);
  const form = useForm<OpenAiKeyFormValues>({
    resolver: zodResolver(openAiKeySchema),
   defaultValues: {
      openAiOrg: team.openAiOrg || "", 
      openAiKey: team.openAiKey || "", 
    },
    mode: "onChange",
    reValidateMode: "onChange",
  });

  const onSubmit = async (data: OpenAiKeyFormValues) => {
    if (!team.url) throw new Error("Teams url is required");
    try {
      const response = await fetch(`/api/teams/${team.url}/openai-keys`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || "Failed to save OpenAI credentials");
      }

      toast.success("OpenAI credentials saved successfully");
    } catch (error) {
      console.error(error);
      toast.error(
        error instanceof Error
          ? error.message
          : "Failed to save OpenAI credentials"
      );
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="text-2xl">OpenAI Keys</CardTitle>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
              <FormField
                control={form.control}
                name="openAiOrg"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>OpenAI Organization</FormLabel>
                    <FormControl>
                      <Input placeholder="org-xxxxxxxxxxxxxxxx" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="openAiKey"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>OpenAI API Key</FormLabel>
                    <div className="relative">
                      <FormControl>
                        <Input
                          type={showOpenAiKey ? "text" : "password"}
                          placeholder="sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
                          {...field}
                          className="pr-10" // Add padding for the eye icon
                        />
                      </FormControl>
                      {field.value && (
                        <button
                          type="button"
                          className="absolute cursor-pointer right-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground"
                          onClick={() => setShowOpenAiKey(!showOpenAiKey)}
                        >
                          {showOpenAiKey ? (
                            <EyeOff className="h-4 w-4" />
                          ) : (
                            <Eye className="h-4 w-4 text-emerald-500" />
                          )}
                        </button>
                      )}
                    </div>
                    <FormMessage />
                    <p className="text-sm text-muted-foreground mt-2">
                      Your key starts with &quot;sk-&quot; and is used to access
                      OpenAI services
                    </p>
                  </FormItem>
                )}
              />
              <CardFooter className="flex justify-end p-0">
                <Button
                  type="submit"
                  disabled={form.formState.isSubmitting}
                  className="cursor-pointer"
                >
                  {form.formState.isSubmitting ? "Saving..." : "Save Changes"}
                </Button>
              </CardFooter>
            </form>
          </Form>
        </CardContent>
      </Card>

      <Card className="border border-emerald-200 dark:border-emerald-900/50 bg-gradient-to-br from-white to-emerald-50 dark:from-gray-900 dark:to-emerald-900/10">
        <CardHeader className="pb-3">
          <div className="flex items-center space-x-3">
            <div className="p-2 rounded-full bg-emerald-100 dark:bg-emerald-900/30">
              <Key className="h-5 w-5 text-emerald-600 dark:text-emerald-400" />
            </div>
            <div>
              <CardTitle className="text-lg font-semibold">
                OpenAI Credentials Guide
              </CardTitle>
              <p className="text-sm text-muted-foreground">
                Where to find your API keys and organization ID
              </p>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="p-4 rounded-lg border border-emerald-100 dark:border-emerald-900/30 bg-white dark:bg-gray-800/50">
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0 p-1.5 mt-0.5 rounded-md bg-emerald-100 dark:bg-emerald-900/30">
                <Building2 className="h-4 w-4 text-emerald-600 dark:text-emerald-400" />
              </div>
              <div>
                <h3 className="font-medium flex items-center gap-2">
                  Organization ID
                  <span className="px-2 py-0.5 text-xs rounded-full bg-emerald-100 text-emerald-800 dark:bg-emerald-900/30 dark:text-emerald-300">
                    Required
                  </span>
                </h3>
                <p className="text-sm text-muted-foreground mt-1">
                  Find this in your{" "}
                  <a
                    href="https://platform.openai.com/account/org-settings"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-primary font-medium inline-flex items-center hover:underline"
                  >
                    OpenAI Organization Settings
                    <ArrowUpRight className="h-3.5 w-3.5 ml-1" />
                  </a>
                </p>
              </div>
            </div>
          </div>

          <div className="p-4 rounded-lg border border-emerald-100 dark:border-emerald-900/30 bg-white dark:bg-gray-800/50">
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0 p-1.5 mt-0.5 rounded-md bg-emerald-100 dark:bg-emerald-900/30">
                <Key className="h-4 w-4 text-emerald-600 dark:text-emerald-400" />
              </div>
              <div>
                <h3 className="font-medium flex items-center gap-2">
                  API Key
                  <span className="px-2 py-0.5 text-xs rounded-full bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-300">
                    Secret
                  </span>
                </h3>
                <p className="text-sm text-muted-foreground mt-1">
                  Create or manage keys in your{" "}
                  <a
                    href="https://platform.openai.com/account/api-keys"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-primary font-medium inline-flex items-center hover:underline"
                  >
                    OpenAI API Keys
                    <ArrowUpRight className="h-3.5 w-3.5 ml-1" />
                  </a>{" "}
                  dashboard
                </p>
                <p className="text-xs text-amber-600 dark:text-amber-400 mt-2 flex items-start">
                  <AlertCircle className="h-3.5 w-3.5 mr-1.5 mt-0.5 flex-shrink-0" />
                  Keep your API key secure and never share it publicly
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
