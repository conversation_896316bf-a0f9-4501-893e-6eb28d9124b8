"use client";

import { useState } from "react";
import {
  Users,
  MoreVertical,
  UserPlus,
  Loader2,
  X,
  Plus,
  Trash2,
} from "lucide-react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { Separator } from "@/components/ui/separator";
import { Team } from "@/types/types";
import { RoleEnum, StatusEnum } from "@/enums/enums";
import { useSession } from "next-auth/react";
import { ITeam } from "@/models/teams";
import { InviteMemberModal } from "@/components/modals/invite-member-modal";
import { useTeamMembers } from "@/hooks/use-team-member-mutations";
import { motion } from "framer-motion";

interface PaginationData {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  totalItems: number;
}

interface MembersSettingsProps {
  team: Team;
  pagination: PaginationData;
  onPageChange: (page: number) => void;
}

export function MembersSettings({
  team,
  pagination,
  onPageChange,
}: MembersSettingsProps) {
  const [isInviteModalOpen, setIsInviteModalOpen] = useState<boolean>(false);
  const [updatingMemberId, setUpdatingMemberId] = useState<string | null>(null);
  const [removingMemberId, setRemovingMemberId] = useState<string | null>(null);
  const [confirmOpen, setConfirmOpen] = useState(false);
  const [selectedMemberId, setSelectedMemberId] = useState<string | null>(null);
  const [confirmCancelOpen, setConfirmCancelOpen] = useState(false);
  const [cancelingInviteId, setCancelingInviteId] = useState<string | null>(
    null
  );

  const { data } = useSession();

  const {
    members,
    isLoading: isMemberLoading,
    pagination: serverPagination,
    updateMemberRole,
    removeMember,
    cancelInvite,
  } = useTeamMembers({
    slug: team.url,
    initialPagination: pagination,
  });


  const MemberSkeleton = () => (
    <tr className="border-b animate-pulse">
      <td className="px-6 py-4">
        <div className="flex items-center gap-3">
          <div className="rounded-full h-10 w-10 bg-gray-200 dark:bg-gray-700"></div>
          <div className="space-y-2">
            <div className="h-4 w-32 bg-gray-200 dark:bg-gray-700 rounded"></div>
            <div className="h-3 w-24 bg-gray-200 dark:bg-gray-700 rounded"></div>
          </div>
        </div>
      </td>
      <td className="px-6 py-4">
        <div className="h-6 w-20 bg-gray-200 dark:bg-gray-700 rounded-full"></div>
      </td>
      <td className="px-6 py-4">
        <div className="h-6 w-16 bg-gray-200 dark:bg-gray-700 rounded-full"></div>
      </td>
      <td className="px-6 py-4">
        <div className="h-4 w-24 bg-gray-200 dark:bg-gray-700 rounded"></div>
      </td>
      <td className="px-6 py-4 text-right">
        <div className="h-8 w-8 bg-gray-200 dark:bg-gray-700 rounded-md"></div>
      </td>
    </tr>
  );

  // Add pagination controls
  const PaginationControls = () => (
    <div className="flex items-center justify-between mt-4">
      <Button
        variant="outline"
        disabled={serverPagination?.page === 1}
        className="cursor-pointer"
        onClick={() => onPageChange(serverPagination?.page - 1)}
      >
        Previous
      </Button>
      <span className="text-sm text-muted-foreground">
        Page {serverPagination?.page} of {serverPagination?.totalPages}
      </span>
      <Button
        variant="outline"
        className="cursor-pointer"
        disabled={serverPagination?.page >= serverPagination?.totalPages}
        onClick={() => onPageChange(serverPagination?.page + 1)}
      >
        Next
      </Button>
    </div>
  );

  const handleRoleChange = async (
    memberId: string,
    newRole: "member" | "admin"
  ) => {
    if (memberId) {
      try {
        setUpdatingMemberId(memberId);
        await updateMemberRole.mutateAsync({ memberId, role: newRole });
      } finally {
        setUpdatingMemberId(null);
      }
    }
  };

  const handleRemoveMember = async (memberId: string) => {
    if (memberId) {
      try {
        setRemovingMemberId(memberId);
        await removeMember.mutateAsync(memberId);
      } finally {
        setRemovingMemberId(null);
        setConfirmOpen(false);
      }
    }
  };

  // const handleConfirmRemove = () => {
  //   if (selectedMemberId) {
  //     handleRemoveMember(selectedMemberId);
  //     setSelectedMemberId(null);
  //   }
  //   setConfirmOpen(false);
  // };

  const handleCancelInvite = async (inviteId: string) => {
    if (inviteId) {
      try {
        setCancelingInviteId(inviteId);
        await cancelInvite.mutateAsync(inviteId);
      } finally {
        setCancelingInviteId(null);
        setConfirmCancelOpen(false);
      }
    }
  };

  // const handleConfirmCancel = () => {
  //   if (selectedMemberId) {
  //     handleCancelInvite(selectedMemberId);
  //     setSelectedMemberId(null);
  //   }
  //   setConfirmCancelOpen(false);
  // };

  const currentUserId = data?.user.id;
  const currentUserMember = members.find(
    (member) => String(member.userId) === String(currentUserId)
  );
  const currentUserRole = currentUserMember?.role;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <Users className="h-6 w-6 text-emerald-500" />
            Team Members
          </h2>
          <p className="text-muted-foreground">
            Manage your team members and their permissions
          </p>
        </div>
        <Button
          onClick={() => setIsInviteModalOpen(true)}
          className="cursor-pointer"
        >
          <UserPlus className="h-4 w-4 mr-2" />
          Invite Member
        </Button>
      </div>

      <Separator />

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {/* Total Members - Loading State */}
        <div className="border rounded-lg p-4 bg-gradient-to-br from-emerald-50 to-emerald-100 dark:from-emerald-900/20 dark:to-emerald-800/20">
          <h3 className="text-sm font-medium text-emerald-800 dark:text-emerald-200">
            {isMemberLoading ? (
              <span className="inline-block h-5 w-24 bg-emerald-200 dark:bg-emerald-800 rounded animate-pulse" />
            ) : (
              "Total Members"
            )}
          </h3>
          <p className="text-2xl font-bold text-emerald-600 dark:text-emerald-400">
            {isMemberLoading ? (
              <span className="inline-block h-8 w-12 bg-emerald-200 dark:bg-emerald-800 rounded animate-pulse" />
            ) : (
              serverPagination.totalPages?? 0
            )}
          </p>
        </div>

        {/* Active - Loading State */}
        <div className="border rounded-lg p-4 bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20">
          <h3 className="text-sm font-medium text-blue-800 dark:text-blue-200">
            {isMemberLoading ? (
              <span className="inline-block h-5 w-16 bg-blue-200 dark:bg-blue-800 rounded animate-pulse" />
            ) : (
              "Active"
            )}
          </h3>
          <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">
            {isMemberLoading ? (
              <span className="inline-block h-8 w-10 bg-blue-200 dark:bg-blue-800 rounded animate-pulse" />
            ) : (
              members.filter((m) => m.status === StatusEnum.ACCEPTED).length
            )}
          </p>
        </div>

        {/* Pending - Loading State */}
        <div className="border rounded-lg p-4 bg-gradient-to-br from-amber-50 to-amber-100 dark:from-amber-900/20 dark:to-amber-800/20">
          <h3 className="text-sm font-medium text-amber-800 dark:text-amber-200">
            {isMemberLoading ? (
              <span className="inline-block h-5 w-20 bg-amber-200 dark:bg-amber-800 rounded animate-pulse" />
            ) : (
              "Pending"
            )}
          </h3>
          <p className="text-2xl font-bold text-amber-600 dark:text-amber-400">
            {isMemberLoading ? (
              <span className="inline-block h-8 w-8 bg-amber-200 dark:bg-amber-800 rounded animate-pulse" />
            ) : (
              members.filter((m) => m.status === StatusEnum.PENDING).length
            )}
          </p>
        </div>
      </div>

      {/* Members Table */}
      <div className="border rounded-lg overflow-hidden">
        <div className="relative overflow-x-auto">
          <table className="w-full text-sm text-left">
            <thead className="text-xs uppercase bg-gray-50 dark:bg-gray-800">
              <tr>
                <th scope="col" className="px-6 py-3">
                  Member
                </th>
                <th scope="col" className="px-6 py-3">
                  Status
                </th>
                <th scope="col" className="px-6 py-3">
                  Role
                </th>
                <th scope="col" className="px-6 py-3">
                  Joined
                </th>
                <th scope="col" className="px-6 py-3 text-right">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody>
              {isMemberLoading ? (
                <>
                  {[...Array(pagination.limit)].map((_, i) => (
                    <MemberSkeleton key={`skeleton-${i}`} />
                  ))}
                </>
              ) : members.length > 0 ? (
                members.map((member) => {
                  const isCurrentUser =
                    String(member.userId) === String(currentUserId);
                  const isTeamOwner =
                    typeof member.teamId === "object" &&
                    "ownerId" in member.teamId &&
                    String((member.teamId as ITeam).ownerId) ===
                      String(currentUserId);

                  // Determine if current user can manage this member
                  const canManageMember =
                    member.status === StatusEnum.ACCEPTED &&
                    ((currentUserRole === RoleEnum.OWNER && !isCurrentUser) || // Owner can manage everyone except themselves
                      (currentUserRole === RoleEnum.ADMIN &&
                        member.role === RoleEnum.MEMBER &&
                        !isCurrentUser)); // Admin can only manage members (not admins or owners)
                  const memberId = String(member._id);
                  const isPending = member.status === StatusEnum.PENDING;

                  return (
                    <tr
                      key={memberId}
                      className={`border-b hover:bg-gray-50 dark:hover:bg-gray-800/50 ${
                        updatingMemberId === memberId ||
                        removingMemberId === memberId
                          ? "opacity-70 pointer-events-none"
                          : ""
                      }`}
                    >
                      {/* Avatar, Name, Email */}
                      <td className="px-6 py-4">
                        <div className="flex items-center gap-3">
                          <Avatar>
                            <AvatarImage src={member?.avatar} />
                            <AvatarFallback>
                              {(member?.name ?? "TM")
                                .split(" ")
                                .map((n: string) => n[0])
                                .join("")}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <p className="font-medium">{member.name}</p>
                            <p className="text-muted-foreground text-sm">
                              {member.email}
                            </p>
                            {isCurrentUser && (
                              <span className="text-xs text-blue-500">
                                (You)
                              </span>
                            )}
                          </div>
                        </div>
                      </td>

                      {/* Status Badge */}
                      <td className="px-6 py-4">
                        <Badge
                          variant={
                            member.status === StatusEnum.ACCEPTED
                              ? "default"
                              : "secondary"
                          }
                        >
                          {member.status === StatusEnum.ACCEPTED ? (
                            <span className="flex items-center">
                              <span className="h-2 w-2 rounded-full bg-emerald-500 mr-2" />
                              Active
                            </span>
                          ) : (
                            <span className="flex items-center">
                              <span className="h-2 w-2 rounded-full bg-amber-500 mr-2" />
                              Pending
                            </span>
                          )}
                        </Badge>
                      </td>

                      {/* Role Badge with Loading State */}
                      <td className="px-6 py-4">
                        {updatingMemberId === memberId ? (
                          <div className="flex items-center gap-2">
                            <Loader2 className="h-4 w-4 animate-spin" />
                            <span>Updating...</span>
                          </div>
                        ) : (
                          <Badge
                            variant={
                              member.role === RoleEnum.OWNER
                                ? "default"
                                : member.role === RoleEnum.ADMIN
                                ? "secondary"
                                : "outline"
                            }
                          >
                            {member.role.charAt(0).toUpperCase() +
                              member.role.slice(1)}
                            {isTeamOwner && " (Owner)"}
                          </Badge>
                        )}
                      </td>

                      {/* Invited Date */}
                      <td className="px-6 py-4">
                        {new Date(member.invitedAt).toLocaleDateString(
                          "en-US",
                          {
                            year: "numeric",
                            month: "short",
                            day: "numeric",
                          }
                        )}
                      </td>

                      {/* Actions Dropdown */}
                      <td className="px-6 py-4 text-right">
                        {removingMemberId === memberId ||
                        cancelingInviteId === memberId ? (
                          <div className="flex justify-end">
                            <Loader2 className="h-5 w-5 animate-spin text-red-500" />
                          </div>
                        ) : canManageMember ||
                          (isPending &&
                            String(member.invitedBy) ===
                              String(currentUserId)) ? (
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button
                                variant="ghost"
                                size="icon"
                                className="rounded-full cursor-pointer"
                                disabled={updatingMemberId === memberId}
                              >
                                <MoreVertical className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              {/* Cancel Invite option - shown for pending invites where current user is the inviter */}
                              {isPending &&
                                String(member.invitedBy) ===
                                  String(currentUserId) && (
                                  <DropdownMenuItem
                                    onClick={() => {
                                      setSelectedMemberId(memberId);
                                      setConfirmCancelOpen(true);
                                    }}
                                    disabled={cancelingInviteId === memberId}
                                    className="text-amber-600 dark:text-amber-400 focus:text-amber-600 dark:focus:text-amber-400 cursor-pointer hover:bg-amber-50 dark:hover:bg-amber-900/20"
                                  >
                                    {cancelingInviteId === memberId ? (
                                      <span className="flex items-center gap-2">
                                        <Loader2 className="h-3 w-3 animate-spin" />
                                        Canceling...
                                      </span>
                                    ) : (
                                      <>
                                        <X className="h-4 w-4 mr-2" />
                                        Cancel Invite
                                      </>
                                    )}
                                  </DropdownMenuItem>
                                )}

                              {/* Role management options - shown for accepted members */}
                              {!isPending && (
                                <>
                                  {/* Owner can promote/demote */}
                                  {currentUserRole === RoleEnum.OWNER && (
                                    <>
                                      {member.role === RoleEnum.MEMBER && (
                                        <DropdownMenuItem
                                          onClick={() =>
                                            handleRoleChange(
                                              memberId,
                                              RoleEnum.ADMIN
                                            )
                                          }
                                          disabled={
                                            updatingMemberId === memberId
                                          }
                                        >
                                          {updatingMemberId === memberId ? (
                                            <span className="flex items-center gap-2">
                                              <Loader2 className="h-3 w-3 animate-spin" />
                                              Making Admin...
                                            </span>
                                          ) : (
                                            "Make Admin"
                                          )}
                                        </DropdownMenuItem>
                                      )}
                                      {member.role === RoleEnum.ADMIN && (
                                        <DropdownMenuItem
                                          onClick={() =>
                                            handleRoleChange(
                                              memberId,
                                              RoleEnum.MEMBER
                                            )
                                          }
                                          disabled={
                                            updatingMemberId === memberId
                                          }
                                        >
                                          {updatingMemberId === memberId ? (
                                            <span className="flex items-center gap-2">
                                              <Loader2 className="h-3 w-3 animate-spin" />
                                              Making Member...
                                            </span>
                                          ) : (
                                            "Make Member"
                                          )}
                                        </DropdownMenuItem>
                                      )}
                                    </>
                                  )}

                                  {/* Admin can promote members to admin */}
                                  {currentUserRole === RoleEnum.ADMIN &&
                                    member.role === RoleEnum.MEMBER && (
                                      <DropdownMenuItem
                                        onClick={() =>
                                          handleRoleChange(
                                            memberId,
                                            RoleEnum.ADMIN
                                          )
                                        }
                                        disabled={updatingMemberId === memberId}
                                      >
                                        Make Admin
                                      </DropdownMenuItem>
                                    )}

                                  {/* Remove option (except for owners and current user) */}
                                  {member.role !== RoleEnum.OWNER &&
                                    !isCurrentUser && (
                                      <DropdownMenuItem
                                        onClick={() => {
                                          setSelectedMemberId(memberId);
                                          setConfirmOpen(true);
                                        }}
                                        disabled={removingMemberId === memberId}
                                        className="text-red-600 dark:text-red-400 focus:text-red-600 dark:focus:text-red-400 cursor-pointer hover:bg-red-50 dark:hover:bg-red-900/20"
                                      >
                                        <Trash2 className="h-4 w-4 mr-2" />
                                        Remove
                                      </DropdownMenuItem>
                                    )}
                                </>
                              )}
                            </DropdownMenuContent>
                          </DropdownMenu>
                        ) : null}
                      </td>

                      {/* Cancel Invite Confirmation Dialog */}
                      <Dialog
                        open={confirmCancelOpen}
                        onOpenChange={setConfirmCancelOpen}
                      >
                        <DialogContent>
                          <DialogHeader>
                            <DialogTitle>Cancel Invitation?</DialogTitle>
                            <DialogDescription>
                              Are you sure you want to cancel this invitation?
                              The user will no longer be able to join the team.
                            </DialogDescription>
                          </DialogHeader>
                          <DialogFooter>
                            <Button
                              variant="outline"
                              disabled={cancelInvite.isPending}
                              onClick={() => setConfirmCancelOpen(false)}
                            >
                              No, Keep It
                            </Button>
                            <Button
                              variant="destructive"
                              disabled={cancelInvite.isPending}
                              onClick={() => {
                                if (selectedMemberId) {
                                  handleCancelInvite(selectedMemberId);
                                }
                              }}
                            >
                              {cancelInvite.isPending ? (
                                <>
                                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                  Canceling...
                                </>
                              ) : (
                                "Yes, Cancel Invite"
                              )}
                            </Button>
                          </DialogFooter>
                        </DialogContent>
                      </Dialog>

                      {/* Remove Member Confirmation Dialog */}
                      <Dialog open={confirmOpen} onOpenChange={setConfirmOpen}>
                        <DialogContent>
                          <DialogHeader>
                            <DialogTitle>Remove Member?</DialogTitle>
                            <DialogDescription>
                              Are you sure you want to remove this member? This
                              action cannot be undone.
                            </DialogDescription>
                          </DialogHeader>
                          <DialogFooter>
                            <Button
                              variant="outline"
                              onClick={() => setConfirmOpen(false)}
                            >
                              Cancel
                            </Button>
                            <Button
                              variant="destructive"
                              onClick={() => {
                                if (selectedMemberId) {
                                  handleRemoveMember(selectedMemberId);
                                }
                              }}
                            >
                              Remove
                            </Button>
                          </DialogFooter>
                        </DialogContent>
                      </Dialog>
                    </tr>
                  );
                })
              ) : (
                <tr>
                  <td colSpan={5} className="py-12 text-center">
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.4 }}
                      className="flex flex-col items-center justify-center space-y-4"
                    >
                      <div className="relative h-16 w-16 text-muted-foreground">
                        <motion.div
                          className="p-4 rounded-full bg-primary/10"
                          animate={{
                            scale: [1, 1.05, 1],
                          }}
                          transition={{
                            repeat: Infinity,
                            duration: 3,
                            ease: "easeInOut",
                          }}
                        >
                          <UserPlus
                            className="h-20 w-20 text-primary"
                            strokeWidth={1.5}
                          />
                        </motion.div>
                      </div>

                      <div className="space-y-2">
                        <h3 className="text-lg font-medium">No members yet</h3>
                        <p className="text-sm text-muted-foreground max-w-md">
                          Invite teammates to collaborate by clicking the
                          &quot;Invite&quot; button above
                        </p>
                      </div>

                      <motion.div
                        whileHover={{ scale: 1.03 }}
                        whileTap={{ scale: 0.97 }}
                      >
                        <Button
                          variant="outline"
                          className="gap-2"
                          onClick={() => setIsInviteModalOpen(true)}
                        >
                          <Plus className="h-4 w-4" />
                          Invite Members
                        </Button>
                      </motion.div>
                    </motion.div>
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>

      <PaginationControls />

      {/* Invite Member Modal */}
      <InviteMemberModal
        isOpen={isInviteModalOpen}
        onOpenChange={setIsInviteModalOpen}
        onInviteSuccess={() => setIsInviteModalOpen(false)}
        teamUrl={team.url}
      />
    </div>
  );
}