"use client";
import { useState } from "react";
import { motion } from "framer-motion";
import { <PERSON><PERSON>ard, FileText, Landmark, RefreshCw, Plus } from "lucide-react";
import toast from "react-hot-toast";
import { BillingForm } from "@/components/forms/billing-form";
import {
  BillingEmailValues,
  BillingFormValues,
  TaxFormValues,
} from "@/types/types";
import { TaxForm } from "@/components/forms/tax-form";
import { BillingEmailForm } from "@/components/forms/billing-email-from";
import { useUpdateBilling } from "@/hooks/use-billing";
import { useSession } from "next-auth/react";
import { useBillingEmail } from "@/hooks/use-billing-email";
import { useTaxInfo } from "@/hooks/use-tax-info";
import { PaymentMethodDialog } from "@/components/dialogs/payment-method";
import { PaymentMethodsTable } from "@/components/tables/payment-methods";

type BillingSectionProps = {
  team: {
    url: string;
  };
  subscription: {
    planName?: string;
    end: string | number | Date;
    monthlyMessageCredits?: number;
  };
  teamBilling?: {
    taxType?: string;
    taxID?: string;
  };
};

const BillingSettings = ({ subscription }: BillingSectionProps) => {
  const [isBillSubmitting, setIsBillSubmitting] = useState<boolean>(false);
  const [dialogOpen, setDialogOpen] = useState<boolean>(false);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const { mutate, isPending } = useUpdateBilling();
  const [submitError, setSubmitError] = useState<Error | null>(null);
  const { updateEmail } = useBillingEmail();
  const { updateTaxInfo } = useTaxInfo();

  const { data } = useSession();

  async function handleBillSubmit(values: BillingEmailValues) {
    setIsBillSubmitting(true);
    try {
      await updateEmail(values.billingEmail);
      toast.success(
        `Billing email updated successfully: ${values.billingEmail}`
      );
    } catch (error) {
      console.error("Failed to update billing email:", error);
      setSubmitError(error as Error);
      let errorMessage = "Failed to update billing email";
      if (error instanceof Error) {
        errorMessage = error.message;
      } else if (typeof error === "string") {
        errorMessage = error;
      }

      toast.error(errorMessage, {
        duration: 5000, // Show for 5 seconds
      });
    } finally {
      setIsBillSubmitting(false);
    }
  }

  async function handleTaxSubmit(values: TaxFormValues) {
    setIsSubmitting(true);
    try {
      await updateTaxInfo(values);
      toast.success("Tax information updated successfully");
    } catch (error) {
      console.error("Failed to update billing email:", error);
      setSubmitError(error as Error);
      let errorMessage = "Failed to update billing email";
      if (error instanceof Error) {
        errorMessage = error.message;
      } else if (typeof error === "string") {
        errorMessage = error;
      }

      toast.error(errorMessage, {
        duration: 5000, // Show for 5 seconds
      });
    } finally {
      setIsSubmitting(false);
    }
  }

  const handleSubmit = async (values: BillingFormValues) => {
    mutate(values, {
      onSuccess: () => {
        toast.success("Billing information saved successfully");
      },
      onError: (error) => {
        console.error("Error saving billing information:", error);
        setSubmitError(error as Error);
        toast.error(error.message || "Failed to save billing information");
      },
      onSettled: () => {
        setIsSubmitting(false);
      },
    });
  };

  // Example of loading existing data
  const defaultValues = {
    organizationName: "Acme Inc",
    countryOrRegion: "United States",
    addressLine1: "123 Main St",
    city: "New York",
    state: "NY",
    postalCode: "10001",
  };

  return (
    <div className="space-y-6">
      {/* Subscription Card */}
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        className="rounded-xl border border-gray-200 bg-white shadow-sm dark:border-gray-700 dark:bg-gray-800"
      >
        <div className="p-6">
          <div className="flex items-center space-x-3 mb-4">
            <div className="p-2 rounded-full bg-emerald-100 dark:bg-emerald-900/30">
              <CreditCard className="h-5 w-5 text-emerald-600 dark:text-emerald-400" />
            </div>
            <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-100">
              Subscription Details
            </h2>
          </div>

          <div className="bg-gray-50 dark:bg-gray-700/30 rounded-lg p-4 mb-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  Current Plan
                </h3>
                <p className="text-lg font-semibold text-emerald-600 dark:text-emerald-400">
                  {subscription.planName || "Free Tier"}
                </p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">
                  Next Renewal
                </h3>
                <p className="text-lg font-semibold">
                  {new Date(subscription.end).toLocaleDateString("en-US", {
                    year: "numeric",
                    month: "long",
                    day: "numeric",
                  })}
                </p>
              </div>
            </div>
          </div>

          <div className="text-center py-4 bg-emerald-50 dark:bg-emerald-900/10 rounded-lg">
            <p className="text-emerald-600 dark:text-emerald-400 font-medium">
              To manage or upgrade your team&apos;s subscription, visit the
              &quot;Plans&quot; tab
            </p>
          </div>
        </div>
      </motion.div>

      {/* Usage Card */}
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.1 }}
        className="rounded-xl border border-gray-200 bg-white shadow-sm dark:border-gray-700 dark:bg-gray-800"
      >
        <div className="p-6">
          <div className="flex items-center space-x-3 mb-4">
            <div className="p-2 rounded-full bg-blue-100 dark:bg-blue-900/30">
              <RefreshCw className="h-5 w-5 text-blue-600 dark:text-blue-400" />
            </div>
            <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-100">
              Usage Summary
            </h2>
          </div>

          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-gray-600 dark:text-gray-300">
                Credits Consumed
              </span>
              <span className="font-semibold">0</span>
            </div>

            <div className="flex justify-between items-center">
              <span className="text-gray-600 dark:text-gray-300">
                Monthly Limit
              </span>
              <span className="font-semibold">
                {subscription.monthlyMessageCredits}
              </span>
            </div>

            <div className="bg-blue-50 dark:bg-blue-900/10 p-4 rounded-lg">
              <p className="text-sm text-blue-600 dark:text-blue-400">
                Your credits renew at the start of every calendar month. Any
                unused credits will roll over to the next month.
              </p>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Billing Details Form */}
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.2 }}
        className="rounded-xl border border-gray-200 bg-white shadow-sm dark:border-gray-700 dark:bg-gray-800"
      >
        <div className="p-6">
          <div className="flex items-center space-x-3 mb-6">
            <div className="p-2 rounded-full bg-purple-100 dark:bg-purple-900/30">
              <Landmark className="h-5 w-5 text-purple-600 dark:text-purple-400" />
            </div>
            <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-100">
              Billing Details
            </h2>
          </div>

          <BillingForm
            defaultValues={defaultValues}
            onSubmit={handleSubmit}
            isSubmitting={isPending}
            error={submitError}
            onErrorDismiss={() => setSubmitError(null)}
          />
        </div>
      </motion.div>

      {/* Billing Email Form */}
      <BillingEmailForm
        onSubmit={handleBillSubmit}
        isSubmitting={isBillSubmitting}
        defaultEmail={data?.user.email ?? "<EMAIL>"}
        error={submitError}
        onErrorDismiss={() => setSubmitError(null)}
      />

      {/* Tax Information Form */}
      <TaxForm
        onSubmit={handleTaxSubmit}
        isSubmitting={isSubmitting}
        error={submitError}
        onErrorDismiss={() => setSubmitError(null)}
        defaultValues={{
          type: "VAT",
          id: "GB123456789",
        }}
      />

      {/* Payment Methods */}
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.5 }}
        className="rounded-xl border border-gray-200 bg-white shadow-sm dark:border-gray-700 dark:bg-gray-800"
      >
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-3">
              <div className="p-2 rounded-full bg-indigo-100 dark:bg-indigo-900/30">
                <CreditCard className="h-5 w-5 text-indigo-600 dark:text-indigo-400" />
              </div>
              <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-100">
                Payment Methods
              </h2>
            </div>
            <button
              onClick={() => setDialogOpen(true)}
              className="inline-flex cursor-pointer items-center rounded-lg bg-emerald-600 px-3 py-2 text-sm font-medium text-white hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2"
            >
              <Plus className="h-4 w-4 mr-1" />
              Add Payment Method
            </button>
          </div>
          {/* List of payment methods would go here */}
          <PaymentMethodDialog open={dialogOpen} onOpenChange={setDialogOpen} />
          <PaymentMethodsTable />
        </div>
      </motion.div>

      {/* Billing History */}
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.6 }}
        className="rounded-xl border border-gray-200 bg-white shadow-sm dark:border-gray-700 dark:bg-gray-800"
      >
        <div className="p-6">
          <div className="flex items-center space-x-3 mb-6">
            <div className="p-2 rounded-full bg-blue-100 dark:bg-blue-900/30">
              <FileText className="h-5 w-5 text-blue-600 dark:text-blue-400" />
            </div>
            <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-100">
              Billing History
            </h2>
          </div>

          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-700/30">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Invoice
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Date
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Amount
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Download
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200 dark:bg-gray-800 dark:divide-gray-700">
                <tr>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-emerald-600 dark:text-emerald-400">
                    INV-2023-05-001
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    May 15, 2023
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    $29.00
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-emerald-100 text-emerald-800 dark:bg-emerald-900/30 dark:text-emerald-400">
                      Paid
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <a
                      href="#"
                      className="text-emerald-600 hover:text-emerald-900 dark:text-emerald-400 dark:hover:text-emerald-300"
                    >
                      PDF
                    </a>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default BillingSettings;
