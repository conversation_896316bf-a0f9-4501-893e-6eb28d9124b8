import { useEffect, useState } from "react";
import {
  RefreshCw,
  Globe,
  BadgeCheck,
  Bot,
  MessageSquare,
  Plus,
  AlertCircle,
  Loader2,
} from "lucide-react";
import {
  <PERSON>,
  Card<PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON>ontent,
  CardFooter,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, Tabs<PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Switch } from "@/components/ui/switch";
import toast from "react-hot-toast";
import { Badge } from "@/components/ui/badge";
import FAQSection from "@/components/FAQs/faqs-section";
import { PlanFormModal } from "@/components/modals/plans-form-modal";
import { Role } from "@/enums/enums";
import { useSession } from "next-auth/react";
import { usePlans } from "@/hooks/use-plans";
import { Skeleton } from "@/components/ui/skeleton";
import type { Plan, PlansSettingsProps, Subscription } from "@/types/types";
import PlanCard from "@/components/cards/plan-card";
import ConfirmDialog from "@/components/modals/confirm-dialog";
import { convertCurrency, getExchangeRates } from "@/utils/currency";

//eslint-disable-next-line @typescript-eslint/no-unused-vars
export function PlansSettings({ team }: PlansSettingsProps) {
  const [billingInterval, setBillingInterval] = useState<"monthly" | "yearly">(
    "monthly"
  );
  const [currency, setCurrency] = useState<"KES" | "USD">("KES");
  const [isLoading, setIsLoading] = useState(false);
  const [isPlanModalOpen, setIsPlanModalOpen] = useState(false);
  const [currentEditingPlan, setCurrentEditingPlan] = useState<Plan | null>(
    null
  );
  const { data: session } = useSession();
  const isAdmin = session?.user?.role === Role.ADMIN;
  const [isConfirmOpen, setIsConfirmOpen] = useState<boolean>(false);
  const [deleteId, setDeleteId] = useState<string | null>(null);

  const [exchangeRates, setExchangeRates] = useState<Record<string, number>>({
    USD: 1,
    KES: 130,
  });

  // Fetch exchange rates on mount and when currency changes
  useEffect(() => {
    const fetchRates = async () => {
      const rates = await getExchangeRates();
      setExchangeRates(rates);
    };
    fetchRates();
  }, [currency]);

  // Use the plans hook
  const {
    plans = [],
    isLoading: isLoadingPlans,
    error: plansError,
    createPlan,
    updatePlan,
    deletePlan,
    isCreating,
    isUpdating,
    isDeleting,
    refetch,
  } = usePlans();

  const subscription: Subscription = {
    autoRechargeCredits: true,
    extraMessageCredits: 0,
    extraChatbots: 0,
    hasCustomDomains: false,
    hasCustomBranding: false,
  };

  //eslint-disable-next-line @typescript-eslint/no-unused-vars
  const handleUpgrade = (planId: string) => {
    setIsLoading(true);
    // Simulate API call
    setTimeout(() => {
      toast.success(`Processing upgrade to selected plan`);
      setIsLoading(false);
    }, 1000);
  };

  const toggleAutoRecharge = () => {
    // Implement auto-recharge toggle logic
    toast.success(
      subscription.autoRechargeCredits
        ? "Auto-recharge disabled"
        : "Auto-recharge enabled"
    );
  };

  const handleOpenPlanModal = (plan: Plan | null) => {
    setCurrentEditingPlan(plan);
    setIsPlanModalOpen(true);
  };

  const handleClosePlanModal = () => {
    setIsPlanModalOpen(false);
    setCurrentEditingPlan(null);
  };

  const handlePlanSubmit = async (planData: Plan) => {
    try {
      if (currentEditingPlan) {
        // Update existing plan
        await updatePlan({ id: currentEditingPlan._id, ...planData });
        toast.success("Plan updated successfully");
      } else {
        // Create new plan
        await createPlan(planData);
        toast.success("Plan created successfully");
      }
      handleClosePlanModal();
    } catch (error) {
      toast.error("Failed to save plan");
      console.error("Error saving plan:", error);
    }
  };

  const confirmLabel = (
    <span className="flex items-center gap-2">
      <Loader2 className="animate-spin h-4 w-4" />
      Deleting...
    </span>
  );

  const handleDeleteClick = (id: string) => {
    setDeleteId(id);
    setIsConfirmOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (deleteId) {
      try {
        await deletePlan(deleteId);
        toast.success("Plan deleted successfully");
      } catch (error) {
        toast.error("Failed to delete plan");
        console.error("Error deleting plan:", error);
      } finally {
        setIsConfirmOpen(false);
        setDeleteId(null);
      }
    }
  };

  const handleCancelDelete = () => {
    setIsConfirmOpen(false);
    setDeleteId(null);
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-3xl font-bold tracking-tight">
          Manage your Team&apos;s subscription plan
        </h1>
        <p className="text-muted-foreground mt-2">
          Choose the perfect plan for your team&apos;s needs
        </p>
      </div>

      {isAdmin && (
        <div className="flex justify-end">
          <Button
            onClick={() => handleOpenPlanModal(null)}
            className="cursor-pointer"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add New Plan
          </Button>
        </div>
      )}

      {/* Billing Interval Toggle */}
      <div className="flex justify-center gap-4">
        <Tabs
          value={billingInterval}
          onValueChange={(value: string) =>
            setBillingInterval(value as "monthly" | "yearly")
          }
          className="w-[400px]"
        >
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="monthly" className="cursor-pointer">
              Monthly
            </TabsTrigger>
            <TabsTrigger value="yearly" className="cursor-pointer">
              Annual (Save 20%)
            </TabsTrigger>
          </TabsList>
        </Tabs>

        <Tabs
          value={currency}
          onValueChange={(value: string) => setCurrency(value as "KES" | "USD")}
          className="w-[200px]"
        >
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="KES" className="cursor-pointer">
              KES
            </TabsTrigger>
            <TabsTrigger value="USD" className="cursor-pointer">
              USD
            </TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      {/* Plans Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {isLoadingPlans ? (
          // Loading Skeletons
          Array.from({ length: 4 }).map((_, index) => (
            <Card key={`skeleton-${index}`} className="overflow-hidden">
              <CardHeader>
                <Skeleton className="h-6 w-3/4 rounded-md" />
                <Skeleton className="h-4 w-1/2 rounded-md" />
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="text-center space-y-2">
                  <Skeleton className="h-8 w-3/4 mx-auto rounded-md" />
                  <Skeleton className="h-4 w-1/3 mx-auto rounded-md" />
                </div>

                <div className="space-y-3">
                  {Array.from({ length: 5 }).map((_, i) => (
                    <div
                      key={`feature-${i}`}
                      className="flex items-center gap-2"
                    >
                      <Skeleton className="h-4 w-4 rounded-full" />
                      <Skeleton className="h-4 w-3/4 rounded-md" />
                    </div>
                  ))}
                </div>
              </CardContent>
              <CardFooter>
                <Skeleton className="h-10 w-full rounded-md" />
              </CardFooter>
            </Card>
          ))
        ) : plansError ? (
          // Error State
          <div className="col-span-full py-12 flex flex-col items-center justify-center space-y-4 text-center">
            <AlertCircle className="h-12 w-12 text-red-500" />
            <h3 className="text-xl font-semibold">Failed to load plans</h3>
            <p className="text-muted-foreground max-w-md">
              We couldn&apos;t load the available plans. Please try again later.
            </p>
            <Button
              variant="outline"
              onClick={() => refetch()}
              className="mt-4"
            >
              <RefreshCw className="mr-2 h-4 w-4" />
              Retry
            </Button>
          </div>
        ) : (
          // Actual Plans
          plans.map((plan) => {
            const convertedPlan = {
              ...plan,
              priceMonthly: convertCurrency(
                plan.priceMonthly,
                "USD", // Assuming prices are stored in USD
                currency,
                exchangeRates
              ),
              priceYearly: convertCurrency(
                plan.priceYearly,
                "USD", // Assuming prices are stored in USD
                currency,
                exchangeRates
              ),
            };

            return (
              <PlanCard
                key={plan._id}
                plan={convertedPlan}
                isAdmin={isAdmin}
                currency={currency}
                billingInterval={billingInterval}
                isUpdating={isUpdating}
                isDeleting={isDeleting}
                isLoading={isLoading}
                onEdit={handleOpenPlanModal}
                onDelete={handleDeleteClick}
                onUpgrade={handleUpgrade}
              />
            );
          })
        )}
      </div>

      {/* Additional Features */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mt-8">
        {/* Auto-recharge */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium flex items-center">
              <RefreshCw className="h-4 w-4 mr-2 text-emerald-500" />
              Auto-recharge credits
            </CardTitle>
            <Switch
              checked={subscription.autoRechargeCredits}
              onCheckedChange={toggleAutoRecharge}
            />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">$9 per 1000 credits</div>
            <p className="text-xs text-muted-foreground mt-2">
              Automatically add credits when balance is low
            </p>
          </CardContent>
          <CardFooter>
            <Button variant="outline" className="w-full cursor-pointer">
              Configure Threshold
            </Button>
          </CardFooter>
        </Card>

        {/* Extra Messages */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium flex items-center">
              <MessageSquare className="h-4 w-4 mr-2 text-blue-500" />
              Extra message credits
            </CardTitle>
            <Badge
              variant={
                subscription.extraMessageCredits > 0 ? "default" : "secondary"
              }
            >
              {subscription.extraMessageCredits > 0 ? "Active" : "Inactive"}
            </Badge>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">$5 per 1000 messages</div>
            <p className="text-xs text-muted-foreground mt-2">
              Additional non-expiring message credits
            </p>
          </CardContent>
          <CardFooter>
            <Button className="w-full cursor-pointer">
              Get Extra Messages
            </Button>
          </CardFooter>
        </Card>

        {/* Extra Chatbots */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium flex items-center">
              <Bot className="h-4 w-4 mr-2 text-purple-500" />
              Extra chatbots
            </CardTitle>
            <Badge
              variant={subscription.extraChatbots > 0 ? "default" : "secondary"}
            >
              {subscription.extraChatbots > 0
                ? `${subscription.extraChatbots} active`
                : "Inactive"}
            </Badge>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">$5 per chatbot</div>
            <p className="text-xs text-muted-foreground mt-2">
              Additional chatbots beyond your plan limit
            </p>
          </CardContent>
          <CardFooter>
            <Button className="w-full cursor-pointer">
              Get Extra Chatbots
            </Button>
          </CardFooter>
        </Card>

        {/* Custom Domains */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium flex items-center">
              <Globe className="h-4 w-4 mr-2 text-amber-500" />
              Custom Domains
            </CardTitle>
            <Badge
              variant={subscription.hasCustomDomains ? "default" : "secondary"}
            >
              {subscription.hasCustomDomains ? "Active" : "Inactive"}
            </Badge>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">$27 / month</div>
            <p className="text-xs text-muted-foreground mt-2">
              Use your own domains for chatbots
            </p>
          </CardContent>
          <CardFooter>
            {subscription.hasCustomDomains ? (
              <Button variant="outline" className="w-full cursor-pointer">
                Manage Domains
              </Button>
            ) : (
              <Button className="w-full cursor-pointer">
                Get Custom Domains
              </Button>
            )}
          </CardFooter>
        </Card>

        {/* Remove Branding */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium flex items-center">
              <BadgeCheck className="h-4 w-4 mr-2 text-rose-500" />
              Remove Branding
            </CardTitle>
            <Badge
              variant={subscription.hasCustomBranding ? "default" : "secondary"}
            >
              {subscription.hasCustomBranding ? "Active" : "Inactive"}
            </Badge>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">$27 / month</div>
            <p className="text-xs text-muted-foreground mt-2">
              Remove &quot;Powered by Chatzuri&quot; branding
            </p>
          </CardContent>
          <CardFooter>
            {subscription.hasCustomBranding ? (
              <Button variant="outline" className="w-full cursor-pointer">
                Customize Branding
              </Button>
            ) : (
              <Button className="w-full cursor-pointer">Remove Branding</Button>
            )}
          </CardFooter>
        </Card>
      </div>

      {/* Plan Form Modal */}
      <PlanFormModal
        open={isPlanModalOpen}
        onClose={handleClosePlanModal}
        plan={currentEditingPlan}
        onSubmit={handlePlanSubmit}
        isLoading={isCreating || isUpdating}
      />
      <FAQSection />

      <ConfirmDialog
        open={isConfirmOpen}
        title="Delete this plan"
        message="Are you sure you want to delete this plan? This action cannot be undone."
        confirmLabel={isDeleting ? confirmLabel : "Delete"}
        onConfirm={handleConfirmDelete}
        onCancel={handleCancelDelete}
        isDeleting={isDeleting}
      />
    </div>
  );
}
