"use client";
import { motion } from "framer-motion";
import React from "react";
import Image from "next/image";
import {
  containerVariants,
  itemVariants,
  mergedVariants,
} from "@/variants/variants";

interface TeamMember {
  name: string;
  role: string;
  image: string;
  bio: string;
  social: string;
}

export default function Teams() {
  const teamMembers: TeamMember[] = [
    {
      name: "<PERSON>",
      role: "CEO & Founder",
      image: "/images/webp/male2.webp",
      bio: "Visionary leader with 10+ years in tech innovation",
      social: "@lawerencenjenga",
    },
    {
      name: "<PERSON>",
      role: "CTO",
      image: "/images/webp/male1.webp",
      bio: "Tech architect building scalable solutions",
      social: "@sarahakoth",
    },
    {
      name: "<PERSON>",
      role: "Lead Designer",
      image: "/images/webp/female2.webp",
      bio: "Creating beautiful, intuitive user experiences",
      social: "@michaelonyango",
    },
    {
      name: "<PERSON>",
      role: "Marketing Director",
      image: "/images/webp/female_user1.webp",
      bio: "Storyteller and brand strategist",
      social: "@emmarok",
    },
  ];
  return (
    <motion.div
      initial="hidden"
      whileInView="visible"
      variants={containerVariants}
      viewport={{ once: true }}
      className="container mx-auto px-6 py-16"
    >
      <div className="text-center mb-16">
        <motion.h2
          variants={itemVariants}
          className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4"
        >
          Meet the{" "}
          <span className="text-emerald-600 dark:text-emerald-400">Minds</span>{" "}
          Behind Chatzuri
        </motion.h2>
        <motion.p
          variants={itemVariants}
          className="max-w-3xl mx-auto text-lg text-gray-600 dark:text-gray-300"
        >
          A diverse team united by passion for AI that makes a difference.
        </motion.p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
        {teamMembers.map((member, index) => (
          <motion.div
            key={index}
            variants={mergedVariants}
            className="bg-white dark:bg-gray-800 cursor-pointer rounded-xl shadow-lg overflow-hidden border border-gray-100 dark:border-gray-700"
            whileHover="hover"
          >
            <div className="relative h-64 overflow-hidden">
              <Image
                src={member.image}
                alt={member.name}
                width={400}
                height={400}
                priority={false}
                loading="lazy"
                quality={85}
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 400px"
                placeholder="blur"
                blurDataURL="/images/placeholder.jpg"
                className="w-full h-full object-cover transition-transform duration-500 hover:scale-105"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-gray-900/70 via-transparent to-transparent"></div>
              <div className="absolute bottom-4 left-4">
                <h3 className="text-2xl font-bold text-white">{member.name}</h3>
                <p className="text-emerald-300">{member.role}</p>
              </div>
            </div>
            <div className="p-6">
              <p className="text-gray-600 dark:text-gray-300 mb-4">
                {member.bio}
              </p>
              <p className="text-sm text-emerald-600 dark:text-emerald-400">
                {member.social}
              </p>
            </div>
          </motion.div>
        ))}
      </div>
    </motion.div>
  );
}
