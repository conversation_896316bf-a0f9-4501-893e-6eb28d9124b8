"use client";

import { motion } from "framer-motion";
import React, { useState } from "react";
import Image from "next/image";
import { itemVariants } from "@/variants/variants";
import { signIn } from "next-auth/react";
import toast from "react-hot-toast";
import { useSearchParams } from "next/navigation";

export default function GoogleSigning() {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const searchParams = useSearchParams();
  const callbackUrl = searchParams.get("callbackUrl") || "/dashboard";

  const handleGoogleSignIn = async () => {
    setIsLoading(true);
    
    toast.promise(
      signIn("google", { callbackUrl: callbackUrl}),
      {
        loading: 'Signing in with Google...',
        success: 'Signed in successfully! Redirecting...',
        error: (err) => {
          setIsLoading(false);
          return `Sign in failed: ${err.message || 'Unknown error'}`;
        },
      }
    );
  };

  return (
    <motion.div variants={itemVariants} className="mb-6">
      <button
        onClick={handleGoogleSignIn}
        disabled={isLoading}
        className={`flex cursor-pointer items-center justify-center gap-3 w-full px-4 py-3 border border-gray-200 dark:border-gray-700 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors ${
          isLoading ? "opacity-70 cursor-not-allowed" : ""
        }`}
      >
        {isLoading ? (
          <div className="animate-spin">
            {/* Replace with your loading icon */}
            <svg className="w-5 h-5" viewBox="0 0 24 24">
              <circle
                className="opacity-25"
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                strokeWidth="4"
              ></circle>
              <path
                className="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
              ></path>
            </svg>
          </div>
        ) : (
          <>
            <Image
              src="/brand-logos/google-icon.svg"
              alt="Google"
              width={20}
              height={20}
            />
            <span>Continue with Google</span>
          </>
        )}
      </button>
    </motion.div>
  );
}
