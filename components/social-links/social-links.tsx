"use client";
import { motion } from "framer-motion";
import React from "react";
import Image from "next/image";
import { SocialIcons } from "@/types/types";

export default function SocialLinks() {
  const socialIcons: SocialIcons[] = [
    { name: "Facebook", icon: "/brand-logos/facebook-4.svg" },
    { name: "Instagram", icon: "/brand-logos/instagram-2-1.svg" },
    { name: "GitHub", icon: "/brand-logos/github.svg" },
    { name: "Behance", icon: "/brand-logos/behance.svg" },
    { name: "Pinterest", icon: "/brand-logos/pinterest-p.svg" },
    { name: "Twitter", icon: "/brand-logos/twitter.svg" },
    { name: "Dribbble", icon: "/brand-logos/dribbble-icon-1.svg" },
  ];

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        ease: "easeOut",
        duration: 0.5,
      },
    },
  };

  return (
    <div className="bg-gray-50 dark:bg-gray-700/30 rounded-xl p-8 sm:p-12 text-center">
      <motion.div
        className="flex flex-wrap justify-center gap-4 sm:gap-6"
        variants={itemVariants}
      >
        {socialIcons.map((social) => (
          <motion.a
            key={social.name}
            href="#"
            className="p-2 rounded-lg bg-white dark:bg-gray-800 shadow-sm hover:shadow-md transition-shadow"
            whileHover={{ y: -3, scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Image
              src={social.icon}
              alt={social.name}
              width={30}
              height={30}
              className="w-7 h-7 object-contain"
            />
          </motion.a>
        ))}
      </motion.div>
    </div>
  );
}
