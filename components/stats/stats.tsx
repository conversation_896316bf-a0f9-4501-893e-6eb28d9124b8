"use client";
import { containerVariants, mergedVariants } from "@/variants/variants";
import { motion } from "framer-motion";
import { Award, Globe, Sparkles, Users } from "lucide-react";
import React from "react";

interface Stat {
  value: string;
  label: string;
  icon: React.ReactNode;
}

export default function StatsPage() {
  const stats: Stat[] = [
    {
      value: "50+",
      label: "Team Members",
      icon: <Users className="w-6 h-6" />,
    },
    { value: "15", label: "Countries", icon: <Globe className="w-6 h-6" /> },
    {
      value: "100%",
      label: "Passionate",
      icon: <Sparkles className="w-6 h-6" />,
    },
    {
      value: "10+",
      label: "Industry Awards",
      icon: <Award className="w-6 h-6" />,
    },
  ];
  return (
    <motion.div
      initial="hidden"
      whileInView="visible"
      variants={containerVariants}
      viewport={{ once: true }}
      className="mt-3 container mx-auto px-6"
    >
      <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <motion.div
            key={index}
            variants={mergedVariants}
            className="bg-white dark:bg-gray-800 cursor-pointer rounded-xl p-6 shadow-md border border-gray-100 dark:border-gray-700 text-center"
            whileHover={{
              y: -5,
              boxShadow: "0 10px 25px -5px rgba(5, 150, 105, 0.1)",
              scale: 1.02,
            }}
          >
            <div className="flex justify-center mb-4 text-emerald-500 dark:text-emerald-400">
              {stat.icon}
            </div>
            <h3 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
              {stat.value}
            </h3>
            <p className="text-gray-600 dark:text-gray-300">{stat.label}</p>
          </motion.div>
        ))}
      </div>
    </motion.div>
  );
}
