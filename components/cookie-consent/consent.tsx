"use client";

import Link from "next/link";
import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { hasCookie, setCookie } from "cookies-next";
import { <PERSON><PERSON> } from "lucide-react";

export default function Consent() {
  const [open, setOpen] = useState<boolean>(false);
  const [showConsent, setShowConsent] = useState<boolean>(false);

  useEffect(() => {
    if (hasCookie("localConsent")) {
      setShowConsent(true);
    } else {
      setTimeout(() => setOpen(true), 2000);
    }
  }, []);

  const acceptCookie = () => {
    setCookie("localConsent", "true", { maxAge: 60 * 60 * 24 * 30 });
    setOpen(false);
  };

  const declineCookie = () => {
    setOpen(false);
  };

  if (showConsent) {
    return null;
  }

  return (
    <AnimatePresence>
      {open && (
        <>
          {/* Semi-transparent overlay */}
          <motion.div
            className="fixed inset-0 bg-black/30 backdrop-blur-sm z-[100]"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
          />

          {/* Consent banner */}
          <motion.div
            className="fixed bottom-0 left-0 right-0 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 shadow-lg z-[200]"
            initial={{ y: 100, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            exit={{ y: 100, opacity: 0 }}
            transition={{ duration: 0.3, ease: "easeOut" }}
          >
            <div className="container mx-auto px-4 py-3">
              <div className="flex flex-col md:flex-row items-center justify-between gap-4">
                <motion.div
                  whileHover={{ scale: 1.02 }}
                  className="flex items-start gap-3  bg-white dark:bg-gray-800 rounded-lg shadow-lg"
                >
                  <motion.div
                    initial={{ scale: 0.8 }}
                    animate={{ scale: 1 }}
                    transition={{ delay: 0.2, type: "spring" }}
                    className="p-2 bg-emerald-100 dark:bg-emerald-900/50 rounded-full"
                  >
                    <Cookie className="w-5 h-5 text-emerald-600 dark:text-emerald-400" />
                  </motion.div>

                  <motion.div
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.3 }}
                    className="text-sm text-gray-600 dark:text-gray-300"
                  >
                    <p>
                      We use cookies to enhance your experience on our website.
                      By continuing, you agree to our use of cookies. Learn more
                      in our{" "}
                      <motion.span whileHover={{ scale: 1.05 }}>
                        <Link
                          href="/privacy"
                          className="text-emerald-600 dark:text-emerald-400 hover:underline font-medium"
                        >
                          Privacy Policy
                        </Link>
                      </motion.span>
                      .
                    </p>
                  </motion.div>
                </motion.div>
                <div className="flex gap-3 flex-shrink-0">
                  <motion.button
                    onClick={declineCookie}
                    className="px-4 py-2 text-sm cursor-pointer font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
                    whileHover={{ scale: 1.03 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    Decline
                  </motion.button>
                  <motion.button
                    onClick={acceptCookie}
                    className="px-4 py-2 cursor-pointer text-sm font-medium text-white bg-emerald-600 hover:bg-emerald-700 rounded-lg transition-colors shadow-sm"
                    whileHover={{ scale: 1.03 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    Accept All
                  </motion.button>
                </div>
              </div>
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
}
