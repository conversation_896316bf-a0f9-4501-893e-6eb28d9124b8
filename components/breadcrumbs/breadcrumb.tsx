"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { ChevronRight } from "lucide-react";
import { Fragment } from "react";
import { motion } from "framer-motion";

export function Breadcrumb() {
  const pathname = usePathname();
  const paths = pathname.split("/").filter(Boolean);

  // Custom mapping for specific path segments
  const pathNames: Record<string, string> = {
    dashboard: "Dashboard",
    profile: "Profile",
    settings: "Settings",
    account: "Account",
    projects: "Projects",
    analytics: "Analytics",
  };

  if (paths.length === 0) return null;

  return (
    <div className="container mx-auto flex mt-3 mb-8">
      <motion.nav 
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        className="flex items-center bg-white dark:bg-gray-800 rounded-full px-6 py-3 shadow-sm dark:shadow-gray-700/20 border border-gray-100 dark:border-gray-700"
      >
        {paths.map((path, index) => {
          const isLast = index === paths.length - 1;
          const displayName =
            pathNames[path] ||
            path.replace(/-/g, " ").replace(/\b\w/g, (l) => l.toUpperCase());
          const href = `/${paths.slice(0, index + 1).join("/")}`;

          return (
            <Fragment key={path}>
              {index === 0 ? (
                <Link 
                  href="/dashboard" 
                  className="flex items-center hover:text-primary transition-colors"
                >
                  <svg 
                    xmlns="http://www.w3.org/2000/svg" 
                    width="20" 
                    height="20" 
                    viewBox="0 0 24 24" 
                    fill="none" 
                    stroke="currentColor" 
                    strokeWidth="2" 
                    strokeLinecap="round" 
                    strokeLinejoin="round"
                    className="mr-2"
                  >
                    <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                    <polyline points="9 22 9 12 15 12 15 22"></polyline>
                  </svg>
                  Home
                </Link>
              ) : (
                <div className="flex items-center">
                  <ChevronRight className="h-4 w-4 mx-3 text-gray-400" />
                  {isLast ? (
                    <span className="text-primary font-medium">{displayName}</span>
                  ) : (
                    <Link
                      href={href}
                      className="hover:text-primary transition-colors"
                    >
                      {displayName}
                    </Link>
                  )}
                </div>
              )}
            </Fragment>
          );
        })}
      </motion.nav>
    </div>
  );
}