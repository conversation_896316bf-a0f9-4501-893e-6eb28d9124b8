"use client";

import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import {
  X,
  Search,
  Zap,
  Database,
  Code,
  MessageSquare,
  Clock,
  Filter,
  GitBranch,
  Send,
  Bot,
  Brain,
  Layers,
  Settings,
  Globe,
  Calculator,
  Mail,
  FileText,
  Image,
  Calendar,
  Webhook,
  Timer
} from "lucide-react";

interface NodePaletteProps {
  isOpen: boolean;
  onClose: () => void;
  onNodeDragStart: (nodeType: any) => void;
}

// Enhanced node types with cluster support and memory
const nodeCategories = {
  triggers: {
    label: "Triggers",
    color: "blue",
    nodes: [
      { 
        type: "webhook", 
        label: "Webhook", 
        icon: Webhook, 
        description: "HTTP webhook trigger",
        config: { method: "POST", path: "/webhook" }
      },
      { 
        type: "schedule", 
        label: "Schedule", 
        icon: Timer, 
        description: "Time-based trigger",
        config: { interval: "daily", time: "09:00" }
      },
      { 
        type: "message", 
        label: "Message", 
        icon: MessageSquare, 
        description: "Chat message trigger",
        config: { channels: ["all"] }
      },
    ]
  },
  
  ai_agents: {
    label: "AI Agents",
    color: "emerald",
    nodes: [
      { 
        type: "ai_agent", 
        label: "AI Agent", 
        icon: Bot, 
        description: "Intelligent AI agent with tools and memory",
        isCluster: true,
        config: { 
          model: "gpt-4o", 
          temperature: 0.7,
          systemPrompt: "You are a helpful assistant.",
          tools: ["web_search", "calculator"],
          memory: { type: "vector", size: 1000 }
        }
      },
      { 
        type: "conversation_agent", 
        label: "Conversation Agent", 
        icon: MessageSquare, 
        description: "Specialized for conversations",
        isCluster: true,
        config: { 
          model: "gpt-4o", 
          conversationMemory: true,
          tools: ["memory", "web_search"]
        }
      },
      { 
        type: "research_agent", 
        label: "Research Agent", 
        icon: Database, 
        description: "Specialized for research tasks",
        isCluster: true,
        config: { 
          model: "gpt-4o", 
          tools: ["web_search", "document_reader", "memory"],
          memory: { type: "vector", size: 5000 }
        }
      },
    ]
  },

  tools: {
    label: "Tools",
    color: "green",
    nodes: [
      { 
        type: "web_search", 
        label: "Web Search", 
        icon: Globe, 
        description: "Search the internet",
        config: { engine: "google", maxResults: 10 }
      },
      { 
        type: "calculator", 
        label: "Calculator", 
        icon: Calculator, 
        description: "Perform calculations",
        config: { precision: 10 }
      },
      {
        type: "memory",
        label: "Memory",
        icon: Brain,
        description: "Store and retrieve information",
        config: { type: "vector", embeddings: "openai" }
      },
      { 
        type: "email", 
        label: "Send Email", 
        icon: Mail, 
        description: "Send email messages",
        config: { provider: "smtp" }
      },
      { 
        type: "document_reader", 
        label: "Document Reader", 
        icon: FileText, 
        description: "Read and analyze documents",
        config: { formats: ["pdf", "docx", "txt"] }
      },
      { 
        type: "image_generator", 
        label: "Image Generator", 
        icon: Image, 
        description: "Generate images from text",
        config: { model: "dall-e-3", size: "1024x1024" }
      },
    ]
  },

  logic: {
    label: "Logic",
    color: "purple",
    nodes: [
      { 
        type: "condition", 
        label: "Condition", 
        icon: GitBranch, 
        description: "If/then logic branching",
        config: { operator: "equals", value: "" }
      },
      { 
        type: "filter", 
        label: "Filter", 
        icon: Filter, 
        description: "Filter data based on criteria",
        config: { criteria: [] }
      },
      { 
        type: "transform", 
        label: "Transform", 
        icon: Settings, 
        description: "Transform data structure",
        config: { mapping: {} }
      },
      { 
        type: "loop", 
        label: "Loop", 
        icon: GitBranch, 
        description: "Iterate over data",
        config: { maxIterations: 100 }
      },
    ]
  },

  actions: {
    label: "Actions",
    color: "orange",
    nodes: [
      { 
        type: "response", 
        label: "Response", 
        icon: MessageSquare, 
        description: "Send response message",
        config: { template: "Hello!" }
      },
      { 
        type: "api_call", 
        label: "API Call", 
        icon: Send, 
        description: "Make HTTP request",
        config: { method: "GET", url: "" }
      },
      { 
        type: "database", 
        label: "Database", 
        icon: Database, 
        description: "Database operation",
        config: { operation: "select", table: "" }
      },
      { 
        type: "notification", 
        label: "Notification", 
        icon: Send, 
        description: "Send notification",
        config: { channels: ["email", "slack"] }
      },
    ]
  },
};

const colorClasses = {
  blue: "from-blue-100 to-blue-200 dark:from-blue-900/30 dark:to-blue-800/40 border-blue-300 dark:border-blue-600",
  emerald: "from-emerald-100 via-blue-100 to-purple-200 dark:from-emerald-900/30 dark:via-blue-900/30 dark:to-purple-800/40 border-emerald-300 dark:border-emerald-600",
  green: "from-green-100 to-green-200 dark:from-green-900/30 dark:to-green-800/40 border-green-300 dark:border-green-600",
  purple: "from-purple-100 to-purple-200 dark:from-purple-900/30 dark:to-purple-800/40 border-purple-300 dark:border-purple-600",
  orange: "from-orange-100 to-orange-200 dark:from-orange-900/30 dark:to-orange-800/40 border-orange-300 dark:border-orange-600",
};

export const EnhancedNodePalette = ({ isOpen, onClose, onNodeDragStart }: NodePaletteProps) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);

  // Clear search when palette opens to prevent autofill issues
  useEffect(() => {
    if (isOpen) {
      setSearchTerm("");
    }
  }, [isOpen]);

  const filteredCategories = Object.entries(nodeCategories).reduce((acc, [key, category]) => {
    const filteredNodes = category.nodes.filter(node =>
      node.label.toLowerCase().includes(searchTerm.toLowerCase()) ||
      node.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      node.type.toLowerCase().includes(searchTerm.toLowerCase())
    );

    if (filteredNodes.length > 0) {
      acc[key] = { ...category, nodes: filteredNodes };
    }

    return acc;
  }, {} as typeof nodeCategories);

  const handleDragStart = (node: any, category: string) => {
    const nodeData = {
      ...node,
      data: {
        label: node.label,
        config: node.config || {},
        isCluster: node.isCluster || false,
        memory: node.config?.memory || null,
        tools: node.config?.tools || [],
      }
    };
    onNodeDragStart(nodeData);
  };

  if (!isOpen) return null;

  return (
    <motion.div
      initial={{ x: -320 }}
      animate={{ x: 0 }}
      exit={{ x: -320 }}
      className="w-96 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 flex flex-col h-full"
    >
      {/* Header */}
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Node Palette
          </h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <input
            type="search"
            id="workflow-node-search"
            name="workflow-node-search"
            placeholder="Search nodes..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            autoComplete="off"
            autoCorrect="off"
            autoCapitalize="off"
            spellCheck="false"
            data-form-type="search"
            data-lpignore="true"
            role="searchbox"
            aria-label="Search workflow nodes"
            className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
          />
        </div>
      </div>

      {/* Categories */}
      <div className="flex-1 overflow-y-auto p-4 space-y-6">
        {Object.entries(filteredCategories).map(([categoryKey, category]) => (
          <div key={categoryKey}>
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3 uppercase tracking-wide flex items-center">
              {category.label}
              <span className="ml-2 text-xs bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 px-2 py-1 rounded-full">
                {category.nodes.length}
              </span>
            </h4>
            
            <div className="space-y-2">
              {category.nodes.map((node) => (
                <div
                  key={node.type}
                  draggable
                  onDragStart={() => handleDragStart(node, categoryKey)}
                  className={`flex items-start p-3 bg-gradient-to-br ${colorClasses[category.color as keyof typeof colorClasses]} rounded-lg border cursor-grab hover:shadow-md transition-all duration-200 active:cursor-grabbing w-full`}
                >
                  <div className="flex items-center space-x-3 flex-1">
                    <div className="flex-shrink-0">
                      <node.icon className="h-5 w-5 text-gray-700 dark:text-gray-300" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2">
                        <div className="text-sm font-medium text-gray-900 dark:text-white">
                          {node.label}
                        </div>
                        {node.isCluster && (
                          <Layers className="h-3 w-3 text-blue-600 dark:text-blue-400" title="Cluster Node" />
                        )}
                        {node.config?.memory && (
                          <Brain className="h-3 w-3 text-purple-600 dark:text-purple-400" title="Has Memory" />
                        )}
                      </div>
                      <div className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                        {node.description}
                      </div>
                      {node.config?.tools && node.config.tools.length > 0 && (
                        <div className="text-xs text-blue-600 dark:text-blue-400 mt-1">
                          Tools: {node.config.tools.slice(0, 2).join(', ')}
                          {node.config.tools.length > 2 && ` +${node.config.tools.length - 2}`}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>

      {/* Footer */}
      <div className="p-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800/50">
        <div className="text-xs text-gray-500 dark:text-gray-400 text-center">
          Drag nodes to the canvas to build your workflow
        </div>
      </div>
    </motion.div>
  );
};

export default EnhancedNodePalette;
