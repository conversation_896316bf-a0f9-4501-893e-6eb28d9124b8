"use client";

import React, { useState, useRef, useCallback } from "react";
import { motion } from "framer-motion";
import { WorkflowNodeComponent } from "./workflow-node";

interface WorkflowNodeData {
  id: string;
  type: string;
  position: { x: number; y: number };
  data: {
    label: string;
    config: Record<string, any>;
  };
}

interface WorkflowEdgeData {
  id: string;
  source: string;
  target: string;
  sourceHandle?: string;
  targetHandle?: string;
}

interface WorkflowCanvasProps {
  nodes: WorkflowNodeData[];
  edges: WorkflowEdgeData[];
  onNodesChange: (nodes: WorkflowNodeData[]) => void;
  onEdgesChange: (edges: WorkflowEdgeData[]) => void;
  onNodeClick: (node: WorkflowNodeData) => void;
  onNodeDelete: (nodeId: string) => void;
}

export const WorkflowCanvas = ({
  nodes,
  edges,
  onNodesChange,
  onEdgesChange,
  onNodeClick,
  onNodeDelete,
}: WorkflowCanvasProps) => {
  const [draggedNode, setDraggedNode] = useState<string | null>(null);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });
  const [canvasOffset, setCanvasOffset] = useState({ x: 0, y: 0 });
  const [scale, setScale] = useState(1);
  const canvasRef = useRef<HTMLDivElement>(null);

  const handleNodeMouseDown = useCallback((nodeId: string, event: React.MouseEvent) => {
    event.preventDefault();
    event.stopPropagation();
    
    const node = nodes.find(n => n.id === nodeId);
    if (!node) return;

    const rect = event.currentTarget.getBoundingClientRect();
    setDragOffset({
      x: event.clientX - rect.left,
      y: event.clientY - rect.top,
    });
    setDraggedNode(nodeId);
  }, [nodes]);

  const handleMouseMove = useCallback((event: React.MouseEvent) => {
    if (!draggedNode || !canvasRef.current) return;

    const canvasRect = canvasRef.current.getBoundingClientRect();
    const newX = (event.clientX - canvasRect.left - dragOffset.x - canvasOffset.x) / scale;
    const newY = (event.clientY - canvasRect.top - dragOffset.y - canvasOffset.y) / scale;

    const updatedNodes = nodes.map(node =>
      node.id === draggedNode
        ? { ...node, position: { x: newX, y: newY } }
        : node
    );

    onNodesChange(updatedNodes);
  }, [draggedNode, dragOffset, canvasOffset, scale, nodes, onNodesChange]);

  const handleMouseUp = useCallback(() => {
    setDraggedNode(null);
  }, []);

  const handleWheel = useCallback((event: React.WheelEvent) => {
    event.preventDefault();
    const delta = event.deltaY > 0 ? 0.9 : 1.1;
    setScale(prev => Math.max(0.1, Math.min(3, prev * delta)));
  }, []);

  const handleCanvasMouseDown = useCallback((event: React.MouseEvent) => {
    if (event.target === canvasRef.current) {
      // Start panning
      const startX = event.clientX - canvasOffset.x;
      const startY = event.clientY - canvasOffset.y;

      const handlePanMove = (e: MouseEvent) => {
        setCanvasOffset({
          x: e.clientX - startX,
          y: e.clientY - startY,
        });
      };

      const handlePanEnd = () => {
        document.removeEventListener('mousemove', handlePanMove);
        document.removeEventListener('mouseup', handlePanEnd);
      };

      document.addEventListener('mousemove', handlePanMove);
      document.addEventListener('mouseup', handlePanEnd);
    }
  }, [canvasOffset]);

  // Render connection lines between nodes
  const renderConnections = () => {
    return edges.map(edge => {
      const sourceNode = nodes.find(n => n.id === edge.source);
      const targetNode = nodes.find(n => n.id === edge.target);
      
      if (!sourceNode || !targetNode) return null;

      const sourceX = sourceNode.position.x + 100; // Node width / 2
      const sourceY = sourceNode.position.y + 50; // Node height / 2
      const targetX = targetNode.position.x + 100;
      const targetY = targetNode.position.y + 50;

      return (
        <svg
          key={edge.id}
          className="absolute inset-0 pointer-events-none"
          style={{
            transform: `translate(${canvasOffset.x}px, ${canvasOffset.y}px) scale(${scale})`,
            transformOrigin: '0 0',
          }}
        >
          <defs>
            <marker
              id={`arrowhead-${edge.id}`}
              markerWidth="10"
              markerHeight="7"
              refX="9"
              refY="3.5"
              orient="auto"
            >
              <polygon
                points="0 0, 10 3.5, 0 7"
                fill="#6b7280"
                className="dark:fill-gray-400"
              />
            </marker>
          </defs>
          <path
            d={`M ${sourceX} ${sourceY} Q ${(sourceX + targetX) / 2} ${sourceY - 50} ${targetX} ${targetY}`}
            stroke="#6b7280"
            strokeWidth="2"
            fill="none"
            markerEnd={`url(#arrowhead-${edge.id})`}
            className="dark:stroke-gray-400"
          />
        </svg>
      );
    });
  };

  return (
    <div
      ref={canvasRef}
      className="relative w-full h-full bg-gray-100 dark:bg-gray-900 overflow-hidden cursor-grab active:cursor-grabbing"
      onMouseMove={handleMouseMove}
      onMouseUp={handleMouseUp}
      onMouseDown={handleCanvasMouseDown}
      onWheel={handleWheel}
    >
      {/* Grid Background */}
      <div
        className="absolute inset-0 opacity-20"
        style={{
          backgroundImage: `
            linear-gradient(to right, #e5e7eb 1px, transparent 1px),
            linear-gradient(to bottom, #e5e7eb 1px, transparent 1px)
          `,
          backgroundSize: `${20 * scale}px ${20 * scale}px`,
          transform: `translate(${canvasOffset.x % (20 * scale)}px, ${canvasOffset.y % (20 * scale)}px)`,
        }}
      />

      {/* Connections */}
      {renderConnections()}

      {/* Nodes */}
      <div
        className="absolute inset-0"
        style={{
          transform: `translate(${canvasOffset.x}px, ${canvasOffset.y}px) scale(${scale})`,
          transformOrigin: '0 0',
        }}
      >
        {nodes.map(node => (
          <motion.div
            key={node.id}
            className="absolute"
            style={{
              left: node.position.x,
              top: node.position.y,
            }}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <WorkflowNodeComponent
              node={node}
              onMouseDown={(e) => handleNodeMouseDown(node.id, e)}
              onClick={() => onNodeClick(node)}
              onDelete={() => onNodeDelete(node.id)}
              isDragging={draggedNode === node.id}
            />
          </motion.div>
        ))}
      </div>

      {/* Empty State */}
      {nodes.length === 0 && (
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="text-center">
            <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
              <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              Start Building Your Workflow
            </h3>
            <p className="text-gray-500 dark:text-gray-400 max-w-sm">
              Add nodes from the palette to create your automated workflow. Connect them to define the flow of execution.
            </p>
          </div>
        </div>
      )}

      {/* Controls */}
      <div className="absolute bottom-4 right-4 flex flex-col space-y-2">
        <button
          onClick={() => setScale(prev => Math.min(3, prev * 1.2))}
          className="p-2 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
        >
          <svg className="w-4 h-4 text-gray-600 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
        </button>
        <button
          onClick={() => setScale(prev => Math.max(0.1, prev * 0.8))}
          className="p-2 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
        >
          <svg className="w-4 h-4 text-gray-600 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
          </svg>
        </button>
        <button
          onClick={() => {
            setScale(1);
            setCanvasOffset({ x: 0, y: 0 });
          }}
          className="p-2 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
        >
          <svg className="w-4 h-4 text-gray-600 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4" />
          </svg>
        </button>
      </div>
    </div>
  );
};
