"use client";

import React from "react";
import { motion } from "framer-motion";
import {
  Zap,
  Bot,
  Wrench,
  Globe,
  Mail,
  Clock,
  GitBranch,
  X,
  ChevronLeft,
  Webhook,
  Calendar,
  MessageSquare,
} from "lucide-react";

interface NodePaletteProps {
  onAddNode: (type: string, subtype?: string) => void;
  isOpen: boolean;
  onToggle: () => void;
}

const nodeCategories = [
  {
    title: "Triggers",
    description: "Start your workflow",
    icon: Zap,
    color: "text-green-600 dark:text-green-400",
    bgColor: "bg-green-100 dark:bg-green-900/30",
    nodes: [
      {
        type: "trigger",
        subtype: "webhook",
        label: "Webhook",
        description: "HTTP endpoint trigger",
        icon: Webhook,
      },
      {
        type: "trigger",
        subtype: "schedule",
        label: "Schedule",
        description: "Time-based trigger",
        icon: Calendar,
      },
      {
        type: "trigger",
        subtype: "email",
        label: "Email",
        description: "<PERSON><PERSON> received trigger",
        icon: Mail,
      },
      {
        type: "trigger",
        subtype: "chat",
        label: "Chat",
        description: "Chat message trigger",
        icon: MessageSquare,
      },
    ],
  },
  {
    title: "Agents",
    description: "AI-powered processing",
    icon: Bo<PERSON>,
    color: "text-blue-600 dark:text-blue-400",
    bgColor: "bg-blue-100 dark:bg-blue-900/30",
    nodes: [
      {
        type: "agent",
        label: "AI Agent",
        description: "Process with AI",
        icon: Bot,
      },
    ],
  },
  {
    title: "Tools",
    description: "Extend agent capabilities",
    icon: Wrench,
    color: "text-purple-600 dark:text-purple-400",
    bgColor: "bg-purple-100 dark:bg-purple-900/30",
    nodes: [
      {
        type: "tool",
        subtype: "web_search",
        label: "Web Search",
        description: "Search the internet",
        icon: Globe,
      },
      {
        type: "tool",
        subtype: "calculator",
        label: "Calculator",
        description: "Perform calculations",
        icon: Wrench,
      },
    ],
  },
  {
    title: "Actions",
    description: "Perform operations",
    icon: Globe,
    color: "text-orange-600 dark:text-orange-400",
    bgColor: "bg-orange-100 dark:bg-orange-900/30",
    nodes: [
      {
        type: "action",
        subtype: "http",
        label: "HTTP Request",
        description: "Make API calls",
        icon: Globe,
      },
      {
        type: "action",
        subtype: "email",
        label: "Send Email",
        description: "Send email messages",
        icon: Mail,
      },
    ],
  },
  {
    title: "Logic",
    description: "Control flow",
    icon: GitBranch,
    color: "text-indigo-600 dark:text-indigo-400",
    bgColor: "bg-indigo-100 dark:bg-indigo-900/30",
    nodes: [
      {
        type: "condition",
        label: "Condition",
        description: "Branch based on logic",
        icon: GitBranch,
      },
    ],
  },
];

export const NodePalette = ({ onAddNode, isOpen, onToggle }: NodePaletteProps) => {
  if (!isOpen) return null;

  return (
    <div className="w-full h-full flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
        <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
          Node Palette
        </h2>
        <button
          onClick={onToggle}
          className="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
        >
          <ChevronLeft className="h-5 w-5" />
        </button>
      </div>

      {/* Categories */}
      <div className="flex-1 overflow-y-auto p-4 space-y-6">
        {nodeCategories.map((category) => {
          const CategoryIcon = category.icon;
          
          return (
            <div key={category.title} className="space-y-3">
              {/* Category Header */}
              <div className="flex items-center space-x-2">
                <div className={`p-1.5 rounded-lg ${category.bgColor}`}>
                  <CategoryIcon className={`h-4 w-4 ${category.color}`} />
                </div>
                <div>
                  <h3 className="text-sm font-medium text-gray-900 dark:text-white">
                    {category.title}
                  </h3>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    {category.description}
                  </p>
                </div>
              </div>

              {/* Nodes */}
              <div className="space-y-2">
                {category.nodes.map((node) => {
                  const NodeIcon = node.icon;
                  
                  return (
                    <motion.button
                      key={`${node.type}-${node.subtype || 'default'}`}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      onClick={() => onAddNode(node.type, node.subtype)}
                      className="w-full p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg border border-gray-200 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700 hover:border-gray-300 dark:hover:border-gray-500 transition-all text-left group"
                    >
                      <div className="flex items-start space-x-3">
                        <div className={`p-2 rounded-md ${category.bgColor} group-hover:scale-110 transition-transform`}>
                          <NodeIcon className={`h-4 w-4 ${category.color}`} />
                        </div>
                        <div className="flex-1 min-w-0">
                          <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                            {node.label}
                          </h4>
                          <p className="text-xs text-gray-500 dark:text-gray-400 line-clamp-2">
                            {node.description}
                          </p>
                        </div>
                      </div>
                    </motion.button>
                  );
                })}
              </div>
            </div>
          );
        })}
      </div>

      {/* Footer */}
      <div className="p-4 border-t border-gray-200 dark:border-gray-700">
        <p className="text-xs text-gray-500 dark:text-gray-400 text-center">
          Drag nodes to the canvas or click to add at center
        </p>
      </div>
    </div>
  );
};
