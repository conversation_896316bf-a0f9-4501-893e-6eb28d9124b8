"use client";

import React, { useState, useCallback, useRef, useEffect } from "react";
import { motion } from "framer-motion";
import { Menu, X } from "lucide-react";
import toast from "react-hot-toast";
import EnhancedNodePalette from "./enhanced-node-palette";
import { NodeConfigPanel } from "./node-config-panel";
import EnhancedWorkflowCanvas from "./enhanced-workflow-canvas";
import ExecutionMonitor from "./execution-monitor";
import { isValidConnection, getConnectionValidationMessage, hasTrigger, isTriggerConnected } from "@/lib/workflow-validation";

interface WorkflowNode {
  id: string;
  type: string;
  subtype?: string;
  position: { x: number; y: number };
  data: {
    label: string;
    config: Record<string, any>;
    isCluster?: boolean;
    memory?: any;
    tools?: string[];
  };
}

interface WorkflowEdge {
  id: string;
  source: string;
  target: string;
  sourceHandle?: string;
  targetHandle?: string;
}

interface WorkflowBuilderProps {
  workflow: {
    _id: string;
    name: string;
    nodes: WorkflowNode[];
    edges: WorkflowEdge[];
  };
  onChange: (nodes: WorkflowNode[], edges: WorkflowEdge[]) => void;
  onExecute?: () => void; // Optional execution handler from parent
  teamId?: string;
}

export const WorkflowBuilder = ({ workflow, onChange, onExecute, teamId }: WorkflowBuilderProps) => {
  const [nodes, setNodes] = useState<WorkflowNode[]>(workflow.nodes || []);
  const [edges, setEdges] = useState<WorkflowEdge[]>(workflow.edges || []);
  const [selectedNode, setSelectedNode] = useState<WorkflowNode | null>(null);
  const [selectedEdge, setSelectedEdge] = useState<WorkflowEdge | null>(null);
  const [isPaletteOpen, setIsPaletteOpen] = useState(true);
  const [isConfigOpen, setIsConfigOpen] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [connectionStart, setConnectionStart] = useState<{ nodeId: string; handle: string } | null>(null);
  const [draggedNode, setDraggedNode] = useState<any>(null);

  // Execution monitoring state
  const [isExecutionMonitorVisible, setIsExecutionMonitorVisible] = useState(false);
  const [executionSteps, setExecutionSteps] = useState<any[]>([]);
  const [isWorkflowRunning, setIsWorkflowRunning] = useState(false);
  const [executionStartTime, setExecutionStartTime] = useState<Date | null>(null);
  const [currentExecutionId, setCurrentExecutionId] = useState<string | null>(null);
  const [currentExecutionType, setCurrentExecutionType] = useState<'workflow_direct' | 'workflow_async' | 'node_test'>('workflow_async');
  const [currentExecutionMode, setCurrentExecutionMode] = useState<'direct' | 'async'>('async');
  const canvasRef = useRef<HTMLDivElement>(null);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    if (!draggedNode || !canvasRef.current) return;

    const rect = canvasRef.current.getBoundingClientRect();
    const x = e.clientX - rect.left - 100; // Center the node
    const y = e.clientY - rect.top - 40;

    const newNode: WorkflowNode = {
      id: `node_${Date.now()}`,
      type: draggedNode.type,
      subtype: draggedNode.subtype,
      position: { x, y },
      data: {
        label: draggedNode.label,
        config: draggedNode.config || {},
        isCluster: draggedNode.isCluster || false,
        memory: draggedNode.data?.memory || null,
        tools: draggedNode.data?.tools || [],
      },
    };

    const newNodes = [...nodes, newNode];
    setNodes(newNodes);
    onChange(newNodes, edges);
    setDraggedNode(null);
  }, [draggedNode, nodes, edges, onChange]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
  }, []);

  const handleNodeClick = useCallback((node: WorkflowNode) => {
    setSelectedNode(node);
    setIsConfigOpen(true);
  }, []);

  const handleNodeUpdate = useCallback((nodeId: string, config: Record<string, any>) => {
    const newNodes = nodes.map(node => 
      node.id === nodeId 
        ? { ...node, data: { ...node.data, config } }
        : node
    );
    setNodes(newNodes);
    onChange(newNodes, edges);
  }, [nodes, edges, onChange]);

  const handleNodeDelete = useCallback((nodeId: string) => {
    const newNodes = nodes.filter(node => node.id !== nodeId);
    const newEdges = edges.filter(edge => edge.source !== nodeId && edge.target !== nodeId);
    setNodes(newNodes);
    setEdges(newEdges);
    onChange(newNodes, newEdges);
    if (selectedNode?.id === nodeId) {
      setSelectedNode(null);
      setIsConfigOpen(false);
    }
  }, [nodes, edges, onChange, selectedNode]);

  const handleConnectionStart = useCallback((nodeId: string, handle: string) => {
    setConnectionStart({ nodeId, handle });
    setIsConnecting(true);
  }, []);

  const handleConnectionEnd = useCallback((nodeId: string, handle: string) => {
    if (connectionStart && connectionStart.nodeId !== nodeId) {
      // Find source and target nodes
      const sourceNode = nodes.find(n => n.id === connectionStart.nodeId);
      const targetNode = nodes.find(n => n.id === nodeId);

      if (!sourceNode || !targetNode) {
        toast.error("Invalid connection: source or target node not found");
        setConnectionStart(null);
        setIsConnecting(false);
        return;
      }

      // Validate connection
      if (!isValidConnection(sourceNode.type, targetNode.type)) {
        const message = getConnectionValidationMessage(sourceNode.type, targetNode.type);
        toast.error(message);
        setConnectionStart(null);
        setIsConnecting(false);
        return;
      }

      // Create valid edge
      const newEdge: WorkflowEdge = {
        id: `edge-${connectionStart.nodeId}-${nodeId}-${Date.now()}`,
        source: connectionStart.nodeId,
        target: nodeId,
        sourceHandle: connectionStart.handle,
        targetHandle: handle,
      };

      const newEdges = [...edges, newEdge];
      setEdges(newEdges);
      onChange(nodes, newEdges);

      // Show success message for valid connection
      toast.success("Connection created successfully!");
    }

    setConnectionStart(null);
    setIsConnecting(false);
  }, [connectionStart, edges, nodes, onChange]);

  const handleEdgeDelete = useCallback((edgeId: string) => {
    const newEdges = edges.filter(edge => edge.id !== edgeId);
    setEdges(newEdges);
    onChange(nodes, newEdges);
    if (selectedEdge?.id === edgeId) {
      setSelectedEdge(null);
    }
  }, [edges, nodes, onChange, selectedEdge]);

  // Execution functions
  const startWorkflowExecution = useCallback((mode: 'direct' | 'async' = 'async') => {
    const executionId = `exec_${Date.now()}`;
    setCurrentExecutionId(executionId);
    setIsWorkflowRunning(true);
    setExecutionStartTime(new Date());
    setIsExecutionMonitorVisible(true);
    setCurrentExecutionType(mode === 'direct' ? 'workflow_direct' : 'workflow_async');
    setCurrentExecutionMode(mode);

    // Initialize execution steps from nodes
    const steps = nodes.map((node, index) => ({
      id: `step_${node.id}`,
      nodeId: node.id,
      nodeName: node.data.label,
      nodeType: node.type,
      status: index === 0 ? 'running' : 'pending',
      startTime: index === 0 ? new Date() : undefined,
      logs: [],
      isNodeTest: false
    }));

    setExecutionSteps(steps);

    // TODO: Integrate with actual workflow execution engine
    toast.success(`${mode === 'direct' ? 'Direct' : 'Asynchronous'} workflow execution started!`);
  }, [nodes]);

  const startNodeTest = useCallback((nodeId: string, nodeName: string, nodeType: string) => {
    const executionId = `test_${nodeId}_${Date.now()}`;
    setCurrentExecutionId(executionId);
    setIsWorkflowRunning(true);
    setExecutionStartTime(new Date());
    setIsExecutionMonitorVisible(true);
    setCurrentExecutionType('node_test');
    setCurrentExecutionMode('direct'); // Node tests are typically direct execution

    // Single step for node test
    const steps = [{
      id: `test_${nodeId}`,
      nodeId: nodeId,
      nodeName: nodeName,
      nodeType: nodeType,
      status: 'running' as const,
      startTime: new Date(),
      logs: [`Testing ${nodeType} node: ${nodeName}`],
      isNodeTest: true
    }];

    setExecutionSteps(steps);

    toast.success(`Testing node: ${nodeName}`);
  }, []);

  const cancelWorkflowExecution = useCallback(() => {
    setIsWorkflowRunning(false);
    setExecutionSteps(prev => prev.map(step =>
      step.status === 'running' || step.status === 'pending'
        ? { ...step, status: 'cancelled' }
        : step
    ));
    setCurrentExecutionId(null);
    toast("Workflow execution cancelled", { icon: "⏹️" });
  }, []);

  const closeExecutionMonitor = useCallback(() => {
    setIsExecutionMonitorVisible(false);
  }, []);

  // Expose execution function to parent
  const handleExecute = useCallback(() => {
    if (onExecute) {
      onExecute(); // Use parent's execution handler if provided
    } else {
      startWorkflowExecution(); // Use built-in execution
    }
  }, [onExecute, startWorkflowExecution]);

  // Make execution functions available globally on the component 
  React.useEffect(() => {
    (window as any).executeWorkflow = handleExecute;
    (window as any).executeWorkflowDirect = () => startWorkflowExecution('direct');
    (window as any).executeWorkflowAsync = () => startWorkflowExecution('async');
    (window as any).testNode = startNodeTest;
    return () => {
      delete (window as any).executeWorkflow;
      delete (window as any).executeWorkflowDirect;
      delete (window as any).executeWorkflowAsync;
      delete (window as any).testNode;
    };
  }, [handleExecute, startWorkflowExecution, startNodeTest]);

  const handleNodeDrag = useCallback((nodeId: string, position: { x: number; y: number }) => {
    const newNodes = nodes.map(node =>
      node.id === nodeId
        ? { ...node, position }
        : node
    );
    setNodes(newNodes);
    onChange(newNodes, edges);
  }, [nodes, edges, onChange]);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        if (isConnecting) {
          setIsConnecting(false);
          setConnectionStart(null);
        }
        if (selectedNode) {
          setSelectedNode(null);
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [isConnecting, selectedNode]);

  return (
    <div className="h-full flex bg-gray-50 dark:bg-gray-900 overflow-hidden relative">
      {/* Node Palette */}
      <motion.div
        initial={false}
        animate={{ width: isPaletteOpen ? 384 : 0 }}
        transition={{ duration: 0.3 }}
        className="bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 overflow-hidden flex-shrink-0"
      >
        <EnhancedNodePalette
          isOpen={isPaletteOpen}
          onClose={() => setIsPaletteOpen(false)}
          onNodeDragStart={(nodeType) => {
            setDraggedNode(nodeType);
          }}
        />
      </motion.div>

      {/* Canvas */}
      <div
        ref={canvasRef}
        className="flex-1 relative min-h-0 bg-gray-100 dark:bg-gray-900 workflow-grid"
        onDrop={handleDrop}
        onDragOver={handleDragOver}
      >
        {/* Palette Toggle Button */}
        <button
          onClick={() => setIsPaletteOpen(!isPaletteOpen)}
          className="absolute top-4 left-4 z-20 p-2 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg hover:shadow-xl transition-all duration-200"
          title={isPaletteOpen ? "Hide Node Palette" : "Show Node Palette"}
        >
          {isPaletteOpen ? (
            <X className="h-5 w-5 text-gray-600 dark:text-gray-400" />
          ) : (
            <Menu className="h-5 w-5 text-gray-600 dark:text-gray-400" />
          )}
        </button>

        {/* Drop zone indicator */}
        {draggedNode && (
          <div className="absolute inset-0 bg-emerald-500/10 border-2 border-dashed border-emerald-500 flex items-center justify-center pointer-events-none z-10">
            <div className="bg-emerald-600 text-white px-4 py-2 rounded-lg text-sm font-medium">
              Drop here to add {draggedNode.label}
            </div>
          </div>
        )}

        {/* Empty state when no nodes */}
        {nodes.length === 0 && !draggedNode && (
          <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
            <div className="text-center">
              <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center">
                <span className="text-2xl text-gray-600 dark:text-gray-300">+</span>
              </div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                Start Building Your Workflow
              </h3>
              <p className="text-gray-500 dark:text-gray-400">
                {isPaletteOpen ? "Drag nodes from the palette" : "Open the palette to add nodes"}
              </p>
            </div>
          </div>
        )}

        <EnhancedWorkflowCanvas
          nodes={nodes}
          edges={edges}
          selectedNode={selectedNode}
          onNodeClick={handleNodeClick}
          onNodeDelete={handleNodeDelete}
          onNodeUpdate={(nodeId, config) => {
            const newNodes = nodes.map(node =>
              node.id === nodeId
                ? { ...node, data: { ...node.data, config } }
                : node
            );
            setNodes(newNodes);
            onChange(newNodes, edges);
          }}
          onNodeDrag={handleNodeDrag}
          onConnectionStart={handleConnectionStart}
          onConnectionEnd={handleConnectionEnd}
          onEdgeDelete={handleEdgeDelete}
          isConnecting={isConnecting}
          connectionStart={connectionStart}
          isDraggingNode={!!draggedNode}
        />

        {/* Toggle Palette Button */}
        {!isPaletteOpen && (
          <motion.button
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            onClick={() => setIsPaletteOpen(true)}
            className="absolute top-4 left-4 z-10 p-2 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
          >
            <svg className="w-5 h-5 text-gray-600 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </motion.button>
        )}
      </div>

      {/* Node Configuration Panel - Docked Right */}
      <motion.div
        initial={false}
        animate={{ width: isConfigOpen && selectedNode ? 384 : 0 }}
        transition={{ duration: 0.3 }}
        className="bg-white dark:bg-gray-800 border-l border-gray-200 dark:border-gray-700 overflow-hidden flex-shrink-0"
      >
        {selectedNode && isConfigOpen && (
          <NodeConfigPanel
            node={selectedNode}
            isOpen={isConfigOpen}
            onUpdate={handleNodeUpdate}
            onClose={() => {
              setIsConfigOpen(false);
              setSelectedNode(null);
            }}
            onDelete={() => handleNodeDelete(selectedNode.id)}
            teamId={teamId}
          />
        )}
      </motion.div>

      {/* Execution Monitor */}
      <ExecutionMonitor
        isVisible={isExecutionMonitorVisible}
        onClose={closeExecutionMonitor}
        onCancel={cancelWorkflowExecution}
        workflowId={workflow._id}
        executionId={currentExecutionId || undefined}
        steps={executionSteps}
        isRunning={isWorkflowRunning}
        totalSteps={currentExecutionType === 'node_test' ? 1 : nodes.length}
        completedSteps={executionSteps.filter(step => step.status === 'completed').length}
        startTime={executionStartTime || undefined}
        executionType={currentExecutionType}
        executionMode={currentExecutionMode}
      />

    </div>
  );
};

export default WorkflowBuilder;
