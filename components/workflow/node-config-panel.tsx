"use client";

import React, { useState, useEffect } from "react";
import { <PERSON>, Settings, Save, Trash2, Play, Loader2 } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";

// Import our comprehensive config components
import { TriggerConfig } from "./configs/trigger-config";
import { AIAgentConfig } from "./configs/ai-agent-config";
import { ToolConfig } from "./configs/tool-config";
import { LogicConfig } from "./configs/logic-config";
import { ActionConfig } from "./configs/action-config";

interface NodeConfigPanelProps {
  node: any;
  isOpen: boolean;
  onClose: () => void;
  onUpdate: (nodeId: string, config: any) => void;
  onDelete: (nodeId: string) => void;
  teamId?: string;
}

export const NodeConfigPanel = ({
  node,
  isOpen,
  onClose,
  onUpdate,
  onDelete,
  teamId
}: NodeConfigPanelProps) => {
  const [config, setConfig] = useState(node?.data?.config || {});
  const [hasChanges, setHasChanges] = useState(false);
  const [isTestingNode, setIsTestingNode] = useState(false);
  const [testResult, setTestResult] = useState<any>(null);

  useEffect(() => {
    if (node) {
      setConfig(node.data?.config || {});
      setHasChanges(false);
    }
  }, [node]);

  const handleSave = () => {
    if (node && hasChanges) {
      onUpdate(node.id, config);
      setHasChanges(false);
    }
  };

  const handleTestNode = async () => {
    if (!node) return;

    setIsTestingNode(true);
    setTestResult(null);

    try {
      console.log("🧪 Testing node:", node.id, node.type);

      // Trigger execution monitor for node test
      if ((window as any).testNode) {
        (window as any).testNode(node.id, node.data?.label || node.type, node.type);
      }

      // Get workflow ID from URL or context
      const pathParts = window.location.pathname.split('/');
      const workflowId = pathParts[pathParts.length - 1];

      const response = await fetch(`/api/workflows/${workflowId}/nodes/${node.id}/test`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          mode: 'direct', // Node tests always use direct execution
          input: { test: true, message: 'Test execution' },
          variables: { test_mode: true }
        }),
      });

      const result = await response.json();
      setTestResult(result);

      if (result.success) {
        console.log('Node test successful:', result);
      } else {
        console.error('Node test failed:', result.error);
      }

    } catch (error) {
      console.error('Error testing node:', error);
      setTestResult({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    } finally {
      setIsTestingNode(false);
    }
  };

  const handleDelete = () => {
    if (node) {
      onDelete(node.id);
      onClose();
    }
  };

  const renderConfigFields = () => {
    if (!node) return null;

    console.log("Rendering config for node:", node);

    // Determine category based on node type
    if (['webhook', 'schedule', 'message'].includes(node.type)) {
      return (
        <TriggerConfig
          nodeType={node.type}
          config={config}
          onUpdate={(newConfig: any) => {
            setConfig((prev: any) => ({ ...prev, ...newConfig }));
            setHasChanges(true);
          }}
          teamId={teamId || ''}
        />
      );
    }
    
    if (['ai_agent', 'conversation_agent', 'research_agent'].includes(node.type)) {
      return (
        <AIAgentConfig
          nodeType={node.type}
          config={config}
          data={node.data}
          onUpdate={(newConfig: any) => {
            setConfig((prev: any) => ({ ...prev, ...newConfig }));
            setHasChanges(true);
          }}
          onDataUpdate={(_newData: any) => {
            // Handle data updates that affect the node structure
            setHasChanges(true);
          }}
          teamId={teamId || ''}
        />
      );
    }
    
    if (['web_search', 'calculator', 'memory', 'email', 'document_reader', 'image_generator', 'tool'].includes(node.type)) {
      return (
        <ToolConfig
          nodeType={node.type}
          config={config}
          onUpdate={(newConfig: any) => {
            setConfig((prev: any) => ({ ...prev, ...newConfig }));
            setHasChanges(true);
          }}
          teamId={teamId || ''}
        />
      );
    }
    
    if (['condition', 'filter', 'transform', 'loop'].includes(node.type)) {
      return (
        <LogicConfig
          nodeType={node.type}
          config={config}
          onUpdate={(newConfig: any) => {
            setConfig((prev: any) => ({ ...prev, ...newConfig }));
            setHasChanges(true);
          }}
        />
      );
    }
    
    if (['response', 'api_call', 'database', 'notification', 'action'].includes(node.type)) {
      return (
        <ActionConfig
          nodeType={node.type}
          config={config}
          onUpdate={(newConfig: any) => {
            setConfig((prev: any) => ({ ...prev, ...newConfig }));
            setHasChanges(true);
          }}
          teamId={teamId || ''}
        />
      );
    }

    // Fallback for unknown types
    return (
      <div className="space-y-4">
        <div className="text-sm text-gray-500 mb-4">
          Node type: {node.type} | Available config keys: {Object.keys(config).join(', ')}
        </div>
        <div>
          <Label htmlFor="node_name">Node Name</Label>
          <Input
            id="node_name"
            value={node.data?.label || ''}
            onChange={(_e) => {
              // Update node label
              setHasChanges(true);
            }}
            placeholder="Enter node name"
          />
        </div>
        <div>
          <Label htmlFor="raw_config">Raw Configuration (JSON)</Label>
          <Textarea
            id="raw_config"
            value={JSON.stringify(config, null, 2)}
            onChange={(e) => {
              try {
                const parsed = JSON.parse(e.target.value);
                setConfig(parsed);
                setHasChanges(true);
              } catch (error) {
                // Invalid JSON, don't update
              }
            }}
            rows={10}
            className="font-mono text-xs"
          />
        </div>
      </div>
    );
  };

  if (!node) {
    return (
      <div className="w-96 h-full bg-white dark:bg-gray-800 flex items-center justify-center">
        <div className="text-gray-500 dark:text-gray-400 text-center">
          <Settings className="h-8 w-8 mx-auto mb-2 opacity-50" />
          <p>Select a node to configure</p>
        </div>
      </div>
    );
  }

  console.log("Config panel rendering for node:", node.type);

  return (
    <div className="w-96 h-full bg-white dark:bg-gray-800 flex flex-col"
    >
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center space-x-2">
          <Settings className="h-5 w-5 text-gray-500" />
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Configure Node
            </h3>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              {node.data.label} • {node.type}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleTestNode}
            disabled={isTestingNode}
          >
            {isTestingNode ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Play className="h-4 w-4" />
            )}
            <span className="ml-1">Test</span>
          </Button>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {/* Test Result Display */}
        {testResult && (
          <div className={`p-3 rounded-lg border ${
            testResult.success
              ? 'bg-green-50 border-green-200 dark:bg-green-900/20 dark:border-green-800'
              : 'bg-red-50 border-red-200 dark:bg-red-900/20 dark:border-red-800'
          }`}>
            <div className="flex items-center justify-between mb-2">
              <h4 className={`text-sm font-medium ${
                testResult.success ? 'text-green-800 dark:text-green-200' : 'text-red-800 dark:text-red-200'
              }`}>
                Test Result: {testResult.success ? 'Success' : 'Failed'}
              </h4>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setTestResult(null)}
                className="h-6 w-6 p-0"
              >
                <X className="h-3 w-3" />
              </Button>
            </div>
            {testResult.success ? (
              <div className="space-y-2">
                <div className="text-xs text-green-700 dark:text-green-300">
                  <strong>Output:</strong>
                  <pre className="mt-1 p-2 bg-green-100 dark:bg-green-900/30 rounded text-xs overflow-x-auto">
                    {JSON.stringify(testResult.result?.output, null, 2)}
                  </pre>
                </div>
                {testResult.result?.variables && Object.keys(testResult.result.variables).length > 0 && (
                  <div className="text-xs text-green-700 dark:text-green-300">
                    <strong>Variables:</strong>
                    <pre className="mt-1 p-2 bg-green-100 dark:bg-green-900/30 rounded text-xs overflow-x-auto">
                      {JSON.stringify(testResult.result.variables, null, 2)}
                    </pre>
                  </div>
                )}
              </div>
            ) : (
              <div className="text-xs text-red-700 dark:text-red-300">
                <strong>Error:</strong> {testResult.error}
              </div>
            )}
          </div>
        )}

        {/* Configuration Fields */}
        {renderConfigFields()}
      </div>

      {/* Footer */}
      <div className="flex items-center justify-between p-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800/50">
        <div className="flex items-center space-x-2">
          {hasChanges && (
            <span className="text-sm text-amber-600 dark:text-amber-400">
              Unsaved changes
            </span>
          )}
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleDelete}
            className="text-red-600 hover:text-red-700 hover:bg-red-50 dark:text-red-400 dark:hover:text-red-300 dark:hover:bg-red-900/20"
          >
            <Trash2 className="h-4 w-4 mr-2" />
            Delete Node
          </Button>
          <Button
            onClick={handleSave}
            size="sm"
            disabled={!hasChanges}
          >
            <Save className="h-4 w-4 mr-2" />
            Save Changes
          </Button>
        </div>
      </div>
    </div>
  );
};
