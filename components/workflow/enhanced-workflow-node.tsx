"use client";

import React, { useState, useRef } from "react";
import { motion } from "framer-motion";
import {
  X,
  Settings,
  Zap,
  Database,
  Code,
  MessageSquare,
  Clock,
  Filter,
  GitBranch,
  Send,
  Bot,
  Brain,
  Layers,
  ArrowLeft,
  ArrowRight,
  Wrench,
  FileText,
  Cpu
} from "lucide-react";

interface NodeHandle {
  id: string;
  type: 'input' | 'output' | 'tool' | 'memory' | 'parser' | 'model';
  position: 'left' | 'right' | 'top' | 'bottom';
  label?: string;
  icon?: React.ComponentType<any>;
  required?: boolean;
  multiple?: boolean;
}

interface WorkflowNodeProps {
  node: {
    id: string;
    type: string;
    subtype?: string;
    position: { x: number; y: number };
    data: {
      label: string;
      config: Record<string, any>;
      isCluster?: boolean;
      memory?: any;
      tools?: string[];
    };
  };
  isSelected: boolean;
  isConnecting: boolean;
  onClick: () => void;
  onDelete: () => void;
  onUpdate: (config: any) => void;
  onDrag: (position: { x: number; y: number }) => void;
  onConnectionStart: (handle: string) => void;
  onConnectionEnd: (handle: string) => void;
}

const nodeIcons: Record<string, any> = {
  // Triggers
  webhook: Zap,
  schedule: Clock,
  message: MessageSquare,
  
  // Tools
  web_search: Database,
  calculator: Code,
  email: Send,
  memory: Brain,
  
  // Logic
  condition: GitBranch,
  filter: Filter,
  transform: Settings,
  
  // Actions
  response: MessageSquare,
  api_call: Send,
  database: Database,
  
  // AI Agents (Cluster nodes)
  ai_agent: Bot,
  cluster: Layers,
};

const nodeColors: Record<string, string> = {
  // Triggers - Blue
  webhook: "from-blue-100 to-blue-200 dark:from-blue-900/30 dark:to-blue-800/40 border-blue-300 dark:border-blue-600",
  schedule: "from-blue-100 to-blue-200 dark:from-blue-900/30 dark:to-blue-800/40 border-blue-300 dark:border-blue-600",
  message: "from-blue-100 to-blue-200 dark:from-blue-900/30 dark:to-blue-800/40 border-blue-300 dark:border-blue-600",
  
  // Tools - Green
  web_search: "from-green-100 to-green-200 dark:from-green-900/30 dark:to-green-800/40 border-green-300 dark:border-green-600",
  calculator: "from-green-100 to-green-200 dark:from-green-900/30 dark:to-green-800/40 border-green-300 dark:border-green-600",
  email: "from-green-100 to-green-200 dark:from-green-900/30 dark:to-green-800/40 border-green-300 dark:border-green-600",
  memory: "from-green-100 to-green-200 dark:from-green-900/30 dark:to-green-800/40 border-green-300 dark:border-green-600",
  
  // Logic - Purple
  condition: "from-purple-100 to-purple-200 dark:from-purple-900/30 dark:to-purple-800/40 border-purple-300 dark:border-purple-600",
  filter: "from-purple-100 to-purple-200 dark:from-purple-900/30 dark:to-purple-800/40 border-purple-300 dark:border-purple-600",
  transform: "from-purple-100 to-purple-200 dark:from-purple-900/30 dark:to-purple-800/40 border-purple-300 dark:border-purple-600",
  
  // Actions - Orange
  response: "from-orange-100 to-orange-200 dark:from-orange-900/30 dark:to-orange-800/40 border-orange-300 dark:border-orange-600",
  api_call: "from-orange-100 to-orange-200 dark:from-orange-900/30 dark:to-orange-800/40 border-orange-300 dark:border-orange-600",
  database: "from-orange-100 to-orange-200 dark:from-orange-900/30 dark:to-orange-800/40 border-orange-300 dark:border-orange-600",
  
  // AI Agents - Gradient
  ai_agent: "from-emerald-100 via-blue-100 to-purple-200 dark:from-emerald-900/30 dark:via-blue-900/30 dark:to-purple-800/40 border-emerald-300 dark:border-emerald-600",
  cluster: "from-emerald-100 via-blue-100 to-purple-200 dark:from-emerald-900/30 dark:via-blue-900/30 dark:to-purple-800/40 border-emerald-300 dark:border-emerald-600",
};

// Handle configuration for different node types
const getNodeHandles = (nodeType: string, isCluster: boolean = false): NodeHandle[] => {
  // Sub-nodes (tools, memory, etc.) only have a single top handle to connect to cluster
  const subNodeTypes = ['memory', 'web_search', 'calculator', 'email', 'database', 'api'];
  if (subNodeTypes.includes(nodeType)) {
    return [
      {
        id: 'cluster-input',
        type: 'input',
        position: 'top',
        icon: ArrowRight, // Arrow pointing into the node
        required: true,
      },
    ];
  }

  // Trigger nodes only have output handles (no input handles)
  const triggerTypes = ['webhook', 'schedule', 'message', 'email_received', 'file_upload'];
  if (triggerTypes.includes(nodeType)) {
    return [
      {
        id: 'output',
        type: 'output',
        position: 'right',
        icon: ArrowRight, // Arrow pointing out of the node
        required: true,
      },
    ];
  }

  const baseHandles: NodeHandle[] = [
    {
      id: 'input',
      type: 'input',
      position: 'left',
      icon: ArrowRight, // Arrow pointing into the node (right direction)
      required: true,
    },
    {
      id: 'output',
      type: 'output',
      position: 'right',
      icon: ArrowRight, // Arrow pointing out of the node
      required: true,
    },
  ];

  // Add sub-handles for cluster nodes (AI agents)
  if (isCluster || nodeType === 'ai_agent') {
    const subHandles: NodeHandle[] = [
      {
        id: 'memory',
        type: 'memory',
        position: 'bottom',
        label: 'memory',
        icon: Brain,
        multiple: true,
      },
      {
        id: 'tools',
        type: 'tool',
        position: 'bottom',
        label: 'tools',
        icon: Wrench,
        multiple: true,
      },
      {
        id: 'parser',
        type: 'parser',
        position: 'bottom',
        label: 'parser',
        icon: FileText,
      },
      {
        id: 'model',
        type: 'model',
        position: 'bottom',
        label: 'model',
        icon: Cpu,
      },
    ];
    return [...baseHandles, ...subHandles];
  }

  // Special cases for nodes with multiple outputs
  if (nodeType === 'condition') {
    return [
      baseHandles[0], // input
      {
        id: 'output-true',
        type: 'output',
        position: 'right',
        label: 'true',
        icon: ArrowRight,
      },
      {
        id: 'output-false',
        type: 'output',
        position: 'right',
        label: 'false',
        icon: ArrowRight,
      },
    ];
  }

  return baseHandles;
};

export const EnhancedWorkflowNode = ({
  node,
  isSelected,
  isConnecting,
  onClick,
  onDelete,
  onUpdate,
  onDrag,
  onConnectionStart,
  onConnectionEnd,
}: WorkflowNodeProps) => {
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const nodeRef = useRef<HTMLDivElement>(null);

  const IconComponent = nodeIcons[node.type] || Settings;
  const colorClass = nodeColors[node.type] || "from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-600 border-gray-300 dark:border-gray-600";

  // Get handles for this node type
  const handles = getNodeHandles(node.type, node.data.isCluster);

  const handleMouseDown = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget || (e.target as HTMLElement).closest('.node-content')) {
      setIsDragging(true);
      setDragStart({
        x: e.clientX - node.position.x,
        y: e.clientY - node.position.y,
      });
      e.preventDefault();
    }
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (isDragging) {
      const newPosition = {
        x: e.clientX - dragStart.x,
        y: e.clientY - dragStart.y,
      };
      onDrag(newPosition);
    }
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  const handleConnectionPointClick = (handle: string, e: React.MouseEvent) => {
    e.stopPropagation();
    if (isConnecting) {
      onConnectionEnd(handle);
    } else {
      onConnectionStart(handle);
    }
  };

  // Render individual handle
  const renderHandle = (handle: NodeHandle, index: number = 0) => {
    const HandleIcon = handle.icon;

    // Position calculations with inline styles for precise positioning
    let style: React.CSSProperties = {};
    let baseClasses = 'absolute w-6 h-6 rounded-full border-2 border-white dark:border-gray-800 cursor-pointer transition-colors z-20 flex items-center justify-center';
    let iconClasses = 'w-3 h-3';

    switch (handle.position) {
      case 'left':
        style = {
          top: '50%',
          left: '-12px',
          transform: 'translateY(-50%)',
        };
        iconClasses += ' text-white';
        break;
      case 'right':
        if (handle.label) {
          // Multiple outputs - stack them vertically
          const outputHandles = handles.filter(h => h.position === 'right');
          const totalOutputs = outputHandles.length;
          const spacing = 30; // pixels between handles
          const startY = totalOutputs > 1 ? -(totalOutputs - 1) * spacing / 2 : 0;
          const yOffset = startY + index * spacing;
          style = {
            top: '50%',
            right: '-12px',
            transform: `translateY(calc(-50% + ${yOffset}px))`,
          };
        } else {
          style = {
            top: '50%',
            right: '-12px',
            transform: 'translateY(-50%)',
          };
        }
        iconClasses += ' text-white';
        break;
      case 'top':
        style = {
          top: '-12px',
          left: '50%',
          transform: 'translateX(-50%)',
        };
        iconClasses += ' text-white';
        break;
      case 'bottom':
        // Sub-handles on bottom - arrange horizontally
        const bottomHandles = handles.filter(h => h.position === 'bottom');
        const totalBottom = bottomHandles.length;
        const nodeWidth = 200; // Node width
        const spacing = nodeWidth / (totalBottom + 1);
        const leftOffset = spacing * (index + 1);
        style = {
          bottom: '-12px',
          left: `${leftOffset}px`,
          transform: 'translateX(-50%)',
        };
        iconClasses += ' text-gray-600 dark:text-gray-300';
        break;
    }

    // Handle colors based on type
    let handleColor = 'bg-emerald-500 hover:bg-emerald-600';
    if (handle.type === 'input') {
      handleColor = 'bg-blue-500 hover:bg-blue-600';
    } else if (handle.type === 'output') {
      handleColor = 'bg-green-500 hover:bg-green-600';
    } else {
      handleColor = 'bg-gray-500 hover:bg-gray-600';
    }

    return (
      <div key={handle.id}>
        <div
          className={`${baseClasses} ${handleColor}`}
          style={style}
          onClick={(e) => handleConnectionPointClick(handle.id, e)}
          title={handle.label || `${handle.type} connection`}
        >
          {HandleIcon && <HandleIcon className={iconClasses} />}
        </div>

        {/* Label for sub-handles */}
        {handle.label && handle.position === 'bottom' && (
          <div
            className="absolute text-xs text-gray-600 dark:text-gray-400 whitespace-nowrap"
            style={{
              bottom: '-28px',
              left: `${(200 / (handles.filter(h => h.position === 'bottom').length + 1)) * (index + 1)}px`,
              transform: 'translateX(-50%)',
            }}
          >
            {handle.label}
          </div>
        )}

        {/* Label for multiple outputs */}
        {handle.label && handle.position === 'right' && (
          <div
            className="absolute text-xs text-gray-600 dark:text-gray-400 whitespace-nowrap flex items-center"
            style={{
              top: '50%',
              right: '-48px',
              transform: `translateY(calc(-50% + ${handle.position === 'right' && index > 0 ? index * 30 : 0}px))`,
            }}
          >
            {handle.label}
          </div>
        )}
      </div>
    );
  };

  return (
    <motion.div
      ref={nodeRef}
      className={`absolute cursor-pointer select-none ${isDragging ? 'z-50' : 'z-20'}`}
      style={{
        left: Math.max(0, node.position.x),
        top: Math.max(0, node.position.y),
        width: 200,
      }}
      initial={{ scale: 0.8, opacity: 0 }}
      animate={{ scale: 1, opacity: 1 }}
      whileHover={{ scale: 1.02 }}
      onMouseDown={handleMouseDown}
      onMouseMove={handleMouseMove}
      onMouseUp={handleMouseUp}
      onMouseLeave={handleMouseUp}
    >
      {/* Selection highlight */}
      {isSelected && (
        <div className="absolute -inset-2 bg-emerald-500/20 border-2 border-emerald-500 rounded-xl animate-pulse" />
      )}

      {/* Main node container */}
      <div
        className={`relative bg-gradient-to-br ${colorClass} rounded-xl shadow-lg border-2 transition-all duration-200 ${
          isSelected ? 'shadow-xl' : 'shadow-md'
        } ${isConnecting ? 'ring-2 ring-blue-400' : ''}`}
        onClick={onClick}
      >
        {/* Dynamic Connection Handles */}
        {handles.map((handle, index) => {
          // For right-side handles, we need to track the index among right handles only
          if (handle.position === 'right') {
            const rightHandles = handles.filter(h => h.position === 'right');
            const rightIndex = rightHandles.findIndex(h => h.id === handle.id);
            return renderHandle(handle, rightIndex);
          }
          // For bottom handles, track index among bottom handles only
          if (handle.position === 'bottom') {
            const bottomHandles = handles.filter(h => h.position === 'bottom');
            const bottomIndex = bottomHandles.findIndex(h => h.id === handle.id);
            return renderHandle(handle, bottomIndex);
          }
          return renderHandle(handle, index);
        })}

        {/* Node content */}
        <div className="node-content p-4">
          {/* Header */}
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center space-x-2">
              <IconComponent className="h-5 w-5 text-gray-700 dark:text-gray-300" />
              {node.data.isCluster && (
                <Layers className="h-4 w-4 text-blue-600 dark:text-blue-400" />
              )}
            </div>
            <div className="flex items-center space-x-1">
              {node.data.memory && (
                <Brain className="h-4 w-4 text-purple-600 dark:text-purple-400" title="Has memory" />
              )}
              {node.data.tools && node.data.tools.length > 0 && (
                <div className="text-xs bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 px-2 py-1 rounded-full">
                  {node.data.tools.length} tools
                </div>
              )}
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  onDelete();
                }}
                className="text-gray-400 hover:text-red-500 transition-colors"
              >
                <X className="h-4 w-4" />
              </button>
            </div>
          </div>

          {/* Node label */}
          <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
            {node.data.label}
          </h3>

          {/* Node type badge */}
          <div className="text-xs text-gray-500 dark:text-gray-400 mb-2">
            {node.type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
            {node.subtype && ` • ${node.subtype}`}
          </div>

          {/* Cluster indicator */}
          {node.data.isCluster && (
            <div className="text-xs bg-gradient-to-r from-blue-100 to-purple-100 dark:from-blue-900/30 dark:to-purple-900/30 text-blue-700 dark:text-blue-300 px-2 py-1 rounded-full mb-2">
              Cluster Node
            </div>
          )}

          {/* Memory indicator */}
          {node.data.memory && (
            <div className="text-xs bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300 px-2 py-1 rounded-full mb-2">
              Memory: {node.data.memory.type || 'Vector'}
            </div>
          )}

          {/* Tools list */}
          {node.data.tools && node.data.tools.length > 0 && (
            <div className="text-xs text-gray-600 dark:text-gray-400">
              Tools: {node.data.tools.slice(0, 2).join(', ')}
              {node.data.tools.length > 2 && ` +${node.data.tools.length - 2} more`}
            </div>
          )}

          {/* Configuration indicator */}
          {node.data.config && Object.keys(node.data.config).length > 0 && (
            <div className="mt-2 flex items-center text-xs text-gray-500 dark:text-gray-400">
              <Settings className="h-3 w-3 mr-1" />
              Configured
            </div>
          )}
        </div>

        {/* Execution status indicator */}
        <div className="absolute top-2 right-2">
          <div className="w-2 h-2 bg-gray-400 rounded-full" title="Ready" />
        </div>
      </div>
    </motion.div>
  );
};

export default EnhancedWorkflowNode;
