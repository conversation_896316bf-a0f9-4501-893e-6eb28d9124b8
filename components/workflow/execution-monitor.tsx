"use client";

import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { 
  X, 
  Square, 
  Play, 
  CheckCircle, 
  AlertCircle, 
  Clock, 
  Loader2,
  ChevronDown,
  ChevronUp
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";

interface ExecutionStep {
  id: string;
  nodeId: string;
  nodeName: string;
  nodeType: string;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
  startTime?: Date;
  endTime?: Date;
  duration?: number;
  error?: string;
  output?: any;
  logs?: string[];
  isNodeTest?: boolean; // Flag for individual node testing
}

type ExecutionType = 'workflow_direct' | 'workflow_async' | 'node_test';
type ExecutionMode = 'direct' | 'async';

interface ExecutionMonitorProps {
  isVisible: boolean;
  onClose: () => void;
  onCancel: () => void;
  workflowId: string;
  executionId?: string;
  steps: ExecutionStep[];
  isRunning: boolean;
  totalSteps: number;
  completedSteps: number;
  startTime?: Date;
  executionType?: ExecutionType;
  executionMode?: ExecutionMode;
  nodeTestResult?: any; // For individual node test results
}

export const ExecutionMonitor = ({
  isVisible,
  onClose,
  onCancel,
  workflowId,
  executionId,
  steps,
  isRunning,
  totalSteps,
  completedSteps,
  startTime,
  executionType = 'workflow_async',
  executionMode = 'async',
  nodeTestResult
}: ExecutionMonitorProps) => {
  const [isExpanded, setIsExpanded] = useState(true);
  const [elapsedTime, setElapsedTime] = useState(0);

  // Update elapsed time
  useEffect(() => {
    if (!isRunning || !startTime) return;

    const interval = setInterval(() => {
      setElapsedTime(Math.floor((Date.now() - startTime.getTime()) / 1000));
    }, 1000);

    return () => clearInterval(interval);
  }, [isRunning, startTime]);

  const formatDuration = (seconds: number) => {
    if (seconds < 60) return `${seconds}s`;
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
  };

  const getStepIcon = (status: ExecutionStep['status']) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'running':
        return <Loader2 className="h-4 w-4 text-blue-500 animate-spin" />;
      case 'failed':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      case 'cancelled':
        return <Square className="h-4 w-4 text-gray-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-400" />;
    }
  };

  const getStepStatusText = (step: ExecutionStep) => {
    switch (step.status) {
      case 'completed':
        return `Completed ${step.duration ? `(${formatDuration(step.duration)})` : ''}`;
      case 'running':
        const runningTime = step.startTime ? Math.floor((Date.now() - step.startTime.getTime()) / 1000) : 0;
        return `Running... (${formatDuration(runningTime)})`;
      case 'failed':
        return `Failed ${step.error ? `- ${step.error}` : ''}`;
      case 'cancelled':
        return 'Cancelled';
      default:
        return 'Pending';
    }
  };

  const progressPercentage = totalSteps > 0 ? (completedSteps / totalSteps) * 100 : 0;

  const getOverallStatus = () => {
    if (steps.some(step => step.status === 'failed')) return 'Failed';
    if (steps.some(step => step.status === 'cancelled')) return 'Cancelled';
    if (isRunning) return 'Running';
    if (completedSteps === totalSteps && totalSteps > 0) return 'Completed';
    return 'Idle';
  };

  const getExecutionTypeLabel = () => {
    switch (executionType) {
      case 'workflow_direct':
        return 'Workflow (Direct)';
      case 'workflow_async':
        return 'Workflow (Async)';
      case 'node_test':
        return 'Node Test';
      default:
        return 'Workflow Execution';
    }
  };

  const getExecutionTypeColor = () => {
    switch (executionType) {
      case 'workflow_direct':
        return 'text-blue-600 dark:text-blue-400';
      case 'workflow_async':
        return 'text-green-600 dark:text-green-400';
      case 'node_test':
        return 'text-purple-600 dark:text-purple-400';
      default:
        return 'text-gray-600 dark:text-gray-400';
    }
  };

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ y: 400, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          exit={{ y: 400, opacity: 0 }}
          transition={{ duration: 0.3, ease: "easeInOut" }}
          className="fixed bottom-0 left-1/2 transform -translate-x-1/2 w-[80%] bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-t-lg shadow-2xl z-50"
          style={{ height: isExpanded ? '400px' : '60px' }}
        >
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900 rounded-t-lg">
            <div className="flex items-center space-x-3">
              <div className="flex items-center space-x-2">
                {isRunning ? (
                  <Loader2 className="h-5 w-5 text-blue-500 animate-spin" />
                ) : (
                  <Play className="h-5 w-5 text-gray-500" />
                )}
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  {getExecutionTypeLabel()}
                </h3>
                <span className={`text-xs px-2 py-1 rounded-full bg-gray-100 dark:bg-gray-700 ${getExecutionTypeColor()}`}>
                  {executionMode.toUpperCase()}
                </span>
              </div>
              
              <div className="text-sm text-gray-600 dark:text-gray-400">
                Progress: {completedSteps}/{totalSteps} steps • 
                Elapsed: {formatDuration(elapsedTime)} • 
                Status: <span className={`font-medium ${
                  getOverallStatus() === 'Running' ? 'text-blue-600' :
                  getOverallStatus() === 'Completed' ? 'text-green-600' :
                  getOverallStatus() === 'Failed' ? 'text-red-600' :
                  'text-gray-600'
                }`}>
                  {getOverallStatus()}
                </span>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              {isRunning && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={onCancel}
                  className="text-red-600 hover:text-red-700 hover:bg-red-50"
                >
                  <Square className="h-4 w-4 mr-1" />
                  Cancel
                </Button>
              )}
              
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsExpanded(!isExpanded)}
                className="p-1"
              >
                {isExpanded ? (
                  <ChevronDown className="h-4 w-4" />
                ) : (
                  <ChevronUp className="h-4 w-4" />
                )}
              </Button>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={onClose}
                className="p-1"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Progress Bar */}
          <div className="px-4 py-2 bg-gray-50 dark:bg-gray-900">
            <Progress value={progressPercentage} className="h-2" />
          </div>

          {/* Execution Steps */}
          {isExpanded && (
            <div className="flex-1 overflow-y-auto p-4">
              <div className="space-y-3">
                {steps.map((step, index) => (
                  <motion.div
                    key={step.id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className={`flex items-center justify-between p-3 rounded-lg border ${
                      step.status === 'running' 
                        ? 'border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-900/20'
                        : step.status === 'completed'
                        ? 'border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-900/20'
                        : step.status === 'failed'
                        ? 'border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-900/20'
                        : 'border-gray-200 bg-gray-50 dark:border-gray-700 dark:bg-gray-800'
                    }`}
                  >
                    <div className="flex items-center space-x-3">
                      <div className="flex items-center justify-center w-8 h-8 rounded-full bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600">
                        <span className="text-sm font-medium text-gray-600 dark:text-gray-300">
                          {index + 1}
                        </span>
                      </div>
                      
                      <div>
                        <div className="flex items-center space-x-2">
                          <h4 className="font-medium text-gray-900 dark:text-white">
                            {step.nodeName}
                          </h4>
                          <span className="text-xs px-2 py-1 rounded-full bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-300">
                            {step.nodeType}
                          </span>
                        </div>
                        
                        {step.logs && step.logs.length > 0 && (
                          <div className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                            {step.logs[step.logs.length - 1]}
                          </div>
                        )}
                      </div>
                    </div>

                    <div className="flex items-center space-x-3">
                      <div className="text-right">
                        <div className="flex items-center space-x-2">
                          {getStepIcon(step.status)}
                          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                            {getStepStatusText(step)}
                          </span>
                        </div>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          )}
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default ExecutionMonitor;
