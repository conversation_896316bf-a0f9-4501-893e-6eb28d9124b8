"use client";

import React from "react";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Button } from "@/components/ui/button";
import { Plus, Trash2 } from "lucide-react";
import { CredentialSelector } from "@/components/credentials/credential-selector";

interface TriggerConfigProps {
  nodeType: string;
  config: Record<string, any>;
  onUpdate: (config: Record<string, any>) => void;
  teamId?: string;
}

export const TriggerConfig = ({ nodeType, config, onUpdate, teamId }: TriggerConfigProps) => {
  const handleChange = (key: string, value: any) => {
    onUpdate({ [key]: value });
  };

  const handleNestedChange = (parentKey: string, key: string, value: any) => {
    onUpdate({
      [parentKey]: {
        ...config[parentKey],
        [key]: value,
      },
    });
  };

  const addArrayItem = (key: string, defaultItem: any) => {
    const currentArray = config[key] || [];
    onUpdate({
      [key]: [...currentArray, defaultItem],
    });
  };

  const removeArrayItem = (key: string, index: number) => {
    const currentArray = config[key] || [];
    onUpdate({
      [key]: currentArray.filter((_: any, i: number) => i !== index),
    });
  };

  const updateArrayItem = (key: string, index: number, value: any) => {
    const currentArray = config[key] || [];
    const newArray = [...currentArray];
    newArray[index] = value;
    onUpdate({
      [key]: newArray,
    });
  };

  if (nodeType === 'webhook') {
    return (
      <div className="space-y-6">
        <div>
          <Label htmlFor="endpoint">Webhook Endpoint</Label>
          <Input
            id="endpoint"
            value={config.endpoint || ''}
            onChange={(e) => handleChange('endpoint', e.target.value)}
            placeholder="/webhook/my-endpoint"
          />
          <p className="text-xs text-gray-500 mt-1">
            The endpoint path for this webhook
          </p>
        </div>

        <div>
          <Label htmlFor="method">HTTP Method</Label>
          <Select value={config.method || 'POST'} onValueChange={(value) => handleChange('method', value)}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="GET">GET</SelectItem>
              <SelectItem value="POST">POST</SelectItem>
              <SelectItem value="PUT">PUT</SelectItem>
              <SelectItem value="PATCH">PATCH</SelectItem>
              <SelectItem value="DELETE">DELETE</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div>
          <Label htmlFor="authentication">Authentication</Label>
          <Select value={config.authentication || 'none'} onValueChange={(value) => handleChange('authentication', value)}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="none">None</SelectItem>
              <SelectItem value="api_key">API Key</SelectItem>
              <SelectItem value="bearer_token">Bearer Token</SelectItem>
              <SelectItem value="basic_auth">Basic Auth</SelectItem>
              <SelectItem value="hmac">HMAC Signature</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {config.authentication === 'api_key' && (
          <div className="space-y-3">
            <div>
              <Label htmlFor="api_key_header">API Key Header</Label>
              <Input
                id="api_key_header"
                value={config.api_key_header || 'X-API-Key'}
                onChange={(e) => handleChange('api_key_header', e.target.value)}
                placeholder="X-API-Key"
              />
            </div>

            {/* Option to use stored credentials */}
            {teamId && (
              <div>
                <Label>Use Stored Credentials</Label>
                <CredentialSelector
                  teamId={teamId}
                  value={config.credentialId}
                  onChange={(credentialId) => handleChange('credentialId', credentialId)}
                  placeholder="Select API credentials (optional)..."
                  allowCreate={true}
                  allowTest={true}
                />
                <p className="text-sm text-gray-600 mt-1">
                  Or enter API key manually below
                </p>
              </div>
            )}

            <div>
              <Label htmlFor="api_key_value">API Key Value</Label>
              <Input
                id="api_key_value"
                type="password"
                value={config.api_key_value || ''}
                onChange={(e) => handleChange('api_key_value', e.target.value)}
                placeholder="Enter API key (if not using stored credentials)"
                disabled={!!config.credentialId}
              />
            </div>
          </div>
        )}

        {config.authentication === 'bearer_token' && (
          <div className="space-y-3">
            {/* Option to use stored credentials */}
            {teamId && (
              <div>
                <Label>Use Stored Credentials</Label>
                <CredentialSelector
                  teamId={teamId}
                  value={config.credentialId}
                  onChange={(credentialId) => handleChange('credentialId', credentialId)}
                  placeholder="Select bearer token credentials (optional)..."
                  allowCreate={true}
                  allowTest={true}
                />
                <p className="text-sm text-gray-600 mt-1">
                  Or enter bearer token manually below
                </p>
              </div>
            )}

            <div>
              <Label htmlFor="bearer_token">Bearer Token</Label>
              <Input
                id="bearer_token"
                type="password"
                value={config.bearer_token || ''}
                onChange={(e) => handleChange('bearer_token', e.target.value)}
                placeholder="Enter bearer token (if not using stored credentials)"
                disabled={!!config.credentialId}
              />
            </div>
          </div>
        )}

        {config.authentication === 'basic_auth' && (
          <div className="space-y-3">
            <div>
              <Label htmlFor="username">Username</Label>
              <Input
                id="username"
                value={config.username || ''}
                onChange={(e) => handleChange('username', e.target.value)}
                placeholder="Username"
              />
            </div>
            <div>
              <Label htmlFor="password">Password</Label>
              <Input
                id="password"
                type="password"
                value={config.password || ''}
                onChange={(e) => handleChange('password', e.target.value)}
                placeholder="Password"
              />
            </div>
          </div>
        )}

        {config.authentication === 'hmac' && (
          <div className="space-y-3">
            <div>
              <Label htmlFor="hmac_secret">HMAC Secret</Label>
              <Input
                id="hmac_secret"
                type="password"
                value={config.hmac_secret || ''}
                onChange={(e) => handleChange('hmac_secret', e.target.value)}
                placeholder="HMAC secret key"
              />
            </div>
            <div>
              <Label htmlFor="hmac_header">Signature Header</Label>
              <Input
                id="hmac_header"
                value={config.hmac_header || 'X-Hub-Signature-256'}
                onChange={(e) => handleChange('hmac_header', e.target.value)}
                placeholder="X-Hub-Signature-256"
              />
            </div>
            <div>
              <Label htmlFor="hmac_algorithm">Algorithm</Label>
              <Select value={config.hmac_algorithm || 'sha256'} onValueChange={(value) => handleChange('hmac_algorithm', value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="sha1">SHA1</SelectItem>
                  <SelectItem value="sha256">SHA256</SelectItem>
                  <SelectItem value="sha512">SHA512</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        )}

        <div className="flex items-center space-x-2">
          <Switch
            id="validate_ssl"
            checked={config.validate_ssl !== false}
            onCheckedChange={(checked) => handleChange('validate_ssl', checked)}
          />
          <Label htmlFor="validate_ssl">Validate SSL Certificate</Label>
        </div>

        <div className="flex items-center space-x-2">
          <Switch
            id="async_response"
            checked={config.async_response || false}
            onCheckedChange={(checked) => handleChange('async_response', checked)}
          />
          <Label htmlFor="async_response">Asynchronous Response</Label>
        </div>

        <div>
          <Label htmlFor="timeout">Timeout (seconds)</Label>
          <Input
            id="timeout"
            type="number"
            value={config.timeout || 30}
            onChange={(e) => handleChange('timeout', parseInt(e.target.value))}
            min="1"
            max="300"
          />
        </div>

        <div>
          <Label htmlFor="rate_limit">Rate Limit (requests per minute)</Label>
          <Input
            id="rate_limit"
            type="number"
            value={config.rate_limit || 60}
            onChange={(e) => handleChange('rate_limit', parseInt(e.target.value))}
            min="1"
            max="10000"
          />
        </div>

        <div>
          <Label>Custom Headers</Label>
          <div className="space-y-2 mt-2">
            {(config.custom_headers || []).map((header: any, index: number) => (
              <div key={index} className="flex space-x-2">
                <Input
                  placeholder="Header name"
                  value={header.name || ''}
                  onChange={(e) => updateArrayItem('custom_headers', index, { ...header, name: e.target.value })}
                />
                <Input
                  placeholder="Header value"
                  value={header.value || ''}
                  onChange={(e) => updateArrayItem('custom_headers', index, { ...header, value: e.target.value })}
                />
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => removeArrayItem('custom_headers', index)}
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            ))}
            <Button
              variant="outline"
              size="sm"
              onClick={() => addArrayItem('custom_headers', { name: '', value: '' })}
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Header
            </Button>
          </div>
        </div>

        <div>
          <Label htmlFor="response_format">Response Format</Label>
          <Select value={config.response_format || 'json'} onValueChange={(value) => handleChange('response_format', value)}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="json">JSON</SelectItem>
              <SelectItem value="xml">XML</SelectItem>
              <SelectItem value="text">Plain Text</SelectItem>
              <SelectItem value="form">Form Data</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
    );
  }

  if (nodeType === 'schedule') {
    return (
      <div className="space-y-6">
        <div>
          <Label htmlFor="schedule_type">Schedule Type</Label>
          <Select value={config.schedule_type || 'cron'} onValueChange={(value) => handleChange('schedule_type', value)}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="cron">Cron Expression</SelectItem>
              <SelectItem value="interval">Interval</SelectItem>
              <SelectItem value="once">One Time</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {config.schedule_type === 'cron' && (
          <div className="space-y-3">
            <div>
              <Label htmlFor="cron_expression">Cron Expression</Label>
              <Input
                id="cron_expression"
                value={config.cron_expression || '0 9 * * *'}
                onChange={(e) => handleChange('cron_expression', e.target.value)}
                placeholder="0 9 * * *"
              />
              <p className="text-xs text-gray-500 mt-1">
                Format: minute hour day month weekday
              </p>
            </div>
            <div className="grid grid-cols-2 gap-3">
              <div>
                <Label>Quick Presets</Label>
                <div className="space-y-1 mt-1">
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full justify-start text-xs"
                    onClick={() => handleChange('cron_expression', '0 9 * * *')}
                  >
                    Daily at 9 AM
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full justify-start text-xs"
                    onClick={() => handleChange('cron_expression', '0 9 * * 1')}
                  >
                    Weekly (Monday 9 AM)
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full justify-start text-xs"
                    onClick={() => handleChange('cron_expression', '0 9 1 * *')}
                  >
                    Monthly (1st at 9 AM)
                  </Button>
                </div>
              </div>
              <div>
                <Label htmlFor="timezone">Timezone</Label>
                <Select value={config.timezone || 'UTC'} onValueChange={(value) => handleChange('timezone', value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="UTC">UTC</SelectItem>
                    <SelectItem value="America/New_York">Eastern Time</SelectItem>
                    <SelectItem value="America/Chicago">Central Time</SelectItem>
                    <SelectItem value="America/Denver">Mountain Time</SelectItem>
                    <SelectItem value="America/Los_Angeles">Pacific Time</SelectItem>
                    <SelectItem value="Europe/London">London</SelectItem>
                    <SelectItem value="Europe/Paris">Paris</SelectItem>
                    <SelectItem value="Asia/Tokyo">Tokyo</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
        )}

        {config.schedule_type === 'interval' && (
          <div className="space-y-3">
            <div className="grid grid-cols-2 gap-3">
              <div>
                <Label htmlFor="interval_value">Interval Value</Label>
                <Input
                  id="interval_value"
                  type="number"
                  value={config.interval_value || 1}
                  onChange={(e) => handleChange('interval_value', parseInt(e.target.value))}
                  min="1"
                />
              </div>
              <div>
                <Label htmlFor="interval_unit">Interval Unit</Label>
                <Select value={config.interval_unit || 'minutes'} onValueChange={(value) => handleChange('interval_unit', value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="seconds">Seconds</SelectItem>
                    <SelectItem value="minutes">Minutes</SelectItem>
                    <SelectItem value="hours">Hours</SelectItem>
                    <SelectItem value="days">Days</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
        )}

        {config.schedule_type === 'once' && (
          <div>
            <Label htmlFor="execute_at">Execute At</Label>
            <Input
              id="execute_at"
              type="datetime-local"
              value={config.execute_at || ''}
              onChange={(e) => handleChange('execute_at', e.target.value)}
            />
          </div>
        )}

        <div className="flex items-center space-x-2">
          <Switch
            id="enabled"
            checked={config.enabled !== false}
            onCheckedChange={(checked) => handleChange('enabled', checked)}
          />
          <Label htmlFor="enabled">Schedule Enabled</Label>
        </div>

        <div>
          <Label htmlFor="max_executions">Max Executions (0 = unlimited)</Label>
          <Input
            id="max_executions"
            type="number"
            value={config.max_executions || 0}
            onChange={(e) => handleChange('max_executions', parseInt(e.target.value))}
            min="0"
          />
        </div>

        <div>
          <Label htmlFor="start_date">Start Date (optional)</Label>
          <Input
            id="start_date"
            type="datetime-local"
            value={config.start_date || ''}
            onChange={(e) => handleChange('start_date', e.target.value)}
          />
        </div>

        <div>
          <Label htmlFor="end_date">End Date (optional)</Label>
          <Input
            id="end_date"
            type="datetime-local"
            value={config.end_date || ''}
            onChange={(e) => handleChange('end_date', e.target.value)}
          />
        </div>
      </div>
    );
  }

  const [activeTab, setActiveTab] = React.useState('general');

  if (nodeType === 'message') {

    return (
      <div className="space-y-6">
        {/* Tab Navigation */}
        <div className="flex space-x-1 bg-gray-100 dark:bg-gray-800 p-1 rounded-lg">
          <button
            onClick={() => setActiveTab('general')}
            className={`flex-1 px-3 py-2 text-sm font-medium rounded-md transition-colors ${
              activeTab === 'general'
                ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm'
                : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
            }`}
          >
            General Settings
          </button>
          <button
            onClick={() => setActiveTab('interface')}
            className={`flex-1 px-3 py-2 text-sm font-medium rounded-md transition-colors ${
              activeTab === 'interface'
                ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm'
                : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
            }`}
          >
            Interface Settings
          </button>
          <button
            onClick={() => setActiveTab('trigger')}
            className={`flex-1 px-3 py-2 text-sm font-medium rounded-md transition-colors ${
              activeTab === 'trigger'
                ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm'
                : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
            }`}
          >
            Trigger Rules
          </button>
        </div>

        {/* General Settings Tab */}
        {activeTab === 'general' && (
          <div className="space-y-4">
            <div>
              <Label htmlFor="displayName">Bot Display Name</Label>
              <Input
                id="displayName"
                value={config.displayName || ''}
                onChange={(e) => handleChange('displayName', e.target.value)}
                placeholder="AI Assistant"
              />
            </div>

            <div>
              <Label htmlFor="messagePlaceholder">Message Placeholder</Label>
              <Input
                id="messagePlaceholder"
                value={config.messagePlaceholder || ''}
                onChange={(e) => handleChange('messagePlaceholder', e.target.value)}
                placeholder="Type your message..."
              />
            </div>

            <div>
              <Label>Initial Messages</Label>
              <div className="space-y-2 mt-2">
                {(config.initialMessages || []).map((message: string, index: number) => (
                  <div key={index} className="flex space-x-2">
                    <Input
                      placeholder="Welcome message"
                      value={message}
                      onChange={(e) => updateArrayItem('initialMessages', index, e.target.value)}
                    />
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => removeArrayItem('initialMessages', index)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => addArrayItem('initialMessages', 'Hello! How can I help you today?')}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Initial Message
                </Button>
              </div>
            </div>

            <div>
              <Label>Suggested Messages</Label>
              <div className="space-y-2 mt-2">
                {(config.suggestedMessages || []).map((message: string, index: number) => (
                  <div key={index} className="flex space-x-2">
                    <Input
                      placeholder="Quick reply suggestion"
                      value={message}
                      onChange={(e) => updateArrayItem('suggestedMessages', index, e.target.value)}
                    />
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => removeArrayItem('suggestedMessages', index)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => addArrayItem('suggestedMessages', 'How can you help me?')}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Suggested Message
                </Button>
              </div>
            </div>

            <div>
              <Label htmlFor="footer">Footer Text</Label>
              <Input
                id="footer"
                value={config.footer || ''}
                onChange={(e) => handleChange('footer', e.target.value)}
                placeholder="Powered by AI Assistant"
              />
            </div>

            <div>
              <Label htmlFor="theme">Theme</Label>
              <Select value={config.theme || 'light'} onValueChange={(value) => handleChange('theme', value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="light">Light</SelectItem>
                  <SelectItem value="dark">Dark</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="isPublic"
                checked={config.isPublic !== false}
                onCheckedChange={(checked) => handleChange('isPublic', checked)}
              />
              <Label htmlFor="isPublic">Public Access</Label>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="loadPreviousSession"
                checked={config.loadPreviousSession !== false}
                onCheckedChange={(checked) => handleChange('loadPreviousSession', checked)}
              />
              <Label htmlFor="loadPreviousSession">Load Previous Session</Label>
            </div>
          </div>
        )}

        {/* Interface Settings Tab */}
        {activeTab === 'interface' && (
          <div className="space-y-4">
            <div>
              <Label htmlFor="customerMessageColor">Customer Message Color</Label>
              <Input
                id="customerMessageColor"
                type="color"
                value={config.customerMessageColor || '#007bff'}
                onChange={(e) => handleChange('customerMessageColor', e.target.value)}
              />
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="customerMessageColorAsChatbotHeader"
                checked={config.customerMessageColorAsChatbotHeader || false}
                onCheckedChange={(checked) => handleChange('customerMessageColorAsChatbotHeader', checked)}
              />
              <Label htmlFor="customerMessageColorAsChatbotHeader">Use Customer Color as Header</Label>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="hasChatBubbleToggle"
                checked={config.hasChatBubbleToggle || false}
                onCheckedChange={(checked) => handleChange('hasChatBubbleToggle', checked)}
              />
              <Label htmlFor="hasChatBubbleToggle">Show Chat Bubble Toggle</Label>
            </div>

            {config.hasChatBubbleToggle && (
              <>
                <div>
                  <Label htmlFor="chatBubbleColor">Chat Bubble Color</Label>
                  <Input
                    id="chatBubbleColor"
                    type="color"
                    value={config.chatBubbleColor || '#007bff'}
                    onChange={(e) => handleChange('chatBubbleColor', e.target.value)}
                  />
                </div>

                <div>
                  <Label htmlFor="alignChatBubble">Chat Bubble Alignment</Label>
                  <Select value={config.alignChatBubble || 'right'} onValueChange={(value) => handleChange('alignChatBubble', value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="left">Left</SelectItem>
                      <SelectItem value="right">Right</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="autoShowInitialDelay">Auto Show Delay (seconds)</Label>
                  <Input
                    id="autoShowInitialDelay"
                    type="number"
                    min="0"
                    value={config.autoShowInitialDelay || 0}
                    onChange={(e) => handleChange('autoShowInitialDelay', parseInt(e.target.value))}
                  />
                </div>
              </>
            )}
          </div>
        )}

        {/* Trigger Rules Tab */}
        {activeTab === 'trigger' && (
          <div className="space-y-4">
            <div>
              <Label htmlFor="trigger_type">Message Trigger Type</Label>
              <Select value={config.trigger_type || 'any'} onValueChange={(value) => handleChange('trigger_type', value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="any">Any Message</SelectItem>
                  <SelectItem value="keyword">Keyword Match</SelectItem>
                  <SelectItem value="regex">Regular Expression</SelectItem>
                  <SelectItem value="intent">Intent Recognition</SelectItem>
                  <SelectItem value="sentiment">Sentiment Analysis</SelectItem>
                </SelectContent>
              </Select>
            </div>

        {config.trigger_type === 'keyword' && (
          <div className="space-y-3">
            <div>
              <Label>Keywords</Label>
              <div className="space-y-2 mt-2">
                {(config.keywords || []).map((keyword: string, index: number) => (
                  <div key={index} className="flex space-x-2">
                    <Input
                      placeholder="Enter keyword"
                      value={keyword}
                      onChange={(e) => updateArrayItem('keywords', index, e.target.value)}
                    />
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => removeArrayItem('keywords', index)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => addArrayItem('keywords', '')}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Keyword
                </Button>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                id="case_sensitive"
                checked={config.case_sensitive || false}
                onCheckedChange={(checked) => handleChange('case_sensitive', checked)}
              />
              <Label htmlFor="case_sensitive">Case Sensitive</Label>
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                id="exact_match"
                checked={config.exact_match || false}
                onCheckedChange={(checked) => handleChange('exact_match', checked)}
              />
              <Label htmlFor="exact_match">Exact Match Only</Label>
            </div>
          </div>
        )}

        {config.trigger_type === 'regex' && (
          <div>
            <Label htmlFor="regex_pattern">Regular Expression Pattern</Label>
            <Input
              id="regex_pattern"
              value={config.regex_pattern || ''}
              onChange={(e) => handleChange('regex_pattern', e.target.value)}
              placeholder="^hello.*world$"
            />
            <p className="text-xs text-gray-500 mt-1">
              Use JavaScript regex syntax
            </p>
          </div>
        )}

        {config.trigger_type === 'intent' && (
          <div className="space-y-3">
            <div>
              <Label>Intents to Match</Label>
              <div className="space-y-2 mt-2">
                {(config.intents || []).map((intent: string, index: number) => (
                  <div key={index} className="flex space-x-2">
                    <Input
                      placeholder="Enter intent name"
                      value={intent}
                      onChange={(e) => updateArrayItem('intents', index, e.target.value)}
                    />
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => removeArrayItem('intents', index)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => addArrayItem('intents', '')}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Intent
                </Button>
              </div>
            </div>
            <div>
              <Label htmlFor="confidence_threshold">Confidence Threshold</Label>
              <Input
                id="confidence_threshold"
                type="number"
                step="0.1"
                min="0"
                max="1"
                value={config.confidence_threshold || 0.8}
                onChange={(e) => handleChange('confidence_threshold', parseFloat(e.target.value))}
              />
            </div>
          </div>
        )}

        {config.trigger_type === 'sentiment' && (
          <div className="space-y-3">
            <div>
              <Label htmlFor="sentiment_target">Target Sentiment</Label>
              <Select value={config.sentiment_target || 'negative'} onValueChange={(value) => handleChange('sentiment_target', value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="positive">Positive</SelectItem>
                  <SelectItem value="negative">Negative</SelectItem>
                  <SelectItem value="neutral">Neutral</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="sentiment_threshold">Sentiment Threshold</Label>
              <Input
                id="sentiment_threshold"
                type="number"
                step="0.1"
                min="0"
                max="1"
                value={config.sentiment_threshold || 0.7}
                onChange={(e) => handleChange('sentiment_threshold', parseFloat(e.target.value))}
              />
            </div>
          </div>
        )}

        <div>
          <Label htmlFor="channels">Channels (leave empty for all)</Label>
          <div className="space-y-2 mt-2">
            {(config.channels || []).map((channel: string, index: number) => (
              <div key={index} className="flex space-x-2">
                <Input
                  placeholder="Channel ID or name"
                  value={channel}
                  onChange={(e) => updateArrayItem('channels', index, e.target.value)}
                />
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => removeArrayItem('channels', index)}
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            ))}
            <Button
              variant="outline"
              size="sm"
              onClick={() => addArrayItem('channels', '')}
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Channel
            </Button>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <Switch
            id="ignore_bots"
            checked={config.ignore_bots !== false}
            onCheckedChange={(checked) => handleChange('ignore_bots', checked)}
          />
          <Label htmlFor="ignore_bots">Ignore Bot Messages</Label>
        </div>

        <div>
          <Label htmlFor="cooldown">Cooldown (seconds)</Label>
          <Input
            id="cooldown"
            type="number"
            value={config.cooldown || 0}
            onChange={(e) => handleChange('cooldown', parseInt(e.target.value))}
            min="0"
          />
          <p className="text-xs text-gray-500 mt-1">
            Minimum time between triggers for the same user
          </p>
        </div>
          </div>
        )}
      </div>
    );
  }

  return <div className="text-sm text-gray-500">Unknown trigger type: {nodeType}</div>;
};
