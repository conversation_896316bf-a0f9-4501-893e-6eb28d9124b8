"use client";

import React from "react";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Button } from "@/components/ui/button";
import { Plus, Trash2 } from "lucide-react";
import { CredentialSelector } from "@/components/credentials/credential-selector";

interface ActionConfigProps {
  nodeType: string;
  config: Record<string, any>;
  onUpdate: (config: Record<string, any>) => void;
  teamId?: string;
}

export const ActionConfig = ({ nodeType, config, onUpdate, teamId }: ActionConfigProps) => {
  const handleChange = (key: string, value: any) => {
    onUpdate({ [key]: value });
  };

  const addArrayItem = (key: string, defaultItem: any) => {
    const currentArray = config[key] || [];
    onUpdate({
      [key]: [...currentArray, defaultItem],
    });
  };

  const removeArrayItem = (key: string, index: number) => {
    const currentArray = config[key] || [];
    onUpdate({
      [key]: currentArray.filter((_: any, i: number) => i !== index),
    });
  };

  const updateArrayItem = (key: string, index: number, value: any) => {
    const currentArray = config[key] || [];
    const newArray = [...currentArray];
    newArray[index] = value;
    onUpdate({
      [key]: newArray,
    });
  };

  if (nodeType === 'response') {
    return (
      <div className="space-y-6">
        <div>
          <Label htmlFor="response_type">Response Type</Label>
          <Select value={config.response_type || 'text'} onValueChange={(value) => handleChange('response_type', value)}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="text">Text</SelectItem>
              <SelectItem value="markdown">Markdown</SelectItem>
              <SelectItem value="html">HTML</SelectItem>
              <SelectItem value="json">JSON</SelectItem>
              <SelectItem value="template">Template</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div>
          <Label htmlFor="message">Message Content</Label>
          <Textarea
            id="message"
            value={config.message || ''}
            onChange={(e) => handleChange('message', e.target.value)}
            rows={6}
            placeholder="Enter your response message here..."
          />
          <p className="text-xs text-gray-500 mt-1">
            Use {`{{variable}}`} syntax to include dynamic content
          </p>
        </div>

        {config.response_type === 'template' && (
          <div>
            <Label htmlFor="template_engine">Template Engine</Label>
            <Select value={config.template_engine || 'handlebars'} onValueChange={(value) => handleChange('template_engine', value)}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="handlebars">Handlebars</SelectItem>
                <SelectItem value="mustache">Mustache</SelectItem>
                <SelectItem value="ejs">EJS</SelectItem>
                <SelectItem value="nunjucks">Nunjucks</SelectItem>
              </SelectContent>
            </Select>
          </div>
        )}

        <div>
          <Label htmlFor="delay">Delay (milliseconds)</Label>
          <Input
            id="delay"
            type="number"
            value={config.delay || 0}
            onChange={(e) => handleChange('delay', parseInt(e.target.value))}
            min="0"
            max="60000"
          />
        </div>

        <div className="flex items-center space-x-2">
          <Switch
            id="typing_indicator"
            checked={config.typing_indicator || false}
            onCheckedChange={(checked) => handleChange('typing_indicator', checked)}
          />
          <Label htmlFor="typing_indicator">Show Typing Indicator</Label>
        </div>

        <div className="flex items-center space-x-2">
          <Switch
            id="parse_markdown"
            checked={config.parse_markdown !== false}
            onCheckedChange={(checked) => handleChange('parse_markdown', checked)}
          />
          <Label htmlFor="parse_markdown">Parse Markdown</Label>
        </div>

        <div>
          <Label>Quick Replies</Label>
          <div className="space-y-2 mt-2">
            {(config.quick_replies || []).map((reply: string, index: number) => (
              <div key={index} className="flex space-x-2">
                <Input
                  placeholder="Quick reply text"
                  value={reply}
                  onChange={(e) => updateArrayItem('quick_replies', index, e.target.value)}
                />
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => removeArrayItem('quick_replies', index)}
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            ))}
            <Button
              variant="outline"
              size="sm"
              onClick={() => addArrayItem('quick_replies', '')}
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Quick Reply
            </Button>
          </div>
        </div>
      </div>
    );
  }

  if (nodeType === 'api_call') {
    return (
      <div className="space-y-6">
        {/* Credential Selection */}
        {teamId && (
          <div>
            <Label htmlFor="credentialId">API Credential</Label>
            <CredentialSelector
              teamId={teamId}
              value={config.credentialId}
              onChange={(credentialId) => handleChange('credentialId', credentialId)}
              placeholder="Select API credential (optional)..."
              allowCreate={true}
              allowTest={true}
            />
            <p className="text-sm text-gray-600 mt-1">
              Optional: Use stored credentials for authentication
            </p>
          </div>
        )}

        <div>
          <Label htmlFor="url">URL</Label>
          <Input
            id="url"
            value={config.url || ''}
            onChange={(e) => handleChange('url', e.target.value)}
            placeholder="https://api.example.com/endpoint"
          />
        </div>

        <div>
          <Label htmlFor="method">HTTP Method</Label>
          <Select value={config.method || 'GET'} onValueChange={(value) => handleChange('method', value)}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="GET">GET</SelectItem>
              <SelectItem value="POST">POST</SelectItem>
              <SelectItem value="PUT">PUT</SelectItem>
              <SelectItem value="PATCH">PATCH</SelectItem>
              <SelectItem value="DELETE">DELETE</SelectItem>
              <SelectItem value="HEAD">HEAD</SelectItem>
              <SelectItem value="OPTIONS">OPTIONS</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div>
          <Label>Headers</Label>
          <div className="space-y-2 mt-2">
            {(config.headers || []).map((header: any, index: number) => (
              <div key={index} className="grid grid-cols-2 gap-2">
                <Input
                  placeholder="Header name"
                  value={header.name || ''}
                  onChange={(e) => updateArrayItem('headers', index, { ...header, name: e.target.value })}
                />
                <div className="flex space-x-2">
                  <Input
                    placeholder="Header value"
                    value={header.value || ''}
                    onChange={(e) => updateArrayItem('headers', index, { ...header, value: e.target.value })}
                  />
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => removeArrayItem('headers', index)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}
            <Button
              variant="outline"
              size="sm"
              onClick={() => addArrayItem('headers', { name: '', value: '' })}
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Header
            </Button>
          </div>
        </div>

        <div>
          <Label htmlFor="body_type">Body Type</Label>
          <Select value={config.body_type || 'json'} onValueChange={(value) => handleChange('body_type', value)}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="none">None</SelectItem>
              <SelectItem value="json">JSON</SelectItem>
              <SelectItem value="form">Form Data</SelectItem>
              <SelectItem value="text">Raw Text</SelectItem>
              <SelectItem value="xml">XML</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {config.body_type !== 'none' && (
          <div>
            <Label htmlFor="body">Request Body</Label>
            <Textarea
              id="body"
              value={config.body || ''}
              onChange={(e) => handleChange('body', e.target.value)}
              rows={6}
              placeholder={config.body_type === 'json' ? '{"key": "{{input.value}}"}' : 'Request body content'}
              className="font-mono text-sm"
            />
          </div>
        )}

        <div>
          <Label htmlFor="timeout">Timeout (seconds)</Label>
          <Input
            id="timeout"
            type="number"
            value={config.timeout || 30}
            onChange={(e) => handleChange('timeout', parseInt(e.target.value))}
            min="1"
            max="300"
          />
        </div>

        <div>
          <Label htmlFor="max_retries">Max Retries</Label>
          <Input
            id="max_retries"
            type="number"
            value={config.max_retries || 3}
            onChange={(e) => handleChange('max_retries', parseInt(e.target.value))}
            min="0"
            max="10"
          />
        </div>

        <div className="flex items-center space-x-2">
          <Switch
            id="follow_redirects"
            checked={config.follow_redirects !== false}
            onCheckedChange={(checked) => handleChange('follow_redirects', checked)}
          />
          <Label htmlFor="follow_redirects">Follow Redirects</Label>
        </div>

        <div className="flex items-center space-x-2">
          <Switch
            id="verify_ssl"
            checked={config.verify_ssl !== false}
            onCheckedChange={(checked) => handleChange('verify_ssl', checked)}
          />
          <Label htmlFor="verify_ssl">Verify SSL Certificate</Label>
        </div>

        <div>
          <Label htmlFor="response_format">Expected Response Format</Label>
          <Select value={config.response_format || 'json'} onValueChange={(value) => handleChange('response_format', value)}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="json">JSON</SelectItem>
              <SelectItem value="text">Text</SelectItem>
              <SelectItem value="xml">XML</SelectItem>
              <SelectItem value="html">HTML</SelectItem>
              <SelectItem value="binary">Binary</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
    );
  }

  if (nodeType === 'database') {
    return (
      <div className="space-y-6">
        <div>
          <Label htmlFor="database_type">Database Type</Label>
          <Select value={config.database_type || 'postgresql'} onValueChange={(value) => handleChange('database_type', value)}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="postgresql">PostgreSQL</SelectItem>
              <SelectItem value="mysql">MySQL</SelectItem>
              <SelectItem value="sqlite">SQLite</SelectItem>
              <SelectItem value="mongodb">MongoDB</SelectItem>
              <SelectItem value="redis">Redis</SelectItem>
              <SelectItem value="elasticsearch">Elasticsearch</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div>
          <Label htmlFor="connection_string">Connection String</Label>
          <Input
            id="connection_string"
            type="password"
            value={config.connection_string || ''}
            onChange={(e) => handleChange('connection_string', e.target.value)}
            placeholder="postgresql://user:password@host:port/database"
          />
        </div>

        <div>
          <Label htmlFor="operation">Operation</Label>
          <Select value={config.operation || 'select'} onValueChange={(value) => handleChange('operation', value)}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="select">SELECT</SelectItem>
              <SelectItem value="insert">INSERT</SelectItem>
              <SelectItem value="update">UPDATE</SelectItem>
              <SelectItem value="delete">DELETE</SelectItem>
              <SelectItem value="custom">Custom Query</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {config.operation === 'select' && (
          <div className="space-y-3">
            <div>
              <Label htmlFor="table">Table</Label>
              <Input
                id="table"
                value={config.table || ''}
                onChange={(e) => handleChange('table', e.target.value)}
                placeholder="users"
              />
            </div>
            <div>
              <Label htmlFor="columns">Columns (comma-separated)</Label>
              <Input
                id="columns"
                value={config.columns || '*'}
                onChange={(e) => handleChange('columns', e.target.value)}
                placeholder="id, name, email"
              />
            </div>
            <div>
              <Label htmlFor="where_clause">WHERE Clause</Label>
              <Input
                id="where_clause"
                value={config.where_clause || ''}
                onChange={(e) => handleChange('where_clause', e.target.value)}
                placeholder="status = 'active'"
              />
            </div>
            <div>
              <Label htmlFor="order_by">ORDER BY</Label>
              <Input
                id="order_by"
                value={config.order_by || ''}
                onChange={(e) => handleChange('order_by', e.target.value)}
                placeholder="created_at DESC"
              />
            </div>
            <div>
              <Label htmlFor="limit">LIMIT</Label>
              <Input
                id="limit"
                type="number"
                value={config.limit || ''}
                onChange={(e) => handleChange('limit', e.target.value ? parseInt(e.target.value) : '')}
                placeholder="100"
              />
            </div>
          </div>
        )}

        {config.operation === 'insert' && (
          <div className="space-y-3">
            <div>
              <Label htmlFor="table">Table</Label>
              <Input
                id="table"
                value={config.table || ''}
                onChange={(e) => handleChange('table', e.target.value)}
                placeholder="users"
              />
            </div>
            <div>
              <Label htmlFor="data">Data (JSON)</Label>
              <Textarea
                id="data"
                value={config.data || ''}
                onChange={(e) => handleChange('data', e.target.value)}
                rows={4}
                placeholder='{"name": "{{input.name}}", "email": "{{input.email}}"}'
                className="font-mono text-sm"
              />
            </div>
          </div>
        )}

        {config.operation === 'custom' && (
          <div>
            <Label htmlFor="query">SQL Query</Label>
            <Textarea
              id="query"
              value={config.query || ''}
              onChange={(e) => handleChange('query', e.target.value)}
              rows={6}
              placeholder="SELECT * FROM users WHERE email = $1"
              className="font-mono text-sm"
            />
            <p className="text-xs text-gray-500 mt-1">
              Use $1, $2, etc. for parameterized queries
            </p>
          </div>
        )}

        <div>
          <Label>Query Parameters</Label>
          <div className="space-y-2 mt-2">
            {(config.parameters || []).map((param: string, index: number) => (
              <div key={index} className="flex space-x-2">
                <Input
                  placeholder="Parameter value"
                  value={param}
                  onChange={(e) => updateArrayItem('parameters', index, e.target.value)}
                />
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => removeArrayItem('parameters', index)}
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            ))}
            <Button
              variant="outline"
              size="sm"
              onClick={() => addArrayItem('parameters', '')}
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Parameter
            </Button>
          </div>
        </div>

        <div>
          <Label htmlFor="pool_size">Connection Pool Size</Label>
          <Input
            id="pool_size"
            type="number"
            value={config.pool_size || 10}
            onChange={(e) => handleChange('pool_size', parseInt(e.target.value))}
            min="1"
            max="100"
          />
        </div>

        <div className="flex items-center space-x-2">
          <Switch
            id="use_transaction"
            checked={config.use_transaction || false}
            onCheckedChange={(checked) => handleChange('use_transaction', checked)}
          />
          <Label htmlFor="use_transaction">Use Transaction</Label>
        </div>
      </div>
    );
  }

  if (nodeType === 'notification') {
    return (
      <div className="space-y-6">
        <div>
          <Label htmlFor="notification_type">Notification Type</Label>
          <Select value={config.notification_type || 'email'} onValueChange={(value) => handleChange('notification_type', value)}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="email">Email</SelectItem>
              <SelectItem value="sms">SMS</SelectItem>
              <SelectItem value="push">Push Notification</SelectItem>
              <SelectItem value="slack">Slack</SelectItem>
              <SelectItem value="discord">Discord</SelectItem>
              <SelectItem value="webhook">Webhook</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {config.notification_type === 'email' && (
          <div className="space-y-3">
            <div>
              <Label htmlFor="to">To (comma-separated)</Label>
              <Input
                id="to"
                value={config.to || ''}
                onChange={(e) => handleChange('to', e.target.value)}
                placeholder="<EMAIL>, <EMAIL>"
              />
            </div>
            <div>
              <Label htmlFor="subject">Subject</Label>
              <Input
                id="subject"
                value={config.subject || ''}
                onChange={(e) => handleChange('subject', e.target.value)}
                placeholder="Notification Subject"
              />
            </div>
            <div>
              <Label htmlFor="body">Body</Label>
              <Textarea
                id="body"
                value={config.body || ''}
                onChange={(e) => handleChange('body', e.target.value)}
                rows={6}
                placeholder="Email body content..."
              />
            </div>
          </div>
        )}

        {config.notification_type === 'sms' && (
          <div className="space-y-3">
            <div>
              <Label htmlFor="phone_number">Phone Number</Label>
              <Input
                id="phone_number"
                value={config.phone_number || ''}
                onChange={(e) => handleChange('phone_number', e.target.value)}
                placeholder="+1234567890"
              />
            </div>
            <div>
              <Label htmlFor="message">Message</Label>
              <Textarea
                id="message"
                value={config.message || ''}
                onChange={(e) => handleChange('message', e.target.value)}
                rows={3}
                placeholder="SMS message content..."
                maxLength={160}
              />
              <p className="text-xs text-gray-500 mt-1">
                {(config.message || '').length}/160 characters
              </p>
            </div>
          </div>
        )}

        {config.notification_type === 'slack' && (
          <div className="space-y-3">
            <div>
              <Label htmlFor="webhook_url">Slack Webhook URL</Label>
              <Input
                id="webhook_url"
                type="password"
                value={config.webhook_url || ''}
                onChange={(e) => handleChange('webhook_url', e.target.value)}
                placeholder="https://hooks.slack.com/services/..."
              />
            </div>
            <div>
              <Label htmlFor="channel">Channel</Label>
              <Input
                id="channel"
                value={config.channel || ''}
                onChange={(e) => handleChange('channel', e.target.value)}
                placeholder="#general"
              />
            </div>
            <div>
              <Label htmlFor="username">Bot Username</Label>
              <Input
                id="username"
                value={config.username || 'Workflow Bot'}
                onChange={(e) => handleChange('username', e.target.value)}
                placeholder="Workflow Bot"
              />
            </div>
            <div>
              <Label htmlFor="text">Message</Label>
              <Textarea
                id="text"
                value={config.text || ''}
                onChange={(e) => handleChange('text', e.target.value)}
                rows={4}
                placeholder="Slack message content..."
              />
            </div>
          </div>
        )}

        <div>
          <Label htmlFor="priority">Priority</Label>
          <Select value={config.priority || 'normal'} onValueChange={(value) => handleChange('priority', value)}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="low">Low</SelectItem>
              <SelectItem value="normal">Normal</SelectItem>
              <SelectItem value="high">High</SelectItem>
              <SelectItem value="urgent">Urgent</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="flex items-center space-x-2">
          <Switch
            id="retry_on_failure"
            checked={config.retry_on_failure !== false}
            onCheckedChange={(checked) => handleChange('retry_on_failure', checked)}
          />
          <Label htmlFor="retry_on_failure">Retry on Failure</Label>
        </div>

        <div>
          <Label htmlFor="max_retries">Max Retries</Label>
          <Input
            id="max_retries"
            type="number"
            value={config.max_retries || 3}
            onChange={(e) => handleChange('max_retries', parseInt(e.target.value))}
            min="0"
            max="10"
          />
        </div>
      </div>
    );
  }

  return <div className="text-sm text-gray-500">Unknown action type: {nodeType}</div>;
};
