"use client";

import React from "react";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Button } from "@/components/ui/button";
import { Plus, Trash2 } from "lucide-react";
import { CredentialSelector } from "@/components/credentials/credential-selector";

interface ToolConfigProps {
  nodeType: string;
  config: Record<string, any>;
  onUpdate: (config: Record<string, any>) => void;
  teamId: string;
}

export const ToolConfig = ({ nodeType, config, onUpdate, teamId }: ToolConfigProps) => {
  const handleChange = (key: string, value: any) => {
    onUpdate({ [key]: value });
  };

  const addArrayItem = (key: string, defaultItem: any) => {
    const currentArray = config[key] || [];
    onUpdate({
      [key]: [...currentArray, defaultItem],
    });
  };

  const removeArrayItem = (key: string, index: number) => {
    const currentArray = config[key] || [];
    onUpdate({
      [key]: currentArray.filter((_: any, i: number) => i !== index),
    });
  };

  const updateArrayItem = (key: string, index: number, value: any) => {
    const currentArray = config[key] || [];
    const newArray = [...currentArray];
    newArray[index] = value;
    onUpdate({
      [key]: newArray,
    });
  };

  if (nodeType === 'web_search') {
    return (
      <div className="space-y-6">
        <div>
          <Label htmlFor="search_engine">Search Engine</Label>
          <Select value={config.search_engine || 'google'} onValueChange={(value) => handleChange('search_engine', value)}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="google">Google</SelectItem>
              <SelectItem value="bing">Bing</SelectItem>
              <SelectItem value="duckduckgo">DuckDuckGo</SelectItem>
              <SelectItem value="searx">SearX</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div>
          <Label>Search API Credentials</Label>
          <CredentialSelector
            teamId={teamId}
            appType="google"
            value={config.credentialId || ''}
            onChange={(credentialId) => handleChange('credentialId', credentialId)}
            placeholder="Select Google API credentials"
          />
        </div>

        <div>
          <Label htmlFor="search_engine_id">Search Engine ID (Google CSE)</Label>
          <Input
            id="search_engine_id"
            value={config.search_engine_id || ''}
            onChange={(e) => handleChange('search_engine_id', e.target.value)}
            placeholder="Custom Search Engine ID"
          />
        </div>

        <div>
          <Label htmlFor="max_results">Max Results</Label>
          <Input
            id="max_results"
            type="number"
            value={config.max_results || 10}
            onChange={(e) => handleChange('max_results', parseInt(e.target.value))}
            min="1"
            max="100"
          />
        </div>

        <div>
          <Label htmlFor="language">Language</Label>
          <Select value={config.language || 'en'} onValueChange={(value) => handleChange('language', value)}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="en">English</SelectItem>
              <SelectItem value="es">Spanish</SelectItem>
              <SelectItem value="fr">French</SelectItem>
              <SelectItem value="de">German</SelectItem>
              <SelectItem value="it">Italian</SelectItem>
              <SelectItem value="pt">Portuguese</SelectItem>
              <SelectItem value="ru">Russian</SelectItem>
              <SelectItem value="ja">Japanese</SelectItem>
              <SelectItem value="ko">Korean</SelectItem>
              <SelectItem value="zh">Chinese</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div>
          <Label htmlFor="country">Country</Label>
          <Select value={config.country || 'us'} onValueChange={(value) => handleChange('country', value)}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="us">United States</SelectItem>
              <SelectItem value="uk">United Kingdom</SelectItem>
              <SelectItem value="ca">Canada</SelectItem>
              <SelectItem value="au">Australia</SelectItem>
              <SelectItem value="de">Germany</SelectItem>
              <SelectItem value="fr">France</SelectItem>
              <SelectItem value="jp">Japan</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="flex items-center space-x-2">
          <Switch
            id="safe_search"
            checked={config.safe_search !== false}
            onCheckedChange={(checked) => handleChange('safe_search', checked)}
          />
          <Label htmlFor="safe_search">Safe Search</Label>
        </div>

        <div className="flex items-center space-x-2">
          <Switch
            id="include_snippets"
            checked={config.include_snippets !== false}
            onCheckedChange={(checked) => handleChange('include_snippets', checked)}
          />
          <Label htmlFor="include_snippets">Include Snippets</Label>
        </div>

        <div>
          <Label htmlFor="timeout">Timeout (seconds)</Label>
          <Input
            id="timeout"
            type="number"
            value={config.timeout || 30}
            onChange={(e) => handleChange('timeout', parseInt(e.target.value))}
            min="5"
            max="120"
          />
        </div>
      </div>
    );
  }

  if (nodeType === 'calculator') {
    return (
      <div className="space-y-6">
        <div>
          <Label htmlFor="precision">Decimal Precision</Label>
          <Input
            id="precision"
            type="number"
            value={config.precision || 10}
            onChange={(e) => handleChange('precision', parseInt(e.target.value))}
            min="1"
            max="50"
          />
        </div>

        <div>
          <Label htmlFor="angle_unit">Angle Unit</Label>
          <Select value={config.angle_unit || 'radians'} onValueChange={(value) => handleChange('angle_unit', value)}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="radians">Radians</SelectItem>
              <SelectItem value="degrees">Degrees</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="flex items-center space-x-2">
          <Switch
            id="allow_complex"
            checked={config.allow_complex || false}
            onCheckedChange={(checked) => handleChange('allow_complex', checked)}
          />
          <Label htmlFor="allow_complex">Allow Complex Numbers</Label>
        </div>

        <div className="flex items-center space-x-2">
          <Switch
            id="scientific_notation"
            checked={config.scientific_notation || false}
            onCheckedChange={(checked) => handleChange('scientific_notation', checked)}
          />
          <Label htmlFor="scientific_notation">Scientific Notation</Label>
        </div>

        <div>
          <Label>Allowed Functions</Label>
          <div className="grid grid-cols-2 gap-2 mt-2">
            {['sin', 'cos', 'tan', 'log', 'ln', 'sqrt', 'pow', 'abs', 'floor', 'ceil', 'round'].map((func) => (
              <div key={func} className="flex items-center space-x-2">
                <Switch
                  id={`func_${func}`}
                  checked={(config.allowed_functions || []).includes(func)}
                  onCheckedChange={(checked) => {
                    const current = config.allowed_functions || [];
                    if (checked) {
                      handleChange('allowed_functions', [...current, func]);
                    } else {
                      handleChange('allowed_functions', current.filter((f: string) => f !== func));
                    }
                  }}
                />
                <Label htmlFor={`func_${func}`} className="text-sm">{func}</Label>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (nodeType === 'email') {
    return (
      <div className="space-y-6">
        <div>
          <Label>Email Credentials</Label>
          <CredentialSelector
            teamId={teamId}
            appType="email"
            value={config.credentialId || ''}
            onChange={(credentialId) => handleChange('credentialId', credentialId)}
            placeholder="Select email credentials"
          />
        </div>

        <div>
          <Label htmlFor="from_name">From Name</Label>
          <Input
            id="from_name"
            value={config.from_name || ''}
            onChange={(e) => handleChange('from_name', e.target.value)}
            placeholder="Your Name"
          />
        </div>

        <div className="flex items-center space-x-2">
          <Switch
            id="use_tls"
            checked={config.use_tls !== false}
            onCheckedChange={(checked) => handleChange('use_tls', checked)}
          />
          <Label htmlFor="use_tls">Use TLS</Label>
        </div>

        <div className="flex items-center space-x-2">
          <Switch
            id="use_ssl"
            checked={config.use_ssl || false}
            onCheckedChange={(checked) => handleChange('use_ssl', checked)}
          />
          <Label htmlFor="use_ssl">Use SSL</Label>
        </div>

        <div>
          <Label htmlFor="timeout">Timeout (seconds)</Label>
          <Input
            id="timeout"
            type="number"
            value={config.timeout || 30}
            onChange={(e) => handleChange('timeout', parseInt(e.target.value))}
            min="5"
            max="300"
          />
        </div>

        <div>
          <Label htmlFor="max_retries">Max Retries</Label>
          <Input
            id="max_retries"
            type="number"
            value={config.max_retries || 3}
            onChange={(e) => handleChange('max_retries', parseInt(e.target.value))}
            min="0"
            max="10"
          />
        </div>

        <div>
          <Label htmlFor="email_template">Default Email Template</Label>
          <Textarea
            id="email_template"
            value={config.email_template || ''}
            onChange={(e) => handleChange('email_template', e.target.value)}
            rows={6}
            placeholder="HTML email template with {{variables}}"
          />
        </div>
      </div>
    );
  }

  if (nodeType === 'memory') {
    return (
      <div className="space-y-6">
        <div>
          <Label htmlFor="memory_type">Memory Type</Label>
          <Select value={config.memory_type || 'vector'} onValueChange={(value) => handleChange('memory_type', value)}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="vector">Vector Database</SelectItem>
              <SelectItem value="key_value">Key-Value Store</SelectItem>
              <SelectItem value="graph">Graph Database</SelectItem>
              <SelectItem value="relational">Relational Database</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Database Credentials for external databases */}
        {config.memory_type !== 'vector' && teamId && (
          <div>
            <Label>Database Credentials</Label>
            <CredentialSelector
              teamId={teamId}
              value={config.credentialId}
              onChange={(credentialId) => handleChange('credentialId', credentialId)}
              placeholder="Select database credentials..."
              allowCreate={true}
              allowTest={true}
            />
            <p className="text-sm text-gray-600 mt-1">
              Required for external database connections
            </p>
          </div>
        )}

        <div>
          <Label htmlFor="embedding_model">Embedding Model</Label>
          <Select value={config.embedding_model || 'text-embedding-3-small'} onValueChange={(value) => handleChange('embedding_model', value)}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="text-embedding-3-small">OpenAI Small</SelectItem>
              <SelectItem value="text-embedding-3-large">OpenAI Large</SelectItem>
              <SelectItem value="text-embedding-ada-002">OpenAI Ada v2</SelectItem>
              <SelectItem value="sentence-transformers">Sentence Transformers</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* AI Credentials for embedding models */}
        {teamId && config.embedding_model?.includes('openai') && (
          <div>
            <Label>AI Service Credentials</Label>
            <CredentialSelector
              teamId={teamId}
              appType="openai"
              value={config.aiCredentialId}
              onChange={(credentialId) => handleChange('aiCredentialId', credentialId)}
              placeholder="Select OpenAI credentials for embeddings..."
              allowCreate={true}
              allowTest={true}
            />
          </div>
        )}

        <div>
          <Label htmlFor="max_entries">Max Entries</Label>
          <Input
            id="max_entries"
            type="number"
            value={config.max_entries || 1000}
            onChange={(e) => handleChange('max_entries', parseInt(e.target.value))}
            min="10"
            max="100000"
          />
        </div>

        <div>
          <Label htmlFor="similarity_threshold">Similarity Threshold</Label>
          <Input
            id="similarity_threshold"
            type="number"
            step="0.01"
            min="0"
            max="1"
            value={config.similarity_threshold || 0.8}
            onChange={(e) => handleChange('similarity_threshold', parseFloat(e.target.value))}
          />
        </div>

        <div className="flex items-center space-x-2">
          <Switch
            id="auto_cleanup"
            checked={config.auto_cleanup || false}
            onCheckedChange={(checked) => handleChange('auto_cleanup', checked)}
          />
          <Label htmlFor="auto_cleanup">Auto Cleanup Old Entries</Label>
        </div>

        <div>
          <Label htmlFor="cleanup_days">Cleanup After (days)</Label>
          <Input
            id="cleanup_days"
            type="number"
            value={config.cleanup_days || 30}
            onChange={(e) => handleChange('cleanup_days', parseInt(e.target.value))}
            min="1"
            max="365"
          />
        </div>
      </div>
    );
  }

  // Default configuration for other tools
  return (
    <div className="space-y-6">
      <div>
        <Label htmlFor="timeout">Timeout (seconds)</Label>
        <Input
          id="timeout"
          type="number"
          value={config.timeout || 30}
          onChange={(e) => handleChange('timeout', parseInt(e.target.value))}
          min="1"
          max="300"
        />
      </div>

      <div>
        <Label htmlFor="max_retries">Max Retries</Label>
        <Input
          id="max_retries"
          type="number"
          value={config.max_retries || 3}
          onChange={(e) => handleChange('max_retries', parseInt(e.target.value))}
          min="0"
          max="10"
        />
      </div>

      <div className="flex items-center space-x-2">
        <Switch
          id="cache_results"
          checked={config.cache_results || false}
          onCheckedChange={(checked) => handleChange('cache_results', checked)}
        />
        <Label htmlFor="cache_results">Cache Results</Label>
      </div>

      <div>
        <Label htmlFor="custom_config">Custom Configuration (JSON)</Label>
        <Textarea
          id="custom_config"
          value={JSON.stringify(config.custom_config || {}, null, 2)}
          onChange={(e) => {
            try {
              const parsed = JSON.parse(e.target.value);
              handleChange('custom_config', parsed);
            } catch (error) {
              // Invalid JSON, don't update
            }
          }}
          rows={8}
          className="font-mono text-xs"
          placeholder='{"key": "value"}'
        />
      </div>
    </div>
  );
};
