"use client";

import React from "react";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Button } from "@/components/ui/button";
import { Slider } from "@/components/ui/slider";
import { Plus, Trash2, Brain, Zap } from "lucide-react";
import { CredentialSelector } from "@/components/credentials/credential-selector";

interface AIAgentConfigProps {
  nodeType: string;
  config: Record<string, any>;
  data: Record<string, any>;
  onUpdate: (config: Record<string, any>) => void;
  onDataUpdate: (data: Record<string, any>) => void;
  teamId?: string;
}

// Helper function to determine app type from model
const getAppTypeFromModel = (model?: string): any => {
  if (!model) return undefined;

  if (model.startsWith('gpt-')) return 'openai';
  if (model.startsWith('claude-')) return 'anthropic';
  if (model.startsWith('llama') || model.startsWith('mixtral') || model.startsWith('gemma')) return 'groq';
  if (model.startsWith('gemini')) return 'google_gemini';

  return 'openai'; // Default fallback
};

export const AIAgentConfig = ({ nodeType, config, data, onUpdate, onDataUpdate, teamId }: AIAgentConfigProps) => {
  const handleChange = (key: string, value: any) => {
    onUpdate({ [key]: value });
  };

  const handleDataChange = (key: string, value: any) => {
    onDataUpdate({ [key]: value });
  };

  const addArrayItem = (key: string, defaultItem: any) => {
    const currentArray = config[key] || [];
    onUpdate({
      [key]: [...currentArray, defaultItem],
    });
  };

  const removeArrayItem = (key: string, index: number) => {
    const currentArray = config[key] || [];
    onUpdate({
      [key]: currentArray.filter((_: any, i: number) => i !== index),
    });
  };

  const updateArrayItem = (key: string, index: number, value: any) => {
    const currentArray = config[key] || [];
    const newArray = [...currentArray];
    newArray[index] = value;
    onUpdate({
      [key]: newArray,
    });
  };

  return (
    <div className="space-y-6">
      {/* Model Configuration */}
      <div className="space-y-4">
        <div className="flex items-center space-x-2">
          <Brain className="h-5 w-5 text-blue-600" />
          <h4 className="font-medium text-gray-900 dark:text-white">Model Configuration</h4>
        </div>

        {/* Credential Selection */}
        {teamId && (
          <div>
            <Label htmlFor="credentialId">AI Service Credential</Label>
            <CredentialSelector
              teamId={teamId}
              appType={getAppTypeFromModel(config.model)}
              value={config.credentialId}
              onChange={(credentialId) => handleChange('credentialId', credentialId)}
              placeholder="Select AI service credential..."
              allowCreate={true}
              allowTest={true}
            />
          </div>
        )}

        <div>
          <Label htmlFor="model">AI Model</Label>
          <Select value={config.model || 'gpt-4o'} onValueChange={(value) => handleChange('model', value)}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="gpt-4o">GPT-4o (Latest)</SelectItem>
              <SelectItem value="gpt-4o-mini">GPT-4o Mini</SelectItem>
              <SelectItem value="gpt-4-turbo">GPT-4 Turbo</SelectItem>
              <SelectItem value="gpt-4">GPT-4</SelectItem>
              <SelectItem value="gpt-3.5-turbo">GPT-3.5 Turbo</SelectItem>
              <SelectItem value="claude-3-5-sonnet">Claude 3.5 Sonnet</SelectItem>
              <SelectItem value="claude-3-opus">Claude 3 Opus</SelectItem>
              <SelectItem value="claude-3-haiku">Claude 3 Haiku</SelectItem>
              <SelectItem value="gemini-pro">Gemini Pro</SelectItem>
              <SelectItem value="llama-2-70b">Llama 2 70B</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div>
          <Label htmlFor="temperature">Temperature: {config.temperature || 0.7}</Label>
          <Slider
            value={[config.temperature || 0.7]}
            onValueChange={(value) => handleChange('temperature', value[0])}
            max={2}
            min={0}
            step={0.1}
            className="mt-2"
          />
          <p className="text-xs text-gray-500 mt-1">
            Controls randomness. Lower = more focused, Higher = more creative
          </p>
        </div>

        <div>
          <Label htmlFor="max_tokens">Max Tokens</Label>
          <Input
            id="max_tokens"
            type="number"
            value={config.max_tokens || 2048}
            onChange={(e) => handleChange('max_tokens', parseInt(e.target.value))}
            min="1"
            max="32000"
          />
          <p className="text-xs text-gray-500 mt-1">
            Maximum length of the response
          </p>
        </div>

        <div>
          <Label htmlFor="top_p">Top P: {config.top_p || 1}</Label>
          <Slider
            value={[config.top_p || 1]}
            onValueChange={(value) => handleChange('top_p', value[0])}
            max={1}
            min={0}
            step={0.1}
            className="mt-2"
          />
          <p className="text-xs text-gray-500 mt-1">
            Controls diversity via nucleus sampling
          </p>
        </div>

        <div>
          <Label htmlFor="frequency_penalty">Frequency Penalty: {config.frequency_penalty || 0}</Label>
          <Slider
            value={[config.frequency_penalty || 0]}
            onValueChange={(value) => handleChange('frequency_penalty', value[0])}
            max={2}
            min={-2}
            step={0.1}
            className="mt-2"
          />
          <p className="text-xs text-gray-500 mt-1">
            Reduces repetition of frequent tokens
          </p>
        </div>

        <div>
          <Label htmlFor="presence_penalty">Presence Penalty: {config.presence_penalty || 0}</Label>
          <Slider
            value={[config.presence_penalty || 0]}
            onValueChange={(value) => handleChange('presence_penalty', value[0])}
            max={2}
            min={-2}
            step={0.1}
            className="mt-2"
          />
          <p className="text-xs text-gray-500 mt-1">
            Encourages talking about new topics
          </p>
        </div>
      </div>

      {/* System Prompt */}
      <div className="space-y-4">
        <div className="flex items-center space-x-2">
          <Zap className="h-5 w-5 text-purple-600" />
          <h4 className="font-medium text-gray-900 dark:text-white">System Instructions</h4>
        </div>

        <div>
          <Label htmlFor="system_prompt">System Prompt</Label>
          <Textarea
            id="system_prompt"
            value={config.system_prompt || ''}
            onChange={(e) => handleChange('system_prompt', e.target.value)}
            rows={6}
            placeholder="You are a helpful AI assistant. Your role is to..."
          />
          <p className="text-xs text-gray-500 mt-1">
            Define the AI's personality, role, and behavior
          </p>
        </div>

        <div>
          <Label htmlFor="context_window">Context Window Strategy</Label>
          <Select value={config.context_strategy || 'sliding'} onValueChange={(value) => handleChange('context_strategy', value)}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="sliding">Sliding Window</SelectItem>
              <SelectItem value="summarize">Summarize Old Messages</SelectItem>
              <SelectItem value="truncate">Truncate Oldest</SelectItem>
              <SelectItem value="compress">Intelligent Compression</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div>
          <Label htmlFor="max_context_messages">Max Context Messages</Label>
          <Input
            id="max_context_messages"
            type="number"
            value={config.max_context_messages || 20}
            onChange={(e) => handleChange('max_context_messages', parseInt(e.target.value))}
            min="1"
            max="100"
          />
        </div>
      </div>

      {/* Memory Configuration */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Brain className="h-5 w-5 text-purple-600" />
            <h4 className="font-medium text-gray-900 dark:text-white">Memory & Knowledge</h4>
          </div>
          <Switch
            checked={data.memory || false}
            onCheckedChange={(checked) => handleDataChange('memory', checked)}
          />
        </div>

        {data.memory && (
          <div className="space-y-3 pl-7">
            <div>
              <Label htmlFor="memory_type">Memory Type</Label>
              <Select value={config.memory_type || 'vector'} onValueChange={(value) => handleChange('memory_type', value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="vector">Vector Database</SelectItem>
                  <SelectItem value="conversation">Conversation History</SelectItem>
                  <SelectItem value="knowledge_graph">Knowledge Graph</SelectItem>
                  <SelectItem value="hybrid">Hybrid (Vector + Graph)</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="memory_size">Memory Size (MB)</Label>
              <Input
                id="memory_size"
                type="number"
                value={config.memory_size || 100}
                onChange={(e) => handleChange('memory_size', parseInt(e.target.value))}
                min="10"
                max="1000"
              />
            </div>

            <div>
              <Label htmlFor="embedding_model">Embedding Model</Label>
              <Select value={config.embedding_model || 'text-embedding-3-small'} onValueChange={(value) => handleChange('embedding_model', value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="text-embedding-3-small">OpenAI Small</SelectItem>
                  <SelectItem value="text-embedding-3-large">OpenAI Large</SelectItem>
                  <SelectItem value="text-embedding-ada-002">OpenAI Ada v2</SelectItem>
                  <SelectItem value="sentence-transformers">Sentence Transformers</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="auto_save_conversations"
                checked={config.auto_save_conversations !== false}
                onCheckedChange={(checked) => handleChange('auto_save_conversations', checked)}
              />
              <Label htmlFor="auto_save_conversations">Auto-save Conversations</Label>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="cross_session_memory"
                checked={config.cross_session_memory || false}
                onCheckedChange={(checked) => handleChange('cross_session_memory', checked)}
              />
              <Label htmlFor="cross_session_memory">Cross-session Memory</Label>
            </div>
          </div>
        )}
      </div>

      {/* Tools Configuration */}
      <div className="space-y-4">
        <div className="flex items-center space-x-2">
          <Zap className="h-5 w-5 text-green-600" />
          <h4 className="font-medium text-gray-900 dark:text-white">Available Tools</h4>
        </div>

        <div>
          <Label>Tools</Label>
          <div className="space-y-2 mt-2">
            {(data.tools || []).map((tool: string, index: number) => (
              <div key={index} className="flex space-x-2">
                <Select
                  value={tool}
                  onValueChange={(value) => {
                    const newTools = [...(data.tools || [])];
                    newTools[index] = value;
                    handleDataChange('tools', newTools);
                  }}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="web_search">Web Search</SelectItem>
                    <SelectItem value="calculator">Calculator</SelectItem>
                    <SelectItem value="email">Email</SelectItem>
                    <SelectItem value="calendar">Calendar</SelectItem>
                    <SelectItem value="file_reader">File Reader</SelectItem>
                    <SelectItem value="image_generator">Image Generator</SelectItem>
                    <SelectItem value="code_interpreter">Code Interpreter</SelectItem>
                    <SelectItem value="database_query">Database Query</SelectItem>
                    <SelectItem value="api_call">API Call</SelectItem>
                  </SelectContent>
                </Select>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    const newTools = (data.tools || []).filter((_: any, i: number) => i !== index);
                    handleDataChange('tools', newTools);
                  }}
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            ))}
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                const newTools = [...(data.tools || []), 'web_search'];
                handleDataChange('tools', newTools);
              }}
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Tool
            </Button>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <Switch
            id="allow_tool_chaining"
            checked={config.allow_tool_chaining !== false}
            onCheckedChange={(checked) => handleChange('allow_tool_chaining', checked)}
          />
          <Label htmlFor="allow_tool_chaining">Allow Tool Chaining</Label>
        </div>

        <div>
          <Label htmlFor="max_tool_calls">Max Tool Calls per Response</Label>
          <Input
            id="max_tool_calls"
            type="number"
            value={config.max_tool_calls || 5}
            onChange={(e) => handleChange('max_tool_calls', parseInt(e.target.value))}
            min="1"
            max="20"
          />
        </div>
      </div>
    </div>
  );
};
