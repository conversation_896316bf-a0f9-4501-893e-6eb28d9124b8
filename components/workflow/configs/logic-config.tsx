"use client";

import React from "react";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Button } from "@/components/ui/button";
import { Plus, Trash2 } from "lucide-react";

interface LogicConfigProps {
  nodeType: string;
  config: Record<string, any>;
  onUpdate: (config: Record<string, any>) => void;
}

export const LogicConfig = ({ nodeType, config, onUpdate }: LogicConfigProps) => {
  const handleChange = (key: string, value: any) => {
    onUpdate({ [key]: value });
  };

  const addArrayItem = (key: string, defaultItem: any) => {
    const currentArray = config[key] || [];
    onUpdate({
      [key]: [...currentArray, defaultItem],
    });
  };

  const removeArrayItem = (key: string, index: number) => {
    const currentArray = config[key] || [];
    onUpdate({
      [key]: currentArray.filter((_: any, i: number) => i !== index),
    });
  };

  const updateArrayItem = (key: string, index: number, value: any) => {
    const currentArray = config[key] || [];
    const newArray = [...currentArray];
    newArray[index] = value;
    onUpdate({
      [key]: newArray,
    });
  };

  if (nodeType === 'condition') {
    return (
      <div className="space-y-6">
        <div>
          <Label htmlFor="condition_type">Condition Type</Label>
          <Select value={config.condition_type || 'simple'} onValueChange={(value) => handleChange('condition_type', value)}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="simple">Simple Comparison</SelectItem>
              <SelectItem value="expression">JavaScript Expression</SelectItem>
              <SelectItem value="regex">Regular Expression</SelectItem>
              <SelectItem value="multiple">Multiple Conditions</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {config.condition_type === 'simple' && (
          <div className="space-y-4">
            <div>
              <Label htmlFor="left_value">Left Value</Label>
              <Input
                id="left_value"
                value={config.left_value || ''}
                onChange={(e) => handleChange('left_value', e.target.value)}
                placeholder="{{input.field}} or static value"
              />
            </div>

            <div>
              <Label htmlFor="operator">Operator</Label>
              <Select value={config.operator || 'equals'} onValueChange={(value) => handleChange('operator', value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="equals">Equals (==)</SelectItem>
                  <SelectItem value="not_equals">Not Equals (!=)</SelectItem>
                  <SelectItem value="greater_than">Greater Than (&gt;)</SelectItem>
                  <SelectItem value="greater_equal">Greater or Equal (&gt;=)</SelectItem>
                  <SelectItem value="less_than">Less Than (&lt;)</SelectItem>
                  <SelectItem value="less_equal">Less or Equal (&lt;=)</SelectItem>
                  <SelectItem value="contains">Contains</SelectItem>
                  <SelectItem value="not_contains">Does Not Contain</SelectItem>
                  <SelectItem value="starts_with">Starts With</SelectItem>
                  <SelectItem value="ends_with">Ends With</SelectItem>
                  <SelectItem value="is_empty">Is Empty</SelectItem>
                  <SelectItem value="is_not_empty">Is Not Empty</SelectItem>
                  <SelectItem value="is_null">Is Null</SelectItem>
                  <SelectItem value="is_not_null">Is Not Null</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="right_value">Right Value</Label>
              <Input
                id="right_value"
                value={config.right_value || ''}
                onChange={(e) => handleChange('right_value', e.target.value)}
                placeholder="{{input.field}} or static value"
              />
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="case_sensitive"
                checked={config.case_sensitive || false}
                onCheckedChange={(checked) => handleChange('case_sensitive', checked)}
              />
              <Label htmlFor="case_sensitive">Case Sensitive</Label>
            </div>
          </div>
        )}

        {config.condition_type === 'expression' && (
          <div className="space-y-4">
            <div>
              <Label htmlFor="expression">JavaScript Expression</Label>
              <Textarea
                id="expression"
                value={config.expression || ''}
                onChange={(e) => handleChange('expression', e.target.value)}
                rows={4}
                placeholder="input.age > 18 && input.country === 'US'"
                className="font-mono text-sm"
              />
              <p className="text-xs text-gray-500 mt-1">
                Use JavaScript syntax. Available variables: input, context, env
              </p>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="strict_mode"
                checked={config.strict_mode || false}
                onCheckedChange={(checked) => handleChange('strict_mode', checked)}
              />
              <Label htmlFor="strict_mode">Strict Mode</Label>
            </div>
          </div>
        )}

        {config.condition_type === 'regex' && (
          <div className="space-y-4">
            <div>
              <Label htmlFor="input_field">Input Field</Label>
              <Input
                id="input_field"
                value={config.input_field || ''}
                onChange={(e) => handleChange('input_field', e.target.value)}
                placeholder="{{input.message}}"
              />
            </div>

            <div>
              <Label htmlFor="regex_pattern">Regular Expression</Label>
              <Input
                id="regex_pattern"
                value={config.regex_pattern || ''}
                onChange={(e) => handleChange('regex_pattern', e.target.value)}
                placeholder="^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
                className="font-mono text-sm"
              />
            </div>

            <div>
              <Label htmlFor="regex_flags">Flags</Label>
              <Input
                id="regex_flags"
                value={config.regex_flags || ''}
                onChange={(e) => handleChange('regex_flags', e.target.value)}
                placeholder="gi"
              />
              <p className="text-xs text-gray-500 mt-1">
                g = global, i = case insensitive, m = multiline
              </p>
            </div>
          </div>
        )}

        {config.condition_type === 'multiple' && (
          <div className="space-y-4">
            <div>
              <Label htmlFor="logic_operator">Logic Operator</Label>
              <Select value={config.logic_operator || 'AND'} onValueChange={(value) => handleChange('logic_operator', value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="AND">AND (all must be true)</SelectItem>
                  <SelectItem value="OR">OR (any must be true)</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label>Conditions</Label>
              <div className="space-y-3 mt-2">
                {(config.conditions || []).map((condition: any, index: number) => (
                  <div key={index} className="border rounded-lg p-3 space-y-2">
                    <div className="grid grid-cols-3 gap-2">
                      <Input
                        placeholder="Left value"
                        value={condition.left || ''}
                        onChange={(e) => updateArrayItem('conditions', index, { ...condition, left: e.target.value })}
                      />
                      <Select
                        value={condition.operator || 'equals'}
                        onValueChange={(value) => updateArrayItem('conditions', index, { ...condition, operator: value })}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="equals">==</SelectItem>
                          <SelectItem value="not_equals">!=</SelectItem>
                          <SelectItem value="greater_than">&gt;</SelectItem>
                          <SelectItem value="less_than">&lt;</SelectItem>
                          <SelectItem value="contains">contains</SelectItem>
                        </SelectContent>
                      </Select>
                      <Input
                        placeholder="Right value"
                        value={condition.right || ''}
                        onChange={(e) => updateArrayItem('conditions', index, { ...condition, right: e.target.value })}
                      />
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => removeArrayItem('conditions', index)}
                    >
                      <Trash2 className="h-4 w-4 mr-2" />
                      Remove
                    </Button>
                  </div>
                ))}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => addArrayItem('conditions', { left: '', operator: 'equals', right: '' })}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Condition
                </Button>
              </div>
            </div>
          </div>
        )}

        <div>
          <Label htmlFor="true_label">True Branch Label</Label>
          <Input
            id="true_label"
            value={config.true_label || 'Yes'}
            onChange={(e) => handleChange('true_label', e.target.value)}
          />
        </div>

        <div>
          <Label htmlFor="false_label">False Branch Label</Label>
          <Input
            id="false_label"
            value={config.false_label || 'No'}
            onChange={(e) => handleChange('false_label', e.target.value)}
          />
        </div>
      </div>
    );
  }

  if (nodeType === 'filter') {
    return (
      <div className="space-y-6">
        <div>
          <Label htmlFor="filter_type">Filter Type</Label>
          <Select value={config.filter_type || 'include'} onValueChange={(value) => handleChange('filter_type', value)}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="include">Include (whitelist)</SelectItem>
              <SelectItem value="exclude">Exclude (blacklist)</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div>
          <Label htmlFor="filter_field">Field to Filter</Label>
          <Input
            id="filter_field"
            value={config.filter_field || ''}
            onChange={(e) => handleChange('filter_field', e.target.value)}
            placeholder="{{input.items}}"
          />
        </div>

        <div>
          <Label htmlFor="filter_condition">Filter Condition</Label>
          <Textarea
            id="filter_condition"
            value={config.filter_condition || ''}
            onChange={(e) => handleChange('filter_condition', e.target.value)}
            rows={3}
            placeholder="item.status === 'active'"
            className="font-mono text-sm"
          />
          <p className="text-xs text-gray-500 mt-1">
            JavaScript expression. Use 'item' to reference each array element
          </p>
        </div>

        <div>
          <Label htmlFor="max_items">Max Items (0 = unlimited)</Label>
          <Input
            id="max_items"
            type="number"
            value={config.max_items || 0}
            onChange={(e) => handleChange('max_items', parseInt(e.target.value))}
            min="0"
          />
        </div>

        <div className="flex items-center space-x-2">
          <Switch
            id="preserve_order"
            checked={config.preserve_order !== false}
            onCheckedChange={(checked) => handleChange('preserve_order', checked)}
          />
          <Label htmlFor="preserve_order">Preserve Order</Label>
        </div>
      </div>
    );
  }

  if (nodeType === 'transform') {
    return (
      <div className="space-y-6">
        <div>
          <Label htmlFor="transform_type">Transform Type</Label>
          <Select value={config.transform_type || 'map'} onValueChange={(value) => handleChange('transform_type', value)}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="map">Map Fields</SelectItem>
              <SelectItem value="reduce">Reduce/Aggregate</SelectItem>
              <SelectItem value="flatten">Flatten Array</SelectItem>
              <SelectItem value="group">Group By</SelectItem>
              <SelectItem value="sort">Sort</SelectItem>
              <SelectItem value="custom">Custom JavaScript</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {config.transform_type === 'map' && (
          <div>
            <Label>Field Mappings</Label>
            <div className="space-y-2 mt-2">
              {(config.field_mappings || []).map((mapping: any, index: number) => (
                <div key={index} className="grid grid-cols-2 gap-2">
                  <Input
                    placeholder="Source field"
                    value={mapping.source || ''}
                    onChange={(e) => updateArrayItem('field_mappings', index, { ...mapping, source: e.target.value })}
                  />
                  <div className="flex space-x-2">
                    <Input
                      placeholder="Target field"
                      value={mapping.target || ''}
                      onChange={(e) => updateArrayItem('field_mappings', index, { ...mapping, target: e.target.value })}
                    />
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => removeArrayItem('field_mappings', index)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
              <Button
                variant="outline"
                size="sm"
                onClick={() => addArrayItem('field_mappings', { source: '', target: '' })}
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Mapping
              </Button>
            </div>
          </div>
        )}

        {config.transform_type === 'reduce' && (
          <div className="space-y-4">
            <div>
              <Label htmlFor="reduce_function">Reduce Function</Label>
              <Select value={config.reduce_function || 'sum'} onValueChange={(value) => handleChange('reduce_function', value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="sum">Sum</SelectItem>
                  <SelectItem value="average">Average</SelectItem>
                  <SelectItem value="min">Minimum</SelectItem>
                  <SelectItem value="max">Maximum</SelectItem>
                  <SelectItem value="count">Count</SelectItem>
                  <SelectItem value="custom">Custom</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="reduce_field">Field to Reduce</Label>
              <Input
                id="reduce_field"
                value={config.reduce_field || ''}
                onChange={(e) => handleChange('reduce_field', e.target.value)}
                placeholder="value"
              />
            </div>

            {config.reduce_function === 'custom' && (
              <div>
                <Label htmlFor="custom_reducer">Custom Reducer Function</Label>
                <Textarea
                  id="custom_reducer"
                  value={config.custom_reducer || ''}
                  onChange={(e) => handleChange('custom_reducer', e.target.value)}
                  rows={4}
                  placeholder="(accumulator, current) => accumulator + current.value"
                  className="font-mono text-sm"
                />
              </div>
            )}
          </div>
        )}

        {config.transform_type === 'group' && (
          <div className="space-y-4">
            <div>
              <Label htmlFor="group_by_field">Group By Field</Label>
              <Input
                id="group_by_field"
                value={config.group_by_field || ''}
                onChange={(e) => handleChange('group_by_field', e.target.value)}
                placeholder="category"
              />
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="include_count"
                checked={config.include_count || false}
                onCheckedChange={(checked) => handleChange('include_count', checked)}
              />
              <Label htmlFor="include_count">Include Count</Label>
            </div>
          </div>
        )}

        {config.transform_type === 'sort' && (
          <div className="space-y-4">
            <div>
              <Label htmlFor="sort_field">Sort By Field</Label>
              <Input
                id="sort_field"
                value={config.sort_field || ''}
                onChange={(e) => handleChange('sort_field', e.target.value)}
                placeholder="timestamp"
              />
            </div>

            <div>
              <Label htmlFor="sort_order">Sort Order</Label>
              <Select value={config.sort_order || 'asc'} onValueChange={(value) => handleChange('sort_order', value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="asc">Ascending</SelectItem>
                  <SelectItem value="desc">Descending</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        )}

        {config.transform_type === 'custom' && (
          <div>
            <Label htmlFor="custom_transform">Custom Transform Function</Label>
            <Textarea
              id="custom_transform"
              value={config.custom_transform || ''}
              onChange={(e) => handleChange('custom_transform', e.target.value)}
              rows={6}
              placeholder="(input) => { return transformedData; }"
              className="font-mono text-sm"
            />
            <p className="text-xs text-gray-500 mt-1">
              Function should return the transformed data
            </p>
          </div>
        )}
      </div>
    );
  }

  if (nodeType === 'loop') {
    return (
      <div className="space-y-6">
        <div>
          <Label htmlFor="loop_type">Loop Type</Label>
          <Select value={config.loop_type || 'for_each'} onValueChange={(value) => handleChange('loop_type', value)}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="for_each">For Each</SelectItem>
              <SelectItem value="while">While</SelectItem>
              <SelectItem value="for">For (count)</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {config.loop_type === 'for_each' && (
          <div>
            <Label htmlFor="array_field">Array Field</Label>
            <Input
              id="array_field"
              value={config.array_field || ''}
              onChange={(e) => handleChange('array_field', e.target.value)}
              placeholder="{{input.items}}"
            />
          </div>
        )}

        {config.loop_type === 'while' && (
          <div>
            <Label htmlFor="while_condition">While Condition</Label>
            <Textarea
              id="while_condition"
              value={config.while_condition || ''}
              onChange={(e) => handleChange('while_condition', e.target.value)}
              rows={2}
              placeholder="counter < 10"
              className="font-mono text-sm"
            />
          </div>
        )}

        {config.loop_type === 'for' && (
          <div className="grid grid-cols-3 gap-3">
            <div>
              <Label htmlFor="start_value">Start</Label>
              <Input
                id="start_value"
                type="number"
                value={config.start_value || 0}
                onChange={(e) => handleChange('start_value', parseInt(e.target.value))}
              />
            </div>
            <div>
              <Label htmlFor="end_value">End</Label>
              <Input
                id="end_value"
                type="number"
                value={config.end_value || 10}
                onChange={(e) => handleChange('end_value', parseInt(e.target.value))}
              />
            </div>
            <div>
              <Label htmlFor="step_value">Step</Label>
              <Input
                id="step_value"
                type="number"
                value={config.step_value || 1}
                onChange={(e) => handleChange('step_value', parseInt(e.target.value))}
              />
            </div>
          </div>
        )}

        <div>
          <Label htmlFor="max_iterations">Max Iterations</Label>
          <Input
            id="max_iterations"
            type="number"
            value={config.max_iterations || 1000}
            onChange={(e) => handleChange('max_iterations', parseInt(e.target.value))}
            min="1"
            max="10000"
          />
          <p className="text-xs text-gray-500 mt-1">
            Safety limit to prevent infinite loops
          </p>
        </div>

        <div className="flex items-center space-x-2">
          <Switch
            id="parallel_execution"
            checked={config.parallel_execution || false}
            onCheckedChange={(checked) => handleChange('parallel_execution', checked)}
          />
          <Label htmlFor="parallel_execution">Parallel Execution</Label>
        </div>

        <div>
          <Label htmlFor="batch_size">Batch Size (for parallel)</Label>
          <Input
            id="batch_size"
            type="number"
            value={config.batch_size || 10}
            onChange={(e) => handleChange('batch_size', parseInt(e.target.value))}
            min="1"
            max="100"
          />
        </div>
      </div>
    );
  }

  return <div className="text-sm text-gray-500">Unknown logic type: {nodeType}</div>;
};
