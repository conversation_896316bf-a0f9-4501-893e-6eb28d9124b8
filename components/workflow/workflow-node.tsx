"use client";

import React from "react";
import { motion } from "framer-motion";
import {
  Zap,
  Bot,
  Wrench,
  Globe,
  Mail,
  GitBranch,
  Trash2,
  Settings,
  Webhook,
  Calendar,
  MessageSquare,
} from "lucide-react";

interface WorkflowNodeData {
  id: string;
  type: string;
  position: { x: number; y: number };
  data: {
    label: string;
    config: Record<string, any>;
  };
}

interface WorkflowNodeProps {
  node: WorkflowNodeData;
  onMouseDown: (event: React.MouseEvent) => void;
  onClick: () => void;
  onDelete: () => void;
  isDragging: boolean;
}

const nodeTypeConfig = {
  trigger: {
    color: "border-green-300 bg-green-50 dark:border-green-700 dark:bg-green-900/20",
    iconColor: "text-green-600 dark:text-green-400",
    icon: Zap,
    subtypeIcons: {
      webhook: Webhook,
      schedule: Calendar,
      email: Mail,
      chat: MessageSquare,
    },
  },
  agent: {
    color: "border-blue-300 bg-blue-50 dark:border-blue-700 dark:bg-blue-900/20",
    iconColor: "text-blue-600 dark:text-blue-400",
    icon: Bo<PERSON>,
  },
  tool: {
    color: "border-purple-300 bg-purple-50 dark:border-purple-700 dark:bg-purple-900/20",
    iconColor: "text-purple-600 dark:text-purple-400",
    icon: Wrench,
  },
  action: {
    color: "border-orange-300 bg-orange-50 dark:border-orange-700 dark:bg-orange-900/20",
    iconColor: "text-orange-600 dark:text-orange-400",
    icon: Globe,
    subtypeIcons: {
      http: Globe,
      email: Mail,
    },
  },
  condition: {
    color: "border-indigo-300 bg-indigo-50 dark:border-indigo-700 dark:bg-indigo-900/20",
    iconColor: "text-indigo-600 dark:text-indigo-400",
    icon: GitBranch,
  },
};

export const WorkflowNodeComponent = ({
  node,
  onMouseDown,
  onClick,
  onDelete,
  isDragging,
}: WorkflowNodeProps) => {
  const config = nodeTypeConfig[node.type as keyof typeof nodeTypeConfig] || nodeTypeConfig.agent;
  
  // Get the appropriate icon
  let IconComponent = config.icon;
  if (config.subtypeIcons && node.data.config.subtype) {
    IconComponent = config.subtypeIcons[node.data.config.subtype as keyof typeof config.subtypeIcons] || config.icon;
  }

  const handleClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    onClick();
  };

  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    onDelete();
  };

  return (
    <motion.div
      className={`relative w-48 min-h-[80px] rounded-lg border-2 ${config.color} shadow-sm cursor-pointer select-none group ${
        isDragging ? 'shadow-lg scale-105' : 'hover:shadow-md'
      }`}
      onMouseDown={onMouseDown}
      onClick={handleClick}
      whileHover={{ scale: isDragging ? 1.05 : 1.02 }}
      transition={{ duration: 0.2 }}
    >
      {/* Connection Points */}
      <div className="absolute -top-2 left-1/2 transform -translate-x-1/2 w-4 h-4 bg-gray-300 dark:bg-gray-600 rounded-full border-2 border-white dark:border-gray-800 opacity-0 group-hover:opacity-100 transition-opacity" />
      <div className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-4 h-4 bg-gray-300 dark:bg-gray-600 rounded-full border-2 border-white dark:border-gray-800 opacity-0 group-hover:opacity-100 transition-opacity" />

      {/* Node Content */}
      <div className="p-4">
        <div className="flex items-start justify-between mb-2">
          <div className="flex items-center space-x-2">
            <div className={`p-1.5 rounded-md ${config.color}`}>
              <IconComponent className={`h-4 w-4 ${config.iconColor}`} />
            </div>
            <div className="flex-1 min-w-0">
              <h3 className="text-sm font-medium text-gray-900 dark:text-white truncate">
                {node.data.label}
              </h3>
              <p className="text-xs text-gray-500 dark:text-gray-400 capitalize">
                {node.type}
              </p>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
            <button
              onClick={handleClick}
              className="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
            >
              <Settings className="h-3 w-3" />
            </button>
            <button
              onClick={handleDelete}
              className="p-1 text-gray-400 hover:text-red-500 transition-colors"
            >
              <Trash2 className="h-3 w-3" />
            </button>
          </div>
        </div>

        {/* Node Details */}
        <div className="space-y-1">
          {node.type === 'agent' && node.data.config.model && (
            <div className="text-xs text-gray-600 dark:text-gray-400">
              Model: {node.data.config.model}
            </div>
          )}
          {node.type === 'trigger' && node.data.config.endpoint && (
            <div className="text-xs text-gray-600 dark:text-gray-400 truncate">
              {node.data.config.endpoint}
            </div>
          )}
          {node.type === 'action' && node.data.config.url && (
            <div className="text-xs text-gray-600 dark:text-gray-400 truncate">
              {node.data.config.url}
            </div>
          )}
          {node.type === 'condition' && node.data.config.expression && (
            <div className="text-xs text-gray-600 dark:text-gray-400 truncate">
              {node.data.config.expression}
            </div>
          )}
        </div>
      </div>

      {/* Status Indicator */}
      <div className="absolute top-2 right-2">
        <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
      </div>

      {/* Drag Overlay */}
      {isDragging && (
        <div className="absolute inset-0 bg-blue-200 dark:bg-blue-800 opacity-20 rounded-lg pointer-events-none" />
      )}
    </motion.div>
  );
};
