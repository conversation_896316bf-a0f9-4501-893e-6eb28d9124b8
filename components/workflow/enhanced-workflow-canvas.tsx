"use client";

import React, { useRef, useCallback, useState } from "react";
import { motion } from "framer-motion";
import { Plus, Minus, RotateCcw } from "lucide-react";
import toast from "react-hot-toast";
import EnhancedWorkflowNode from "./enhanced-workflow-node";
import { isValidConnection, getConnectionValidationMessage } from "@/lib/workflow-validation";

interface WorkflowCanvasProps {
  nodes: any[];
  edges: any[];
  selectedNode: any;
  onNodeClick: (node: any) => void;
  onNodeDelete: (nodeId: string) => void;
  onNodeUpdate: (nodeId: string, config: any) => void;
  onNodeDrag: (nodeId: string, position: { x: number; y: number }) => void;
  onConnectionStart: (nodeId: string, handle: string) => void;
  onConnectionEnd: (nodeId: string, handle: string) => void;
  onEdgeDelete: (edgeId: string) => void;
  isConnecting: boolean;
  connectionStart: { nodeId: string; handle: string } | null;
  isDraggingNode?: boolean;
}

export const EnhancedWorkflowCanvas = ({
  nodes,
  edges,
  selectedNode,
  onNodeClick,
  onNodeDelete,
  onNodeUpdate,
  onNodeDrag,
  onConnectionStart,
  onConnectionEnd,
  onEdgeDelete,
  isConnecting,
  connectionStart,
  isDraggingNode = false,
}: WorkflowCanvasProps) => {
  const canvasRef = useRef<HTMLDivElement>(null);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [canvasOffset, setCanvasOffset] = useState({ x: 0, y: 0 });
  const [scale, setScale] = useState(1);
  const [hoveredEdge, setHoveredEdge] = useState<string | null>(null);
  const [isPanning, setIsPanning] = useState(false);
  const [lastPanPoint, setLastPanPoint] = useState({ x: 0, y: 0 });

  const handleCanvasClick = useCallback((e: React.MouseEvent) => {
    if (e.target === canvasRef.current) {
      // Clear selection when clicking on empty canvas
      onNodeClick(null);
    }
  }, [onNodeClick]);

  // Handle zoom with mouse wheel
  const handleWheel = useCallback((e: React.WheelEvent) => {
    e.preventDefault();
    const delta = e.deltaY > 0 ? 0.9 : 1.1;
    const newScale = Math.min(Math.max(scale * delta, 0.1), 3);
    setScale(newScale);
  }, [scale]);

  // Handle panning
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    if (e.button === 1 || (e.button === 0 && e.ctrlKey)) { // Middle mouse or Ctrl+Left click
      setIsPanning(true);
      setLastPanPoint({ x: e.clientX, y: e.clientY });
      e.preventDefault();
    }
  }, []);

  const handleMouseMove = useCallback((e: React.MouseEvent) => {
    if (isPanning) {
      const deltaX = e.clientX - lastPanPoint.x;
      const deltaY = e.clientY - lastPanPoint.y;
      setCanvasOffset(prev => ({
        x: prev.x + deltaX,
        y: prev.y + deltaY
      }));
      setLastPanPoint({ x: e.clientX, y: e.clientY });
    }

    // Update mouse position for connection preview
    if (isConnecting) {
      const rect = canvasRef.current?.getBoundingClientRect();
      if (rect) {
        setMousePosition({
          x: (e.clientX - rect.left - canvasOffset.x) / scale,
          y: (e.clientY - rect.top - canvasOffset.y) / scale,
        });
      }
    }
  }, [isPanning, lastPanPoint, isConnecting, canvasOffset, scale]);

  const handleMouseUp = useCallback(() => {
    setIsPanning(false);
  }, []);

  // Zoom control functions
  const zoomIn = useCallback(() => {
    setScale(prev => Math.min(prev * 1.2, 3));
  }, []);

  const zoomOut = useCallback(() => {
    setScale(prev => Math.max(prev / 1.2, 0.1));
  }, []);

  const resetZoom = useCallback(() => {
    setScale(1);
    setCanvasOffset({ x: 0, y: 0 });
  }, []);



  // Helper function to get handle position
  const getHandlePosition = (node: any, handleId: string, isSource: boolean = false) => {
    const nodeWidth = 200;
    const nodeHeight = 80;
    const handleOffset = 12; // Distance from node edge to handle center
    const verticalAdjustment = 24; // Fix for offset issue - 16px + 8px = 24px total

    // Default to output handle if not specified
    if (!handleId || handleId === 'output') {
      return {
        x: node.position.x + nodeWidth + handleOffset,
        y: node.position.y + nodeHeight / 2 + verticalAdjustment,
      };
    }

    if (handleId === 'input') {
      return {
        x: node.position.x - handleOffset,
        y: node.position.y + nodeHeight / 2 + verticalAdjustment,
      };
    }

    if (handleId === 'cluster-input') {
      // If this is a target (isSource = false), move it up by 28px
      const targetAdjustment = isSource ? 0 : -28;
      return {
        x: node.position.x + nodeWidth / 2,
        y: node.position.y - handleOffset + verticalAdjustment + targetAdjustment,
      };
    }

    // Sub-handles on bottom of cluster nodes
    const subHandles = ['memory', 'tools', 'parser', 'model'];
    if (subHandles.includes(handleId)) {
      const index = subHandles.indexOf(handleId);
      const totalHandles = subHandles.length;
      const spacing = nodeWidth / (totalHandles + 1);
      const leftOffset = spacing * (index + 1);
      // Only apply the 28px adjustment when this is a SOURCE handle (beginning of edge)
      const clusterHandleAdjustment = isSource ? 28 : 0;
      return {
        x: node.position.x + leftOffset,
        y: node.position.y + nodeHeight + handleOffset + verticalAdjustment + clusterHandleAdjustment,
      };
    }

    // Multiple outputs (true/false)
    if (handleId.startsWith('output-')) {
      const outputHandles = ['output-true', 'output-false'];
      const index = outputHandles.indexOf(handleId);
      const yOffset = (index - 0.5) * 30; // Center around middle, stack vertically
      return {
        x: node.position.x + nodeWidth + handleOffset,
        y: node.position.y + nodeHeight / 2 + yOffset + verticalAdjustment,
      };
    }

    // Default to output position
    return {
      x: node.position.x + nodeWidth + handleOffset,
      y: node.position.y + nodeHeight / 2 + verticalAdjustment,
    };
  };

  const renderEdge = (edge: any) => {
    const sourceNode = nodes.find(n => n.id === edge.source);
    const targetNode = nodes.find(n => n.id === edge.target);

    if (!sourceNode || !targetNode) return null;

    // Get precise handle positions
    const sourcePos = getHandlePosition(sourceNode, edge.sourceHandle || 'output', true); // isSource = true
    const targetPos = getHandlePosition(targetNode, edge.targetHandle || 'input', false); // isSource = false

    // Determine if this is a tool/sub-node connection
    const isToolConnection = edge.targetHandle === 'cluster-input' ||
                           ['memory', 'tools', 'parser', 'model'].includes(edge.sourceHandle);

    // Choose marker based on connection type
    const marker = isToolConnection ? 'url(#diamond)' : 'url(#arrowhead)';
    const strokeColor = isToolConnection ? '#10b981' : '#6366f1';

    // Create curved path
    const midX = (sourcePos.x + targetPos.x) / 2;
    const midY = (sourcePos.y + targetPos.y) / 2;

    // For vertical connections (cluster to sub-node), use different curve
    const isVertical = Math.abs(sourcePos.y - targetPos.y) > Math.abs(sourcePos.x - targetPos.x);
    let pathData;

    if (isVertical) {
      // Vertical curve for cluster-to-subnode connections
      pathData = `M ${sourcePos.x} ${sourcePos.y} C ${sourcePos.x} ${midY}, ${targetPos.x} ${midY}, ${targetPos.x} ${targetPos.y}`;
    } else {
      // Horizontal curve for regular workflow connections
      pathData = `M ${sourcePos.x} ${sourcePos.y} C ${midX} ${sourcePos.y}, ${midX} ${targetPos.y}, ${targetPos.x} ${targetPos.y}`;
    }

    // Calculate midpoint for X indicator (reuse existing midX/midY if available)
    const edgeMidX = (sourcePos.x + targetPos.x) / 2;
    const edgeMidY = (sourcePos.y + targetPos.y) / 2;

    return (
      <g key={edge.id}>
        <path
          d={pathData}
          stroke={hoveredEdge === edge.id ? "#ef4444" : strokeColor}
          strokeWidth={hoveredEdge === edge.id ? "3" : "2"}
          fill="none"
          markerEnd={marker}
          className="cursor-pointer transition-all duration-200"
          onMouseEnter={() => setHoveredEdge(edge.id)}
          onMouseLeave={() => setHoveredEdge(null)}
          onClick={(e) => {
            e.stopPropagation();
            onEdgeDelete(edge.id);
          }}
        />
        {/* Invisible wider path for easier hovering/clicking */}
        <path
          d={pathData}
          stroke="transparent"
          strokeWidth="12"
          fill="none"
          className="cursor-pointer"
          onMouseEnter={() => setHoveredEdge(edge.id)}
          onMouseLeave={() => setHoveredEdge(null)}
          onClick={(e) => {
            e.stopPropagation();
            onEdgeDelete(edge.id);
          }}
        />

        {/* X indicator on hover */}
        {hoveredEdge === edge.id && (
          <g>
            {/* Background circle for X */}
            <circle
              cx={edgeMidX}
              cy={edgeMidY}
              r="10"
              fill="white"
              stroke="#ef4444"
              strokeWidth="2"
              className="drop-shadow-sm"
            />
            {/* X icon using SVG paths */}
            <g stroke="#ef4444" strokeWidth="2" strokeLinecap="round">
              <line x1={edgeMidX - 4} y1={edgeMidY - 4} x2={edgeMidX + 4} y2={edgeMidY + 4} />
              <line x1={edgeMidX + 4} y1={edgeMidY - 4} x2={edgeMidX - 4} y2={edgeMidY + 4} />
            </g>
          </g>
        )}
      </g>
    );
  };

  const renderConnectionPreview = () => {
    if (!isConnecting || !connectionStart) return null;

    const sourceNode = nodes.find(n => n.id === connectionStart.nodeId);
    if (!sourceNode) return null;

    // Get precise handle position for the connection start
    const sourcePos = getHandlePosition(sourceNode, connectionStart.handle, true); // isSource = true
    const targetX = mousePosition.x;
    const targetY = mousePosition.y;

    // Determine connection type for preview
    const isToolConnection = ['memory', 'tools', 'parser', 'model'].includes(connectionStart.handle);
    const strokeColor = isToolConnection ? '#10b981' : '#6366f1';

    const midX = (sourcePos.x + targetX) / 2;
    const pathData = `M ${sourcePos.x} ${sourcePos.y} C ${midX} ${sourcePos.y}, ${midX} ${targetY}, ${targetX} ${targetY}`;

    return (
      <path
        d={pathData}
        stroke={strokeColor}
        strokeWidth="2"
        strokeDasharray="5,5"
        fill="none"
        className="pointer-events-none"
      />
    );
  };

  return (
    <div
      ref={canvasRef}
      className="absolute inset-0 bg-transparent overflow-hidden cursor-grab active:cursor-grabbing"
      onClick={handleCanvasClick}
      onMouseMove={handleMouseMove}
      onMouseDown={handleMouseDown}
      onMouseUp={handleMouseUp}
      onWheel={handleWheel}
      style={{ cursor: isPanning ? 'grabbing' : 'grab' }}
    >


      {/* Connection overlay */}
      {isConnecting && (
        <div className="absolute inset-0 bg-blue-500/10 pointer-events-none">
          <div className="absolute top-4 left-1/2 transform -translate-x-1/2 bg-blue-600 text-white px-3 py-1 rounded-full text-sm">
            Click on a node to connect
          </div>
        </div>
      )}

      {/* Zoomable and pannable content container */}
      <div
        style={{
          transform: `translate(${canvasOffset.x}px, ${canvasOffset.y}px) scale(${scale})`,
          transformOrigin: '0 0',
          width: '100%',
          height: '100%',
        }}
      >
        {/* SVG for edges */}
        <svg
          className="absolute inset-0 pointer-events-none"
          style={{
            width: '100%',
            height: '100%',
          }}
        >
          {/* Marker definitions */}
          <defs>
            {/* Arrow marker for regular workflow connections */}
            <marker
              id="arrowhead"
              markerWidth="10"
              markerHeight="7"
              refX="9"
              refY="3.5"
              orient="auto"
            >
              <polygon
                points="0 0, 10 3.5, 0 7"
                fill="#6366f1"
              />
            </marker>

            {/* Diamond marker for tool/sub-node connections */}
            <marker
              id="diamond"
              markerWidth="8"
              markerHeight="8"
              refX="4"
              refY="4"
              orient="auto"
            >
              <polygon
                points="4 0, 8 4, 4 8, 0 4"
                fill="#10b981"
                stroke="#059669"
                strokeWidth="1"
              />
            </marker>
          </defs>

          {/* Render edges */}
          <g className="pointer-events-auto">
            {edges.map(renderEdge)}
            {renderConnectionPreview()}
          </g>
        </svg>

        {/* Render nodes */}
        {nodes.map((node: any) => (
          <EnhancedWorkflowNode
            key={node.id}
            node={node}
            isSelected={selectedNode?.id === node.id}
            isConnecting={isConnecting}
            onClick={() => onNodeClick(node)}
            onDelete={() => onNodeDelete(node.id)}
            onUpdate={(config) => onNodeUpdate(node.id, config)}
            onDrag={(position) => onNodeDrag(node.id, position)}
            onConnectionStart={(handle) => onConnectionStart(node.id, handle)}
            onConnectionEnd={(handle) => onConnectionEnd(node.id, handle)}
          />
        ))}
      </div>



      {/* Zoom Controls */}
      <div className="absolute bottom-4 right-4 flex flex-col gap-2">
        {/* Zoom buttons */}
        <div className="flex flex-col bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-lg overflow-hidden">
          <button
            onClick={zoomIn}
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors border-b border-gray-200 dark:border-gray-700"
            title="Zoom In"
          >
            <Plus className="w-4 h-4 text-gray-600 dark:text-gray-400" />
          </button>
          <button
            onClick={resetZoom}
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors border-b border-gray-200 dark:border-gray-700"
            title="Reset Zoom"
          >
            <RotateCcw className="w-4 h-4 text-gray-600 dark:text-gray-400" />
          </button>
          <button
            onClick={zoomOut}
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
            title="Zoom Out"
          >
            <Minus className="w-4 h-4 text-gray-600 dark:text-gray-400" />
          </button>
        </div>

        {/* Scale indicator */}
        <div className="bg-white dark:bg-gray-800 px-3 py-2 rounded-lg text-xs text-gray-600 dark:text-gray-400 border border-gray-200 dark:border-gray-700 shadow-lg text-center font-medium">
          {Math.round(scale * 100)}%
        </div>
      </div>

      {/* Connection instructions */}
      {isConnecting && (
        <div className="absolute bottom-4 left-4 bg-blue-600 text-white px-3 py-2 rounded-lg text-sm">
          <div className="font-medium">Connecting nodes</div>
          <div className="text-blue-100">Click on another node to create connection</div>
          <div className="text-blue-100">Press ESC to cancel</div>
        </div>
      )}

      {/* Canvas instructions */}
      {!isConnecting && nodes.length === 0 && (
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-center">
          <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 max-w-md">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
              Welcome to Workflow Builder
            </h3>
            <div className="text-sm text-gray-600 dark:text-gray-400 space-y-2">
              <p>• Drag nodes from the left panel to start building</p>
              <p>• Mouse wheel to zoom in/out</p>
              <p>• Middle-click or Ctrl+drag to pan around</p>
              <p>• Connect nodes by dragging from handles</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default EnhancedWorkflowCanvas;
