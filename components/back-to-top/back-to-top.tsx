"use client"
import { motion, useScroll, useAnimation } from 'framer-motion';
import { ArrowUp } from 'lucide-react';
import { useEffect } from 'react';

export const BackToTop = () => {
  const { scrollY } = useScroll();
  const controls = useAnimation();

  useEffect(() => {
    return scrollY.on('change',(latest) => {
      if (latest > 300) {
        controls.start('visible');
      } else {
        controls.start('hidden');
      }
    });
  }, [scrollY, controls]);

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  const variants = {
    hidden: { 
      opacity: 0,
      y: 20,
      transition: { duration: 0.3 }
    },
    visible: { 
      opacity: 1,
      y: 0,
      transition: { 
        type: 'spring',
        stiffness: 100,
        damping: 10
      }
    }
  };

  return (
    <motion.button
      aria-label="Back to top"
      className="fixed cursor-pointer bottom-6 right-6 z-40 p-3 rounded-full bg-gradient-to-br from-emerald-500 to-teal-600 text-white shadow-lg hover:from-emerald-600 hover:to-teal-700 transition-all focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2 dark:focus:ring-offset-gray-900"
      onClick={scrollToTop}
      initial="hidden"
      animate={controls}
      variants={variants}
      whileHover={{ scale: 1.1 }}
      whileTap={{ scale: 0.95 }}
    >
      <ArrowUp className="w-5 h-5" />
    </motion.button>
  );
};