"use client";

import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Button } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON>Header,
  <PERSON><PERSON>Title,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Role } from "@/enums/enums";
import {
  useCreateFAQ,
  useDeleteFAQ,
  useFAQs,
  useUpdateFAQ,
} from "@/hooks/use-faqs";
import { faqSchema } from "@/validations/validations";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Edit,
  HelpCircle,
  Loader2,
  MoreVertical,
  Plus,
  RefreshCw,
  Trash,
  TriangleAlert,
} from "lucide-react";
import { useSession } from "next-auth/react";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { Skeleton } from "../ui/skeleton";
import ConfirmDialog from "../modals/confirm-dialog";
import { IFAQ } from "@/models/faq";

export default function FAQSection() {
  const { data: session } = useSession();
  const [isConfirmOpen, setIsConfirmOpen] = useState<boolean>(false);
  const { data: faqs, isLoading, isError, refetch, isFetching } = useFAQs();
  const [deleteId, setDeleteId] = useState<string | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState<boolean>(false);
  //eslint-disable-next-line  @typescript-eslint/no-explicit-any
  const [editingFAQ, setEditingFAQ] = useState<any>(null);
  const createFAQ = useCreateFAQ();
  const updateFAQ = useUpdateFAQ();
  const deleteFAQ = useDeleteFAQ();

  const form = useForm({
    resolver: zodResolver(faqSchema),
    defaultValues: {
      question: "",
      answer: "",
    },
    mode: "onChange",
    reValidateMode: "onChange",
  });

  //eslint-disable-next-line  @typescript-eslint/no-explicit-any
  const handleOpenDialog = (faq: any = null) => {
    if (faq) {
      setEditingFAQ(faq);
      form.reset({
        question: faq.question,
        answer: faq.answer,
      });
    } else {
      setEditingFAQ(null);
      form.reset();
    }
    setIsDialogOpen(true);
  };

  //eslint-disable-next-line  @typescript-eslint/no-explicit-any
  const onSubmit = async (data: any) => {
    try {
      if (editingFAQ) {
        await updateFAQ.mutateAsync({ id: editingFAQ.id, ...data });
      } else {
        await createFAQ.mutateAsync(data);
      }
      form.reset();
      setIsDialogOpen(false);
    } catch (error) {
      console.error("Failed to save FAQ:", error);
    }
  };


  const handleDeleteClick = (id: string) => {
    setDeleteId(id);
    setIsConfirmOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (deleteId) {
      try {
        await deleteFAQ.mutateAsync(deleteId);
      } catch (error) {
        console.error("Failed to delete FAQ:", error);
      } finally {
        setIsConfirmOpen(false);
        setDeleteId(null);
      }
    }
  };

  const handleCancelDelete = () => {
    setIsConfirmOpen(false);
    setDeleteId(null);
  };

  if (isLoading)
    return (
      <div className="mt-12">
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center gap-3">
            <Skeleton className="h-8 w-8 rounded-full" />
            <Skeleton className="h-8 w-48 rounded-md" />
          </div>
          {session?.user.role === Role.ADMIN && (
            <Skeleton className="h-10 w-24 rounded-md" />
          )}
        </div>

        <div className="space-y-4">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="border rounded-lg p-4">
              <Skeleton className="h-6 w-3/4 rounded-md mb-3" />
              <Skeleton className="h-4 w-full rounded-md" />
            </div>
          ))}
        </div>
      </div>
    );

  if (isError)
    return (
      <div className="mt-12">
        <div className="flex items-center justify-between mb-8">
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <HelpCircle className="h-6 w-6 text-emerald-500" />
            Frequently Asked Questions
          </h2>
          {session?.user.role === Role.ADMIN && (
            <Dialog>
              <DialogTrigger asChild>
                <Button variant="outline" className="gap-2">
                  <Plus className="h-4 w-4" />
                  Add FAQ
                </Button>
              </DialogTrigger>
            </Dialog>
          )}
        </div>

        <div className="rounded-lg border border-dashed border-red-200 bg-red-50 p-8 text-center">
          <TriangleAlert className="h-10 w-10 text-red-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-red-800 mb-2">
            Failed to load FAQs
          </h3>
          <p className="text-red-600 mb-4">
            We couldn&apos;t load the FAQs. Please try again.
          </p>
          <Button
            variant="outline"
            className="text-red-600 border-red-300 hover:bg-red-100"
            onClick={() => refetch()}
          >
            {isFetching ? (
              <span className="flex items-center gap-1">
                <Loader2 className="w-5 h-5 animate-spin" /> Retrying...
              </span>
            ) : (
              <span className="flex items-center gap-1">
                <RefreshCw className="h-4 w-4 mr-2" />
                Retry
              </span>
            )}
          </Button>
        </div>
      </div>
    );

  const confirmLabel = (
  <span className="flex items-center gap-2">
    <Loader2 className="animate-spin h-4 w-4" />
    Deleting...
  </span>
);

  return (
    <div className="mt-12">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-2xl font-bold flex items-center gap-2">
          <HelpCircle className="h-6 w-6 text-emerald-500" />
          Frequently Asked Questions
        </h2>
        {session?.user.role === Role.ADMIN && (
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button
                variant="outline"
                className="gap-2 cursor-pointer"
                onClick={() => handleOpenDialog()}
              >
                <Plus className="h-4 w-4" />
                Add FAQ
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>
                  {editingFAQ ? "Edit FAQ" : "Add New FAQ"}
                </DialogTitle>
              </DialogHeader>
              <Form {...form}>
                <form
                  onSubmit={form.handleSubmit(onSubmit)}
                  className="space-y-4"
                >
                  <FormField
                    control={form.control}
                    name="question"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Question</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter the question" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="answer"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Answer</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Enter the detailed answer"
                            rows={5}
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <div className="flex justify-end gap-2">
                    <Button
                      type="button"
                      variant="outline"
                      className="cursor-pointer"
                      onClick={() => setIsDialogOpen(false)}
                    >
                      Cancel
                    </Button>
                    <Button
                      type="submit"
                      disabled={
                        !form.formState.isValid ||
                        createFAQ.isPending ||
                        updateFAQ.isPending
                      }
                      className="relative cursor-pointer"
                    >
                      {createFAQ.isPending || updateFAQ.isPending ? (
                        <>
                          <span className="opacity-0">
                            {editingFAQ ? "Updating..." : "Saving..."}
                          </span>
                          <span className="absolute inset-0 flex items-center justify-center">
                            <Loader2 className="h-4 w-4 animate-spin" />
                          </span>
                        </>
                      ) : editingFAQ ? (
                        "Update FAQ"
                      ) : (
                        "Save FAQ"
                      )}
                    </Button>
                  </div>
                </form>
              </Form>
            </DialogContent>
          </Dialog>
        )}
      </div>

      {faqs?.length === 0 ? (
        <div className="text-center py-12 px-4">
          <div className="max-w-md mx-auto">
            <div className="inline-flex items-center justify-center bg-amber-50 rounded-full p-4 mb-4">
              <HelpCircle className="h-8 w-8 text-amber-500" />
            </div>
            <h3 className="text-lg font-medium text-emerald-500 mb-2">
              No FAQs Available Yet
            </h3>
            <p className="text-gray-500 mb-6">
              We couldn&apos;t find any frequently asked questions. Check back
              later or ask us directly if you need help.
            </p>
            {session?.user.role === Role.ADMIN && (
              <Button
                onClick={() => handleOpenDialog()}
                className="gap-2 cursor-pointer"
              >
                <Plus className="h-4 w-4" />
                Create First FAQ
              </Button>
            )}
          </div>
        </div>
      ) : (
        <Accordion type="single" collapsible className="w-full space-y-2">
          {faqs?.map((faq: IFAQ, index: number) => (
            <AccordionItem
              key={faq.id}
              value={`item-${index}`}
              className="border rounded-lg overflow-hidden hover:shadow-sm transition-shadow dark:border-gray-700"
            >
              <div className="flex items-center justify-between">
                <AccordionTrigger className="px-4 py-3 hover:no-underline hover:bg-gray-50 dark:hover:bg-gray-800 flex-1 text-left cursor-pointer">
                  <div className="flex items-center gap-3">
                    <span className="flex-shrink-0 h-6 w-6 rounded-full bg-emerald-100 dark:bg-emerald-900/30 text-emerald-600 dark:text-emerald-400 flex items-center justify-center">
                      {index + 1}
                    </span>
                    <span className="font-medium dark:text-gray-100">
                      {faq.question}
                    </span>
                  </div>
                </AccordionTrigger>
                {session?.user.role === Role.ADMIN && (
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild className="cursor-pointer">
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 w-8 p-0 mr-2 text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700"
                      >
                        <MoreVertical className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent
                      align="end"
                      className="dark:border-gray-700 dark:bg-gray-800"
                    >
                      <DropdownMenuItem
                        className="cursor-pointer dark:hover:bg-gray-700"
                        onClick={() => handleOpenDialog(faq)}
                      >
                        <Edit className="h-4 w-4 mr-2" />
                        <span className="dark:text-gray-200">Edit</span>
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        className="cursor-pointer text-red-600 dark:text-red-400 dark:hover:bg-gray-700"
                        onClick={() => handleDeleteClick(faq.id)}
                        disabled={deleteFAQ.isPending}
                      >
                        <Trash className="h-4 w-4 mr-2" />
                        <span>Delete</span>
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                )}
              </div>
              <AccordionContent className="px-4 py-3 bg-gray-50 dark:bg-gray-800/50 text-gray-700 dark:text-gray-300">
                <div className="pl-9">
                  <div className="prose prose-sm max-w-none dark:prose-invert">
                    {faq.answer
                      .split("\n")
                      .map((paragraph: string, i: number) => (
                        <p key={i}>{paragraph}</p>
                      ))}
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>
      )}
      <ConfirmDialog
        open={isConfirmOpen}
        title="Delete this question"
        message="Are you sure you want to delete this faq? This action cannot be undone."
        confirmLabel={deleteFAQ.isPending ? confirmLabel : "Delete"}
        onConfirm={handleConfirmDelete}
        onCancel={handleCancelDelete}
        isDeleting={deleteFAQ.isPending}
      />
    </div>
  );
}
