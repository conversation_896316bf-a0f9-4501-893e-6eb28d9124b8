"use client";

import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { X, Save, Bot } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";

// Import tab components
import { GeneralTab } from "@/components/agent-settings/general-tab";
import { StyleTab } from "@/components/agent-settings/style-tab";
import { LLMTab } from "@/components/agent-settings/llm-tab";
import { SecurityTab } from "@/components/agent-settings/security-tab";
import { LimitsTab } from "@/components/agent-settings/limits-tab";
import { IconTab } from "@/components/agent-settings/icon-tab";
import { LeadsTab } from "@/components/agent-settings/leads-tab";
import { CustomDomainsTab } from "@/components/agent-settings/custom-domains-tab";

interface EditAgentModalProps {
  isOpen: boolean;
  onClose: () => void;
  agent: any;
  onUpdate: (updatedAgent: any) => void;
}

export const EditAgentModal = ({ isOpen, onClose, agent, onUpdate }: EditAgentModalProps) => {
  const [formData, setFormData] = useState<any>({});
  const [isLoading, setIsLoading] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);
  const [activeTab, setActiveTab] = useState("general");

  useEffect(() => {
    if (agent) {
      setFormData({ ...agent });
      setHasChanges(false);
    }
  }, [agent]);

  const handleFieldChange = (field: string, value: any) => {
    setFormData((prev: any) => ({
      ...prev,
      [field]: value,
    }));
    setHasChanges(true);
  };

  const handleNestedFieldChange = (section: string, field: string, value: any) => {
    setFormData((prev: any) => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value,
      },
    }));
    setHasChanges(true);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      // Clean the data before sending
      const cleanData = {
        ...formData,
        // Ensure numeric fields are numbers
        temperature: parseFloat(formData.temperature) || 0.7,
        maxTokens: parseInt(formData.maxTokens) || 4000,
        topP: parseFloat(formData.topP) || 1,
        frequencyPenalty: parseFloat(formData.frequencyPenalty) || 0,
        presencePenalty: parseFloat(formData.presencePenalty) || 0,
        ipLimit: parseInt(formData.ipLimit) || 0,
        ipLimitTimeframe: parseInt(formData.ipLimitTimeframe) || 60,
        usageCount: parseInt(formData.usageCount) || 0,
        // Ensure arrays are arrays
        tools: Array.isArray(formData.tools) ? formData.tools : [],
        capabilities: Array.isArray(formData.capabilities) ? formData.capabilities : [],
        allowedDomains: Array.isArray(formData.allowedDomains) ? formData.allowedDomains : [],
        workflowIds: Array.isArray(formData.workflowIds) ? formData.workflowIds : [],
        // Remove any undefined or null values that might cause issues
        ...(formData.description !== undefined && { description: formData.description }),
        ...(formData.displayName !== undefined && { displayName: formData.displayName }),
        ...(formData.iconPath !== undefined && { iconPath: formData.iconPath }),
        ...(formData.profilePicturePath !== undefined && { profilePicturePath: formData.profilePicturePath }),
      };

      // Remove any fields that are read-only
      delete cleanData._id;
      delete cleanData.userId;
      delete cleanData.teamId;
      delete cleanData.createdAt;
      delete cleanData.updatedAt;



      const response = await fetch(`/api/agents/${agent._id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(cleanData),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to update agent');
      }

      // Convert date strings to Date objects
      const updatedAgent = {
        ...result.data,
        createdAt: new Date(result.data.createdAt),
        updatedAt: new Date(result.data.updatedAt),
        lastUsed: result.data.lastUsed ? new Date(result.data.lastUsed) : null,
        lastTrainedAt: result.data.lastTrainedAt ? new Date(result.data.lastTrainedAt) : null,
      };

      onUpdate(updatedAgent);
      setHasChanges(false);
      onClose();
      
    } catch (error) {
      console.error("Error updating agent:", error);
      alert(error instanceof Error ? error.message : 'Failed to update agent');
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    if (hasChanges) {
      if (confirm("You have unsaved changes. Are you sure you want to close?")) {
        onClose();
        setHasChanges(false);
      }
    } else {
      onClose();
    }
  };

  if (!agent) return null;

  return (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={handleClose}
            className="absolute inset-0 bg-black/50 backdrop-blur-sm"
          />

          {/* Modal */}
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 20 }}
            className="relative w-full max-w-4xl max-h-[90vh] bg-white dark:bg-gray-800 rounded-2xl shadow-2xl border border-gray-200/70 dark:border-gray-700/50 flex flex-col"
          >
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 rounded-full bg-gradient-to-br from-blue-100 to-purple-200 dark:from-blue-900/30 dark:to-purple-800/40 flex items-center justify-center">
                  <Bot className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                  <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                    Edit Agent: {agent.name}
                  </h2>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Configure your agent settings and capabilities
                  </p>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                {hasChanges && (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    className="px-2 py-1 bg-orange-100 dark:bg-orange-900/30 text-orange-600 dark:text-orange-400 text-xs rounded-full"
                  >
                    Unsaved changes
                  </motion.div>
                )}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleClose}
                  className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                >
                  <X className="w-5 h-5" />
                </Button>
              </div>
            </div>

            {/* Content */}
            <form onSubmit={handleSubmit} className="flex flex-col flex-1 min-h-0">
              <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
                {/* Tab Navigation */}
                <div className="px-6 pt-4 border-b border-gray-200 dark:border-gray-700">
                  <TabsList className="grid w-full grid-cols-8 bg-gray-100 dark:bg-gray-700">
                    <TabsTrigger value="general" className="text-xs">General</TabsTrigger>
                    <TabsTrigger value="style" className="text-xs">Style</TabsTrigger>
                    <TabsTrigger value="llm" className="text-xs">LLM</TabsTrigger>
                    <TabsTrigger value="security" className="text-xs">Security</TabsTrigger>
                    <TabsTrigger value="limits" className="text-xs">Limits</TabsTrigger>
                    <TabsTrigger value="icon" className="text-xs">Icon</TabsTrigger>
                    <TabsTrigger value="leads" className="text-xs">Leads</TabsTrigger>
                    <TabsTrigger value="domains" className="text-xs">Domains</TabsTrigger>
                  </TabsList>
                </div>

                {/* Tab Content */}
                <div className="flex-1 overflow-y-auto p-6 min-h-0">
                  <TabsContent value="general" className="mt-0">
                    <GeneralTab
                      data={formData}
                      onChange={handleFieldChange}
                    />
                  </TabsContent>

                  <TabsContent value="style" className="mt-0">
                    <StyleTab
                      data={formData}
                      onChange={handleFieldChange}
                    />
                  </TabsContent>

                  <TabsContent value="llm" className="mt-0">
                    <LLMTab
                      data={formData}
                      onChange={handleFieldChange}
                    />
                  </TabsContent>

                  <TabsContent value="security" className="mt-0">
                    <SecurityTab
                      data={formData}
                      onChange={handleFieldChange}
                    />
                  </TabsContent>

                  <TabsContent value="limits" className="mt-0">
                    <LimitsTab
                      data={formData}
                      onChange={handleFieldChange}
                    />
                  </TabsContent>

                  <TabsContent value="icon" className="mt-0">
                    <IconTab
                      data={formData}
                      onChange={handleFieldChange}
                    />
                  </TabsContent>

                  <TabsContent value="leads" className="mt-0">
                    <LeadsTab
                      data={formData}
                      onChange={handleFieldChange}
                      onNestedChange={handleNestedFieldChange}
                    />
                  </TabsContent>

                  <TabsContent value="domains" className="mt-0">
                    <CustomDomainsTab
                      data={formData}
                      onChange={handleFieldChange}
                    />
                  </TabsContent>
                </div>

                {/* Footer */}
                <div className="flex justify-end space-x-3 p-6 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800/50">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleClose}
                    disabled={isLoading}
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    disabled={isLoading || !hasChanges}
                    className="bg-emerald-600 hover:bg-emerald-700 text-white"
                  >
                    <Save className="h-4 w-4 mr-2" />
                    {isLoading ? "Saving..." : "Save Changes"}
                  </Button>
                </div>
              </Tabs>
            </form>
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  );
};
