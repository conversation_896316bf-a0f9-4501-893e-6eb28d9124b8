"use client";

import { useState } from "react";
import { Loader2, MailPlus, UserPlus } from "lucide-react";
import { motion } from "framer-motion";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "../ui/dialog";
import { Input } from "../ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select";
import { Button } from "../ui/button";
import { useTeamMembers } from "@/hooks/use-team-member-mutations";

interface InviteMemberModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onInviteSuccess?: () => void;
  teamUrl: string;
}

type RoleType = "member" | "admin";

export function InviteMemberModal({
  isOpen,
  onOpenChange,
  teamUrl,
}: InviteMemberModalProps) {
  const [email, setEmail] = useState<string>("");
  const [role, setRole] = useState<RoleType>("member");
  const { inviteMember } = useTeamMembers({ slug: teamUrl });
  const handleInviteMember = async () => {
    if (!email) return;
    await inviteMember.mutateAsync(email);
    setEmail("");
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.2 }}
        >
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <motion.div
                animate={{ rotate: [0, 10, -5, 0] }}
                transition={{ duration: 1.5, repeat: Infinity }}
              >
                <MailPlus className="h-5 w-5 text-emerald-500" />
              </motion.div>
              Invite Team Member
            </DialogTitle>
            <DialogDescription>
              Invite new members to collaborate in your team
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <label htmlFor="email" className="text-sm font-medium">
                Email Address
              </label>
              <Input
                id="email"
                type="email"
                placeholder="<EMAIL>"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="focus:ring-2 focus:ring-emerald-200 focus:border-emerald-500"
              />
            </div>

            <div className="grid gap-2">
              <label htmlFor="role" className="text-sm font-medium">
                Role
              </label>
              <Select
                value={role}
                onValueChange={(value: RoleType) => setRole(value)}
              >
                <SelectTrigger className="cursor-pointer w-full hover:border-emerald-300 transition-colors">
                  <SelectValue placeholder="Select role" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem
                    value="member"
                    className="cursor-pointer hover:bg-emerald-50 dark:hover:bg-emerald-900/20 focus:bg-emerald-50 dark:focus:bg-emerald-900/20"
                  >
                    Member
                  </SelectItem>
                  <SelectItem
                    value="admin"
                    className="cursor-pointer hover:bg-emerald-50 dark:hover:bg-emerald-900/20 focus:bg-emerald-50 dark:focus:bg-emerald-900/20"
                  >
                    Admin
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <DialogFooter>
            <motion.div whileHover={{ scale: 1.03 }} whileTap={{ scale: 0.97 }}>
              <Button
                variant="outline"
                disabled={inviteMember.isPending}
                onClick={() => onOpenChange(false)}
                className="cursor-pointer"
              >
                Cancel
              </Button>
            </motion.div>

            <motion.div whileHover={{ scale: 1.03 }} whileTap={{ scale: 0.95 }}>
              <Button
                onClick={handleInviteMember}
                disabled={inviteMember.isPending || !email}
                className="cursor-pointer bg-emerald-600 hover:bg-emerald-700 transition-colors dark:text-white"
              >
                {inviteMember.isPending ? (
                  <span className="flex gap-1 items-center">
                    <Loader2 className="animate-spin w-5 h-5" />
                    Sending invite...
                  </span>
                ) : (
                  <span className="flex gap-1 items-center">
                    <UserPlus className="h-5 w-5" />
                    Send Invitation
                  </span>
                )}
              </Button>
            </motion.div>
          </DialogFooter>
        </motion.div>
      </DialogContent>
    </Dialog>
  );
}
