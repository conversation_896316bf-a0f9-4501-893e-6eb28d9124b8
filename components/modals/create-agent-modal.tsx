"use client";

import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "lucide-react";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { formSchema } from "@/validations/validations";
import { useCreateAgent } from "@/hooks/use-agents";
import { useParams } from "next/navigation";

interface CreateAgentModalProps {
  isOpen: boolean;
  onClose: () => void;
  teamId: string;
}

export const CreateAgentModal = ({
  isOpen,
  onClose,
  teamId,
}: CreateAgentModalProps) => {
  const params = useParams();
  const teamSlug = typeof params.slug === "string" ? params.slug : "";
  const { mutate: createAgent, isPending: isCreating } = useCreateAgent(teamSlug);

  // Initialize form
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      chatbotName: "",
    },
    mode: "onChange",
    reValidateMode: "onChange",
  });

  const handleSubmit = async (values: z.infer<typeof formSchema>) => {
    createAgent(
      { name: values.chatbotName, teamId },
      {
        onSuccess: () => {
          onClose();
          form.reset();
        },
      }
    );
  };

  return (
    <Dialog
      open={isOpen}
      onOpenChange={(open) => {
        if (!open) {
          onClose();
          form.reset();
        }
      }}
    >
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Create New Chatbot</DialogTitle>
          <DialogDescription>
            Build an AI assistant for your team
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleSubmit)}
            className="space-y-6"
          >
            <FormField
              control={form.control}
              name="chatbotName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Chatbot Name</FormLabel>
                  <FormControl>
                    <Input placeholder="e.g. Customer Support Bot" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex justify-end space-x-3">
              <Button
                type="button"
                className="cursor-pointer"
                variant="outline"
                onClick={() => {
                  onClose();
                  form.reset();
                }}
                disabled={isCreating}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isCreating}
                className="bg-emerald-600 hover:bg-emerald-700 dark:text-white cursor-pointer"
              >
                {isCreating ? "Creating..." : "Create Chatbot"}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};
