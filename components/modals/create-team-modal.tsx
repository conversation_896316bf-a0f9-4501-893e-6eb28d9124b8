"use client";

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Title,
} from "@/components/ui/dialog";
import { CreateTeamForm } from "@/components/forms/create-team-form";
import { useCreateTeam } from "@/hooks/use-teams";
import { TeamApiResponse, TeamFormValues } from "@/types/types";
import { useSession } from "next-auth/react";
import toast from "react-hot-toast";
import { AxiosError } from "axios";

interface CreateTeamModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function CreateTeamModal({ open, onOpenChange }: CreateTeamModalProps) {
  const createTeam = useCreateTeam();
  const { data: session } = useSession();

  const handleSubmit = async (values: TeamFormValues) => {
    if (!session?.user?.id) {
      toast.error("You must be logged in to create a team");
      return;
    }

    const promise = createTeam.mutateAsync({
      ...values,
      ownerId: session.user.id,
      openAiKey: values.openAiKey ?? undefined,
      metaData: values.metaData ?? undefined,
    });

    toast.promise(promise, {
      loading: "Creating team...",
      success: "Team created successfully",
      error: (err: AxiosError<TeamApiResponse>) => {
        return err.response?.data?.error || "Failed to create team";
      },
    });

    try {
      await promise;
      onOpenChange(false);
    } catch (error) {
      console.error("Failed to create team:", error);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="text-2xl">Create New Team</DialogTitle>
        </DialogHeader>
        <CreateTeamForm
          onSubmit={handleSubmit}
          isPending={createTeam.isPending}
        />
      </DialogContent>
    </Dialog>
  );
}
