"use client";

import { Team } from "@/types/types";
import { TeamSettingsContent } from "../teams/settings/team-settings-content";

interface TeamSettingsModalProps {
  team: Team;
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
}

export function TeamSettingsModal({
  isOpen,
  onOpenChange,
  team,
}: TeamSettingsModalProps) {
  return (
    <TeamSettingsContent
      team={team}
      isOpen={isOpen}
      onOpenChange={onOpenChange}
    />
  );
}