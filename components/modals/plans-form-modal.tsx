import { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import { Loader2 } from "lucide-react";
import { Plan } from "@/types/types";

interface PlanFormModalProps {
  open: boolean;
  plan: Plan | null;
  onClose: () => void;
  //eslint-disable-next-line  @typescript-eslint/no-explicit-any
  onSubmit: (planData: any) => void;
  isLoading?: boolean;
}

type FeatureKey =
  | "hasBasicAnalytics"
  | "hasApiAccess"
  | "hasCustomBranding"
  | "hasCustomDomains"
  | "hasNotifications";

export function PlanFormModal({
  open,
  plan,
  onClose,
  onSubmit,
  isLoading,
}: PlanFormModalProps) {
   const [formData, setFormData] = useState({
    name: "",
    description: "",
    priceMonthly: 0,
    priceYearly: 0,
    discountPercentage: 0,
    maxMembers: 1,
    maxChatbots: 1,
    maxTrainingChars: 0,
    maxTrainingLinks: 0,
    monthlyMessageCredits: 0,
    models: "GPT-3.5",
    features: {
      hasBasicAnalytics: false,
      hasApiAccess: false,
      hasCustomBranding: false,
      hasCustomDomains: false,
      hasNotifications: false,
    },
    isActive: true,
    isDefault: false,
  });

  // Initialize form when plan changes
  useEffect(() => {
    if (plan) {
      setFormData({
        name: plan.name || "",
        description: plan.description || "",
        priceMonthly: plan.priceMonthly || 0,
        priceYearly: plan.priceYearly || 0,
        discountPercentage: plan.discountPercentage || 0,
        maxMembers: plan.maxMembers || 1,
        maxChatbots: plan.maxChatbots || 1,
        maxTrainingChars: plan.maxTrainingChars || 0,
        maxTrainingLinks: plan.maxTrainingLinks || 0,
        monthlyMessageCredits: plan.monthlyMessageCredits || 0,
        models: plan.models?.join(", ") || "GPT-3.5",
        features: {
          hasBasicAnalytics: plan.features?.hasBasicAnalytics || false,
          hasApiAccess: plan.features?.hasApiAccess || false,
          hasCustomBranding: plan.features?.hasCustomBranding || false,
          hasCustomDomains: plan.features?.hasCustomDomains || false,
          hasNotifications: plan.features?.hasNotifications || false,
        },
        isActive: plan.isActive ?? true,
        isDefault: plan.isDefault ?? false,
      });
    } else {
      // Reset form when creating new plan
      setFormData({
        name: "",
        description: "",
        priceMonthly: 0,
        priceYearly: 0,
        discountPercentage: 0,
        maxMembers: 1,
        maxChatbots: 1,
        maxTrainingChars: 0,
        maxTrainingLinks: 0,
        monthlyMessageCredits: 0,
        models: "GPT-3.5",
        features: {
          hasBasicAnalytics: false,
          hasApiAccess: false,
          hasCustomBranding: false,
          hasCustomDomains: false,
          hasNotifications: false,
        },
        isActive: true,
        isDefault: false,
      });
    }
  }, [plan]);


  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: name === "models" ? value.split(",").map((m) => m.trim()) : value,
    }));
  };

  const handleNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: Number(value),
    }));
  };

  const handleFeatureToggle = (feature: FeatureKey) => {
    setFormData((prev) => ({
      ...prev,
      features: {
        ...prev.features,
        [feature]: !prev.features[feature],
      },
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit({
      ...formData,
      models: (formData.models as string)
        .split(",")
        .map((m: string) => m.trim()),
    });
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{plan ? "Edit Plan" : "Create New Plan"}</DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label htmlFor="name">Plan Name</Label>
              <Input
                id="name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleChange}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="priceMonthly">Monthly Price</Label>
              <Input
                id="priceMonthly"
                name="priceMonthly"
                type="number"
                min="0"
                step="0.01"
                value={formData.priceMonthly}
                onChange={handleNumberChange}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="priceYearly">Yearly Price</Label>
              <Input
                id="priceYearly"
                name="priceYearly"
                type="number"
                min="0"
                step="0.01"
                value={formData.priceYearly}
                onChange={handleNumberChange}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="discountPercentage">Discount Percentage</Label>
              <Input
                id="discountPercentage"
                name="discountPercentage"
                type="number"
                min="0"
                max="100"
                value={formData.discountPercentage}
                onChange={handleNumberChange}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="maxMembers">Max Team Members</Label>
              <Input
                id="maxMembers"
                name="maxMembers"
                type="number"
                min="1"
                value={formData.maxMembers}
                onChange={handleNumberChange}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="maxChatbots">Max Agents</Label>
              <Input
                id="maxChatbots"
                name="maxChatbots"
                type="number"
                min="1"
                value={formData.maxChatbots}
                onChange={handleNumberChange}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="maxTrainingChars">Max Training Characters</Label>
              <Input
                id="maxTrainingChars"
                name="maxTrainingChars"
                type="number"
                min="0"
                value={formData.maxTrainingChars}
                onChange={handleNumberChange}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="maxTrainingLinks">Max Training Links</Label>
              <Input
                id="maxTrainingLinks"
                name="maxTrainingLinks"
                type="number"
                min="0"
                value={formData.maxTrainingLinks}
                onChange={handleNumberChange}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="monthlyMessageCredits">
                Monthly Message Credits
              </Label>
              <Input
                id="monthlyMessageCredits"
                name="monthlyMessageCredits"
                type="number"
                min="0"
                value={formData.monthlyMessageCredits}
                onChange={handleNumberChange}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="models">Models (comma separated)</Label>
              <Input
                id="models"
                name="models"
                value={formData.models}
                onChange={handleChange}
                required
              />
            </div>
          </div>

          <div className="space-y-4">
            <h3 className="font-medium">Features</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center space-x-2">
                <Switch
                  id="hasBasicAnalytics"
                  checked={formData.features.hasBasicAnalytics}
                  onCheckedChange={() =>
                    handleFeatureToggle("hasBasicAnalytics")
                  }
                />
                <Label htmlFor="hasBasicAnalytics">Basic Analytics</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="hasApiAccess"
                  checked={formData.features.hasApiAccess}
                  onCheckedChange={() => handleFeatureToggle("hasApiAccess")}
                />
                <Label htmlFor="hasApiAccess">API Access</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="hasCustomBranding"
                  checked={formData.features.hasCustomBranding}
                  onCheckedChange={() =>
                    handleFeatureToggle("hasCustomBranding")
                  }
                />
                <Label htmlFor="hasCustomBranding">Custom Branding</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="hasCustomDomains"
                  checked={formData.features.hasCustomDomains}
                  onCheckedChange={() =>
                    handleFeatureToggle("hasCustomDomains")
                  }
                />
                <Label htmlFor="hasCustomDomains">Custom Domains</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="hasNotifications"
                  checked={formData.features.hasNotifications}
                  onCheckedChange={() =>
                    handleFeatureToggle("hasNotifications")
                  }
                />
                <Label htmlFor="hasNotifications">Notifications</Label>
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Switch
              id="isActive"
              checked={formData.isActive}
              onCheckedChange={() =>
                setFormData((prev) => ({ ...prev, isActive: !prev.isActive }))
              }
            />
            <Label htmlFor="isActive">Active Plan</Label>
          </div>

          <div className="flex items-center space-x-2">
            <Switch
              id="isDefault"
              checked={formData.isDefault}
              onCheckedChange={() =>
                setFormData((prev) => ({ ...prev, isDefault: !prev.isDefault }))
              }
            />
            <Label htmlFor="isDefault">Default Plan</Label>
          </div>

          <div className="flex justify-end gap-4">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              className="cursor-pointer"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isLoading}
              className="flex items-center gap-1 cursor-pointer"
            >
              {isLoading ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : null}
              {plan ? "Update Plan" : "Create Plan"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
