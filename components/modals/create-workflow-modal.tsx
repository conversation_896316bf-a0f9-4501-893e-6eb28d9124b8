"use client";

import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { X, Workflow, Zap, GitBranch } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

interface CreateWorkflowModalProps {
  isOpen: boolean;
  onClose: () => void;
  teamId: string;
  teamUrl: string;
  onSuccess?: () => void;
}

export const CreateWorkflowModal = ({ isOpen, onClose, teamId, teamUrl, onSuccess }: CreateWorkflowModalProps) => {
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    template: "",
    triggers: [] as string[],
  });
  const [isLoading, setIsLoading] = useState(false);

  const templates = [
    { value: "blank", label: "Blank Workflow", description: "Start from scratch" },
    { value: "customer_onboarding", label: "Customer Onboarding", description: "Automated new customer setup" },
    { value: "lead_qualification", label: "Lead Qualification", description: "Score and route leads" },
    { value: "content_generation", label: "Content Generation", description: "Automated content creation" },
    { value: "data_processing", label: "Data Processing", description: "Process and analyze data" },
  ];

  const availableTriggers = [
    { value: "webhook", label: "Webhook", description: "HTTP endpoint trigger" },
    { value: "schedule", label: "Schedule", description: "Time-based trigger" },
    { value: "email", label: "Email", description: "Email received trigger" },
    { value: "file_upload", label: "File Upload", description: "File upload trigger" },
    { value: "chat", label: "Chat Message", description: "Chat interaction trigger" },
  ];

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const response = await fetch('/api/workflows', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          teamId,
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to create workflow');
      }

      // Success - close modal and reset form
      onClose();
      setFormData({
        name: "",
        description: "",
        template: "",
        triggers: [],
      });

      // Call success callback if provided
      if (onSuccess) {
        onSuccess();
      }

      // Redirect to the workflow builder
      const workflowId = result.data._id;
      window.location.href = `/dashboard/team/${teamUrl}/workflows/${workflowId}`;

    } catch (error) {
      console.error("Error creating workflow:", error);
      alert(error instanceof Error ? error.message : 'Failed to create workflow');
    } finally {
      setIsLoading(false);
    }
  };

  const handleTriggerToggle = (trigger: string) => {
    setFormData(prev => ({
      ...prev,
      triggers: prev.triggers.includes(trigger)
        ? prev.triggers.filter(t => t !== trigger)
        : [...prev.triggers, trigger]
    }));
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={onClose}
            className="absolute inset-0 bg-black/50 backdrop-blur-sm"
          />

          {/* Modal */}
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 20 }}
            className="relative w-full max-w-2xl max-h-[90vh] overflow-y-auto bg-white dark:bg-gray-800 rounded-2xl shadow-2xl border border-gray-200/70 dark:border-gray-700/50"
          >
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 rounded-full bg-gradient-to-br from-purple-100 to-indigo-200 dark:from-purple-900/30 dark:to-indigo-800/40 flex items-center justify-center">
                  <Workflow className="w-5 h-5 text-purple-600 dark:text-purple-400" />
                </div>
                <div>
                  <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                    Create New Workflow
                  </h2>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Build an automated workflow with visual builder
                  </p>
                </div>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <X className="w-5 h-5" />
              </Button>
            </div>

            {/* Form */}
            <form onSubmit={handleSubmit} className="p-6 space-y-6">
              {/* Basic Info */}
              <div className="space-y-4">
                <div>
                  <Label htmlFor="name">Workflow Name</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                    placeholder="e.g., Customer Onboarding"
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                    placeholder="Describe what this workflow does..."
                    rows={3}
                  />
                </div>
              </div>

              {/* Template Selection */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white flex items-center">
                  <GitBranch className="w-5 h-5 mr-2 text-purple-500" />
                  Template
                </h3>

                <div>
                  <Label htmlFor="template">Choose a starting template</Label>
                  <Select value={formData.template} onValueChange={(value) => setFormData(prev => ({ ...prev, template: value }))}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select a template" />
                    </SelectTrigger>
                    <SelectContent>
                      {templates.map((template) => (
                        <SelectItem key={template.value} value={template.value}>
                          <div>
                            <div className="font-medium">{template.label}</div>
                            <div className="text-xs text-gray-500">{template.description}</div>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Triggers */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white flex items-center">
                  <Zap className="w-5 h-5 mr-2 text-yellow-500" />
                  Triggers
                </h3>
                <Label>How should this workflow be triggered?</Label>
                <div className="space-y-2">
                  {availableTriggers.map((trigger) => (
                    <button
                      key={trigger.value}
                      type="button"
                      onClick={() => handleTriggerToggle(trigger.value)}
                      className={`w-full p-4 text-left rounded-lg border transition-colors ${
                        formData.triggers.includes(trigger.value)
                          ? "bg-emerald-50 border-emerald-200 text-emerald-700 dark:bg-emerald-900/20 dark:border-emerald-700 dark:text-emerald-400"
                          : "bg-gray-50 border-gray-200 text-gray-700 hover:bg-gray-100 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-600"
                      }`}
                    >
                      <div className="font-medium">{trigger.label}</div>
                      <div className="text-sm opacity-75">{trigger.description}</div>
                    </button>
                  ))}
                </div>
              </div>

              {/* Actions */}
              <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700">
                <Button
                  type="button"
                  variant="outline"
                  onClick={onClose}
                  disabled={isLoading}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={isLoading || !formData.name || !formData.template}
                  className="bg-emerald-600 hover:bg-emerald-700 text-white"
                >
                  {isLoading ? "Creating..." : "Create Workflow"}
                </Button>
              </div>
            </form>
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  );
};
