"use client";

import { itemVariants } from "@/variants/variants";
import { motion } from "framer-motion";
import { signOut, useSession } from "next-auth/react";
import React, { useState } from "react";
import { Button } from "../ui/button";
import { LogOut, Rocket, User, Terminal } from "lucide-react";
import ConfirmDialog from "../modals/confirm-dialog";
import { useIsMobile } from "@/hooks/use-mobile";
import NextLink from "next/link";

export default function AuthButtons({
  setIsOpen = () => {},
}: {
  setIsOpen: (value: boolean) => void;
}) {
  const [confirmOpen, setConfirmOpen] = useState<boolean>(false);
  const isMobile = useIsMobile();

  const MotionWrapper = isMobile ? motion.li : motion.div;

  const { data: session, status } = useSession();
  const isLoading = status === "loading";

  return (
    <>
      {isLoading ? null : session ? (
        <>
          <MotionWrapper variants={itemVariants}>
            <NextLink
              href="/dashboard"
              className={`${
                isMobile
                  ? "flex items-center gap-3 px-4 py-3 text-gray-700 hover:text-blue-600 dark:text-gray-300 dark:hover:text-blue-400 transition-colors font-medium hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg"
                  : "px-4 py-2 text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors font-medium flex items-center gap-1"
              }`}
              onClick={() => setIsOpen(false)}
            >
              <Terminal size={18} />
              Go to Console
            </NextLink>
          </MotionWrapper>
          
          <MotionWrapper
            variants={itemVariants}
            className="mt-2"
          >
            <Button
              variant="ghost"
              onClick={() => setConfirmOpen(true)}
              className="group flex w-full cursor-pointer justify-center items-center gap-3 px-4 py-3 text-gray-700 hover:text-white dark:text-gray-300 font-medium rounded-lg transition-all duration-300 bg-gradient-to-r from-transparent hover:from-rose-500/10 hover:to-rose-600/20 dark:hover:from-rose-900/30 dark:hover:to-rose-800/40 border border-rose-100 hover:border-rose-200 dark:hover:border-rose-800"
            >
              <div className="relative">
                <LogOut
                  size={18}
                  className="text-rose-500 group-hover:text-rose-600 dark:text-rose-400 dark:group-hover:text-rose-300 transition-colors"
                />
                <div className="absolute inset-0 bg-rose-500/10 dark:bg-rose-400/10 rounded-full scale-0 group-hover:scale-100 transition-transform duration-300" />
              </div>
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-rose-500 to-rose-600 dark:from-rose-400 dark:to-rose-300">
                Sign Out
              </span>
            </Button>

            <ConfirmDialog
              open={confirmOpen}
              message="Are you sure you want to sign out?"
              confirmLabel="Sign Out"
              cancelLabel="Cancel"
              onConfirm={() => {
                setConfirmOpen(false);
                setIsOpen(false)
                signOut({ callbackUrl: "/login" });
              }}
              onCancel={() => setConfirmOpen(false)}
            />
          </MotionWrapper>
        </>
      ) : (
        <>
          <MotionWrapper
            variants={itemVariants}
            className={`mt-2 ${
              isMobile
                ? "border-t border-gray-200 dark:border-gray-700 pt-2"
                : ""
            }`}
          >
            <NextLink
              href="/login"
              className={`${
                isMobile
                  ? "flex items-center gap-3 px-4 py-3 text-gray-700 hover:text-emerald-600 dark:text-gray-300 dark:hover:text-emerald-400 transition-colors font-medium hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg"
                  : "px-4 py-2 text-gray-700 dark:text-gray-300 hover:text-emerald-600 dark:hover:text-emerald-400 transition-colors font-medium flex items-center gap-1"
              }`}
              onClick={() => setIsOpen(false)}
            >
              <User size={18} />
              Sign In
            </NextLink>
          </MotionWrapper>

          <MotionWrapper
            variants={itemVariants}
            whileHover={!isMobile ? { scale: 1.05 } : {}}
            whileTap={!isMobile ? { scale: 0.95 } : {}}
          >
            <NextLink
              href="/register"
              className={`${
                isMobile
                  ? "flex items-center justify-center gap-2 px-4 py-3 bg-gradient-to-r from-emerald-500 to-teal-600 text-white rounded-lg font-medium hover:from-emerald-600 hover:to-teal-700 transition-all mt-2 mx-4"
                  : "px-6 py-2.5 bg-gradient-to-r from-emerald-500 to-teal-600 text-white rounded-lg font-medium hover:from-emerald-600 hover:to-teal-700 transition-all shadow-lg hover:shadow-emerald-500/20 flex items-center gap-2 relative overflow-hidden"
              }`}
              onClick={() => setIsOpen(false)}
            >
              {!isMobile && (
                <motion.span
                  className="absolute inset-0 bg-gradient-to-r from-emerald-600 to-teal-700 opacity-0 hover:opacity-100 transition-opacity"
                  aria-hidden="true"
                />
              )}
              <Rocket size={18} className="relative z-10" />
              <span className="relative z-10">Get Started</span>
            </NextLink>
          </MotionWrapper>
        </>
      )}
    </>
  );
}