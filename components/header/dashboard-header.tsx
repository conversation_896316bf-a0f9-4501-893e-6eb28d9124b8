"use client";

import React, { useState } from "react";
import {
  <PERSON>,
  <PERSON>,
  User,
  <PERSON><PERSON>s,
  LogOut,
  Users,
  ChevronDown,
  PlusCircle,
  Zap,
  BadgeCheck,
  Sparkles,
  Rocket,
  Crown,
} from "lucide-react";
import { useTheme } from "next-themes";
import Image from "next/image";
import Link from "next/link";
import { signOut, useSession } from "next-auth/react";
import { Skeleton } from "../ui/skeleton";
import ConfirmDialog from "../modals/confirm-dialog";
import { CreateTeamModal } from "@/components/modals/create-team-modal";
import { useRouter } from "next/navigation";
import { useTeams } from "@/hooks/use-teams";
import { motion } from "framer-motion";
import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar";

// Team badge component
const TeamBadge = ({ plan }: { plan?: string }) => {
  if (!plan) return null;

  const badgeConfig: Record<string, { icon: React.ReactNode; color: string }> =
    {
      free: {
        icon: <Sparkles className="h-3 w-3" />,
        color: "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200",
      },
      hobby: {
        icon: <Rocket className="h-3 w-3" />,
        color: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",
      },
      pro: {
        icon: <BadgeCheck className="h-3 w-3" />,
        color:
          "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200",
      },
      enterprise: {
        icon: <Crown className="h-3 w-3" />,
        color:
          "bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-200",
      },
    };

  const config = badgeConfig[plan.toLowerCase()] || badgeConfig.free;

  return (
    <span
      className={`inline-flex items-center gap-1 rounded-full px-2 py-0.5 text-xs font-medium ${config.color}`}
    >
      {config.icon}
      {plan.charAt(0).toUpperCase() + plan.slice(1)}
    </span>
  );
};

export default function DashboardHeader() {
  const { theme, setTheme } = useTheme();
  const [confirmOpen, setConfirmOpen] = useState<boolean>(false);
  const [isProfileOpen, setIsProfileOpen] = useState<boolean>(false);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState<boolean>(false);
  const [isTeamsOpen, setIsTeamsOpen] = useState<boolean>(false);
  const { data: session, status } = useSession();
  const isLoading = status === "loading";
  const router = useRouter();
  const [credits] = useState(20);

  // Fetch teams from database
  const { data: teamsData, isLoading: isTeamsLoading } = useTeams();
  const teams = teamsData?.data || [];

  const toggleTheme = () => {
    setTheme(theme === "dark" ? "light" : "dark");
  };

  // Get team initials for avatar fallback
  const getTeamInitials = (name: string) => {
    return name
      .split(" ")
      .map((word) => word[0])
      .join("")
      .toUpperCase()
      .substring(0, 2);
  };

  // Get random team color
  const getTeamColor = (id: string) => {
    const colors = [
      "bg-red-500",
      "bg-blue-500",
      "bg-green-500",
      "bg-yellow-500",
      "bg-purple-500",
      "bg-pink-500",
      "bg-indigo-500",
    ];
    return colors[id.charCodeAt(0) % colors.length];
  };

  return (
    <header className="sticky top-0 z-10 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700">
      <div className="px-6 py-3 flex items-center justify-between">
        {/* Left side - Logo and Teams dropdown */}
        <div className="flex items-center space-x-6">
          <Link href="/dashboard" className="flex items-center">
            <div className="relative w-[150px] h-[40px]">
              <Image
                src="/images/chatzuri-logo-trans-rect.png"
                alt="Chatzuri Logo"
                fill
                className="object-contain dark:brightness-0 dark:invert"
                priority
                placeholder="blur"
                blurDataURL="/images/placeholder.jpg"
                sizes="(max-width: 768px) 120px, 150px"
              />
            </div>
          </Link>

          <div className="relative hidden lg:block">
            <button
              onClick={() => setIsTeamsOpen(!isTeamsOpen)}
              className="cursor-pointer flex items-center space-x-1 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors"
            >
              <span>My Teams</span>
              <ChevronDown
                className={`h-4 w-4 transition-transform ${
                  isTeamsOpen ? "rotate-180" : ""
                }`}
              />
            </button>

            {isTeamsOpen && (
              <div className="absolute left-0 mt-2 w-72 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden z-20">
                <div className="p-2 border-b border-gray-200 dark:border-gray-700">
                  <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 px-2 py-1">
                    YOUR TEAMS
                  </h3>
                  {isTeamsLoading ? (
                    // Loading skeleton
                    <div className="space-y-2 p-2">
                      {[...Array(3)].map((_, i) => (
                        <div
                          key={i}
                          className="flex items-center justify-between px-2 py-2"
                        >
                          <div className="flex items-center space-x-2">
                            <Skeleton className="h-6 w-6 rounded-full" />
                            <Skeleton className="h-4 w-32 rounded" />
                          </div>
                          <Skeleton className="h-5 w-12 rounded-full" />
                        </div>
                      ))}
                    </div>
                  ) : Array.isArray(teams) && teams.length > 0 ? (
                    teams.map((team) => (
                      <button
                        key={String(team._id)}
                        onClick={() =>
                          router.push(`/dashboard/team/${team.url}`)
                        }
                        className="cursor-pointer w-full flex items-center justify-between text-left px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md transition-colors"
                      >
                        <div className="flex items-center space-x-2">
                          {team.avatar ? (
                            <Image
                              src={team.avatar}
                              alt={team.name}
                              width={24}
                              height={24}
                              className="rounded-full h-6 w-6 object-cover"
                            />
                          ) : (
                            <div
                              className={`flex items-center justify-center h-6 w-6 rounded-full text-white text-xs font-medium ${getTeamColor(
                                String(team._id)
                              )}`}
                            >
                              {getTeamInitials(team.name)}
                            </div>
                          )}
                          <span className="truncate max-w-[160px]">
                            {team.name}
                          </span>
                        </div>
                        <TeamBadge plan={team.plan} />
                      </button>
                    ))
                  ) : (
                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.3 }}
                      className="flex flex-col items-center justify-center p-6 space-y-4 text-center"
                    >
                      <div className="p-3 rounded-full bg-gray-100 dark:bg-gray-800">
                        <Users className="h-6 w-6 text-gray-400 dark:text-gray-500" />
                      </div>
                      <div>
                        <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100">
                          No teams yet
                        </h4>
                        <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                          Get started by creating your first team
                        </p>
                      </div>
                    </motion.div>
                  )}
                </div>
                <button
                  onClick={() => setIsCreateModalOpen(true)}
                  className="w-full cursor-pointer flex items-center px-3 py-2 text-sm text-emerald-600 dark:text-emerald-400 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                >
                  <PlusCircle className="h-4 w-4 mr-2" />
                  Create New Team
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Right side - Combined credits and profile dropdown */}
        <div className="flex items-center gap-4">
          {/* Theme switcher */}
          <button
            onClick={toggleTheme}
            className="cursor-pointer p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
            aria-label="Toggle theme"
          >
            {theme === "dark" ? (
              <Sun className="h-5 w-5 text-amber-400" />
            ) : (
              <Moon className="h-5 w-5 text-indigo-600" />
            )}
          </button>

          {/* Credits display */}
          <div className="flex items-center space-x-2 bg-emerald-50 dark:bg-emerald-900/20 px-3 py-1.5 rounded-full">
            <Zap className="h-4 w-4 text-emerald-600 dark:text-emerald-400" />
            <span className="text-sm font-medium text-emerald-600 dark:text-emerald-400">
              Credits: {credits}
            </span>
          </div>

          {/* Profile dropdown */}
          <div className="relative">
            <button
              onClick={() => setIsProfileOpen(!isProfileOpen)}
              className="flex cursor-pointer items-center space-x-2"
            >
              <div className="h-8 w-8">
                {isLoading ? (
                  <Skeleton className="h-8 w-8 rounded-full" />
                ) : (
                  <Avatar className="h-8 w-8 ring-2 ring-white dark:ring-gray-800">
                    <AvatarImage
                      src={session?.user?.image || "/images/user-avatar.png"}
                      alt="User Profile"
                    />
                    <AvatarFallback className="bg-gray-200 dark:bg-gray-700">
                      {session?.user?.name
                        ? session.user.name
                            .split(" ")
                            .map((n) => n[0])
                            .join("")
                            .toUpperCase()
                        : "?"}
                    </AvatarFallback>
                  </Avatar>
                )}
              </div>
              <ChevronDown
                className={`h-4 w-4 text-gray-500 dark:text-gray-400 transition-transform ${
                  isProfileOpen ? "rotate-180" : ""
                }`}
              />
            </button>

            {isProfileOpen && (
              <div className="absolute right-0 mt-2 w-56 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden z-20">
                <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                  <div className="flex items-center space-x-3">
                    <div className="relative h-10 w-10">
                      {isLoading ? (
                        <Skeleton className="h-10 w-10 rounded-full" />
                      ) : (
                        <div className="h-10 w-10 rounded-full bg-gray-200 dark:bg-gray-700 overflow-hidden flex items-center justify-center ring-2 ring-white dark:ring-gray-800">
                          <Avatar className="h-8 w-8 ring-2 ring-white dark:ring-gray-800">
                            <AvatarImage
                              src={
                                session?.user?.image ||
                                "/images/user-avatar.png"
                              }
                              alt="User Profile"
                            />
                            <AvatarFallback className="bg-gray-200 dark:bg-gray-700">
                              {session?.user?.name
                                ? session.user.name
                                    .split(" ")
                                    .map((n) => n[0])
                                    .join("")
                                    .toUpperCase()
                                : "?"}
                            </AvatarFallback>
                          </Avatar>
                        </div>
                      )}
                    </div>
                    <div>
                      {isLoading ? (
                        <>
                          <Skeleton className="h-4 w-24 mb-1" />
                          <Skeleton className="h-3 w-32" />
                        </>
                      ) : (
                        <>
                          <p className="text-sm font-medium text-gray-900 dark:text-white">
                            {session?.user?.name || "John Doe"}
                          </p>
                          <p className="text-xs text-gray-500 dark:text-gray-400">
                            {session?.user?.email || "<EMAIL>"}
                          </p>
                        </>
                      )}
                    </div>
                  </div>
                </div>

                <div className="p-1">
                  <button
                    onClick={() =>
                      router.push(`/profile/${session?.user?.email}`)
                    }
                    className="w-full cursor-pointer flex items-center px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md transition-colors"
                  >
                    <User className="h-4 w-4 mr-2" />
                    My Account
                  </button>
                  <button
                    onClick={() => router.push("/settings")}
                    className="w-full cursor-pointer flex items-center px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md transition-colors"
                  >
                    <Settings className="h-4 w-4 mr-2" />
                    Settings
                  </button>
                  <button
                    onClick={() => setIsCreateModalOpen(true)}
                    className="w-full cursor-pointer flex items-center px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md transition-colors"
                  >
                    <Users className="h-4 w-4 mr-2" />
                    Create Team
                  </button>
                </div>

                <div className="p-1 cursor-pointer border-t border-gray-200 dark:border-gray-700">
                  <button
                    onClick={() => setConfirmOpen(true)}
                    className="w-full cursor-pointer flex items-center px-3 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md transition-colors"
                  >
                    <LogOut className="h-4 w-4 mr-2" />
                    Sign Out
                  </button>
                  <ConfirmDialog
                    open={confirmOpen}
                    message="Are you sure you want to sign out?"
                    confirmLabel="Sign Out"
                    cancelLabel="Cancel"
                    onConfirm={() => {
                      setConfirmOpen(false);
                      signOut({ callbackUrl: "/login" });
                    }}
                    onCancel={() => setConfirmOpen(false)}
                  />
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
      <CreateTeamModal
        open={isCreateModalOpen}
        onOpenChange={setIsCreateModalOpen}
      />
    </header>
  );
}
