"use client";

import { motion, AnimatePresence } from "framer-motion";
import {
  Menu,
  Moon,
  Sun,
  BookOpen,
  DollarSign,
  Share2,
  FileCode2,
  X,
} from "lucide-react";
import Link from "next/link";
import Image from "next/image";
import { useState, useEffect } from "react";
import { useTheme } from "next-themes";
import { useIsMobile } from "@/hooks/use-mobile";
import { containerVariants, itemVariants } from "@/variants/variants";
import AuthButtons from "../buttons/auth-buttons";

export const Header = () => {
  const { setTheme, resolvedTheme } = useTheme();
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [scrolled, setScrolled] = useState<boolean>(false);

  const isMobile = useIsMobile();

  useEffect(() => {
    const handleScroll = () => {
      const isScrolled = window.scrollY > 10;
      const isShortPage = document.body.scrollHeight <= window.innerHeight;
      setScrolled(isScrolled || isShortPage);
    };
    window.addEventListener("scroll", handleScroll);
    window.addEventListener("resize", handleScroll);
    return () => {
      window.removeEventListener("scroll", handleScroll);
      window.removeEventListener("resize", handleScroll);
    };
  }, []);

  const navItems = [
    { name: "Affiliates", href: "/affiliates", icon: <Share2 size={18} /> },
    { name: "Pricing", href: "/pricing", icon: <DollarSign size={18} /> },
    { name: "API", href: "/docs", icon: <FileCode2 size={18} /> },
    { name: "Guides", href: "/guides", icon: <BookOpen size={18} /> },
    { name: "Blog", href: "/blogs", icon: <BookOpen size={18} /> },
  ];

  const logoVariants = {
    hover: {
      scale: 1.05,
      transition: { type: "spring", stiffness: 300 },
    },
    tap: { scale: 0.95 },
  };

  return (
    <motion.header
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      transition={{ type: "spring", stiffness: 100, damping: 15 }}
      className={`fixed w-full z-50 transition-all duration-300 ${
        scrolled || isMobile
          ? "bg-white/90 dark:bg-gray-900/90 backdrop-blur-md shadow-sm"
          : "bg-transparent"
      }`}
    >
      <div className="container mx-auto px-4 py-3">
        <div className="flex items-center justify-between">
          <motion.div variants={logoVariants} whileHover="hover" whileTap="tap">
            <Link href="/" className="flex items-center">
              <div className="relative w-[150px] h-[40px]">
                <Image
                  src="/images/chatzuri-logo-trans-rect.png"
                  alt="Chatzuri Logo"
                  fill
                  className="object-contain dark:brightness-0 dark:invert"
                  priority
                  placeholder="blur"
                  blurDataURL="/images/placeholder.jpg"
                  sizes="(max-width: 768px) 120px, 150px"
                />
              </div>
            </Link>
          </motion.div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center gap-8">
            <motion.ul
              variants={containerVariants}
              initial="hidden"
              animate="visible"
              className="flex items-center gap-6"
            >
              {navItems.map((item) => (
                <motion.li key={item.name} variants={itemVariants}>
                  <Link
                    href={item.href}
                    className="flex items-center gap-1.5 text-gray-600 hover:text-emerald-600 dark:text-gray-300 dark:hover:text-emerald-400 transition-colors font-medium group"
                  >
                    <motion.span
                      className="group-hover:scale-110 transition-transform"
                      whileHover={{ scale: 1.1 }}
                    >
                      {item.icon}
                    </motion.span>
                    <span>{item.name}</span>
                  </Link>
                </motion.li>
              ))}
            </motion.ul>
          </nav>

          {/* Right Side Controls */}
          <div className="flex items-center gap-4">
            {/* Dark Mode Toggle */}
            <motion.button
              onClick={() =>
                setTheme(resolvedTheme === "dark" ? "light" : "dark")
              }
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              className="p-2 cursor-pointer rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
              aria-label="Toggle dark mode"
            >
              {resolvedTheme === "dark" ? (
                <Sun className="text-gray-700 dark:text-yellow-300" size={20} />
              ) : (
                <Moon className="text-gray-700 dark:text-gray-300" size={20} />
              )}
            </motion.button>

            {/* Desktop Auth Buttons */}
            <div className="hidden md:flex items-center gap-3">
              <AuthButtons setIsOpen={setIsOpen} />
            </div>

            {/* Mobile Menu Button */}
            <motion.button
              onClick={() => {
                if (window.scrollY < 20) {
                  window.scrollBy({ top: 20, behavior: "smooth" });
                }
                setIsOpen(!isOpen);
              }}
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              className="md:hidden p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
              aria-label="Toggle menu"
              aria-expanded={isOpen}
            >
              <AnimatePresence mode="wait" initial={false}>
                <motion.div
                  key={isOpen ? "close" : "menu"}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.8 }}
                  transition={{ duration: 0.2 }}
                >
                  {isOpen ? (
                    <X className="text-gray-700 dark:text-gray-300" size={24} />
                  ) : (
                    <Menu
                      className="text-gray-700 dark:text-gray-300"
                      size={24}
                    />
                  )}
                </motion.div>
              </AnimatePresence>
            </motion.button>
          </div>
        </div>

        {/* Mobile Menu */}
        <motion.div
          initial={false}
          animate={isOpen ? "open" : "closed"}
          variants={{
            open: {
              opacity: 1,
              height: "auto",
              transition: {
                type: "spring",
                bounce: 0,
                duration: 0.3,
              },
            },
            closed: {
              opacity: 0,
              height: 0,
              transition: {
                type: "spring",
                bounce: 0,
                duration: 0.2,
              },
            },
          }}
          className="md:hidden overflow-hidden "
        >
          <motion.ul
            variants={containerVariants}
            className="flex flex-col gap-2 py-4"
          >
            {navItems.map((item) => (
              <motion.li key={item.name} variants={itemVariants}>
                <Link
                  href={item.href}
                  className="flex items-center gap-3 px-4 py-3 text-gray-700 hover:text-emerald-600 dark:text-gray-300 dark:hover:text-emerald-400 transition-colors font-medium hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg"
                  onClick={() => setIsOpen(false)}
                >
                  <motion.span whileHover={{ scale: 1.1 }}>
                    {item.icon}
                  </motion.span>
                  {item.name}
                </Link>
              </motion.li>
            ))}
            <AuthButtons setIsOpen={setIsOpen} />
          </motion.ul>
        </motion.div>
      </div>
    </motion.header>
  );
};
