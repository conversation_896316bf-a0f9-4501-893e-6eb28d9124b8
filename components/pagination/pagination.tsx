"use client";

import { ChevronLeft, ChevronRight, MoreHorizontal } from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { motion } from "framer-motion";

interface PaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  className?: string;
}

export const Pagination = ({
  currentPage,
  totalPages,
  onPageChange,
  className,
}: PaginationProps) => {
  const MAX_VISIBLE_PAGES = 5;

  const getPageNumbers = () => {
    if (totalPages <= MAX_VISIBLE_PAGES) {
      return Array.from({ length: totalPages }, (_, i) => i + 1);
    }

    let startPage = Math.max(
      1,
      currentPage - Math.floor(MAX_VISIBLE_PAGES / 2)
    );
    let endPage = startPage + MAX_VISIBLE_PAGES - 1;

    if (endPage > totalPages) {
      endPage = totalPages;
      startPage = Math.max(1, endPage - MAX_VISIBLE_PAGES + 1);
    }

    const pages = [];
    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }

    return pages;
  };

  const pageNumbers = getPageNumbers();

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, ease: "easeOut" }}
      className={cn("flex items-center justify-center gap-1.5", className)}
    >
      {/* Previous Button - Animated with spring effect */}
      <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => onPageChange(Math.max(1, currentPage - 1))}
          disabled={currentPage === 1}
          className={cn(
            "h-10 w-10 p-0 cursor-pointer rounded-full border",
            "bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700",
            "hover:bg-emerald-50 dark:hover:bg-emerald-900/30",
            "hover:border-emerald-300 dark:hover:border-emerald-600",
            "disabled:opacity-40 disabled:pointer-events-none",
            "transition-all duration-300 ease-out",
            "shadow-sm hover:shadow-emerald-100 dark:hover:shadow-emerald-900/20"
          )}
        >
          <ChevronLeft className="h-4 w-4 text-emerald-600 dark:text-emerald-400 transition-transform" />
        </Button>
      </motion.div>

      {/* First Page */}
      {!pageNumbers.includes(1) && (
        <>
          <motion.div
            whileHover={{ scale: 1.05 }}
            transition={{ type: "spring", stiffness: 400, damping: 10 }}
          >
            <Button
              variant={currentPage === 1 ? "secondary" : "ghost"}
              size="sm"
              onClick={() => onPageChange(1)}
              className={cn(
                "h-10 w-10 p-0 rounded-full cursor-pointer font-medium",
                "transition-all duration-300 ease-out",
                currentPage === 1
                  ? "bg-gradient-to-br from-emerald-100 to-emerald-50 dark:from-emerald-900/60 dark:to-emerald-900/30 text-emerald-800 dark:text-emerald-100 border-emerald-300 dark:border-emerald-700 shadow-inner"
                  : "border border-gray-200 dark:border-gray-700 hover:border-emerald-300 dark:hover:border-emerald-600 bg-white dark:bg-gray-800"
              )}
            >
              1
            </Button>
          </motion.div>
          <motion.div
            animate={{ rotate: 90, scale: 1.2 }}
            transition={{ repeat: Infinity, duration: 1.5, ease: "linear" }}
          >
            <MoreHorizontal className="h-4 w-4 text-gray-400 mx-1" />
          </motion.div>
        </>
      )}

      {/* Page Numbers - Each with individual animations */}
      {pageNumbers.map((page) => (
        <motion.div
          key={page}
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.95 }}
          transition={{ type: "spring", stiffness: 500 }}
        >
          <Button
            variant={currentPage === page ? "secondary" : "ghost"}
            size="sm"
            onClick={() => onPageChange(page)}
            className={cn(
              "h-10 w-10 p-0 rounded-full cursor-pointer font-medium",
              "transition-all duration-300 ease-out",
              currentPage === page
                ? "bg-gradient-to-br from-emerald-100 to-emerald-50 dark:from-emerald-900/60 dark:to-emerald-900/30 text-emerald-800 dark:text-emerald-100 border-emerald-300 dark:border-emerald-700 shadow-inner"
                : "border border-gray-200 dark:border-gray-700 hover:border-emerald-300 dark:hover:border-emerald-600 bg-white dark:bg-gray-800 hover:bg-emerald-50/50 dark:hover:bg-emerald-900/20"
            )}
          >
            {page}
          </Button>
        </motion.div>
      ))}

      {/* Last Page */}
      {!pageNumbers.includes(totalPages) && totalPages > 0 && (
        <>
          <motion.div
            animate={{ rotate: 90, scale: 1.2 }}
            transition={{ repeat: Infinity, duration: 1.5, ease: "linear" }}
          >
            <MoreHorizontal className="h-4 w-4 text-gray-400 mx-1" />
          </motion.div>
          <motion.div
            whileHover={{ scale: 1.05 }}
            transition={{ type: "spring", stiffness: 400, damping: 10 }}
          >
            <Button
              variant={currentPage === totalPages ? "secondary" : "ghost"}
              size="sm"
              onClick={() => onPageChange(totalPages)}
              className={cn(
                "h-10 w-10 p-0 rounded-full cursor-pointer font-medium",
                "transition-all duration-300 ease-out",
                currentPage === totalPages
                  ? "bg-gradient-to-br from-emerald-100 to-emerald-50 dark:from-emerald-900/60 dark:to-emerald-900/30 text-emerald-800 dark:text-emerald-100 border-emerald-300 dark:border-emerald-700 shadow-inner"
                  : "border border-gray-200 dark:border-gray-700 hover:border-emerald-300 dark:hover:border-emerald-600 bg-white dark:bg-gray-800"
              )}
            >
              {totalPages}
            </Button>
          </motion.div>
        </>
      )}

      {/* Next Button - Animated with spring effect */}
      <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => onPageChange(Math.min(totalPages, currentPage + 1))}
          disabled={currentPage === totalPages}
          className={cn(
            "h-10 w-10 p-0 cursor-pointer rounded-full border",
            "bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700",
            "hover:bg-emerald-50 dark:hover:bg-emerald-900/30",
            "hover:border-emerald-300 dark:hover:border-emerald-600",
            "disabled:opacity-40 disabled:pointer-events-none",
            "transition-all duration-300 ease-out",
            "shadow-sm hover:shadow-emerald-100 dark:hover:shadow-emerald-900/20"
          )}
        >
          <ChevronRight className="h-4 w-4 text-emerald-600 dark:text-emerald-400 transition-transform" />
        </Button>
      </motion.div>
    </motion.div>
  );
};
