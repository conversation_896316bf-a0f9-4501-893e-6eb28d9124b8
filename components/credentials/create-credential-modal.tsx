'use client';

import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>alog<PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Loader2, TestTube, Eye, EyeOff, CheckCircle, XCircle } from 'lucide-react';
import { useCredentials } from '@/hooks/use-credentials';
import { useIntegrations } from '@/hooks/use-integrations';
import { AppType, CredentialField } from '@/lib/integrations/types/integration-types';

interface CreateCredentialModalProps {
  isOpen: boolean;
  onClose: () => void;
  teamId: string;
  onSuccess?: () => void;
}

export function CreateCredentialModal({ isO<PERSON>, onClose, teamId, onSuccess }: CreateCredentialModalProps) {
  const [step, setStep] = useState<'select' | 'configure'>('select');
  const [selectedAppType, setSelectedAppType] = useState<AppType | null>(null);
  const [credentialName, setCredentialName] = useState('');
  const [credentialData, setCredentialData] = useState<Record<string, any>>({});
  const [showPasswords, setShowPasswords] = useState<Record<string, boolean>>({});
  const [testResult, setTestResult] = useState<any>(null);
  const [isTesting, setIsTesting] = useState(false);

  const { createCredential, testNewCredentials, loading } = useCredentials({ teamId, autoLoad: false });
  const { integrations, getIntegration } = useIntegrations();

  // Reset form
  const resetForm = () => {
    setStep('select');
    setSelectedAppType(null);
    setCredentialName('');
    setCredentialData({});
    setShowPasswords({});
    setTestResult(null);
    setIsTesting(false);
  };

  // Handle close
  const handleClose = () => {
    resetForm();
    onClose();
  };

  // Handle app type selection
  const handleAppTypeSelect = (appType: AppType) => {
    setSelectedAppType(appType);
    setStep('configure');
    
    // Set default credential name
    const integration = getIntegration(appType);
    if (integration) {
      setCredentialName(`${integration.name} - ${new Date().toLocaleDateString()}`);
    }
  };

  // Handle field change
  const handleFieldChange = (fieldName: string, value: any) => {
    setCredentialData(prev => ({
      ...prev,
      [fieldName]: value
    }));
    
    // Clear test result when credentials change
    setTestResult(null);
  };

  // Toggle password visibility
  const togglePasswordVisibility = (fieldName: string) => {
    setShowPasswords(prev => ({
      ...prev,
      [fieldName]: !prev[fieldName]
    }));
  };

  // Test credentials
  const handleTestCredentials = async () => {
    if (!selectedAppType) return;

    setIsTesting(true);
    try {
      const result = await testNewCredentials(selectedAppType, credentialData);
      setTestResult(result);
    } finally {
      setIsTesting(false);
    }
  };

  // Create credential
  const handleCreate = async () => {
    if (!selectedAppType || !credentialName.trim()) return;

    const credential = await createCredential({
      name: credentialName.trim(),
      appType: selectedAppType,
      credentials: credentialData
    });

    if (credential) {
      handleClose();
      onSuccess?.();
    }
  };

  // Get current integration
  const currentIntegration = selectedAppType ? getIntegration(selectedAppType) : null;

  // Render field input
  const renderFieldInput = (field: CredentialField) => {
    const value = credentialData[field.name] || '';
    const isPassword = field.type === 'password';
    const showPassword = showPasswords[field.name];

    switch (field.type) {
      case 'select':
        return (
          <Select value={value} onValueChange={(val) => handleFieldChange(field.name, val)}>
            <SelectTrigger>
              <SelectValue placeholder={field.placeholder} />
            </SelectTrigger>
            <SelectContent>
              {field.options?.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        );

      case 'textarea':
        return (
          <Textarea
            value={value}
            onChange={(e) => handleFieldChange(field.name, e.target.value)}
            placeholder={field.placeholder}
            rows={3}
          />
        );

      case 'password':
        return (
          <div className="relative">
            <Input
              type={showPassword ? 'text' : 'password'}
              value={value}
              onChange={(e) => handleFieldChange(field.name, e.target.value)}
              placeholder={field.placeholder}
              className="pr-10"
            />
            <Button
              type="button"
              variant="ghost"
              size="sm"
              className="absolute right-0 top-0 h-full px-3"
              onClick={() => togglePasswordVisibility(field.name)}
            >
              {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
            </Button>
          </div>
        );

      default:
        return (
          <Input
            type={field.type}
            value={value}
            onChange={(e) => handleFieldChange(field.name, e.target.value)}
            placeholder={field.placeholder}
          />
        );
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-auto">
        <DialogHeader>
          <DialogTitle>
            {step === 'select' ? 'Add New Credential' : `Configure ${currentIntegration?.name}`}
          </DialogTitle>
        </DialogHeader>

        {step === 'select' && (
          <div className="space-y-4">
            <p className="text-gray-600">
              Select the application or service you want to connect:
            </p>

            <div className="grid grid-cols-2 gap-3 max-h-96 overflow-auto">
              {integrations.map((integration) => (
                <button
                  key={integration.id}
                  onClick={() => handleAppTypeSelect(integration.id)}
                  className="flex items-center gap-3 p-4 border rounded-lg hover:bg-gray-50 transition-colors text-left"
                >
                  <div 
                    className="w-10 h-10 rounded-lg flex items-center justify-center text-white text-lg flex-shrink-0"
                    style={{ backgroundColor: integration.color }}
                  >
                    {integration.icon}
                  </div>
                  <div className="min-w-0">
                    <h3 className="font-medium truncate">{integration.name}</h3>
                    <p className="text-sm text-gray-600 truncate">{integration.description}</p>
                  </div>
                </button>
              ))}
            </div>
          </div>
        )}

        {step === 'configure' && currentIntegration && (
          <div className="space-y-6">
            {/* Credential Name */}
            <div className="space-y-2">
              <Label htmlFor="credentialName">Credential Name</Label>
              <Input
                id="credentialName"
                value={credentialName}
                onChange={(e) => setCredentialName(e.target.value)}
                placeholder="Enter a name for this credential"
              />
            </div>

            {/* Integration Fields */}
            <div className="space-y-4">
              <h3 className="font-medium">Connection Details</h3>
              
              {currentIntegration.requiredFields.map((field) => (
                <div key={field.name} className="space-y-2">
                  <Label htmlFor={field.name}>
                    {field.label}
                    {field.required && <span className="text-red-500 ml-1">*</span>}
                  </Label>
                  {renderFieldInput(field)}
                  {field.description && (
                    <p className="text-sm text-gray-600">{field.description}</p>
                  )}
                </div>
              ))}
            </div>

            {/* Test Connection */}
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  onClick={handleTestCredentials}
                  disabled={isTesting || !credentialName.trim()}
                  className="flex items-center gap-2"
                >
                  {isTesting ? (
                    <Loader2 className="w-4 h-4 animate-spin" />
                  ) : (
                    <TestTube className="w-4 h-4" />
                  )}
                  Test Connection
                </Button>
                
                {testResult && (
                  <div className="flex items-center gap-2">
                    {testResult.isValid ? (
                      <div className="flex items-center gap-1 text-green-600">
                        <CheckCircle className="w-4 h-4" />
                        <span className="text-sm">Connection successful</span>
                      </div>
                    ) : (
                      <div className="flex items-center gap-1 text-red-600">
                        <XCircle className="w-4 h-4" />
                        <span className="text-sm">Connection failed</span>
                      </div>
                    )}
                  </div>
                )}
              </div>

              {testResult && !testResult.isValid && testResult.errors.length > 0 && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                  <p className="text-sm text-red-800 font-medium mb-1">Connection Errors:</p>
                  <ul className="text-sm text-red-700 space-y-1">
                    {testResult.errors.map((error: string, index: number) => (
                      <li key={index}>• {error}</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>

            {/* Actions */}
            <div className="flex justify-between pt-4">
              <Button
                variant="outline"
                onClick={() => setStep('select')}
              >
                Back
              </Button>

              <div className="flex gap-2">
                <Button
                  variant="outline"
                  onClick={handleClose}
                >
                  Cancel
                </Button>
                
                <Button
                  onClick={handleCreate}
                  disabled={loading || !credentialName.trim()}
                  className="flex items-center gap-2"
                >
                  {loading && <Loader2 className="w-4 h-4 animate-spin" />}
                  Create Credential
                </Button>
              </div>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
