'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useCredentials } from '@/hooks/use-credentials';
import { useIntegrations } from '@/hooks/use-integrations';
import { CredentialSelector } from './credential-selector';
import { CredentialManagerModal } from './credential-manager-modal';
import { TestTube, Settings, CheckCircle, XCircle, Loader2 } from 'lucide-react';

interface CredentialTestProps {
  teamId: string;
}

export function CredentialTest({ teamId }: CredentialTestProps) {
  const [selectedCredential, setSelectedCredential] = useState<string>('');
  const [showManager, setShowManager] = useState(false);
  const [testResults, setTestResults] = useState<Record<string, any>>({});
  const [testing, setTesting] = useState<Record<string, boolean>>({});

  const { 
    credentials, 
    loading, 
    testCredential,
    refresh 
  } = useCredentials({ teamId });

  const { integrations } = useIntegrations();

  // Test individual credential
  const handleTestCredential = async (credentialId: string) => {
    setTesting(prev => ({ ...prev, [credentialId]: true }));
    
    try {
      const result = await testCredential(credentialId);
      setTestResults(prev => ({ ...prev, [credentialId]: result }));
    } finally {
      setTesting(prev => ({ ...prev, [credentialId]: false }));
    }
  };

  // Get integration info
  const getIntegrationInfo = (appType: string) => {
    const integration = integrations.find(i => i.id === appType);
    return {
      name: integration?.name || appType,
      icon: integration?.icon || '🔌',
      color: integration?.color || '#6B7280'
    };
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            🔌 Credential System Test
          </CardTitle>
          <CardDescription>
            Test the credential management system integration
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Credential Manager Button */}
          <div>
            <Button
              onClick={() => setShowManager(true)}
              className="flex items-center gap-2"
            >
              <Settings className="w-4 h-4" />
              Open Credential Manager
            </Button>
          </div>

          {/* Credential Selector Test */}
          <div>
            <h3 className="font-medium mb-3">Credential Selector Test</h3>
            <CredentialSelector
              teamId={teamId}
              value={selectedCredential}
              onChange={setSelectedCredential}
              placeholder="Test credential selector..."
              allowCreate={true}
              allowTest={true}
            />
          </div>

          {/* AI Service Selector Test */}
          <div>
            <h3 className="font-medium mb-3">AI Service Credential Test</h3>
            <CredentialSelector
              teamId={teamId}
              appType="openai"
              value=""
              onChange={() => {}}
              placeholder="OpenAI credentials only..."
              allowCreate={true}
              allowTest={true}
            />
          </div>

          {/* Credentials List */}
          <div>
            <h3 className="font-medium mb-3">Available Credentials</h3>
            {loading ? (
              <div className="flex items-center gap-2">
                <Loader2 className="w-4 h-4 animate-spin" />
                Loading credentials...
              </div>
            ) : credentials.length === 0 ? (
              <p className="text-gray-600">No credentials found. Create some to test!</p>
            ) : (
              <div className="space-y-3">
                {credentials.map((credential) => {
                  const integration = getIntegrationInfo(credential.appType);
                  const isTestingThis = testing[credential.id];
                  const testResult = testResults[credential.id];
                  
                  return (
                    <div
                      key={credential.id}
                      className="flex items-center justify-between p-3 border rounded-lg"
                    >
                      <div className="flex items-center gap-3">
                        <div 
                          className="w-8 h-8 rounded flex items-center justify-center text-white text-sm"
                          style={{ backgroundColor: integration.color }}
                        >
                          {integration.icon}
                        </div>
                        <div>
                          <p className="font-medium">{credential.name}</p>
                          <p className="text-sm text-gray-600">{integration.name}</p>
                        </div>
                        
                        {credential.isActive ? (
                          <Badge variant="default" className="bg-green-100 text-green-800">
                            Active
                          </Badge>
                        ) : (
                          <Badge variant="secondary">Inactive</Badge>
                        )}
                      </div>

                      <div className="flex items-center gap-2">
                        {testResult && (
                          <div className="flex items-center gap-1">
                            {testResult.isValid ? (
                              <CheckCircle className="w-4 h-4 text-green-600" />
                            ) : (
                              <XCircle className="w-4 h-4 text-red-600" />
                            )}
                            <span className="text-sm">
                              {testResult.isValid ? 'Valid' : 'Invalid'}
                            </span>
                          </div>
                        )}
                        
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleTestCredential(credential.id)}
                          disabled={isTestingThis}
                          className="flex items-center gap-1"
                        >
                          {isTestingThis ? (
                            <Loader2 className="w-3 h-3 animate-spin" />
                          ) : (
                            <TestTube className="w-3 h-3" />
                          )}
                          Test
                        </Button>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </div>

          {/* Integration Stats */}
          <div>
            <h3 className="font-medium mb-3">Available Integrations</h3>
            <div className="grid grid-cols-2 gap-3">
              {integrations.slice(0, 6).map((integration) => (
                <div
                  key={integration.id}
                  className="flex items-center gap-2 p-2 border rounded"
                >
                  <div 
                    className="w-6 h-6 rounded flex items-center justify-center text-white text-xs"
                    style={{ backgroundColor: integration.color }}
                  >
                    {integration.icon}
                  </div>
                  <span className="text-sm">{integration.name}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Refresh Button */}
          <div>
            <Button
              variant="outline"
              onClick={refresh}
              disabled={loading}
              className="flex items-center gap-2"
            >
              {loading ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <TestTube className="w-4 h-4" />
              )}
              Refresh Data
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Credential Manager Modal */}
      <CredentialManagerModal
        isOpen={showManager}
        onClose={() => setShowManager(false)}
        teamId={teamId}
      />
    </div>
  );
}
