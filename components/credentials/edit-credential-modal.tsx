'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON><PERSON>Header, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Loader2, TestTube, Eye, EyeOff, CheckCircle, XCircle } from 'lucide-react';
import { useCredentials } from '@/hooks/use-credentials';
import { useIntegrations } from '@/hooks/use-integrations';
import { Credential } from '@/lib/integrations/types/integration-types';

interface EditCredentialModalProps {
  isOpen: boolean;
  onClose: () => void;
  credential: Credential;
  teamId: string;
  onSuccess?: () => void;
}

export function EditCredentialModal({ 
  isOpen, 
  onClose, 
  credential, 
  teamId, 
  onSuccess 
}: EditCredentialModalProps) {
  const [credentialName, setCredentialName] = useState(credential.name);
  const [isActive, setIsActive] = useState(credential.isActive);
  const [testResult, setTestResult] = useState<any>(null);
  const [isTesting, setIsTesting] = useState(false);

  const { updateCredential, testCredential, loading } = useCredentials({ teamId, autoLoad: false });
  const { getIntegration } = useIntegrations();

  // Reset form when credential changes
  useEffect(() => {
    setCredentialName(credential.name);
    setIsActive(credential.isActive);
    setTestResult(null);
  }, [credential]);

  // Handle test credential
  const handleTestCredential = async () => {
    setIsTesting(true);
    try {
      const result = await testCredential(credential.id);
      setTestResult(result);
    } finally {
      setIsTesting(false);
    }
  };

  // Handle update
  const handleUpdate = async () => {
    const updatedCredential = await updateCredential(credential.id, {
      name: credentialName.trim(),
      isActive
    });

    if (updatedCredential) {
      onClose();
      onSuccess?.();
    }
  };

  // Get integration info
  const integration = getIntegration(credential.appType);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-lg">
        <DialogHeader>
          <DialogTitle>Edit Credential</DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Integration Info */}
          <div className="flex items-center gap-3 p-4 bg-gray-50 rounded-lg">
            <div 
              className="w-10 h-10 rounded-lg flex items-center justify-center text-white text-lg"
              style={{ backgroundColor: integration?.color || '#6B7280' }}
            >
              {integration?.icon || '🔌'}
            </div>
            <div>
              <h3 className="font-medium">{integration?.name || credential.appType}</h3>
              <p className="text-sm text-gray-600">
                Created {new Date(credential.createdAt).toLocaleDateString()}
              </p>
            </div>
          </div>

          {/* Credential Name */}
          <div className="space-y-2">
            <Label htmlFor="credentialName">Credential Name</Label>
            <Input
              id="credentialName"
              value={credentialName}
              onChange={(e) => setCredentialName(e.target.value)}
              placeholder="Enter credential name"
            />
          </div>

          {/* Active Status */}
          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="isActive">Active Status</Label>
              <p className="text-sm text-gray-600">
                Inactive credentials cannot be used in workflows
              </p>
            </div>
            <Switch
              id="isActive"
              checked={isActive}
              onCheckedChange={setIsActive}
            />
          </div>

          {/* Usage Stats */}
          <div className="space-y-2">
            <Label>Usage Statistics</Label>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <p className="text-gray-600">Usage Count</p>
                <p className="font-medium">{credential.usageCount || 0}</p>
              </div>
              <div>
                <p className="text-gray-600">Last Used</p>
                <p className="font-medium">
                  {credential.lastUsedAt 
                    ? new Date(credential.lastUsedAt).toLocaleDateString()
                    : 'Never'
                  }
                </p>
              </div>
            </div>
          </div>

          {/* Test Connection */}
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                onClick={handleTestCredential}
                disabled={isTesting || !isActive}
                className="flex items-center gap-2"
              >
                {isTesting ? (
                  <Loader2 className="w-4 h-4 animate-spin" />
                ) : (
                  <TestTube className="w-4 h-4" />
                )}
                Test Connection
              </Button>
              
              {testResult && (
                <div className="flex items-center gap-2">
                  {testResult.isValid ? (
                    <div className="flex items-center gap-1 text-green-600">
                      <CheckCircle className="w-4 h-4" />
                      <span className="text-sm">Connection successful</span>
                    </div>
                  ) : (
                    <div className="flex items-center gap-1 text-red-600">
                      <XCircle className="w-4 h-4" />
                      <span className="text-sm">Connection failed</span>
                    </div>
                  )}
                </div>
              )}
            </div>

            {testResult && !testResult.isValid && testResult.errors.length > 0 && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                <p className="text-sm text-red-800 font-medium mb-1">Connection Errors:</p>
                <ul className="text-sm text-red-700 space-y-1">
                  {testResult.errors.map((error: string, index: number) => (
                    <li key={index}>• {error}</li>
                  ))}
                </ul>
              </div>
            )}
          </div>

          {/* Actions */}
          <div className="flex justify-end gap-2 pt-4">
            <Button
              variant="outline"
              onClick={onClose}
            >
              Cancel
            </Button>
            
            <Button
              onClick={handleUpdate}
              disabled={loading || !credentialName.trim()}
              className="flex items-center gap-2"
            >
              {loading && <Loader2 className="w-4 h-4 animate-spin" />}
              Update Credential
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
