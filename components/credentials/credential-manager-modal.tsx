'use client';

import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { 
  Search, 
  Plus, 
  Filter, 
  Edit, 
  Trash2, 
  TestTube, 
  Eye, 
  EyeOff,
  Loader2,
  CheckCircle,
  XCircle,
  AlertTriangle
} from 'lucide-react';
import { useCredentials } from '@/hooks/use-credentials';
import { useIntegrations } from '@/hooks/use-integrations';
import { Credential, AppType } from '@/lib/integrations/types/integration-types';
import { CreateCredentialModal } from './create-credential-modal';
import { EditCredentialModal } from './edit-credential-modal';
import { ConfirmDialog } from '@/components/ui/confirm-dialog';

interface CredentialManagerModalProps {
  isOpen: boolean;
  onClose: () => void;
  teamId: string;
}

export function CredentialManagerModal({ isOpen, onClose, teamId }: CredentialManagerModalProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [editingCredential, setEditingCredential] = useState<Credential | null>(null);
  const [deletingCredential, setDeletingCredential] = useState<Credential | null>(null);
  const [testingCredentials, setTestingCredentials] = useState<Set<string>>(new Set());

  const { 
    credentials, 
    loading, 
    error,
    deleteCredential,
    testCredential,
    refresh
  } = useCredentials({ teamId });

  const { integrations, getIntegration } = useIntegrations();

  // Filter credentials
  const filteredCredentials = credentials.filter(credential => {
    const matchesSearch = credential.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         credential.appType.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesCategory = selectedCategory === 'all' || 
                           getIntegration(credential.appType)?.category === selectedCategory;
    
    return matchesSearch && matchesCategory;
  });

  // Handle test credential
  const handleTestCredential = async (credential: Credential) => {
    setTestingCredentials(prev => new Set(prev).add(credential.id));
    
    try {
      await testCredential(credential.id);
    } finally {
      setTestingCredentials(prev => {
        const newSet = new Set(prev);
        newSet.delete(credential.id);
        return newSet;
      });
    }
  };

  // Handle delete credential
  const handleDeleteCredential = async () => {
    if (!deletingCredential) return;
    
    const success = await deleteCredential(deletingCredential.id);
    if (success) {
      setDeletingCredential(null);
    }
  };

  // Get integration info
  const getIntegrationInfo = (appType: AppType) => {
    const integration = getIntegration(appType);
    return {
      name: integration?.name || appType,
      icon: integration?.icon || '🔌',
      color: integration?.color || '#6B7280'
    };
  };

  // Get status badge
  const getStatusBadge = (credential: Credential) => {
    if (!credential.isActive) {
      return <Badge variant="secondary">Inactive</Badge>;
    }
    
    if (credential.lastUsedAt) {
      const daysSinceUsed = Math.floor(
        (Date.now() - new Date(credential.lastUsedAt).getTime()) / (1000 * 60 * 60 * 24)
      );
      
      if (daysSinceUsed < 7) {
        return <Badge variant="default" className="bg-green-100 text-green-800">Active</Badge>;
      } else if (daysSinceUsed < 30) {
        return <Badge variant="secondary">Recently Used</Badge>;
      }
    }
    
    return <Badge variant="outline">Unused</Badge>;
  };

  return (
    <>
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-4xl h-[80vh] flex flex-col p-0">
          <DialogHeader className="px-6 py-4 border-b flex-shrink-0">
            <DialogTitle className="flex items-center gap-2">
              🔌 Credentials (Apps) Manager
            </DialogTitle>
          </DialogHeader>

          {/* Toolbar */}
          <div className="px-6 py-4 border-b flex-shrink-0">
            <div className="flex items-center gap-4">
              <Button 
                onClick={() => setShowCreateModal(true)}
                className="flex items-center gap-2"
              >
                <Plus className="w-4 h-4" />
                Add New
              </Button>

              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Search credentials..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>

              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="px-3 py-2 border rounded-md bg-white"
              >
                <option value="all">All Categories</option>
                <option value="ai_services">AI Services</option>
                <option value="business_apps">Business Apps</option>
                <option value="communication">Communication</option>
                <option value="storage">Storage</option>
              </select>

              <Button
                variant="outline"
                onClick={refresh}
                disabled={loading}
                className="flex items-center gap-2"
              >
                {loading ? (
                  <Loader2 className="w-4 h-4 animate-spin" />
                ) : (
                  <Filter className="w-4 h-4" />
                )}
                Refresh
              </Button>
            </div>
          </div>

          {/* Credentials List */}
          <div className="flex-1 overflow-auto px-6 py-4">
            {error && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
                <div className="flex items-center gap-2 text-red-800">
                  <XCircle className="w-4 h-4" />
                  {error}
                </div>
              </div>
            )}

            {loading && credentials.length === 0 ? (
              <div className="flex items-center justify-center py-12">
                <Loader2 className="w-6 h-6 animate-spin mr-2" />
                Loading credentials...
              </div>
            ) : filteredCredentials.length === 0 ? (
              <div className="text-center py-12">
                <div className="text-gray-400 text-lg mb-2">🔌</div>
                <p className="text-gray-600 mb-4">
                  {searchQuery || selectedCategory !== 'all' 
                    ? 'No credentials match your filters' 
                    : 'No credentials found'
                  }
                </p>
                <Button onClick={() => setShowCreateModal(true)}>
                  Create your first credential
                </Button>
              </div>
            ) : (
              <div className="space-y-3">
                {filteredCredentials.map((credential) => {
                  const integration = getIntegrationInfo(credential.appType);
                  const isTesting = testingCredentials.has(credential.id);
                  
                  return (
                    <div
                      key={credential.id}
                      className="border rounded-lg p-4 hover:bg-gray-50 transition-colors"
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div 
                            className="w-10 h-10 rounded-lg flex items-center justify-center text-white text-lg"
                            style={{ backgroundColor: integration.color }}
                          >
                            {integration.icon}
                          </div>
                          
                          <div>
                            <div className="flex items-center gap-2">
                              <h3 className="font-medium">{credential.name}</h3>
                              {getStatusBadge(credential)}
                            </div>
                            <p className="text-sm text-gray-600">
                              {integration.name}
                              {credential.lastUsedAt && (
                                <span className="ml-2">
                                  • Last used {new Date(credential.lastUsedAt).toLocaleDateString()}
                                </span>
                              )}
                            </p>
                          </div>
                        </div>

                        <div className="flex items-center gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleTestCredential(credential)}
                            disabled={isTesting}
                            className="flex items-center gap-1"
                          >
                            {isTesting ? (
                              <Loader2 className="w-3 h-3 animate-spin" />
                            ) : (
                              <TestTube className="w-3 h-3" />
                            )}
                            Test
                          </Button>

                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setEditingCredential(credential)}
                            className="flex items-center gap-1"
                          >
                            <Edit className="w-3 h-3" />
                            Edit
                          </Button>

                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setDeletingCredential(credential)}
                            className="flex items-center gap-1 text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="w-3 h-3" />
                            Delete
                          </Button>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>

      {/* Create Credential Modal */}
      <CreateCredentialModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        teamId={teamId}
        onSuccess={refresh}
      />

      {/* Edit Credential Modal */}
      {editingCredential && (
        <EditCredentialModal
          isOpen={!!editingCredential}
          onClose={() => setEditingCredential(null)}
          credential={editingCredential}
          teamId={teamId}
          onSuccess={refresh}
        />
      )}

      {/* Delete Confirmation */}
      <ConfirmDialog
        isOpen={!!deletingCredential}
        onClose={() => setDeletingCredential(null)}
        onConfirm={handleDeleteCredential}
        title="Delete Credential"
        description={`Are you sure you want to delete "${deletingCredential?.name}"? This action cannot be undone.`}
        confirmText="Delete"
        confirmVariant="destructive"
      />
    </>
  );
}
