import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { CredentialSelector } from '../credential-selector'
import { useCredentials } from '@/hooks/use-credentials'
import { useIntegrations } from '@/hooks/use-integrations'

// Mock hooks
jest.mock('@/hooks/use-credentials')
jest.mock('@/hooks/use-integrations')

const mockUseCredentials = useCredentials as jest.MockedFunction<typeof useCredentials>
const mockUseIntegrations = useIntegrations as jest.MockedFunction<typeof useIntegrations>

describe('CredentialSelector', () => {
  const defaultProps = {
    teamId: 'team-123',
    onChange: jest.fn(),
  }

  const mockCredentials = [
    {
      id: 'cred-1',
      name: 'OpenAI Production',
      appType: 'openai' as const,
      isActive: true,
      lastUsedAt: new Date('2024-01-01'),
      usageCount: 5,
      teamId: 'team-123',
      encryptedCredentials: 'encrypted',
      createdAt: new Date(),
      updatedAt: new Date(),
      createdBy: 'user-1'
    },
    {
      id: 'cred-2',
      name: 'Groq Development',
      appType: 'groq' as const,
      isActive: true,
      lastUsedAt: new Date('2024-01-02'),
      usageCount: 2,
      teamId: 'team-123',
      encryptedCredentials: 'encrypted',
      createdAt: new Date(),
      updatedAt: new Date(),
      createdBy: 'user-1'
    }
  ]

  const mockIntegrations = [
    {
      id: 'openai',
      name: 'OpenAI',
      icon: '🤖',
      color: '#10A37F',
      category: 'ai_services' as const
    },
    {
      id: 'groq',
      name: 'Groq',
      icon: '⚡',
      color: '#F55036',
      category: 'ai_services' as const
    }
  ]

  beforeEach(() => {
    mockUseCredentials.mockReturnValue({
      credentials: mockCredentials,
      loading: false,
      error: null,
      total: 2,
      loadCredentials: jest.fn(),
      createCredential: jest.fn(),
      updateCredential: jest.fn(),
      deleteCredential: jest.fn(),
      testCredential: jest.fn(),
      testNewCredentials: jest.fn(),
      getCredential: jest.fn(),
      getCredentialsByAppType: jest.fn((appType) => 
        mockCredentials.filter(c => c.appType === appType)
      ),
      refresh: jest.fn()
    })

    mockUseIntegrations.mockReturnValue({
      integrations: mockIntegrations,
      loading: false,
      error: null,
      categories: ['ai_services'],
      loadIntegrations: jest.fn(),
      getIntegration: jest.fn((appType) => 
        mockIntegrations.find(i => i.id === appType)
      ),
      getIntegrationsByCategory: jest.fn(),
      getAIServices: jest.fn(),
      getBusinessApps: jest.fn(),
      searchIntegrations: jest.fn(),
      refresh: jest.fn()
    })
  })

  afterEach(() => {
    jest.clearAllMocks()
  })

  describe('rendering', () => {
    it('should render credential selector with placeholder', () => {
      render(<CredentialSelector {...defaultProps} />)
      
      expect(screen.getByText('Select credential...')).toBeInTheDocument()
    })

    it('should render custom placeholder', () => {
      render(
        <CredentialSelector 
          {...defaultProps} 
          placeholder="Choose API credential..." 
        />
      )
      
      expect(screen.getByText('Choose API credential...')).toBeInTheDocument()
    })

    it('should show loading state', () => {
      mockUseCredentials.mockReturnValue({
        ...mockUseCredentials(),
        loading: true
      })

      render(<CredentialSelector {...defaultProps} />)
      
      expect(screen.getByText('Loading...')).toBeInTheDocument()
    })

    it('should be disabled when disabled prop is true', () => {
      render(<CredentialSelector {...defaultProps} disabled={true} />)
      
      const trigger = screen.getByRole('combobox')
      expect(trigger).toBeDisabled()
    })
  })

  describe('credential filtering', () => {
    it('should show all active credentials when no appType specified', async () => {
      const user = userEvent.setup()
      render(<CredentialSelector {...defaultProps} />)
      
      await user.click(screen.getByRole('combobox'))
      
      expect(screen.getByText('OpenAI Production')).toBeInTheDocument()
      expect(screen.getByText('Groq Development')).toBeInTheDocument()
    })

    it('should filter credentials by appType', async () => {
      const user = userEvent.setup()
      render(<CredentialSelector {...defaultProps} appType="openai" />)
      
      await user.click(screen.getByRole('combobox'))
      
      expect(screen.getByText('OpenAI Production')).toBeInTheDocument()
      expect(screen.queryByText('Groq Development')).not.toBeInTheDocument()
    })

    it('should show "No credential" option', async () => {
      const user = userEvent.setup()
      render(<CredentialSelector {...defaultProps} />)
      
      await user.click(screen.getByRole('combobox'))
      
      expect(screen.getByText('No credential')).toBeInTheDocument()
    })
  })

  describe('credential selection', () => {
    it('should call onChange when credential is selected', async () => {
      const user = userEvent.setup()
      const onChange = jest.fn()
      
      render(<CredentialSelector {...defaultProps} onChange={onChange} />)
      
      await user.click(screen.getByRole('combobox'))
      await user.click(screen.getByText('OpenAI Production'))
      
      expect(onChange).toHaveBeenCalledWith('cred-1')
    })

    it('should call onChange with undefined when "No credential" is selected', async () => {
      const user = userEvent.setup()
      const onChange = jest.fn()
      
      render(<CredentialSelector {...defaultProps} onChange={onChange} />)
      
      await user.click(screen.getByRole('combobox'))
      await user.click(screen.getByText('No credential'))
      
      expect(onChange).toHaveBeenCalledWith(undefined)
    })

    it('should show selected credential info', () => {
      render(<CredentialSelector {...defaultProps} value="cred-1" />)
      
      expect(screen.getByText('OpenAI')).toBeInTheDocument()
      expect(screen.getByText(/Last used/)).toBeInTheDocument()
      expect(screen.getByText(/Used 5 times/)).toBeInTheDocument()
    })
  })

  describe('add credential functionality', () => {
    it('should show add button when allowCreate is true', () => {
      render(<CredentialSelector {...defaultProps} allowCreate={true} />)
      
      expect(screen.getByText('Add')).toBeInTheDocument()
    })

    it('should not show add button when allowCreate is false', () => {
      render(<CredentialSelector {...defaultProps} allowCreate={false} />)
      
      expect(screen.queryByText('Add')).not.toBeInTheDocument()
    })

    it('should open create modal when add button is clicked', async () => {
      const user = userEvent.setup()
      render(<CredentialSelector {...defaultProps} allowCreate={true} />)
      
      await user.click(screen.getByText('Add'))
      
      // Modal should be rendered (we'd need to mock the modal component)
      // This test would be more complete with proper modal mocking
    })
  })

  describe('test credential functionality', () => {
    it('should show test button when allowTest is true and credential is selected', () => {
      render(
        <CredentialSelector 
          {...defaultProps} 
          value="cred-1" 
          allowTest={true} 
        />
      )
      
      expect(screen.getByText('Test')).toBeInTheDocument()
    })

    it('should not show test button when no credential is selected', () => {
      render(<CredentialSelector {...defaultProps} allowTest={true} />)
      
      expect(screen.queryByText('Test')).not.toBeInTheDocument()
    })

    it('should call testCredential when test button is clicked', async () => {
      const user = userEvent.setup()
      const testCredential = jest.fn()
      
      mockUseCredentials.mockReturnValue({
        ...mockUseCredentials(),
        testCredential
      })

      render(
        <CredentialSelector 
          {...defaultProps} 
          value="cred-1" 
          allowTest={true} 
        />
      )
      
      await user.click(screen.getByText('Test'))
      
      expect(testCredential).toHaveBeenCalledWith('cred-1')
    })

    it('should show loading state when testing credential', async () => {
      const user = userEvent.setup()
      const testCredential = jest.fn(() => new Promise(resolve => setTimeout(resolve, 100)))
      
      mockUseCredentials.mockReturnValue({
        ...mockUseCredentials(),
        testCredential
      })

      render(
        <CredentialSelector 
          {...defaultProps} 
          value="cred-1" 
          allowTest={true} 
        />
      )
      
      await user.click(screen.getByText('Test'))
      
      // Should show loading spinner
      expect(screen.getByTestId('loading-spinner')).toBeInTheDocument()
    })
  })

  describe('empty state', () => {
    it('should show empty state when no credentials available', async () => {
      const user = userEvent.setup()
      
      mockUseCredentials.mockReturnValue({
        ...mockUseCredentials(),
        credentials: []
      })

      render(<CredentialSelector {...defaultProps} allowCreate={true} />)
      
      await user.click(screen.getByRole('combobox'))
      
      expect(screen.getByText('No credentials available')).toBeInTheDocument()
      expect(screen.getByText('Create Credential')).toBeInTheDocument()
    })

    it('should show create link in description when no credentials and allowCreate is true', () => {
      mockUseCredentials.mockReturnValue({
        ...mockUseCredentials(),
        credentials: []
      })

      render(<CredentialSelector {...defaultProps} allowCreate={true} />)
      
      expect(screen.getByText(/Create one now/)).toBeInTheDocument()
    })
  })

  describe('error handling', () => {
    it('should handle credential loading errors gracefully', () => {
      mockUseCredentials.mockReturnValue({
        ...mockUseCredentials(),
        error: 'Failed to load credentials'
      })

      render(<CredentialSelector {...defaultProps} />)
      
      // Component should still render without crashing
      expect(screen.getByRole('combobox')).toBeInTheDocument()
    })
  })
})
