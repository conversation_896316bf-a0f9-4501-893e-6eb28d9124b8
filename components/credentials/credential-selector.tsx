'use client';

import React, { useState } from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Plus, TestTube, Loader2, CheckCircle, XCircle, Settings } from 'lucide-react';
import { useCredentials } from '@/hooks/use-credentials';
import { useIntegrations } from '@/hooks/use-integrations';
import { AppType, Credential } from '@/lib/integrations/types/integration-types';
import { CreateCredentialModal } from './create-credential-modal';

interface CredentialSelectorProps {
  teamId: string;
  appType?: AppType;
  value?: string;
  onChange?: (credentialId: string | undefined) => void;
  placeholder?: string;
  allowCreate?: boolean;
  allowTest?: boolean;
  className?: string;
  disabled?: boolean;
}

export function CredentialSelector({
  teamId,
  appType,
  value,
  onChange,
  placeholder = "Select credential...",
  allowCreate = true,
  allowTest = true,
  className,
  disabled = false
}: CredentialSelectorProps) {
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [testingCredential, setTestingCredential] = useState<string | null>(null);

  const { 
    credentials, 
    loading, 
    testCredential,
    getCredentialsByAppType,
    refresh
  } = useCredentials({ teamId, appType });

  const { getIntegration } = useIntegrations();

  // Filter credentials by app type if specified
  const availableCredentials = appType 
    ? getCredentialsByAppType(appType)
    : credentials.filter(cred => cred.isActive);

  // Get selected credential
  const selectedCredential = value ? credentials.find(cred => cred.id === value) : undefined;

  // Handle test credential
  const handleTestCredential = async (credentialId: string) => {
    setTestingCredential(credentialId);
    try {
      await testCredential(credentialId);
    } finally {
      setTestingCredential(null);
    }
  };

  // Handle create success
  const handleCreateSuccess = () => {
    refresh();
    setShowCreateModal(false);
  };

  // Get integration info
  const getIntegrationInfo = (credential: Credential) => {
    const integration = getIntegration(credential.appType);
    return {
      name: integration?.name || credential.appType,
      icon: integration?.icon || '🔌',
      color: integration?.color || '#6B7280'
    };
  };

  return (
    <>
      <div className={`space-y-2 ${className}`}>
        <div className="flex items-center gap-2">
          <div className="flex-1">
            <Select
              value={value || ''}
              onValueChange={(val) => onChange?.(val || undefined)}
              disabled={disabled || loading}
            >
              <SelectTrigger>
                <SelectValue placeholder={loading ? "Loading..." : placeholder} />
              </SelectTrigger>
              <SelectContent>
                {availableCredentials.length === 0 ? (
                  <div className="p-4 text-center text-gray-500">
                    <p className="text-sm">No credentials available</p>
                    {allowCreate && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setShowCreateModal(true)}
                        className="mt-2"
                      >
                        Create Credential
                      </Button>
                    )}
                  </div>
                ) : (
                  <>
                    <SelectItem value="">
                      <span className="text-gray-500">No credential</span>
                    </SelectItem>
                    {availableCredentials.map((credential) => {
                      const integration = getIntegrationInfo(credential);
                      const isTesting = testingCredential === credential.id;
                      
                      return (
                        <SelectItem key={credential.id} value={credential.id}>
                          <div className="flex items-center gap-2 w-full">
                            <div 
                              className="w-4 h-4 rounded flex items-center justify-center text-white text-xs"
                              style={{ backgroundColor: integration.color }}
                            >
                              {integration.icon}
                            </div>
                            <span className="flex-1">{credential.name}</span>
                            {credential.lastUsedAt && (
                              <Badge variant="outline" className="text-xs">
                                Active
                              </Badge>
                            )}
                          </div>
                        </SelectItem>
                      );
                    })}
                  </>
                )}
              </SelectContent>
            </Select>
          </div>

          {allowCreate && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowCreateModal(true)}
              disabled={disabled}
              className="flex items-center gap-1"
            >
              <Plus className="w-3 h-3" />
              Add
            </Button>
          )}

          {allowTest && selectedCredential && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleTestCredential(selectedCredential.id)}
              disabled={disabled || testingCredential === selectedCredential.id}
              className="flex items-center gap-1"
            >
              {testingCredential === selectedCredential.id ? (
                <Loader2 className="w-3 h-3 animate-spin" />
              ) : (
                <TestTube className="w-3 h-3" />
              )}
              Test
            </Button>
          )}
        </div>

        {/* Selected Credential Info */}
        {selectedCredential && (
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <div className="flex items-center gap-1">
              <div 
                className="w-3 h-3 rounded flex items-center justify-center text-white text-xs"
                style={{ backgroundColor: getIntegrationInfo(selectedCredential).color }}
              >
                {getIntegrationInfo(selectedCredential).icon}
              </div>
              <span>{getIntegrationInfo(selectedCredential).name}</span>
            </div>
            
            {selectedCredential.lastUsedAt && (
              <span>
                • Last used {new Date(selectedCredential.lastUsedAt).toLocaleDateString()}
              </span>
            )}
            
            {selectedCredential.usageCount > 0 && (
              <span>
                • Used {selectedCredential.usageCount} times
              </span>
            )}
          </div>
        )}

        {/* No Credentials Message */}
        {!loading && availableCredentials.length === 0 && allowCreate && (
          <div className="text-sm text-gray-500">
            No credentials available. 
            <Button
              variant="link"
              size="sm"
              onClick={() => setShowCreateModal(true)}
              className="p-0 h-auto ml-1"
            >
              Create one now
            </Button>
          </div>
        )}
      </div>

      {/* Create Credential Modal */}
      <CreateCredentialModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        teamId={teamId}
        onSuccess={handleCreateSuccess}
      />
    </>
  );
}
