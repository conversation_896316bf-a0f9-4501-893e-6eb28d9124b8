import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import { LogOut, MoreVertical, Settings, Share2 } from "lucide-react";
import { Team } from "@/types/types";
import { useSession } from "next-auth/react";
import { DangerZone } from "../dangerzone/delete-team";
import Link from "next/link";

export const TeamActionsMenu = ({
  team,
  onDelete,
  isDeleting,
  onLeave,
  onShare,
  onSettings,
}: {
  team: Team;
  isDeleting?: boolean;
  onDelete: () => Promise<void>;
  onLeave: () => void;
  onShare: () => void;
  onSettings: () => void;
}) => {
  const { data } = useSession();
  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className="cursor-pointer rounded-full h-8 w-8 p-0 text-gray-400 hover:text-gray-800 dark:hover:text-gray-200"
        >
          <MoreVertical className="h-4 w-4" />
          <span className="sr-only">Team actions</span>
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-48 p-1" align="end">
        <div className="space-y-1">
          {/* Mobile/Tablet - Show as link */}
          <Link
            href={`/dashboard/team/${team.url}/settings`}
            className="md:hidden cursor-pointer w-full flex items-center gap-1 px-3 py-1.5 text-sm hover:bg-gray-100 dark:hover:bg-gray-700 rounded"
          >
            <Settings className="h-4 w-4 text-gray-500 dark:text-gray-400" />
            <span>Team Settings</span>
          </Link>

          {/* Desktop - Show as button */}
          <button
            onClick={onSettings}
            className="hidden md:flex cursor-pointer w-full items-center gap-1 px-3 py-1.5 text-sm hover:bg-gray-100 dark:hover:bg-gray-700 rounded"
          >
            <Settings className="h-4 w-4 text-gray-500 dark:text-gray-400" />
            <span>Team Settings</span>
          </button>

          <button
            onClick={onShare}
            className="cursor-pointer w-full flex items-center gap-1 px-3 py-1.5 text-sm hover:bg-gray-100 dark:hover:bg-gray-700 rounded"
          >
            <Share2 className="h-4 w-4 text-gray-500 dark:text-gray-400" />
            <span>Share Team</span>
          </button>
          {team.ownerId?.toString() === data?.user.id ? (
            <DangerZone
              team={team}
              onDelete={onDelete}
              isDeleting={isDeleting}
            />
          ) : (
            <button
              onClick={onLeave}
              className="cursor-pointer w-full flex items-center gap-1 px-3 py-1.5 text-sm text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 rounded"
            >
              <LogOut className="h-4 w-4" />
              <span>Leave Team</span>
            </button>
          )}
        </div>
      </PopoverContent>
    </Popover>
  );
};
