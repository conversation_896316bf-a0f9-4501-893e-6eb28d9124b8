import Link from "next/link";
import { usePathname } from "next/navigation";

export const TeamTabs = ({ teamUrl }: { teamUrl: string }) => {
  const pathname = usePathname();
  const tabs = [
    { name: "General", href: `/dashboard/${teamUrl}` },
    { name: "Members", href: `/dashboard/${teamUrl}/members` },
    { name: "Plans", href: `/dashboard/${teamUrl}/plans` },
    { name: "<PERSON><PERSON>", href: `/dashboard/${teamUrl}/billing` },
    { name: "API Keys", href: `/dashboard/${teamUrl}/api-keys` },
    { name: "OpenAI", href: `/dashboard/${teamUrl}/openai` },
  ];

  return (
    <div className="border-b border-gray-200 dark:border-gray-700 mb-6">
      <nav className="flex overflow-x-auto scrollbar-hide -mb-px">
        {tabs.map((tab) => (
          <Link
            key={tab.name}
            href={tab.href}
            className={`flex-shrink-0 whitespace-nowrap px-4 py-4 border-b-2 font-medium text-sm ${
              pathname === tab.href
                ? "border-emerald-500 text-emerald-600 dark:text-emerald-400"
                : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300"
            }`}
          >
            {tab.name}
          </Link>
        ))}
      </nav>
    </div>
  );
};