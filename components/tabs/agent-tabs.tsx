"use client";
import React from "react";
import { Activity, Link2, MessageSquare, Plug, Settings } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import Link from "next/link";
import { useParams, usePathname } from "next/navigation";
import { cn } from "@/lib/utils";

export default function AgentTabs() {
  const params = useParams();
  const pathname = usePathname();
  const { slug: teamUrl, id: agentId } = params;

  const getActiveTab = () => {
    const segments = pathname.split("/");
    return segments[segments.length - 1] || "playground";
  };

  const tabs = [
    {
      value: "playground",
      icon: <MessageSquare className="h-5 w-5" />,
      label: "Playground",
      href: `/dashboard/team/${teamUrl}/agent/${agentId}/playground`,
    },
    {
      value: "activity",
      icon: <Activity className="h-5 w-5" />,
      label: "Activity",
      href: `/dashboard/team/${teamUrl}/agent/${agentId}/activity`,
    },
    {
      value: "sources",
      icon: <Link2 className="h-5 w-5" />,
      label: "Sources",
      href: `/dashboard/team/${teamUrl}/agent/${agentId}/sources`,
    },
    {
      value: "connect",
      icon: <Plug className="h-5 w-5" />,
      label: "Connect",
      href: `/dashboard/team/${teamUrl}/agent/${agentId}/connect`,
    },
    {
      value: "settings",
      icon: <Settings className="h-5 w-5" />,
      label: "Settings",
      href: `/dashboard/team/${teamUrl}/agent/${agentId}/settings`,
    },
  ];

  const activeTab = getActiveTab();

  return (
    <div className="mb-8 w-full">
      <div className="relative">
        <div className="flex items-center justify-between bg-gray-100 dark:bg-gray-800 rounded-lg p-1">
          {tabs.map((tab) => {
            const isActive = activeTab === tab.value;
            return (
              <motion.div
                key={tab.value}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                transition={{ type: "spring", stiffness: 400, damping: 20 }}
                className="flex-1 text-center"
              >
                <Link
                  href={tab.href}
                  className={cn(
                    "relative flex flex-col items-center py-3 px-2 rounded-md transition-colors",
                    isActive
                      ? "text-emerald-600 dark:text-emerald-400"
                      : "text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                  )}
                >
                  <div
                    className={cn(
                      "p-2 mb-1 rounded-lg transition-all",
                      isActive
                        ? "bg-emerald-100 dark:bg-emerald-900/50"
                        : "bg-white dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600"
                    )}
                  >
                    <AnimatePresence mode="wait">
                      <motion.div
                        key={isActive ? "active" : "inactive"}
                        initial={{ scale: 0.9, opacity: 0.8 }}
                        animate={{ scale: 1, opacity: 1 }}
                        transition={{ type: "spring", stiffness: 500 }}
                      >
                        {tab.icon}
                      </motion.div>
                    </AnimatePresence>
                  </div>
                  <span className="text-sm font-medium">{tab.label}</span>

                  {isActive && (
                    <motion.div
                      className="absolute -bottom-1 left-0 right-0 h-1 bg-emerald-500 dark:bg-emerald-400 rounded-t-full"
                      layoutId="activeTabUnderline"
                      transition={{
                        type: "spring",
                        stiffness: 300,
                        damping: 30,
                      }}
                    />
                  )}
                </Link>
              </motion.div>
            );
          })}
        </div>
      </div>
    </div>
  );
}
