"use client";

import React from "react";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";

interface GeneralTabProps {
  data: any;
  onChange: (field: string, value: any) => void;
}

export const GeneralTab = ({ data, onChange }: GeneralTabProps) => {
  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          Basic Information
        </h3>
        
        <div className="space-y-4">
          <div>
            <Label htmlFor="name">Agent Name</Label>
            <Input
              id="name"
              value={data.name || ""}
              onChange={(e) => onChange("name", e.target.value)}
              placeholder="Enter agent name"
              className="mt-1"
            />
          </div>

          <div>
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={data.description || ""}
              onChange={(e) => onChange("description", e.target.value)}
              placeholder="Describe what this agent does..."
              rows={3}
              className="mt-1"
            />
          </div>

          <div>
            <Label htmlFor="displayName">Display Name</Label>
            <Input
              id="displayName"
              value={data.displayName || ""}
              onChange={(e) => onChange("displayName", e.target.value)}
              placeholder="Name shown to users (optional)"
              className="mt-1"
            />
          </div>
        </div>
      </div>

      <div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          Behavior Settings
        </h3>
        
        <div className="space-y-4">
          <div>
            <Label htmlFor="language">Language</Label>
            <select
              id="language"
              value={data.language || "en"}
              onChange={(e) => onChange("language", e.target.value)}
              className="mt-1 block w-full rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 px-3 py-2 text-sm focus:border-emerald-500 focus:outline-none focus:ring-1 focus:ring-emerald-500"
            >
              <option value="en">English</option>
              <option value="es">Spanish</option>
              <option value="fr">French</option>
              <option value="de">German</option>
              <option value="it">Italian</option>
              <option value="pt">Portuguese</option>
              <option value="zh">Chinese</option>
              <option value="ja">Japanese</option>
              <option value="ko">Korean</option>
            </select>
          </div>

          <div>
            <Label htmlFor="initialMessages">Initial Messages</Label>
            <Textarea
              id="initialMessages"
              value={data.initialMessages || ""}
              onChange={(e) => onChange("initialMessages", e.target.value)}
              placeholder="Messages shown when conversation starts (one per line)"
              rows={3}
              className="mt-1"
            />
          </div>

          <div>
            <Label htmlFor="suggestedMessages">Suggested Messages</Label>
            <Textarea
              id="suggestedMessages"
              value={data.suggestedMessages || ""}
              onChange={(e) => onChange("suggestedMessages", e.target.value)}
              placeholder="Quick reply suggestions (one per line)"
              rows={3}
              className="mt-1"
            />
          </div>

          <div>
            <Label htmlFor="messagePlaceholder">Message Placeholder</Label>
            <Input
              id="messagePlaceholder"
              value={data.messagePlaceholder || ""}
              onChange={(e) => onChange("messagePlaceholder", e.target.value)}
              placeholder="Placeholder text for message input"
              className="mt-1"
            />
          </div>

          <div>
            <Label htmlFor="footer">Footer Text</Label>
            <Input
              id="footer"
              value={data.footer || ""}
              onChange={(e) => onChange("footer", e.target.value)}
              placeholder="Text shown at bottom of chat"
              className="mt-1"
            />
          </div>
        </div>
      </div>

      <div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          Status & Visibility
        </h3>
        
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="isActive">Active</Label>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Whether this agent is active and can be used
              </p>
            </div>
            <Switch
              id="isActive"
              checked={data.isActive ?? true}
              onCheckedChange={(checked) => onChange("isActive", checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="isPublic">Public</Label>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Allow public access to this agent
              </p>
            </div>
            <Switch
              id="isPublic"
              checked={data.isPublic ?? false}
              onCheckedChange={(checked) => onChange("isPublic", checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="isTrained">Trained</Label>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Whether this agent has been trained with custom data
              </p>
            </div>
            <Switch
              id="isTrained"
              checked={data.isTrained ?? false}
              onCheckedChange={(checked) => onChange("isTrained", checked)}
            />
          </div>
        </div>
      </div>

      <div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          Capabilities
        </h3>
        
        <div>
          <Label>Agent Capabilities</Label>
          <p className="text-sm text-gray-500 dark:text-gray-400 mb-3">
            Select the capabilities this agent should have
          </p>
          
          <div className="grid grid-cols-2 gap-3">
            {[
              'conversation',
              'analysis',
              'research',
              'writing',
              'coding',
              'math',
              'translation',
              'summarization'
            ].map((capability) => (
              <label
                key={capability}
                className="flex items-center space-x-2 p-3 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer"
              >
                <input
                  type="checkbox"
                  checked={(data.capabilities || []).includes(capability)}
                  onChange={(e) => {
                    const capabilities = data.capabilities || [];
                    if (e.target.checked) {
                      onChange("capabilities", [...capabilities, capability]);
                    } else {
                      onChange("capabilities", capabilities.filter((c: string) => c !== capability));
                    }
                  }}
                  className="rounded border-gray-300 text-emerald-600 focus:ring-emerald-500"
                />
                <span className="text-sm font-medium text-gray-900 dark:text-white capitalize">
                  {capability}
                </span>
              </label>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};
