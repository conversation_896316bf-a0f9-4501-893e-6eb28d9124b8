"use client";

import React from "react";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";

interface LimitsTabProps {
  data: any;
  onChange: (field: string, value: any) => void;
}

export const LimitsTab = ({ data, onChange }: LimitsTabProps) => {
  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          Rate Limiting
        </h3>
        
        <div className="space-y-4">
          <div>
            <Label htmlFor="ipLimit">IP Limit</Label>
            <Input
              id="ipLimit"
              type="number"
              value={data.ipLimit || 0}
              onChange={(e) => onChange("ipLimit", parseInt(e.target.value) || 0)}
              min="0"
              className="mt-1"
            />
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
              Maximum requests per IP address (0 = unlimited)
            </p>
          </div>

          <div>
            <Label htmlFor="ipLimitTimeframe">Timeframe (minutes)</Label>
            <Input
              id="ipLimitTimeframe"
              type="number"
              value={data.ipLimitTimeframe || 60}
              onChange={(e) => onChange("ipLimitTimeframe", parseInt(e.target.value) || 60)}
              min="1"
              className="mt-1"
            />
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
              Time window for rate limiting
            </p>
          </div>

          <div>
            <Label htmlFor="ipLimitMessage">Rate Limit Message</Label>
            <Textarea
              id="ipLimitMessage"
              value={data.ipLimitMessage || ""}
              onChange={(e) => onChange("ipLimitMessage", e.target.value)}
              placeholder="Message shown when rate limit is exceeded"
              rows={2}
              className="mt-1"
            />
          </div>
        </div>
      </div>
    </div>
  );
};
