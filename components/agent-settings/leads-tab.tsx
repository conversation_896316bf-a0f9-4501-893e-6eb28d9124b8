"use client";

import React from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";

interface LeadsTabProps {
  data: any;
  onChange: (field: string, value: any) => void;
  onNestedChange: (section: string, field: string, value: any) => void;
}

export const LeadsTab = ({ data, onChange }: LeadsTabProps) => {
  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          Lead Generation
        </h3>
        
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="leadTitleEnabled">Enable Lead Collection</Label>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Show lead collection form to users
              </p>
            </div>
            <Switch
              id="leadTitleEnabled"
              checked={data.leadTitleEnabled ?? false}
              onCheckedChange={(checked) => onChange("leadTitleEnabled", checked)}
            />
          </div>

          {data.leadTitleEnabled && (
            <>
              <div>
                <Label htmlFor="leadTitle">Form Title</Label>
                <Input
                  id="leadTitle"
                  value={data.leadTitle || ""}
                  onChange={(e) => onChange("leadTitle", e.target.value)}
                  placeholder="Contact Information"
                  className="mt-1"
                />
              </div>

              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="leadEmailEnabled">Collect Email</Label>
                  </div>
                  <Switch
                    id="leadEmailEnabled"
                    checked={data.leadEmailEnabled ?? false}
                    onCheckedChange={(checked) => onChange("leadEmailEnabled", checked)}
                  />
                </div>
                {data.leadEmailEnabled && (
                  <Input
                    value={data.leadEmail || ""}
                    onChange={(e) => onChange("leadEmail", e.target.value)}
                    placeholder="Email label"
                    className="ml-6"
                  />
                )}

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="leadNameEnabled">Collect Name</Label>
                  </div>
                  <Switch
                    id="leadNameEnabled"
                    checked={data.leadNameEnabled ?? false}
                    onCheckedChange={(checked) => onChange("leadNameEnabled", checked)}
                  />
                </div>
                {data.leadNameEnabled && (
                  <Input
                    value={data.leadName || ""}
                    onChange={(e) => onChange("leadName", e.target.value)}
                    placeholder="Name label"
                    className="ml-6"
                  />
                )}

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="leadPhoneEnabled">Collect Phone</Label>
                  </div>
                  <Switch
                    id="leadPhoneEnabled"
                    checked={data.leadPhoneEnabled ?? false}
                    onCheckedChange={(checked) => onChange("leadPhoneEnabled", checked)}
                  />
                </div>
                {data.leadPhoneEnabled && (
                  <Input
                    value={data.leadPhone || ""}
                    onChange={(e) => onChange("leadPhone", e.target.value)}
                    placeholder="Phone label"
                    className="ml-6"
                  />
                )}

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="leadMessageEnabled">Collect Message</Label>
                  </div>
                  <Switch
                    id="leadMessageEnabled"
                    checked={data.leadMessageEnabled ?? false}
                    onCheckedChange={(checked) => onChange("leadMessageEnabled", checked)}
                  />
                </div>
                {data.leadMessageEnabled && (
                  <Input
                    value={data.leadMessage || ""}
                    onChange={(e) => onChange("leadMessage", e.target.value)}
                    placeholder="Message label"
                    className="ml-6"
                  />
                )}
              </div>
            </>
          )}
        </div>
      </div>

      <div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          Notifications
        </h3>
        
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="leadsNotificationEmailEnabled">Lead Notifications</Label>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Get notified when new leads are collected
              </p>
            </div>
            <Switch
              id="leadsNotificationEmailEnabled"
              checked={data.leadsNotificationEmailEnabled ?? false}
              onCheckedChange={(checked) => onChange("leadsNotificationEmailEnabled", checked)}
            />
          </div>

          {data.leadsNotificationEmailEnabled && (
            <div>
              <Label htmlFor="leadsNotificationEmail">Notification Email</Label>
              <Input
                id="leadsNotificationEmail"
                type="email"
                value={data.leadsNotificationEmail || ""}
                onChange={(e) => onChange("leadsNotificationEmail", e.target.value)}
                placeholder="<EMAIL>"
                className="mt-1"
              />
            </div>
          )}

          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="conversationsNotificationEmailEnabled">Conversation Notifications</Label>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Get notified about new conversations
              </p>
            </div>
            <Switch
              id="conversationsNotificationEmailEnabled"
              checked={data.conversationsNotificationEmailEnabled ?? false}
              onCheckedChange={(checked) => onChange("conversationsNotificationEmailEnabled", checked)}
            />
          </div>

          {data.conversationsNotificationEmailEnabled && (
            <div>
              <Label htmlFor="conversationsNotificationEmail">Notification Email</Label>
              <Input
                id="conversationsNotificationEmail"
                type="email"
                value={data.conversationsNotificationEmail || ""}
                onChange={(e) => onChange("conversationsNotificationEmail", e.target.value)}
                placeholder="<EMAIL>"
                className="mt-1"
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
