"use client";

import React from "react";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";

interface SecurityTabProps {
  data: any;
  onChange: (field: string, value: any) => void;
}

export const SecurityTab = ({ data, onChange }: SecurityTabProps) => {
  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          Access Control
        </h3>
        
        <div className="space-y-4">
          <div>
            <Label htmlFor="allowedDomains">Allowed Domains</Label>
            <Textarea
              id="allowedDomains"
              value={(data.allowedDomains || []).join('\n')}
              onChange={(e) => onChange("allowedDomains", e.target.value.split('\n').filter(d => d.trim()))}
              placeholder="example.com&#10;subdomain.example.com"
              rows={3}
              className="mt-1"
            />
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
              One domain per line. Leave empty to allow all domains.
            </p>
          </div>
        </div>
      </div>

      <div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          Webhooks
        </h3>
        
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="webhooksEnabled">Enable Webhooks</Label>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Allow this agent to send webhook notifications
              </p>
            </div>
            <Switch
              id="webhooksEnabled"
              checked={data.webhooksEnabled ?? false}
              onCheckedChange={(checked) => onChange("webhooksEnabled", checked)}
            />
          </div>

          {data.webhooksEnabled && (
            <div>
              <Label htmlFor="webhooks">Webhook URLs</Label>
              <Textarea
                id="webhooks"
                value={data.webhooks || ""}
                onChange={(e) => onChange("webhooks", e.target.value)}
                placeholder="https://example.com/webhook&#10;https://api.example.com/notify"
                rows={3}
                className="mt-1"
              />
              <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                One URL per line
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
