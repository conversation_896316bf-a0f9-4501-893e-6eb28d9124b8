"use client";

import React from "react";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";

interface CustomDomainsTabProps {
  data: any;
  onChange: (field: string, value: any) => void;
}

export const CustomDomainsTab = ({ data, onChange }: CustomDomainsTabProps) => {
  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          Custom Domains
        </h3>
        
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="customDomainsEnabled">Enable Custom Domains</Label>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Allow this agent to be accessed via custom domains
              </p>
            </div>
            <Switch
              id="customDomainsEnabled"
              checked={data.customDomainsEnabled ?? false}
              onCheckedChange={(checked) => onChange("customDomainsEnabled", checked)}
            />
          </div>

          {data.customDomainsEnabled && (
            <div>
              <Label htmlFor="customDomains">Domain List</Label>
              <Textarea
                id="customDomains"
                value={data.customDomains || ""}
                onChange={(e) => onChange("customDomains", e.target.value)}
                placeholder="chat.example.com&#10;support.mycompany.com"
                rows={4}
                className="mt-1"
              />
              <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                One domain per line. Make sure to configure DNS records properly.
              </p>
            </div>
          )}
        </div>
      </div>

      <div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          Domain Configuration
        </h3>
        
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
          <h4 className="font-medium text-gray-900 dark:text-white mb-2">
            DNS Setup Instructions
          </h4>
          <div className="text-sm text-gray-600 dark:text-gray-400 space-y-2">
            <p>To use custom domains, you need to configure DNS records:</p>
            <ol className="list-decimal list-inside space-y-1 ml-4">
              <li>Add a CNAME record pointing to: <code className="bg-gray-200 dark:bg-gray-600 px-1 rounded">agents.chatzuri.com</code></li>
              <li>Or add an A record pointing to: <code className="bg-gray-200 dark:bg-gray-600 px-1 rounded">192.168.1.1</code></li>
              <li>Wait for DNS propagation (up to 24 hours)</li>
              <li>Test your domain to ensure it's working</li>
            </ol>
          </div>
        </div>
      </div>
    </div>
  );
};
