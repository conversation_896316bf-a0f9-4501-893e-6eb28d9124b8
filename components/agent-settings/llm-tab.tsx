"use client";

import React from "react";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

interface LLMTabProps {
  data: any;
  onChange: (field: string, value: any) => void;
}

export const LLMTab = ({ data, onChange }: LLMTabProps) => {
  const models = [
    { value: "gpt-4o", label: "GPT-4o (Latest)", description: "Most capable model" },
    { value: "gpt-4", label: "GPT-4", description: "High performance" },
    { value: "gpt-3.5-turbo", label: "GPT-3.5 Turbo", description: "Fast and efficient" },
    { value: "claude-3-sonnet", label: "Claude 3 Sonnet", description: "Balanced performance" },
    { value: "claude-3-haiku", label: "Claude 3 Haiku", description: "Fast responses" },
  ];

  const availableTools = [
    { id: "web_search", name: "Web Search", description: "Search the internet for information" },
    { id: "calculator", name: "<PERSON><PERSON><PERSON>", description: "Perform mathematical calculations" },
    { id: "file_reader", name: "File Reader", description: "Read and analyze uploaded files" },
    { id: "image_generator", name: "Image Generator", description: "Generate images from text" },
    { id: "code_analyzer", name: "Code Analyzer", description: "Analyze and review code" },
    { id: "documentation_generator", name: "Documentation Generator", description: "Generate documentation" },
    { id: "email_sender", name: "Email Sender", description: "Send emails" },
    { id: "calendar_manager", name: "Calendar Manager", description: "Manage calendar events" },
  ];

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          Model Configuration
        </h3>
        
        <div className="space-y-4">
          <div>
            <Label htmlFor="model">AI Model</Label>
            <Select value={data.model || ""} onValueChange={(value) => onChange("model", value)}>
              <SelectTrigger className="mt-1">
                <SelectValue placeholder="Select a model" />
              </SelectTrigger>
              <SelectContent>
                {models.map((model) => (
                  <SelectItem key={model.value} value={model.value}>
                    <div>
                      <div className="font-medium">{model.label}</div>
                      <div className="text-xs text-gray-500">{model.description}</div>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="temperature">
              Temperature: {data.temperature || 0.7}
            </Label>
            <p className="text-sm text-gray-500 dark:text-gray-400 mb-2">
              Controls randomness in responses (0 = focused, 2 = creative)
            </p>
            <input
              type="range"
              id="temperature"
              min="0"
              max="2"
              step="0.1"
              value={data.temperature || 0.7}
              onChange={(e) => onChange("temperature", parseFloat(e.target.value))}
              className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
            />
            <div className="flex justify-between text-xs text-gray-500 mt-1">
              <span>Focused</span>
              <span>Balanced</span>
              <span>Creative</span>
            </div>
          </div>

          <div>
            <Label htmlFor="maxTokens">Max Tokens</Label>
            <Input
              id="maxTokens"
              type="number"
              value={data.maxTokens || 4000}
              onChange={(e) => onChange("maxTokens", parseInt(e.target.value))}
              min="1"
              max="32000"
              className="mt-1"
            />
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
              Maximum number of tokens in the response
            </p>
          </div>

          <div>
            <Label htmlFor="topP">
              Top P: {data.topP || 1}
            </Label>
            <p className="text-sm text-gray-500 dark:text-gray-400 mb-2">
              Controls diversity via nucleus sampling
            </p>
            <input
              type="range"
              id="topP"
              min="0"
              max="1"
              step="0.1"
              value={data.topP || 1}
              onChange={(e) => onChange("topP", parseFloat(e.target.value))}
              className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
            />
          </div>

          <div>
            <Label htmlFor="frequencyPenalty">
              Frequency Penalty: {data.frequencyPenalty || 0}
            </Label>
            <p className="text-sm text-gray-500 dark:text-gray-400 mb-2">
              Reduces repetition of frequent tokens
            </p>
            <input
              type="range"
              id="frequencyPenalty"
              min="-2"
              max="2"
              step="0.1"
              value={data.frequencyPenalty || 0}
              onChange={(e) => onChange("frequencyPenalty", parseFloat(e.target.value))}
              className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
            />
          </div>

          <div>
            <Label htmlFor="presencePenalty">
              Presence Penalty: {data.presencePenalty || 0}
            </Label>
            <p className="text-sm text-gray-500 dark:text-gray-400 mb-2">
              Encourages talking about new topics
            </p>
            <input
              type="range"
              id="presencePenalty"
              min="-2"
              max="2"
              step="0.1"
              value={data.presencePenalty || 0}
              onChange={(e) => onChange("presencePenalty", parseFloat(e.target.value))}
              className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
            />
          </div>
        </div>
      </div>

      <div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          System Prompt
        </h3>
        
        <div>
          <Label htmlFor="systemPrompt">Instructions</Label>
          <Textarea
            id="systemPrompt"
            value={data.systemPrompt || ""}
            onChange={(e) => onChange("systemPrompt", e.target.value)}
            placeholder="You are a helpful assistant that..."
            rows={6}
            className="mt-1"
          />
          <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
            Define the agent's personality, role, and behavior guidelines
          </p>
        </div>
      </div>

      <div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          Available Tools
        </h3>
        
        <div>
          <Label>Tool Selection</Label>
          <p className="text-sm text-gray-500 dark:text-gray-400 mb-3">
            Choose which tools this agent can use
          </p>
          
          <div className="space-y-2">
            {availableTools.map((tool) => (
              <label
                key={tool.id}
                className="flex items-start space-x-3 p-3 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer"
              >
                <input
                  type="checkbox"
                  checked={(data.tools || []).includes(tool.id)}
                  onChange={(e) => {
                    const tools = data.tools || [];
                    if (e.target.checked) {
                      onChange("tools", [...tools, tool.id]);
                    } else {
                      onChange("tools", tools.filter((t: string) => t !== tool.id));
                    }
                  }}
                  className="mt-1 rounded border-gray-300 text-emerald-600 focus:ring-emerald-500"
                />
                <div className="flex-1">
                  <div className="text-sm font-medium text-gray-900 dark:text-white">
                    {tool.name}
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    {tool.description}
                  </div>
                </div>
              </label>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};
