"use client";

import React from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Upload, X } from "lucide-react";

interface IconTabProps {
  data: any;
  onChange: (field: string, value: any) => void;
}

export const IconTab = ({ data, onChange }: IconTabProps) => {
  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          Agent Icon
        </h3>
        
        <div className="space-y-4">
          <div>
            <Label htmlFor="iconPath">Icon URL</Label>
            <Input
              id="iconPath"
              value={data.iconPath || ""}
              onChange={(e) => onChange("iconPath", e.target.value)}
              placeholder="https://example.com/icon.png"
              className="mt-1"
            />
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
              URL to the agent's icon image
            </p>
          </div>

          <div>
            <Label>Upload Icon</Label>
            <div className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md dark:border-gray-600">
              <div className="space-y-1 text-center">
                <Upload className="mx-auto h-12 w-12 text-gray-400" />
                <div className="flex text-sm text-gray-600 dark:text-gray-400">
                  <Button
                    type="button"
                    variant="outline"
                    className="relative cursor-pointer"
                  >
                    Upload a file
                    <input
                      type="file"
                      className="sr-only"
                      accept="image/*"
                      onChange={(e) => {
                        // TODO: Implement file upload
                        console.log("File upload:", e.target.files?.[0]);
                      }}
                    />
                  </Button>
                  <p className="pl-1">or drag and drop</p>
                </div>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  PNG, JPG, GIF up to 2MB
                </p>
              </div>
            </div>
          </div>

          {data.iconPath && (
            <div className="flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <img
                src={data.iconPath}
                alt="Agent icon"
                className="w-12 h-12 rounded-lg object-cover"
                onError={(e) => {
                  (e.target as HTMLImageElement).style.display = 'none';
                }}
              />
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-900 dark:text-white">
                  Current Icon
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
                  {data.iconPath}
                </p>
              </div>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => onChange("iconPath", "")}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          )}
        </div>
      </div>

      <div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          Profile Picture
        </h3>
        
        <div className="space-y-4">
          <div>
            <Label htmlFor="profilePicturePath">Profile Picture URL</Label>
            <Input
              id="profilePicturePath"
              value={data.profilePicturePath || ""}
              onChange={(e) => onChange("profilePicturePath", e.target.value)}
              placeholder="https://example.com/profile.png"
              className="mt-1"
            />
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
              URL to the agent's profile picture
            </p>
          </div>

          {data.profilePicturePath && (
            <div className="flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <img
                src={data.profilePicturePath}
                alt="Agent profile"
                className="w-12 h-12 rounded-full object-cover"
                onError={(e) => {
                  (e.target as HTMLImageElement).style.display = 'none';
                }}
              />
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-900 dark:text-white">
                  Current Profile Picture
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
                  {data.profilePicturePath}
                </p>
              </div>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => onChange("profilePicturePath", "")}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
