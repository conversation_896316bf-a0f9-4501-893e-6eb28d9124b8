"use client";

import React, { useState, useEffect } from "react";
import { Plus, Workflow } from "lucide-react";
import { motion } from "framer-motion";
import { useTheme } from "next-themes";
import { WorkflowCard } from "@/components/cards/workflow-card";
import { CreateWorkflowModal } from "@/components/modals/create-workflow-modal";

interface WorkflowsSectionProps {
  teamId: string;
  teamUrl: string;
  title?: string;
  showCreateButton?: boolean;
  maxItems?: number;
  className?: string;
}

interface Workflow {
  _id: string;
  name: string;
  description?: string;
  status: "draft" | "active" | "inactive";
  version: string;
  nodes: any[];
  edges: any[];
  triggers: string[];
  createdAt: Date;
  updatedAt: Date;
  lastExecuted?: Date;
  executionCount: number;
}

export const WorkflowsSection = ({
  teamId,
  teamUrl,
  title = "Workflows",
  showCreateButton = true,
  maxItems,
  className = "",
}: WorkflowsSectionProps) => {
  const [workflows, setWorkflows] = useState<Workflow[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isCreatingWorkflow, setIsCreatingWorkflow] = useState(false);
  const { resolvedTheme } = useTheme();

  // Fetch workflows from API
  const fetchWorkflows = async () => {
    if (!teamId) return;
    
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await fetch(`/api/workflows?teamId=${teamId}&limit=${maxItems || 50}`);
      const result = await response.json();
      
      if (!response.ok) {
        throw new Error(result.error || 'Failed to fetch workflows');
      }
      
      if (result.success && result.data) {
        // Transform the data to match our interface
        const transformedWorkflows = result.data.map((workflow: any) => ({
          ...workflow,
          nodes: Array.isArray(workflow.nodes) ? workflow.nodes.length : (workflow.nodes || 0), // Convert nodes array to count
          createdAt: new Date(workflow.createdAt),
          updatedAt: new Date(workflow.updatedAt),
          lastExecuted: workflow.lastExecuted ? new Date(workflow.lastExecuted) : undefined,
        }));

        setWorkflows(transformedWorkflows);
      } else {
        setWorkflows([]);
      }
    } catch (err) {
      console.error('Error fetching workflows:', err);
      setError(err instanceof Error ? err.message : 'Failed to load workflows');
      setWorkflows([]);
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch workflows on mount and when teamId changes
  useEffect(() => {
    fetchWorkflows();
  }, [teamId, maxItems]);

  // Handle workflow creation success
  const handleWorkflowCreated = () => {
    setIsCreatingWorkflow(false);
    fetchWorkflows(); // Refresh the list
  };

  const displayedWorkflows = maxItems ? workflows.slice(0, maxItems) : workflows;

  if (isLoading) {
    return (
      <div className={`${className}`}>
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            {title}
          </h2>
          {showCreateButton && (
            <div className="w-32 h-10 bg-gray-200 dark:bg-gray-700 rounded-md animate-pulse"></div>
          )}
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(3)].map((_, i) => (
            <div
              key={i}
              className="h-48 bg-gray-200 dark:bg-gray-700 rounded-xl animate-pulse"
            ></div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`${className}`}>
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            {title}
          </h2>
          {showCreateButton && (
            <button
              onClick={() => setIsCreatingWorkflow(true)}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-emerald-600 hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500 dark:bg-emerald-500 dark:hover:bg-emerald-600 transition-colors"
            >
              <Plus className="h-4 w-4 mr-2" />
              New Workflow
            </button>
          )}
        </div>
        <div className="text-center py-8 bg-red-50 dark:bg-red-900/20 rounded-xl border border-red-200 dark:border-red-800">
          <p className="text-red-600 dark:text-red-400 mb-4">{error}</p>
          <button
            onClick={fetchWorkflows}
            className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-md transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={`${className}`}>
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4 sm:mb-0">
          {title}
          <span className="ml-2 text-emerald-500 dark:text-emerald-400 bg-emerald-100 dark:bg-emerald-900/30 px-2 py-1 rounded-full text-sm">
            {workflows.length}
          </span>
        </h2>

        {showCreateButton && (
          <button
            onClick={() => setIsCreatingWorkflow(true)}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-emerald-600 hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500 dark:bg-emerald-500 dark:hover:bg-emerald-600 transition-colors"
          >
            <Plus className="h-4 w-4 mr-2" />
            New Workflow
          </button>
        )}
      </div>

      {/* Workflows Grid */}
      {displayedWorkflows.length > 0 ? (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
          {displayedWorkflows.map((workflow) => (
            <WorkflowCard
              key={workflow._id}
              workflow={workflow}
              teamUrl={teamUrl}
              theme={resolvedTheme || "light"}
            />
          ))}
        </div>
      ) : (
        <div className="text-center py-16 bg-white/50 dark:bg-gray-800/50 rounded-xl border border-dashed border-gray-300 dark:border-gray-600">
          <div className="mx-auto h-24 w-24 text-gray-400 dark:text-gray-500 mb-4">
            <Workflow className="h-full w-full" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-1">
            No workflows yet
          </h3>
          <p className="text-gray-500 dark:text-gray-400 mb-6">
            Get started by creating your first automated workflow.
          </p>
          {showCreateButton && (
            <button
              onClick={() => setIsCreatingWorkflow(true)}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-emerald-600 hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500 dark:bg-emerald-500 dark:hover:bg-emerald-600"
            >
              <Plus className="h-4 w-4 mr-2" />
              Create Workflow
            </button>
          )}
        </div>
      )}

      {/* Create Workflow Modal */}
      <CreateWorkflowModal
        isOpen={isCreatingWorkflow}
        onClose={() => setIsCreatingWorkflow(false)}
        teamId={teamId}
        teamUrl={teamUrl}
        onSuccess={handleWorkflowCreated}
      />
    </div>
  );
};
