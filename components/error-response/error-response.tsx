"use client";
import { itemVariants } from "@/variants/variants";
import { motion } from "framer-motion";
import { AlertTriangle, Info } from "lucide-react";
import React from "react";

export default function ErrorResponse() {
  return (
    <motion.section
      className="mb-8"
      variants={itemVariants}
      whileHover={{ scale: 1.005 }}
    >
      <div className="relative group">
        {/* Gradient glow effect */}
        <div className="absolute -inset-1 bg-gradient-to-r from-red-500/10 to-purple-600/10 dark:from-red-900/20 dark:to-purple-900/20 rounded-xl blur opacity-70 group-hover:opacity-100 transition duration-300"></div>

        {/* Content container */}
        <div className="relative p-6 rounded-xl bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border border-red-100/50 dark:border-red-900/30 shadow-sm hover:shadow-md transition-all">
          {/* Header with icon */}
          <div className="flex items-start mb-4">
            <div className="p-2 mr-3 rounded-full bg-red-100/50 dark:bg-red-900/20 text-red-600 dark:text-red-400">
              <AlertTriangle className="w-5 h-5" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                Error Handling
              </h2>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                How to handle API errors gracefully
              </p>
            </div>
          </div>

          {/* Content with animated elements */}
          <div className="space-y-4 lg:pl-14">
            <motion.div
              whileHover={{ x: 3 }}
              className="p-4 rounded-lg bg-red-50/50 dark:bg-red-900/10 border border-red-100 dark:border-red-900/20"
            >
              <p className="text-gray-700 dark:text-gray-300">
                If there are any errors during the API request, appropriate HTTP
                status codes will be returned along with error messages in the
                response body.
              </p>
            </motion.div>

            <motion.div
              whileHover={{ x: 3 }}
              transition={{ delay: 0.05 }}
              className="p-4 rounded-lg bg-emerald-50/50 dark:bg-emerald-900/10 border border-emerald-100 dark:border-emerald-900/20"
            >
              <p className="text-gray-700 dark:text-gray-300">
                That&apos;s it! You should now be able to create a chatbot using
                the create API.
              </p>
            </motion.div>
          </div>

          {/* Pro tip */}
          <div className="mt-6 flex items-start text-sm text-gray-500 dark:text-gray-400">
            <Info className="flex-shrink-0 w-4 h-4 mr-2 mt-0.5 text-gray-400 dark:text-gray-500" />
            <span>
              Remember to implement proper error logging and user notifications
              in your application
            </span>
          </div>

          {/* Status code examples */}
          <div className="mt-6 grid grid-cols-1 sm:grid-cols-3 gap-3">
            {[
              {
                code: "400",
                text: "Bad Request",
                color:
                  "bg-amber-100 dark:bg-amber-900/20 text-amber-800 dark:text-amber-200",
              },
              {
                code: "401",
                text: "Unauthorized",
                color:
                  "bg-red-100 dark:bg-red-900/20 text-red-800 dark:text-red-200",
              },
              {
                code: "500",
                text: "Server Error",
                color:
                  "bg-purple-100 dark:bg-purple-900/20 text-purple-800 dark:text-purple-200",
              },
            ].map((item, index) => (
              <motion.div
                key={index}
                whileHover={{ y: -2 }}
                className={`${item.color} p-3 rounded-lg text-center shadow-xs`}
              >
                <div className="font-mono font-bold text-lg">{item.code}</div>
                <div className="text-sm">{item.text}</div>
              </motion.div>
            ))}
          </div>
        </div>
      </div>
    </motion.section>
  );
}
