"use client";
import {
  Globe,
  BadgeCheck,
  Users,
  Bot,
  FileText,
  Link as LinkIcon,
  BarChart2,
  Code,
  Palette,
  Edit,
  Trash2,
  Check,
  Crown,
  ArrowRight,
  Sparkles,
  Zap,
} from "lucide-react";
import {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
  CardFooter,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import type { Plan } from "@/types/types";
import { motion } from "framer-motion";
import { useEffect, useState } from "react";

// Add this utility function for currency conversion
const convertCurrency = (
  amount: number,
  fromCurrency: "USD" | "KES",
  toCurrency: "USD" | "KES",
  exchangeRate: number
): number => {
  if (fromCurrency === toCurrency) return amount;
  if (fromCurrency === "USD" && toCurrency === "KES")
    return amount * exchangeRate;
  return amount / exchangeRate; // KES to USD
};

const PlanCard = ({
  plan,
  isAdmin = false,
  currency = "KES",
  billingInterval = "monthly",
  isUpdating = false,
  isDeleting = false,
  isLoading = false,
  onEdit,
  onDelete,
  onUpgrade,
  className = "",
}: {
  plan: Plan;
  isAdmin?: boolean;
  currency?: "KES" | "USD";
  billingInterval?: "monthly" | "yearly";
  isUpdating?: boolean;
  isDeleting?: boolean;
  isLoading?: boolean;
  onEdit: (plan: Plan) => void;
  onDelete: (id: string) => void;
  onUpgrade: (id: string) => void;
  className?: string;
}) => {
  const price =
    billingInterval === "monthly" ? plan.priceMonthly : plan.priceYearly;
  const pricePeriod = billingInterval === "monthly" ? "month" : "year";
  const currencySymbol = currency === "KES" ? "KES " : "$ ";
  const [exchangeRate, setExchangeRate] = useState<number>(130);
  //eslint-disable-next-line  @typescript-eslint/no-unused-vars
  const [isRateLoading, setIsRateLoading] = useState<boolean>(false);
  const features = [
    {
      icon: <Zap className="h-5 w-5" />,
      text: `${plan.monthlyMessageCredits.toLocaleString()} message credits/month`,
    },
    {
      icon: <Users className="h-5 w-5" />,
      text: `Up to ${plan.maxMembers} members`,
    },
    {
      icon: <Bot className="h-5 w-5" />,
      text: `Up to ${plan.maxChatbots} chatbots`,
    },
    {
      icon: <FileText className="h-5 w-5" />,
      text: `Up to ${(plan.maxTrainingChars / 1000000).toFixed(
        1
      )}M training chars/bot`,
    },
    {
      icon: <LinkIcon className="h-5 w-5" />,
      text: `Up to ${plan.maxTrainingLinks} links to train on`,
    },
    {
      icon: <Globe className="h-5 w-5" />,
      text: `Embed on Unlimited websites`,
    },
    {
      icon: <BarChart2 className="h-5 w-5" />,
      text: plan.hasBasicAnalytics ? "Analytics included" : "No analytics",
    },
    {
      icon: <Code className="h-5 w-5" />,
      text: plan.hasApiAccess ? "API Access" : "No API Access",
    },
    {
      icon: <Palette className="h-5 w-5" />,
      text: plan.hasCustomBranding ? "Custom Branding" : "No Custom Branding",
    },
    {
      icon: <Globe className="h-5 w-5" />,
      text: plan.hasCustomDomains ? "Custom Domains" : "No Custom Domains",
    },
  ];

  // Fetch exchange rate when currency changes
  useEffect(() => {
    const fetchExchangeRate = async () => {
      setIsRateLoading(true);
      try {
        // Using Frankfurter.app - a free API that doesn't require an API key
        const response = await fetch(
          "https://api.frankfurter.app/latest?from=USD&to=KES"
        );
        const data = await response.json();
        setExchangeRate(data.rates?.KES || 150); // Fallback to 150 if API fails
      } catch (error) {
        console.error("Failed to fetch exchange rate:", error);
        setExchangeRate(130); // Fallback rate
      } finally {
        setIsRateLoading(false);
      }
    };

    fetchExchangeRate();
  }, []);

  // Convert prices based on selected currency
  const convertedPriceMonthly = convertCurrency(
    plan.priceMonthly,
    "USD", // Assuming prices are stored in USD
    currency,
    exchangeRate
  );

  const convertedPriceYearly = convertCurrency(
    plan.priceYearly,
    "USD", // Assuming prices are stored in USD
    currency,
    exchangeRate
  );

  // Format price with proper decimals based on currency
  const formattedPrice = new Intl.NumberFormat(
    currency === "KES" ? "en-KE" : "en-US",
    {
      style: "decimal",
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }
  ).format(price);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      whileHover={{ scale: 1.02 }}
      className={`h-full ${className}`}
    >
      {/* Glowing border effect */}
      <div
        className={`relative h-full ${plan.isCurrent ? "glowing-card" : ""}`}
      >
        {/* Animated sparkles */}
        {plan.isDefault && (
          <>
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 8, repeat: Infinity, ease: "linear" }}
              className="absolute -top-2 -left-2 w-4 h-4"
            >
              <Sparkles className="h-4 w-4 text-amber-400 dark:text-amber-300 animate-pulse" />
            </motion.div>
            <motion.div
              animate={{ rotate: -360 }}
              transition={{ duration: 10, repeat: Infinity, ease: "linear" }}
              className="absolute -top-2 -right-2 w-4 h-4"
            >
              <Sparkles className="h-4 w-4 text-amber-400 dark:text-amber-300 animate-pulse" />
            </motion.div>
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 12, repeat: Infinity, ease: "linear" }}
              className="absolute -bottom-2 -left-2 w-4 h-4"
            >
              <Sparkles className="h-4 w-4 text-amber-400 dark:text-amber-300 animate-pulse" />
            </motion.div>
            <motion.div
              animate={{ rotate: -360 }}
              transition={{ duration: 9, repeat: Infinity, ease: "linear" }}
              className="absolute -bottom-2 -right-2 w-4 h-4"
            >
              <Sparkles className="h-4 w-4 text-amber-400 dark:text-amber-300 animate-pulse" />
            </motion.div>
          </>
        )}

        <Card
          className={`h-full flex flex-col border-2 relative overflow-hidden transition-all ${
            plan.isCurrent
              ? "border-emerald-500 dark:border-emerald-400 shadow-[0_0_20px_rgba(16,185,129,0.3)] dark:shadow-[0_0_20px_rgba(52,211,153,0.3)]"
              : "border-transparent"
          } ${plan.isCurrent ? "shadow-lg" : "shadow-md hover:shadow-lg"} ${
            plan.isDefault
              ? "hover:shadow-[0_0_25px_rgba(245,158,11,0.2)] dark:hover:shadow-[0_0_25px_rgba(234,179,8,0.3)]"
              : ""
          } bg-white/95 dark:bg-gray-800/95 backdrop-blur-sm`}
        >
          {/* Premium badge */}
          {plan.isDefault && (
            <div className="absolute top-0 left-0 bg-gradient-to-r from-amber-500 to-amber-600 dark:from-amber-600 dark:to-amber-700 text-white text-xs font-bold px-3 py-1 rounded-br-lg flex items-center z-10 shadow-sm">
              <Crown className="h-3 w-3 mr-1" />
              <span>Recommended</span>
            </div>
          )}

          {/* Current plan badge */}
          {plan.isCurrent && (
            <div className="absolute top-0 right-0 bg-gradient-to-r from-emerald-500 to-emerald-600 dark:from-emerald-600 dark:to-emerald-700 text-white text-xs font-bold px-3 py-1 rounded-bl-lg flex items-center z-10 shadow-sm">
              <Check className="h-3 w-3 mr-1" />
              <span>Current Plan</span>
            </div>
          )}

          {/* Admin actions */}
          {isAdmin && (
            <div className="absolute top-2 right-2 flex gap-2 z-10">
              <Button
                variant="outline"
                size="sm"
                onClick={() => onEdit(plan)}
                disabled={isUpdating || isDeleting}
                className="p-2 cursor-pointer bg-white/90 dark:bg-gray-700/90 backdrop-blur-sm hover:bg-white dark:hover:bg-gray-600 shadow-sm"
                title="Edit plan"
              >
                <Edit className="h-4 w-4" />
              </Button>
              <Button
                variant="destructive"
                size="sm"
                onClick={() => onDelete(plan._id)}
                disabled={isUpdating || isDeleting || plan.isDefault}
                className="p-2 cursor-pointer bg-white/90 dark:bg-gray-700/90 backdrop-blur-sm hover:bg-white dark:hover:bg-gray-600 shadow-sm"
                title="Delete plan"
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          )}

          <CardHeader className="pb-3">
            <CardTitle className="flex items-center justify-between text-lg font-bold text-gray-900 dark:text-white">
              <span className="truncate flex items-center">
                {plan.name}
                {plan.isCurrent && (
                  <BadgeCheck className="h-5 w-5 text-emerald-500 dark:text-emerald-400 ml-2" />
                )}
              </span>
            </CardTitle>
            <CardDescription className="text-sm text-gray-600 dark:text-gray-300">
              Optimal for {plan.maxMembers} team members
            </CardDescription>
          </CardHeader>

          <CardContent className="flex-1 space-y-6">
            {/* Price section */}
            <div className="text-center bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-700 dark:to-gray-800 rounded-lg p-4 border border-gray-200/50 dark:border-gray-700/50">
              <div className="flex justify-center items-baseline">
                <span className="text-3xl font-bold text-gray-900 dark:text-white">
                  {currencySymbol}
                  {formattedPrice}
                </span>
                <span className="text-base text-muted-foreground dark:text-gray-400 ml-1">
                  /{pricePeriod}
                </span>
              </div>
              {billingInterval === "yearly" && (
                <p className="text-sm text-emerald-600 dark:text-emerald-400 mt-1 font-medium">
                  Save{" "}
                  {Math.round(
                    (1 - convertedPriceYearly / (convertedPriceMonthly * 12)) *
                      100
                  )}
                  %
                </p>
              )}
            </div>

            {/* Features list */}
            <ul className="space-y-3">
              {features.map((feature, index) => (
                <motion.li
                  key={index}
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.05 }}
                  className="flex items-start text-gray-700 dark:text-gray-300"
                >
                  <span className="text-emerald-500 dark:text-emerald-400 mr-2 mt-0.5">
                    {feature.icon}
                  </span>
                  <span className="text-sm">{feature.text}</span>
                </motion.li>
              ))}
            </ul>
          </CardContent>

          <CardFooter>
            <Button
              className={`w-full cursor-pointer transition-all relative overflow-hidden group ${
                plan.isCurrent
                  ? "bg-emerald-600 hover:bg-emerald-700 dark:bg-emerald-700 dark:hover:bg-emerald-800 shadow-md"
                  : plan.isDefault
                  ? "bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 dark:from-amber-600 dark:to-amber-700 dark:hover:from-amber-700 dark:hover:to-amber-800 shadow-md"
                  : "bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800"
              }`}
              onClick={() => onUpgrade(plan._id)}
              disabled={plan.isCurrent || isLoading}
              size="lg"
            >
              {/* Shimmer effect for default plan */}
              {plan.isDefault && !isLoading && !plan.isCurrent && (
                <motion.span
                  className="absolute -inset-1 bg-gradient-to-r from-yellow-400/30 to-amber-600/30 dark:from-yellow-500/30 dark:to-amber-700/30 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-md"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 0.3 }}
                  transition={{
                    repeat: Infinity,
                    duration: 2,
                    repeatType: "reverse",
                  }}
                />
              )}

              {/* Button content */}
              <span className="flex items-center justify-center relative z-10">
                {isLoading ? (
                  <>
                    <svg
                      className="animate-spin -ml-1 mr-3 h-5 w-5 text-white dark:text-gray-100"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"
                      ></circle>
                      <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      ></path>
                    </svg>
                    <span className="text-white dark:text-gray-100 font-medium">
                      Processing...
                    </span>
                  </>
                ) : plan.isCurrent ? (
                  <span className="text-white dark:text-gray-100 font-medium flex items-center">
                    <Check className="h-5 w-5 mr-2" />
                    Current Plan
                  </span>
                ) : (
                  <>
                    {plan.isDefault && (
                      <Sparkles className="h-5 w-5 mr-2 text-white dark:text-amber-200 animate-pulse" />
                    )}
                    <span className="text-white dark:text-gray-100 font-medium">
                      Upgrade Now
                    </span>
                    <ArrowRight className="h-5 w-5 ml-2 text-white dark:text-gray-100 transition-transform group-hover:translate-x-1" />
                  </>
                )}
              </span>

              {/* Subtle pulse effect for current plan */}
              {plan.isCurrent && (
                <motion.span
                  className="absolute inset-0 rounded-md bg-white/10 dark:bg-white/5"
                  animate={{ opacity: [0.1, 0.2, 0.1] }}
                  transition={{ duration: 3, repeat: Infinity }}
                />
              )}
            </Button>
          </CardFooter>
        </Card>
      </div>
    </motion.div>
  );
};

export default PlanCard;
