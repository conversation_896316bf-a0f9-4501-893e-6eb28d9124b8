"use client";
import { motion } from "framer-motion";
import Image from "next/image";
import {
  Download,
  Link2,
  <PERSON>tings,
  Facebook,
  Instagram,
  ShoppingCart,
  Zap
} from "lucide-react";

const IntegrationCards = () => {
  const handleDownload = async () => {
    try {
      const response = await fetch("/p/download-plugin/wp");
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", "chatzuri-chatbot-wp.zip");
      document.body.appendChild(link);
      link.click();
      link.remove();
    } catch (error) {
      console.error("Download failed:", error);
    }
  };

  // Define the possible status values as a union type
  type IntegrationStatus =
    | "coming-soon"
    | "ready"
    | "beta"
    | "new"
    | "early-access";

  type Integration = {
    id: number;
    name: string;
    logo: string;
    description: string;
    status: IntegrationStatus;
    action: {
      text: string;
      icon: React.ElementType;
      color: string;
      buttonColor: string;
      disabled?: boolean;
      onClick?: () => void;
    };
  };

  const integrations: Integration[] = [
    {
      id: 1,
      name: "Zapier",
      logo: "/images/logos/zapier-logo.svg",
      description: "Connect with thousands of apps",
      status: "coming-soon",
      action: {
        text: "Coming Soon",
        icon: Zap,
        color:
          "bg-purple-100 text-purple-800 dark:bg-purple-900/50 dark:text-purple-200",
        buttonColor: "bg-gray-400 dark:bg-gray-600",
        disabled: true,
      },
    },
    {
      id: 2,
      name: "Wordpress",
      logo: "/images/logos/wordpress-logo.png",
      description: "Official chat widget plugin",
      status: "ready",
      action: {
        text: "Download",
        icon: Download,
        color:
          "bg-emerald-100 text-emerald-800 dark:bg-emerald-900/50 dark:text-emerald-200",
        buttonColor:
          "bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600",
        onClick: handleDownload,
      },
    },
    {
      id: 3,
      name: "Slack",
      logo: "/images/logos/slack-logo.png",
      description: "Receive and respond in Slack",
      status: "ready",
      action: {
        text: "Configure",
        icon: Settings,
        color:
          "bg-emerald-100 text-emerald-800 dark:bg-emerald-900/50 dark:text-emerald-200",
        buttonColor:
          "bg-purple-600 hover:bg-purple-700 dark:bg-purple-500 dark:hover:bg-purple-600",
      },
    },
    {
      id: 4,
      name: "WhatsApp",
      logo: "/images/logos/whatsapp-logo.png",
      description: "Connect WhatsApp Business",
      status: "beta",
      action: {
        text: "Connect",
        icon: Link2,
        color:
          "bg-amber-100 text-amber-800 dark:bg-amber-900/50 dark:text-amber-200",
        buttonColor:
          "bg-green-600 hover:bg-green-700 dark:bg-green-500 dark:hover:bg-green-600",
      },
    },
    {
      id: 5,
      name: "Messenger",
      logo: "/images/logos/messanger-logo.png",
      description: "Automate Facebook conversations",
      status: "ready",
      action: {
        text: "Connect",
        icon: Facebook,
        color:
          "bg-emerald-100 text-emerald-800 dark:bg-emerald-900/50 dark:text-emerald-200",
        buttonColor:
          "bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600",
      },
    },
    {
      id: 6,
      name: "Instagram",
      logo: "/images/logos/instagram-logo.png",
      description: "Automate DMs and comments",
      status: "ready",
      action: {
        text: "Connect",
        icon: Instagram,
        color:
          "bg-emerald-100 text-emerald-800 dark:bg-emerald-900/50 dark:text-emerald-200",
        buttonColor:
          "bg-pink-600 hover:bg-pink-700 dark:bg-pink-500 dark:hover:bg-pink-600",
      },
    },
    {
      id: 7,
      name: "Shopify",
      logo: "/images/logos/shopify-logo.png",
      description: "Personalized shopping assistance",
      status: "new",
      action: {
        text: "Install",
        icon: ShoppingCart,
        color:
          "bg-blue-100 text-blue-800 dark:bg-blue-900/50 dark:text-blue-200",
        buttonColor:
          "bg-indigo-600 hover:bg-indigo-700 dark:bg-indigo-500 dark:hover:bg-indigo-600",
      },
    },
  ];

  const statusVariants = {
    "coming-soon": {
      text: "Coming Soon",
      emoji: "🔜",
      color:
        "bg-purple-100 text-purple-800 dark:bg-purple-900/50 dark:text-purple-200",
      animation: {
        scale: [1, 1.05, 1],
        transition: { duration: 1.5, repeat: Infinity },
      },
    },
    ready: {
      text: "Ready",
      emoji: "🚀",
      color:
        "bg-emerald-100 text-emerald-800 dark:bg-emerald-900/50 dark:text-emerald-200",
      animation: {
        rotate: [0, 5, -5, 0],
        transition: { duration: 0.5 },
      },
    },
    beta: {
      text: "Beta",
      emoji: "🧪",
      color:
        "bg-amber-100 text-amber-800 dark:bg-amber-900/50 dark:text-amber-200",
      animation: {
        y: [0, -3, 0],
        transition: { duration: 1, repeat: Infinity },
      },
    },
    new: {
      text: "New!",
      emoji: "✨",
      color: "bg-blue-100 text-blue-800 dark:bg-blue-900/50 dark:text-blue-200",
      animation: {
        opacity: [1, 0.7, 1],
        transition: { duration: 1.5, repeat: Infinity },
      },
    },
    "early-access": {
      text: "Early Access",
      emoji: "🎟️",
      color:
        "bg-violet-100 text-violet-800 dark:bg-violet-900/50 dark:text-violet-200",
      animation: {
        x: [0, 2, -2, 0],
        transition: { duration: 1, repeat: Infinity },
      },
    },
  };

  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
        ease: "easeOut",
      },
    },
    hover: {
      y: -5,
      boxShadow: "0 10px 25px -5px rgba(0, 0, 0, 0.1)",
      transition: {
        duration: 0.3,
        ease: "easeOut",
      },
    },
  };

  return (
    <div className="p-6">
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5 }}
        className="mb-8"
      >
        <h2 className="text-2xl font-bold text-gray-800 dark:text-white flex items-center">
          <Zap className="h-6 w-6 text-emerald-500 mr-3" />
          <span>Integrations Hub</span>
        </h2>
        <p className="text-gray-600 dark:text-gray-400 mt-2">
          Connect your chatbot with your favorite tools and platforms
        </p>
      </motion.div>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
        {integrations.map((integration, index) => (
          <motion.div
            key={integration.id}
            variants={cardVariants}
            initial="hidden"
            animate="visible"
            whileHover="hover"
            custom={index}
            transition={{ delay: index * 0.1 }}
            className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden"
          >
            <div className="p-5">
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0 bg-gradient-to-br from-emerald-50 to-blue-50 dark:from-gray-700 dark:to-gray-800 rounded-lg p-3 shadow-inner">
                  <div className="relative h-10 w-10">
                    <Image
                      src={integration.logo}
                      alt={integration.name}
                      fill
                      className="object-contain"
                    />
                  </div>
                </div>

                <div className="flex-1 min-w-0">
                  <div className="flex justify-between items-start">
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                      {integration.name}
                    </h3>
                    <motion.span
                      className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        statusVariants[integration.status].color
                      }`}
                      animate={statusVariants[integration.status].animation}
                    >
                      {statusVariants[integration.status].emoji}{" "}
                      {statusVariants[integration.status].text}
                    </motion.span>
                  </div>
                  <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                    {integration.description}
                  </p>
                </div>
              </div>

              <motion.button
                whileTap={{ scale: 0.95 }}
                disabled={integration.action.disabled}
                onClick={integration.action.onClick}
                className={`mt-4 cursor-pointer w-full inline-flex justify-center items-center px-4 py-2 rounded-lg text-sm font-medium text-white ${integration.action.buttonColor} transition-colors`}
              >
                <integration.action.icon className="h-4 w-4 mr-2" />
                {integration.action.text}
              </motion.button>
            </div>
          </motion.div>
        ))}
      </div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.5 }}
        className="mt-10 bg-gradient-to-r from-emerald-50 to-blue-50 dark:from-gray-800 dark:to-gray-900 rounded-xl p-6 border border-gray-200 dark:border-gray-700"
      >
        <div className="flex items-center">
          <div className="flex-shrink-0 bg-white dark:bg-gray-700 p-3 rounded-lg shadow-sm">
            <Zap className="h-6 w-6 text-emerald-500" />
          </div>
          <div className="ml-4">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              Need another integration?
            </h3>
            <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
              Suggest new integrations and vote on what we build next
            </p>
          </div>
        </div>
        <div className="mt-4">
          <button className="cursor-pointer inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-emerald-600 hover:bg-emerald-700 dark:bg-emerald-500 dark:hover:bg-emerald-600">
            Request Integration
          </button>
        </div>
      </motion.div>
    </div>
  );
};

export default IntegrationCards;
