"use client";

import { motion } from "framer-motion";
import Link from "next/link";
import {
  ArrowRight,
  Workflow,
  GitBranch,
  Zap,
  Clock,
  Play,
  Pause,
  FileText,
  Activity,
} from "lucide-react";
import { use, useEffect, useState } from "react";

interface WorkflowCardProps {
  workflow: {
    _id: string;
    name: string;
    description?: string;
    status: "draft" | "active" | "inactive";
    version: string;
    nodes: number;
    triggers: string[];
    createdAt: Date;
    lastExecuted?: Date;
    executionCount: number;
  };
  teamUrl: string;
  theme: string;

}

const iconComponents = [
  Workflow,
  GitBranch,
  Activity,
  Zap,
];

const statusConfig = {
  active: {
    color: "text-emerald-600 dark:text-emerald-400",
    bg: "bg-emerald-500/10",
    icon: Play,
    label: "Active"
  },
  inactive: {
    color: "text-gray-600 dark:text-gray-400",
    bg: "bg-gray-500/10",
    icon: Pause,
    label: "Inactive"
  },
  draft: {
    color: "text-orange-600 dark:text-orange-400",
    bg: "bg-orange-500/10",
    icon: FileText,
    label: "Draft"
  }
};

export const WorkflowCard = ({ workflow, teamUrl }: WorkflowCardProps) => {
  //eslint-disable-next-line @typescript-eslint/no-explicit-any
  const [randomIcon, setRandomIcon] = useState<React.ComponentType<any>>(Workflow);
  const [isHovered, setIsHovered] = useState(false);
  useEffect(() => {
    // Select a random icon when component mounts
    const randomIndex = Math.floor(Math.random() * iconComponents.length);
    setRandomIcon(iconComponents[randomIndex]);
  }, []);

  const IconComponent = randomIcon;
  const status = statusConfig[workflow.status];
  const StatusIcon = status.icon;

  return (
    <motion.div
      whileHover={{ y: -8 }}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
      className="group relative"
    >
      
      <Link
        href={`/dashboard/team/${teamUrl}/workflows/${workflow._id}`}
        className="block h-full"
      >
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden h-full flex flex-col group-hover:shadow-lg transition-all duration-300 hover:border-emerald-400 dark:hover:border-emerald-500">
          {/* Status badge with pulse animation */}
          <div className="absolute top-3 right-3 z-10">
            <motion.div
              animate={{
                scale: isHovered ? [1, 1.1, 1] : 1,
                opacity: isHovered ? [0.8, 1, 0.8] : 1,
              }}
              transition={{ duration: 1.5, repeat: Infinity }}
              className={`${status.bg} ${status.color} text-xs px-2 py-1 rounded-full flex items-center`}
            >
              <StatusIcon className="w-2 h-2 mr-1" />
              {status.label}
            </motion.div>
          </div>

          <div className="p-6 flex-grow flex flex-col items-center text-center">
            {/* Animated icon placeholder */}
            <motion.div
              animate={{
                y: isHovered ? [-5, 5, -5] : 0,
                rotate: isHovered ? [0, 5, -5, 0] : 0,
              }}
              transition={{
                duration: 4,
                repeat: Infinity,
                ease: "easeInOut",
              }}
              className="relative w-20 h-20 mb-4 rounded-full bg-gradient-to-br from-purple-100 to-indigo-200 dark:from-purple-900/30 dark:to-indigo-800/40 flex items-center justify-center border-2 border-gray-200 dark:border-gray-600 group-hover:border-emerald-400 dark:group-hover:border-emerald-500 transition-colors"
            >
              <IconComponent className="w-8 h-8 text-purple-600 dark:text-purple-400" />

              {/* Floating particles effect */}
              {isHovered && (
                <>
                  <motion.div
                    initial={{ scale: 0, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    transition={{ delay: 0.2 }}
                    className="absolute -top-1 -right-1 w-3 h-3 rounded-full bg-purple-400/70"
                  />
                  <motion.div
                    initial={{ scale: 0, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    transition={{ delay: 0.4 }}
                    className="absolute -bottom-1 -left-1 w-2 h-2 rounded-full bg-indigo-400/70"
                  />
                </>
              )}
            </motion.div>

            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-1 lowercase first-letter:uppercase">
              {workflow.name}
            </h3>
            <p className="text-sm text-gray-500 dark:text-gray-400 line-clamp-2 mb-3">
              {workflow.description || "No description provided"}
            </p>

            {/* Version and nodes info */}
            <div className="flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400 mb-2">
              <span className="font-medium text-purple-600 dark:text-purple-400">
                v{workflow.version}
              </span>
              <span>•</span>
              <span>{workflow.nodes} nodes</span>
            </div>

            {/* Execution count and last executed */}
            <div className="flex items-center justify-center space-x-4 text-xs text-gray-500 dark:text-gray-400">
              <div className="flex items-center">
                <Activity className="h-3 w-3 mr-1" />
                <span>{workflow.executionCount} runs</span>
              </div>
              {workflow.lastExecuted && (
                <>
                  <span>•</span>
                  <div className="flex items-center">
                    <Clock className="h-3 w-3 mr-1" />
                    <span>
                      {workflow.lastExecuted.toLocaleDateString()}
                    </span>
                  </div>
                </>
              )}
            </div>
          </div>

          {/* Footer with animated arrow */}
          <div className="px-4 py-3 bg-gradient-to-r from-gray-50/50 to-gray-100/30 dark:from-gray-700/30 dark:to-gray-800/20 border-t border-gray-200 dark:border-gray-700 flex justify-between items-center">
            <div className="flex items-center space-x-2">
              <span className="text-xs text-gray-500 dark:text-gray-400">
                Created {workflow.createdAt.toLocaleDateString()}
              </span>
              {workflow.triggers.length > 0 && (
                <>
                  <span className="text-xs text-gray-400">•</span>
                  <div className="flex items-center text-xs text-gray-500 dark:text-gray-400">
                    <Zap className="h-3 w-3 mr-1" />
                    <span>{workflow.triggers.length} triggers</span>
                  </div>
                </>
              )}
            </div>
            <motion.div
              animate={{
                x: isHovered ? [0, 4, 0] : 0,
              }}
              transition={{
                repeat: Infinity,
                duration: 1.5,
                ease: "easeInOut",
              }}
            >
              <ArrowRight className="h-4 w-4 text-gray-400 group-hover:text-emerald-500 dark:group-hover:text-emerald-400 transition-colors" />
            </motion.div>
          </div>

          {/* Hover overlay effect */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: isHovered ? 0.1 : 0 }}
            className="absolute inset-0 bg-purple-400 pointer-events-none"
          />
        </div>
      </Link>
    </motion.div>
  );
};
