"use client";

import { motion } from "framer-motion";
import { <PERSON>ev<PERSON>R<PERSON>, Star } from "lucide-react";
import Image from "next/image";
import { Team } from "@/types/types";
import { cn } from "@/lib/utils";
import { getRandomAvatar, stringToColor } from "@/utils/avatar";
import { useUpdateTeam, useDeleteTeam } from "@/hooks/use-teams";
import toast from "react-hot-toast";
import Link from "next/link";
import { TeamActionsMenu } from "../popover/teams-actions-menu";
import { useState } from "react";
import { TeamSettingsModal } from "../modals/team-settings-modal";

interface TeamCardProps {
  teams: Team[];
  team: Team;
  index: number;
  setTeams: (teams: Team[]) => void;
}

export function TeamCard({ team, teams, index, setTeams }: TeamCardProps) {
  const updateTeam = useUpdateTeam();
  const deleteTeam = useDeleteTeam();
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);

  const toggleFavorite = async (slug: string) => {
    const currentTeam = teams.find((t) => t.url === slug);
    if (!currentTeam) return;

    // Optimistic update
    setTeams(
      teams.map((t) =>
        t.url === slug ? { ...t, isFavorite: !t.isFavorite } : t
      )
    );

    // Using toast.promise for unified toast handling
    toast.promise(
      updateTeam.mutateAsync({
        slug,
        data: { isFavorite: !currentTeam.isFavorite },
      }),
      {
        loading: "Updating favorite...",
        success: () => {
          return `Team ${
            !currentTeam.isFavorite ? "added to" : "removed from"
          } favorites`;
        },
        //eslint-disable-next-line @typescript-eslint/no-explicit-any
        error: (err: any) => {
          // Revert optimistic update on error
          setTeams(teams);
          return err instanceof Error
            ? err.message
            : "Failed to update favorite status";
        },
      },
      {
        position: "bottom-center",
        style: {
          minWidth: "200px",
        },
      }
    );
  };

  const handleDelete = async () => {
    await deleteTeam.mutateAsync(team.url);
  };

  const handleLeave = () => {
    if (confirm(`Are you sure you want to leave "${team.name}"?`)) {
      alert("leave team");

      // leaveTeam.mutate(team.url);
    }
  };

  const handleShare = () => {
    if (typeof window !== undefined) {
      const inviteLink = `${window.location.origin}/invite/${team.url}`;
      navigator.clipboard.writeText(inviteLink);
      toast.success("Invite link copied to clipboard");
    }
  };

  const handleSettings = () => {
    setIsModalOpen(!isModalOpen);
  };

  return (
    <motion.div
      key={team._id.toString()}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: index * 0.1, type: "spring", stiffness: 100 }}
      whileHover={{
        y: -8,
        boxShadow:
          "0 20px 25px -5px rgba(16, 185, 129, 0.1), 0 10px 10px -5px rgba(16, 185, 129, 0.04)",
      }}
      className="relative group rounded-2xl bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 overflow-hidden shadow-sm hover:shadow-xl transition-all duration-300"
    >
      {/* Gradient accent top */}
      <div
        className="absolute top-0 left-0 right-0 h-2 transition-all duration-500 group-hover:h-3"
        style={{ backgroundColor: team.color }}
      />

      {/* Main content */}
      <div className="p-6">
        {/* Header with menu button */}
        <div className="flex justify-between items-start mb-4">
          <h3 className="text-xl font-bold text-gray-900 dark:text-white truncate lowercase first-letter:uppercase">
            {team.name}
          </h3>
          <div className="flex space-x-2">
            <button
              onClick={() => toggleFavorite(team.url)}
              className={cn(
                "p-1.5 rounded-full cursor-pointer transition-all z-10",
                team.isFavorite
                  ? "text-yellow-400 hover:bg-yellow-400/10"
                  : "text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700"
              )}
            >
              <Star
                className={`h-5 w-5 transition-all ${
                  team.isFavorite ? "fill-current scale-110" : ""
                }`}
              />
            </button>
            <TeamActionsMenu
              team={team}
              onDelete={handleDelete}
              isDeleting={deleteTeam.isPending}
              onLeave={handleLeave}
              onShare={handleShare}
              onSettings={handleSettings}
            />
          </div>
          {isModalOpen && (
            <TeamSettingsModal
              team={team}
              isOpen={isModalOpen}
              onOpenChange={setIsModalOpen}
            />
          )}
        </div>

        {/* Description */}
        <p className="text-gray-600 dark:text-gray-300 mb-6 line-clamp-3">
          {team.description || "No description provided"}
        </p>
      </div>

      {/* Card footer - Main CTA Link */}
      <Link
        href={`/dashboard/team/${team.url}`}
        className="block border-t border-gray-100 dark:border-gray-700 bg-gray-50 dark:bg-gray-700/30 hover:bg-gray-100 dark:hover:bg-gray-700/50 transition-colors"
      >
        <div className="flex items-center justify-between p-4">
          <div className="flex items-center">
            {/* Member avatars */}
            <div className="flex -space-x-2 mr-3">
              {Array.from({ length: Math.min(team.membersCount, 5) }).map(
                (_, i) => {
                  const member = team.members?.[i];
                  const avatarUrl =
                    member &&
                    member.userId &&
                    typeof member.userId === "object" &&
                    "avatar" in member.userId
                      ? (member.userId.avatar as string)
                      : getRandomAvatar();
                  return (
                    <div
                      key={member?._id?.toString() || i}
                      className="relative h-8 w-8 rounded-full border-2 border-white dark:border-gray-800 overflow-hidden"
                      style={{ zIndex: 5 - i }}
                    >
                      {avatarUrl ? (
                        <Image
                          src={avatarUrl}
                          alt={member?.name || `Team member ${i + 1}`}
                          width={32}
                          height={32}
                          className="object-cover w-full h-full"
                          onError={(e) => {
                            (e.target as HTMLImageElement).src =
                              getRandomAvatar();
                          }}
                        />
                      ) : (
                        <div
                          className="w-full h-full flex items-center justify-center text-xs font-medium text-white"
                          style={{
                            backgroundColor: stringToColor(
                              member?.name || team.name
                            ),
                          }}
                        >
                          {member?.name?.charAt(0).toUpperCase() || "?"}
                        </div>
                      )}

                      {i === 4 && team.membersCount > 5 && (
                        <div className="absolute inset-0 bg-black/50 flex items-center justify-center text-white text-xs font-bold">
                          +{team.membersCount - 4}
                        </div>
                      )}
                    </div>
                  );
                }
              )}
            </div>

            <div className="text-sm text-gray-500 dark:text-gray-400">
              <span className="font-medium text-gray-700 dark:text-gray-300">
                {team.membersCount}
              </span>{" "}
              {team.membersCount === 1 ? "team member" : "team members"}
            </div>
          </div>

          <div className="flex items-center text-emerald-600 dark:text-emerald-400">
            <span className="text-sm font-medium mr-1">View team</span>
            <ChevronRight className="h-4 w-4" />
          </div>
        </div>
      </Link>
    </motion.div>
  );
}
