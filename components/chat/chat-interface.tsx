"use client";

import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Send, 
  Paperclip, 
  Mic, 
  MicOff, 
  ThumbsUp, 
  ThumbsDown, 
  RotateCcw, 
  Copy, 
  Download, 
  Mail, 
  Printer, 
  Share2,
  MessageSquare,
  X,
  Minimize2,
  Maximize2
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { ScrollArea } from '@/components/ui/scroll-area';

interface Message {
  id: string;
  content: string;
  sender: 'user' | 'bot';
  timestamp: Date;
  type: 'text' | 'file' | 'audio';
  fileUrl?: string;
  fileName?: string;
  feedback?: 'positive' | 'negative';
}

interface ChatConfig {
  // General Settings
  initialMessages?: string[];
  suggestedMessages?: string[];
  messagePlaceholder?: string;
  footer?: string;
  theme?: 'light' | 'dark';
  isPublic?: boolean;
  allowedDomains?: string[];
  ipLimit?: number;
  ipLimitTimeframe?: number;
  ipLimitMessage?: string;
  displayName?: string;
  iconPath?: string;
  loadPreviousSession?: boolean;

  // Interface Settings
  customerMessageColor?: string;
  customerMessageColorAsChatbotHeader?: boolean;
  hasChatBubbleToggle?: boolean;
  chatBubbleColor?: string;
  alignChatBubble?: 'left' | 'right';
  autoShowInitialDelay?: number;
}

interface ChatInterfaceProps {
  workflowId: string;
  config: ChatConfig;
  isPreview?: boolean;
  isEmbedded?: boolean;
  sessionId?: string;
  onMessage?: (message: string, files?: File[]) => void;
}

// Helper function to generate session ID
const generateSessionId = () => {
  return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

export const ChatInterface = ({
  workflowId,
  config,
  isPreview = false,
  isEmbedded = false,
  sessionId,
  onMessage
}: ChatInterfaceProps) => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isRecording, setIsRecording] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [currentSessionId, setCurrentSessionId] = useState(sessionId || generateSessionId());

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const audioRef = useRef<MediaRecorder | null>(null);

  // Initialize with welcome messages
  useEffect(() => {
    if (config.initialMessages && config.initialMessages.length > 0) {
      const welcomeMessages: Message[] = config.initialMessages.map((content, index) => ({
        id: `welcome-${index}`,
        content,
        sender: 'bot',
        timestamp: new Date(),
        type: 'text'
      }));
      setMessages(welcomeMessages);
    }

    // Load previous session if enabled
    if (config.loadPreviousSession && currentSessionId) {
      loadPreviousMessages();
    }
  }, [config.initialMessages, config.loadPreviousSession, currentSessionId]);

  const loadPreviousMessages = async () => {
    try {
      const response = await fetch(`/api/chat/${workflowId}/sessions/${currentSessionId}/messages`);
      if (response.ok) {
        const previousMessages = await response.json();
        setMessages(prev => [...prev, ...previousMessages]);
      }
    } catch (error) {
      console.error('Failed to load previous messages:', error);
    }
  };

  // Auto-scroll to bottom
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const sendMessage = async (content: string, files?: File[]) => {
    if (!content.trim() && !files?.length) return;

    const userMessage: Message = {
      id: `user-${Date.now()}`,
      content,
      sender: 'user',
      timestamp: new Date(),
      type: files?.length ? 'file' : 'text',
      fileName: files?.[0]?.name
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsLoading(true);

    try {
      // Send to workflow execution
      const formData = new FormData();
      formData.append('message', content);
      formData.append('sessionId', currentSessionId);
      formData.append('workflowId', workflowId);
      
      if (files?.length) {
        files.forEach(file => formData.append('files', file));
      }

      const response = await fetch(`/api/chat/${workflowId}/message`, {
        method: 'POST',
        body: formData
      });

      if (response.ok) {
        const result = await response.json();
        
        const botMessage: Message = {
          id: `bot-${Date.now()}`,
          content: result.response || 'I received your message.',
          sender: 'bot',
          timestamp: new Date(),
          type: 'text'
        };

        setMessages(prev => [...prev, botMessage]);
      } else {
        throw new Error('Failed to send message');
      }
    } catch (error) {
      console.error('Error sending message:', error);
      const errorMessage: Message = {
        id: `error-${Date.now()}`,
        content: 'Sorry, I encountered an error. Please try again.',
        sender: 'bot',
        timestamp: new Date(),
        type: 'text'
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }

    // Call parent callback if provided
    if (onMessage) {
      onMessage(content, files);
    }
  };

  const handleSuggestedMessage = (message: string) => {
    sendMessage(message);
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    if (files.length > 0) {
      sendMessage(`Uploaded ${files.length} file(s)`, files);
    }
  };

  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const mediaRecorder = new MediaRecorder(stream);
      audioRef.current = mediaRecorder;
      
      const chunks: BlobPart[] = [];
      mediaRecorder.ondataavailable = (event) => {
        chunks.push(event.data);
      };
      
      mediaRecorder.onstop = () => {
        const audioBlob = new Blob(chunks, { type: 'audio/wav' });
        const audioFile = new File([audioBlob], 'voice-message.wav', { type: 'audio/wav' });
        sendMessage('Voice message', [audioFile]);
      };
      
      mediaRecorder.start();
      setIsRecording(true);
    } catch (error) {
      console.error('Error starting recording:', error);
    }
  };

  const stopRecording = () => {
    if (audioRef.current && isRecording) {
      audioRef.current.stop();
      audioRef.current.stream.getTracks().forEach(track => track.stop());
      setIsRecording(false);
    }
  };

  const provideFeedback = async (messageId: string, feedback: 'positive' | 'negative') => {
    setMessages(prev => prev.map(msg => 
      msg.id === messageId ? { ...msg, feedback } : msg
    ));

    // Send feedback to backend
    try {
      await fetch(`/api/chat/${workflowId}/feedback`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          messageId,
          feedback,
          sessionId: currentSessionId
        })
      });
    } catch (error) {
      console.error('Error sending feedback:', error);
    }
  };

  const regenerateResponse = async (messageId: string) => {
    const messageIndex = messages.findIndex(msg => msg.id === messageId);
    if (messageIndex === -1) return;

    const userMessage = messages[messageIndex - 1];
    if (userMessage && userMessage.sender === 'user') {
      // Remove the bot message and regenerate
      setMessages(prev => prev.filter(msg => msg.id !== messageId));
      await sendMessage(userMessage.content);
    }
  };

  const copyConversation = () => {
    const conversation = messages
      .map(msg => `${msg.sender === 'user' ? 'You' : config.displayName || 'Assistant'}: ${msg.content}`)
      .join('\n');
    navigator.clipboard.writeText(conversation);
  };

  const downloadPDF = () => {
    // Implementation for PDF download
    console.log('Download PDF functionality to be implemented');
  };

  const emailConversation = () => {
    const conversation = messages
      .map(msg => `${msg.sender === 'user' ? 'You' : config.displayName || 'Assistant'}: ${msg.content}`)
      .join('\n');
    const subject = `Chat Conversation - ${new Date().toLocaleDateString()}`;
    const body = encodeURIComponent(conversation);
    window.open(`mailto:?subject=${subject}&body=${body}`);
  };

  const shareConversation = () => {
    if (navigator.share) {
      const conversation = messages
        .map(msg => `${msg.sender === 'user' ? 'You' : config.displayName || 'Assistant'}: ${msg.content}`)
        .join('\n');
      navigator.share({
        title: 'Chat Conversation',
        text: conversation
      });
    }
  };

  if (config.hasChatBubbleToggle && isMinimized) {
    return (
      <motion.div
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        className={`fixed ${config.alignChatBubble === 'left' ? 'left-4' : 'right-4'} bottom-4 z-50`}
      >
        <Button
          onClick={() => setIsMinimized(false)}
          className="w-16 h-16 rounded-full shadow-lg"
          style={{ backgroundColor: config.chatBubbleColor || '#007bff' }}
        >
          <MessageSquare className="w-6 h-6 text-white" />
        </Button>
      </motion.div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className={`flex flex-col h-full max-w-4xl mx-auto bg-white dark:bg-gray-900 ${
        isEmbedded ? 'rounded-lg shadow-lg' : ''
      }`}
      style={{ 
        maxHeight: isEmbedded ? '600px' : '100vh',
        ...(config.theme === 'dark' ? { backgroundColor: '#1f2937' } : {})
      }}
    >
      {/* Header */}
      <div 
        className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700"
        style={{
          backgroundColor: config.customerMessageColorAsChatbotHeader 
            ? config.customerMessageColor 
            : undefined
        }}
      >
        <div className="flex items-center space-x-3">
          {config.iconPath && (
            <img 
              src={config.iconPath} 
              alt="Bot Avatar" 
              className="w-8 h-8 rounded-full"
            />
          )}
          <div>
            <h3 className="font-semibold text-gray-900 dark:text-white">
              {config.displayName || 'AI Assistant'}
            </h3>
            <p className="text-xs text-gray-500 dark:text-gray-400">
              Online
            </p>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          {config.hasChatBubbleToggle && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsMinimized(true)}
            >
              <Minimize2 className="w-4 h-4" />
            </Button>
          )}
          
          <div className="flex items-center space-x-1">
            <Button variant="ghost" size="sm" onClick={copyConversation}>
              <Copy className="w-4 h-4" />
            </Button>
            <Button variant="ghost" size="sm" onClick={downloadPDF}>
              <Download className="w-4 h-4" />
            </Button>
            <Button variant="ghost" size="sm" onClick={emailConversation}>
              <Mail className="w-4 h-4" />
            </Button>
            <Button variant="ghost" size="sm" onClick={shareConversation}>
              <Share2 className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Messages */}
      <ScrollArea className="flex-1 p-4">
        <div className="space-y-4">
          {messages.map((message) => (
            <div
              key={message.id}
              className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
            >
              <div
                className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                  message.sender === 'user'
                    ? 'text-white'
                    : 'bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-white'
                }`}
                style={{
                  backgroundColor: message.sender === 'user' 
                    ? config.customerMessageColor || '#007bff'
                    : undefined
                }}
              >
                <p className="text-sm">{message.content}</p>
                <p className="text-xs opacity-70 mt-1">
                  {message.timestamp.toLocaleTimeString()}
                </p>
                
                {message.sender === 'bot' && (
                  <div className="flex items-center space-x-2 mt-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => provideFeedback(message.id, 'positive')}
                      className={message.feedback === 'positive' ? 'text-green-600' : ''}
                    >
                      <ThumbsUp className="w-3 h-3" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => provideFeedback(message.id, 'negative')}
                      className={message.feedback === 'negative' ? 'text-red-600' : ''}
                    >
                      <ThumbsDown className="w-3 h-3" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => regenerateResponse(message.id)}
                    >
                      <RotateCcw className="w-3 h-3" />
                    </Button>
                  </div>
                )}
              </div>
            </div>
          ))}
          
          {isLoading && (
            <div className="flex justify-start">
              <div className="bg-gray-100 dark:bg-gray-800 px-4 py-2 rounded-lg">
                <div className="flex space-x-1">
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                </div>
              </div>
            </div>
          )}
          
          <div ref={messagesEndRef} />
        </div>
      </ScrollArea>

      {/* Suggested Messages */}
      {config.suggestedMessages && config.suggestedMessages.length > 0 && (
        <div className="px-4 py-2 border-t border-gray-200 dark:border-gray-700">
          <div className="flex flex-wrap gap-2">
            {config.suggestedMessages.map((suggestion, index) => (
              <Button
                key={index}
                variant="outline"
                size="sm"
                onClick={() => handleSuggestedMessage(suggestion)}
                className="text-xs"
              >
                {suggestion}
              </Button>
            ))}
          </div>
        </div>
      )}

      {/* Input Area */}
      <div className="p-4 border-t border-gray-200 dark:border-gray-700">
        <div className="flex items-center space-x-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => fileInputRef.current?.click()}
          >
            <Paperclip className="w-4 h-4" />
          </Button>
          
          <div className="flex-1">
            <Input
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              placeholder={config.messagePlaceholder || "Type your message..."}
              onKeyPress={(e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                  e.preventDefault();
                  sendMessage(inputValue);
                }
              }}
              disabled={isLoading}
            />
          </div>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={isRecording ? stopRecording : startRecording}
            className={isRecording ? 'text-red-600' : ''}
          >
            {isRecording ? <MicOff className="w-4 h-4" /> : <Mic className="w-4 h-4" />}
          </Button>
          
          <Button
            onClick={() => sendMessage(inputValue)}
            disabled={!inputValue.trim() || isLoading}
            size="sm"
          >
            <Send className="w-4 h-4" />
          </Button>
        </div>
        
        {config.footer && (
          <p className="text-xs text-gray-500 dark:text-gray-400 mt-2 text-center">
            {config.footer}
          </p>
        )}
      </div>

      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        multiple
        className="hidden"
        onChange={handleFileUpload}
        accept="image/*,audio/*,video/*,.pdf,.doc,.docx,.txt"
      />
    </motion.div>
  );
};
