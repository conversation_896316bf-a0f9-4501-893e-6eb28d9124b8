"use client";
import { motion } from "framer-motion";
import {
  Twitter,
  Linkedin,
  Github,
  Mail,
  MapPin,
  Users,
  Shield,
  FileText,
  Cookie,
  FileCode2,
  BookOpen,
  HelpCircle,
  DollarSign,
  Rocket,
  MessageCircle,
} from "lucide-react";
import Link from "next/link";
import Image from "next/image";

export const Footer = () => {
  const currentYear = new Date().getFullYear();

  const footerLinks = [
    {
      title: "Product",
      links: [
        { name: "Pricing", href: "/pricing", icon: <DollarSign size={16} /> },
        { name: "Security", href: "/security", icon: <Shield size={16} /> },
        { name: "Affiliates", href: "/affiliates", icon: <Rocket size={16} /> },
      ],
    },
    {
      title: "Resources",
      links: [
        { name: "API", href: "/docs", icon: <FileCode2 size={16} /> },
        { name: "Guides", href: "/guides", icon: <BookOpen size={16} /> },
        { name: "Blog", href: "/blogs", icon: <BookOpen size={16} /> },
        { name: "Help", href: "/help", icon: <HelpCircle size={16} /> },
      ],
    },
    {
      title: "Company",
      links: [
        {
          name: "About us",
          href: "/about",
          icon: <MessageCircle size={16} />,
        },
        {
          name: "Privacy Policy",
          href: "/privacy",
          icon: <FileText size={16} />,
        },
        {
          name: "Terms of Service",
          href: "/terms",
          icon: <FileText size={16} />,
        },
        { name: "Cookie Policy", href: "/cookies", icon: <Cookie size={16} /> },
        { name: "DPA", href: "/dpa", icon: <FileText size={16} /> },
      ],
    },
    {
      title: "About",
      links: [
        { name: "Teams", href: "/teams", icon: <Users size={16} /> },
        {
          name: "Singapore, Nairobi",
          href: "/locations",
          icon: <MapPin size={16} />,
        },
      ],
    },
  ];

  const socialLinks = [
    {
      name: "Twitter",
      href: "https://twitter.com",
      icon: <Twitter size={18} />,
    },
    {
      name: "LinkedIn",
      href: "https://linkedin.com",
      icon: <Linkedin size={18} />,
    },
    { name: "GitHub", href: "https://github.com", icon: <Github size={18} /> },
    {
      name: "Email",
      href: "mailto:<EMAIL>",
      icon: <Mail size={18} />,
    },
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 100,
        damping: 10,
      },
    },
  };

  return (
    <motion.footer
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true, margin: "-100px" }}
      variants={containerVariants}
      className="bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-800"
    >
      <div className="container mx-auto px-4 py-12">
        {/* Logo and description - full width on small screens */}
        <motion.div
          variants={itemVariants}
          className="w-full mb-8 md:mb-12 lg:hidden"
        >
          <Link href="/" className="inline-block">
            <div className="relative w-[150px] h-[40px]">
              <Image
                src="/images/chatzuri-logo-trans-rect.png"
                alt="Chatzuri"
                fill
                sizes="150px"
                className="object-contain dark:brightness-0 dark:invert"
                placeholder="blur"
                blurDataURL="/images/placeholder.jpg"
              />
            </div>
          </Link>
          <p className="mt-2 text-sm text-gray-600 dark:text-gray-300 md:max-w-[200px]">
            AI-powered chatbots are transforming customer interactions by
            providing instant, intelligent responses around the clock. They help
            businesses reduce operational costs, improve response times, and
            scale support without compromising quality. These chatbots
            understand natural language, learn from conversations, and integrate
            with existing systems to offer personalized experiences that enhance
            customer satisfaction and loyalty.
          </p>

          {/* Social links */}
          <div className="mt-4 flex space-x-4">
            {socialLinks.map((social) => (
              <motion.a
                key={social.name}
                variants={itemVariants}
                href={social.href}
                target="_blank"
                rel="noopener noreferrer"
                className="text-gray-500 hover:text-emerald-600 dark:text-gray-400 dark:hover:text-emerald-400 transition-colors"
                whileHover={{ y: -2 }}
              >
                {social.icon}
              </motion.a>
            ))}
          </div>
        </motion.div>

        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-5 gap-8">
          <motion.div
            variants={itemVariants}
            className="hidden lg:block lg:col-span-1"
          >
            <Link href="/" className="inline-block">
              <div className="relative w-[150px] h-[40px]">
                <Image
                  src="/images/chatzuri-logo-trans-rect.png"
                  alt="Chatzuri"
                  fill
                  sizes="150px"
                  className="object-contain dark:brightness-0 dark:invert"
                  placeholder="blur"
                  blurDataURL="/images/placeholder.jpg"
                />
              </div>
            </Link>
            <p className="mt-2 text-sm text-gray-600 dark:text-gray-300 max-w-[400px]">
              AI-powered chatbots are transforming customer interactions by
              providing instant, intelligent responses around the clock. They
              help businesses reduce operational costs, improve response times,
              and scale support without compromising quality. These chatbots
              understand natural language, learn from conversations, and
              integrate with existing systems to offer personalized experiences
              that enhance customer satisfaction and loyalty.
            </p>

            {/* Social links*/}
            <div className="mt-4 hidden lg:flex space-x-4">
              {socialLinks.map((social) => (
                <motion.a
                  key={social.name}
                  variants={itemVariants}
                  href={social.href}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-gray-500 hover:text-emerald-600 dark:text-gray-400 dark:hover:text-emerald-400 transition-colors"
                  whileHover={{ y: -2 }}
                >
                  {social.icon}
                </motion.a>
              ))}
            </div>
          </motion.div>

          {/* Footer links */}
          {footerLinks.map((column) => (
            <motion.div
              key={column.title}
              variants={itemVariants}
              className="col-span-1"
            >
              <h3 className="text-sm font-semibold text-gray-900 dark:text-white uppercase tracking-wider mb-4">
                {column.title}
              </h3>
              <ul className="space-y-3">
                {column.links.map((link) => (
                  <motion.li
                    key={link.name}
                    variants={itemVariants}
                    whileHover={{ x: 4 }}
                  >
                    <Link
                      href={link.href}
                      className="flex items-center gap-2 text-sm text-gray-600 hover:text-emerald-600 dark:text-gray-400 dark:hover:text-emerald-400 transition-colors"
                    >
                      <span className="opacity-70">{link.icon}</span>
                      {link.name}
                    </Link>
                  </motion.li>
                ))}
              </ul>
            </motion.div>
          ))}
        </div>

        {/* Bottom copyright */}
        <motion.div
          variants={itemVariants}
          className="mt-12 pt-8 border-t border-gray-200 dark:border-gray-800"
        >
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-sm text-gray-600 dark:text-gray-400 order-2 md:order-1 mt-4 md:mt-0">
              &copy; {currentYear} Chatzuri. All rights reserved.
            </p>
            <div className="order-1 md:order-2 flex flex-col md:flex-row items-center space-y-2 md:space-y-0 md:space-x-6">
              <p className="text-xs text-gray-500 dark:text-gray-500">
                Made with ❤️ for businesses worldwide
              </p>
              <div className="flex space-x-4 md:space-x-6">
                <Link
                  href="/terms"
                  className="text-xs text-gray-500 hover:text-emerald-600 dark:text-gray-500 dark:hover:text-emerald-400 transition-colors"
                >
                  Terms of Service
                </Link>
                <Link
                  href="/privacy"
                  className="text-xs text-gray-500 hover:text-emerald-600 dark:text-gray-500 dark:hover:text-emerald-400 transition-colors"
                >
                  Privacy Policy
                </Link>
                <Link
                  href="/cookies"
                  className="text-xs text-gray-500 hover:text-emerald-600 dark:text-gray-500 dark:hover:text-emerald-400 transition-colors"
                >
                  Cookie Policy
                </Link>
                <Link
                  href="https://tevinly.com"
                  target="_blank"
                  referrerPolicy="no-referrer"
                  className="text-xs text-gray-500 hover:text-emerald-600 dark:text-gray-500 dark:hover:text-emerald-400 transition-colors"
                >
                  Tevinly
                </Link>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </motion.footer>
  );
};
