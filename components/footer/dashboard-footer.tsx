"use client";
import { motion } from "framer-motion";
import Link from "next/link";
import { containerVariants, itemVariants } from "@/variants/variants";

export const DashboardFooter = () => {
  const currentYear = new Date().getFullYear();

  return (
    <motion.footer
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true, margin: "-50px" }}
      variants={containerVariants}
      className="bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-800 w-full mt-auto"
    >
      <div className="container mx-auto px-4 py-3">
        <motion.div variants={itemVariants}>
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-sm text-gray-600 dark:text-gray-400 order-2 md:order-1 mt-4 md:mt-0">
              &copy; {currentYear} Chatzuri. All rights reserved.
            </p>
            <div className="order-1 md:order-2 flex flex-col md:flex-row items-center space-y-2 md:space-y-0 md:space-x-6">
              <p className="text-xs text-gray-500 dark:text-gray-500">
                Made with ❤️ for businesses worldwide
              </p>
              <div className="flex space-x-4 md:space-x-6">
                <Link
                  href="/terms"
                  className="text-xs text-gray-500 hover:text-emerald-600 dark:text-gray-500 dark:hover:text-emerald-400 transition-colors"
                >
                  Terms of Service
                </Link>
                <Link
                  href="/privacy"
                  className="text-xs text-gray-500 hover:text-emerald-600 dark:text-gray-500 dark:hover:text-emerald-400 transition-colors"
                >
                  Privacy Policy
                </Link>
                <Link
                  href="/cookies"
                  className="text-xs text-gray-500 hover:text-emerald-600 dark:text-gray-500 dark:hover:text-emerald-400 transition-colors"
                >
                  Cookie Policy
                </Link>
                <Link
                  href="https://tevinly.com"
                  target="_blank"
                  referrerPolicy="no-referrer"
                  className="text-xs text-gray-500 hover:text-emerald-600 dark:text-gray-500 dark:hover:text-emerald-400 transition-colors"
                >
                  Tevinly
                </Link>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </motion.footer>
  );
};
