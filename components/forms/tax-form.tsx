"use client";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { motion } from "framer-motion";
import { FileText, AlertCircle, Check, RefreshCw } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import toast from "react-hot-toast";
import { TaxFormValues } from "@/types/types";
import { taxFormSchema } from "@/validations/validations";
import { useTaxInfo } from "@/hooks/use-tax-info";
import { useEffect, useState } from "react";
import { ErrorDialog } from "../error/error-dialog";

interface TaxFormProps {
  defaultValues?: Partial<TaxFormValues>;
  onSubmit: (values: TaxFormValues) => Promise<void> | void;
  isSubmitting?: boolean;
  headerIcon?: React.ReactNode;
  headerTitle?: string;
  warningMessage?: string;
  error?: Error | null;
  onErrorDismiss?: () => void;
}

export function TaxForm({
  defaultValues,
  onSubmit,
  isSubmitting = false,
  headerIcon = <FileText className="h-5 w-5 text-red-600 dark:text-red-400" />,
  headerTitle = "Tax Information",
  warningMessage = "Providing accurate tax information ensures proper invoicing and may affect your billing.",
  error,
  onErrorDismiss,
}: TaxFormProps) {
  const { data, isLoading, updateError } = useTaxInfo();
  const [errorDialogOpen, setErrorDialogOpen] = useState<boolean>(false);

  
  const form = useForm<TaxFormValues>({
    resolver: zodResolver(taxFormSchema),
    defaultValues: {
      type: "",
      id: "",
      ...defaultValues,
    },
    mode: "onChange",
    reValidateMode: "onChange",
  });

  // Update form when data loads
  useEffect(() => {
    if (data) {
      form.reset({
        type: data.taxType || "",
        id: data.taxId || "",
      });
    }
  }, [data, form]);

  // Handle submission errors
  useEffect(() => {
    if (updateError) {
      setErrorDialogOpen(true);
    }
  }, [updateError]);

  async function handleSubmit(values: TaxFormValues) {
    try {
      await onSubmit(values);
    } catch (error) {
      console.log(error);
      toast.error("Failed to update tax information");
    }
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, delay: 0.4 }}
      className="rounded-xl border border-gray-200 bg-white shadow-sm dark:border-gray-700 dark:bg-gray-800"
    >
      <div className="p-6">
        <div className="flex items-center space-x-3 mb-6">
          <div className="p-2 rounded-full bg-red-100 dark:bg-red-900/30">
            {headerIcon}
          </div>
          <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-100">
            {headerTitle}
          </h2>
        </div>

        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleSubmit)}
            className="space-y-4 relative"
          >
            {/* Loading overlay */}
            {isLoading && (
              <div className="absolute inset-0 bg-gray-200 dark:bg-gray-800 bg-opacity-20 dark:bg-opacity-20 flex items-center justify-center rounded-lg z-10">
                <span className="flex gap-1 items-center">
                  <RefreshCw className="h-8 w-8 animate-spin text-emerald-600 dark:text-emerald-400" />
                  Loading...
                </span>
              </div>
            )}
            <FormField
              control={form.control}
              name="type"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Tax Type</FormLabel>
                  <FormControl>
                    <Input placeholder="e.g., VAT, GST, etc." {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="id"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Tax ID</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Your tax identification number"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="bg-amber-50 dark:bg-amber-900/10 p-3 rounded-lg flex items-start">
              <AlertCircle className="h-4 w-4 text-amber-500 dark:text-amber-400 mt-0.5 mr-2 flex-shrink-0" />
              <p className="text-sm text-amber-600 dark:text-amber-400">
                {warningMessage}
              </p>
            </div>

            <div className="mt-6 flex justify-end">
              <Button
                type="submit"
                disabled={isSubmitting}
                className="bg-emerald-600 hover:bg-emerald-700 dark:bg-emerald-700 dark:hover:bg-emerald-800 cursor-pointer dark:text-white"
              >
                {isSubmitting ? (
                  <>
                    <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Check className="mr-2 h-4 w-4" />
                    Save Tax Details
                  </>
                )}
              </Button>
            </div>
          </form>
        </Form>
        <ErrorDialog
          open={errorDialogOpen}
          onOpenChange={(open) => {
            setErrorDialogOpen(open);
            if (!open && onErrorDismiss) {
              onErrorDismiss();
            }
          }}
          title="Submission Error"
          description={error?.message || "Failed to save billing information"}
        />
      </div>
    </motion.div>
  );
}
