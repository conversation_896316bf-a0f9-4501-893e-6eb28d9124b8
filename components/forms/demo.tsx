"use client"
import { zod<PERSON><PERSON>ol<PERSON> } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Globe, Sparkles, Loader2 } from "lucide-react";
import { motion } from "framer-motion";

const formSchema = z.object({
  websiteUrl: z.string().url({
    message: "Please enter a valid URL (e.g., https://yourwebsite.com)",
  }),
});

export function DemoForm() {
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      websiteUrl: "",
    },
    mode: "onBlur",
    reValidateMode: "onBlur",
  });

  async function onSubmit(values: z.infer<typeof formSchema>) {
    try {
      console.log(values);
      // Example:
      // const response = await fetch('/api/generate-demo', {
      //   method: 'POST',
      //   body: JSON.stringify(values),
      // });
      // const data = await response.json();
    } catch (error) {
      console.error("Error submitting form:", error);
    }
  }

  const isSubmitting = form.formState.isSubmitting;

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-5">
        <FormField
          control={form.control}
          name="websiteUrl"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <div className="relative">
                  <Globe className="absolute left-3 top-2.5 w-5 h-5 text-gray-400" />
                  <Input
                    placeholder="https://yourwebsite.com"
                    className="w-full pl-10 pr-4 py-3"
                    {...field}
                  />
                </div>
              </FormControl>
              <FormMessage />
              <FormDescription className="mt-2">
                We&apos;ll create a personalized demo based on your website content
              </FormDescription>
            </FormItem>
          )}
        />

        <motion.div
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
        >
          <Button
            type="submit"
            className="w-full py-3 px-6 cursor-pointer bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700 text-white font-medium rounded-lg transition-all"
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <span className="flex items-center justify-center gap-2">
                <Loader2 className="w-4 h-4 animate-spin" />
                Processing...
              </span>
            ) : (
              <span className="flex items-center justify-center gap-2">
                <Sparkles className="w-4 h-4" />
                Generate My Demo
              </span>
            )}
          </Button>
        </motion.div>
      </form>
    </Form>
  );
}