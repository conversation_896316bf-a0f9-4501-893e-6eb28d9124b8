"use client";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { ColorPicker } from "@/components/color-picker/color-picker";
import { Plus, Save } from "lucide-react";
import { Switch } from "@/components/ui/switch";
import { TeamFormValues } from "@/types/types";
import { baseTeamFormSchema, teamFormSchema } from "@/validations/validations";
import { useEffect } from "react";

interface CreateTeamFormProps {
  onSubmit: (values: TeamFormValues) => Promise<void>;
  isEditing?: boolean;
  initialData?: TeamFormValues;
  isPending?: boolean;
  hideDefaultSubmitButton?: boolean;
}

export function CreateTeamForm({
  onSubmit,
  isPending,
  hideDefaultSubmitButton = false,
  isEditing = false,
  initialData,
}: CreateTeamFormProps) {

  const form = useForm<TeamFormValues>({
    resolver: zodResolver(isEditing ? teamFormSchema : baseTeamFormSchema),
    defaultValues: {
      name: "",
      url: "",
      description: "",
      color: "#3b82f6",
      metaData: null,
      isFavorite: false,
    },
    mode: "onChange",
    reValidateMode: "onChange",
  });

  const { watch, setValue } = form;
  const nameValue = watch("name");

  useEffect(() => {
    if (!isEditing) {
      const slug = nameValue
        ?.toLowerCase()
        .replace(/[^a-z0-9]+/g, "-")
        .replace(/^-+|-+$/g, "");
      setValue("url", slug);
    }
  }, [nameValue, setValue, isEditing]);

  useEffect(() => {
    if (isEditing && initialData) {
      form.reset({
        ...initialData,
        // Ensure null values are handled
        openAiKey: initialData.openAiKey || null,
        metaData: initialData.metaData || null,
      });
    }
  }, [isEditing, initialData, form]);

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="space-y-6"
        data-tab="general"
      >
        {/* Basic Fields (always shown) */}
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Team Name</FormLabel>
              <FormControl>
                <Input placeholder="Acme Inc" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="url"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Team URL</FormLabel>
              <FormControl>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-500">
                    chatzuri.team/
                  </div>
                  <Input
                    className="pl-32"
                    placeholder="acme-inc"
                    disabled={isEditing}
                    {...field}
                    onChange={(e) => {
                      const formattedValue = e.target.value
                        .replace(/\s+/g, "-")
                        .toLowerCase();
                      field.onChange(formattedValue);
                    }}
                  />
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Description</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="What's this team about?"
                  className="resize-none"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="color"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Team Color</FormLabel>
              <FormControl>
                <ColorPicker
                  value={field.value ?? "#3b82f6"}
                  onChange={field.onChange}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Edit Mode Only Fields */}
        {isEditing && (
          <>
            <FormField
              control={form.control}
              name="metaData"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Custom Metadata (JSON)</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="{}"
                      className="font-mono text-sm min-h-[100px]"
                      {...field}
                      value={field.value || ""}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="isFavorite"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <FormLabel className="text-base">Favorite Team</FormLabel>
                    <p className="text-sm text-muted-foreground">
                      Pin this team to the top of your list
                    </p>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                      className="data-[state=checked]:bg-emerald-500 cursor-pointer"
                    />
                  </FormControl>
                </FormItem>
              )}
            />
          </>
        )}

        <Button
          type="submit"
          className={`cursor-pointer w-full ${
            hideDefaultSubmitButton ? "hidden" : ""
          }`}
          disabled={isPending}
        >
          {isPending ? (
            <>
              <svg
                className="animate-spin -ml-1 mr-2 h-4 w-4 text-white dark:text-black"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  className="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="4"
                ></circle>
                <path
                  className="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                ></path>
              </svg>
              {isEditing ? "Saving..." : "Creating..."}
            </>
          ) : (
            <>
              {isEditing ? (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Save Changes
                </>
              ) : (
                <>
                  <Plus className="h-4 w-4 mr-2" />
                  Create Team
                </>
              )}
            </>
          )}
        </Button>
      </form>
    </Form>
  );
}
