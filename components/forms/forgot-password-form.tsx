"use client";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { itemVariants } from "@/variants/variants";
import { motion } from "framer-motion";
import { useState } from "react";
import { Mail, ArrowRight, Loader2 } from "lucide-react";
import Link from "next/link";
import { ForgotPasswordData } from "@/types/types";
import { ForgotPasswordSchema } from "@/validations/validations";
import toast from "react-hot-toast";

export default function ForgotPasswordForm() {
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  const form = useForm<ForgotPasswordData>({
    resolver: zodResolver(ForgotPasswordSchema),
    defaultValues: {
      email: "",
    },
    mode: "onChange",
    reValidateMode: "onChange",
  });

  const onSubmit = async (values: ForgotPasswordData) => {
    setIsLoading(true);
    const toastId = toast.loading("Sending reset link...");

    const controller = new AbortController();
    const signal = controller.signal;

    // Set timeout to abort after 15 seconds
    const timeoutId = setTimeout(() => {
      controller.abort();
    }, 15000);

    try {
      const response = await fetch("/api/auth/forgot-password", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(values),
        signal,
      });

      clearTimeout(timeoutId);

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.message || "Something went wrong.");
      }

      toast.success("Reset link sent successfully!", { id: toastId });
      setIsSubmitted(true);
    //eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (error: any) {
      clearTimeout(timeoutId);

      if (error.name === "AbortError") {
        toast.error("Request timed out. Please try again.", { id: toastId });
      } else {
        toast.error(error.message || "Failed to send reset link.", {
          id: toastId,
        });
      }

      console.error("Forgot password error:", error.message);
    } finally {
      setIsLoading(false);
    }
  };

  if (isSubmitted) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="text-center space-y-6"
      >
        <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-emerald-100 dark:bg-emerald-900">
          <Mail className="h-6 w-6 text-emerald-600 dark:text-emerald-400" />
        </div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-white">
          Check your email
        </h3>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          We&apos;ve sent a password reset link to{" "}
          <span className="font-medium text-emerald-600 dark:text-emerald-400">
            {form.getValues("email")}
          </span>
        </p>
        <Button
          asChild
          className="mt-4 w-full bg-emerald-600 hover:bg-emerald-700"
        >
          <Link href="/login">Return to sign in</Link>
        </Button>
      </motion.div>
    );
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <motion.div variants={itemVariants}>
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Email</FormLabel>
                <div className="relative">
                  <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                  <FormControl>
                    <Input
                      placeholder="<EMAIL>"
                      className="pl-10 bg-white dark:bg-gray-800"
                      {...field}
                    />
                  </FormControl>
                </div>
                <FormMessage />
              </FormItem>
            )}
          />
        </motion.div>

        <motion.div variants={itemVariants}>
          <Button
            type="submit"
            disabled={isLoading || !form.formState.isValid}
            className="w-full cursor-pointer dark:text-gray-50 bg-emerald-600 hover:bg-emerald-700"
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Sending reset link...
              </>
            ) : (
              <>
                Send reset link
                <ArrowRight className="ml-2 h-4 w-4" />
              </>
            )}
          </Button>
        </motion.div>

        <motion.div
          variants={itemVariants}
          className="text-center text-sm text-gray-600 dark:text-gray-400"
        >
          Remember your password?{" "}
          <Link
            href="/login"
            className="font-medium text-emerald-600 dark:text-emerald-400 hover:text-emerald-800 dark:hover:text-emerald-300"
          >
            Sign in
          </Link>
        </motion.div>
      </form>
    </Form>
  );
}
