import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { RefreshCw, Check } from "lucide-react";
import { BillingFormValues } from "@/types/types";
import { billingFormSchema } from "@/validations/validations";
import { useEffect, useState } from "react";
import { useBillingData } from "@/hooks/use-billing";
import { ErrorDialog } from "../error/error-dialog";

interface BillingFormProps {
  defaultValues?: Partial<BillingFormValues>;
  onSubmit: (values: BillingFormValues) => Promise<void>;
  isSubmitting?: boolean;
  className?: string;
  error?: Error | null;
  onErrorDismiss?: () => void;
}

export function BillingForm({
  defaultValues,
  onSubmit,
  isSubmitting = false,
  className = "",
  error,
  onErrorDismiss,
}: BillingFormProps) {
  const [errorDialogOpen, setErrorDialogOpen] = useState<boolean>(false);
  const { data, isLoading } = useBillingData();
  const form = useForm<BillingFormValues>({
    resolver: zodResolver(billingFormSchema),
    defaultValues: {
      organizationName: "",
      countryOrRegion: "",
      addressLine1: "",
      addressLine2: "",
      city: "",
      state: "",
      postalCode: "",
      ...defaultValues,
    },
    mode: "onChange",
    reValidateMode: "onChange",
  });

  useEffect(() => {
    if (data?.data) {
      form.reset(data.data);
    }
  }, [data, form]);

  useEffect(() => {
    if (error) {
      setErrorDialogOpen(true);
    }
  }, [error]);

  return (
    <>
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className={`space-y-6 ${className} relative`}
        >
          {/* Loading overlay */}
          {isLoading && (
            <div className="absolute inset-0 bg-gray-200 dark:bg-gray-800 bg-opacity-20 dark:bg-opacity-20 flex items-center justify-center rounded-lg z-10">
              <span className="flex gap-1 items-center">
                <RefreshCw className="h-8 w-8 animate-spin text-emerald-600 dark:text-emerald-400" />
                Loading...
              </span>
            </div>
          )}

          <FormField
            control={form.control}
            name="organizationName"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Organization Name</FormLabel>
                <FormControl>
                  <Input
                    placeholder="Your organization name"
                    {...field}
                    className="dark:bg-gray-800"
                    disabled={isLoading}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="countryOrRegion"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Country or Region</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Country or region"
                      {...field}
                      className="dark:bg-gray-800"
                      disabled={isLoading}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="addressLine1"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Address Line 1</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Street address"
                      {...field}
                      className="dark:bg-gray-800"
                      disabled={isLoading}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <FormField
            control={form.control}
            name="addressLine2"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Address Line 2 (Optional)</FormLabel>
                <FormControl>
                  <Input
                    placeholder="Apartment, suite, etc."
                    {...field}
                    className="dark:bg-gray-800"
                    disabled={isLoading}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <FormField
              control={form.control}
              name="city"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>City</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="City"
                      {...field}
                      className="dark:bg-gray-800"
                      disabled={isLoading}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="state"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>State/Province</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="State or province"
                      {...field}
                      className="dark:bg-gray-800"
                      disabled={isLoading}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="postalCode"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Postal Code</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Postal code"
                      {...field}
                      className="dark:bg-gray-800"
                      disabled={isLoading}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <div className="flex justify-end">
            <Button
              type="submit"
              disabled={isSubmitting || isLoading}
              className="bg-emerald-600 hover:bg-emerald-700 dark:bg-emerald-700 dark:hover:bg-emerald-800 cursor-pointer dark:text-white"
            >
              {isSubmitting ? (
                <>
                  <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                  Saving...
                </>
              ) : (
                <>
                  <Check className="mr-2 h-4 w-4" />
                  Save Changes
                </>
              )}
            </Button>
          </div>
        </form>
      </Form>
      <ErrorDialog
        open={errorDialogOpen}
        onOpenChange={(open) => {
          setErrorDialogOpen(open);
          if (!open && onErrorDismiss) {
            onErrorDismiss();
          }
        }}
        title="Submission Error"
        description={error?.message || "Failed to save billing information"}
      />
    </>
  );
}
