"use client";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { itemVariants } from "@/variants/variants";
import { motion } from "framer-motion";
import { useState } from "react";
import {
  ArrowRight,
  Eye,
  EyeOff,
  Loader2,
  Mail,
  Lock,
  Smartphone,
  CheckCheck,
} from "lucide-react";
import Link from "next/link";
import { RegisterData } from "@/types/types";
import { RegisterSchema } from "@/validations/validations";
import { signIn } from "next-auth/react";
import toast from "react-hot-toast";
import { useRouter, useSearchParams } from "next/navigation";

export default function RegisterForm() {
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const searchParams = useSearchParams();
  const router = useRouter();
  const callbackUrl = searchParams.get("callbackUrl") || "/dashboard";

  const form = useForm<RegisterData>({
    resolver: zodResolver(RegisterSchema),
    defaultValues: {
      email: "",
      mobile: "",
      password: "",
      confirmPassword: "",
      terms: false,
    },
    mode: "onChange",
    reValidateMode: "onChange",
  });

  const onSubmit = async (values: RegisterData) => {
    setIsLoading(true);
    const toastId = toast.loading("Creating account...");
    const controller = new AbortController();
    const signal = controller.signal;

    // Set up  timeout
    const timeoutId = setTimeout(() => {
      controller.abort();
    }, 25000); // 25
    try {
      const response = await fetch("/api/auth/register", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          email: values.email,
          password: values.password,
          mobile: values.mobile,
        }),
        signal,
      });
      clearTimeout(timeoutId);
      if (!response.ok) {
        throw new Error(await response.text());
      }

      // Automatically sign in the user after registration
      const signInResult = await signIn("credentials", {
        email: values.email,
        password: values.password,
        redirect: false,
      });

      if (signInResult?.error) {
        throw new Error(signInResult.error);
      }

      toast.success("Account created successfully! Redirecting...");
      setIsSubmitted(true);

      // Redirect to dashboard after 2 seconds
      setTimeout(() => {
        router.push(callbackUrl);
      }, 2000);
      //eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (error: any) {
      clearTimeout(timeoutId);
      if (error.message.includes("already exists")) {
        form.setFocus("email");
        toast.error("This email is already registered", { id: toastId });
      } else if (error.message.includes("Validation failed")) {
        toast.error("Please check your registration details", { id: toastId });
      } else {
        toast.error(error.message || "Registration failed. Please try again.", {
          id: toastId,
        });
      }
    } finally {
      setIsLoading(false);
    }
  };

  if (isSubmitted) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="text-center space-y-6"
      >
        <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-emerald-100 dark:bg-emerald-900">
          <CheckCheck className="h-6 w-6 text-emerald-600 dark:text-emerald-400" />
        </div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-white">
          Registration Successful!
        </h3>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          You&apos;ll be redirected to your dashboard shortly
        </p>
      </motion.div>
    );
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        {/* Email Field */}
        <motion.div variants={itemVariants}>
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Email</FormLabel>
                <div className="relative">
                  <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                  <FormControl>
                    <Input
                      placeholder="<EMAIL>"
                      className="pl-10 bg-white dark:bg-gray-800"
                      {...field}
                    />
                  </FormControl>
                </div>
                <FormMessage />
              </FormItem>
            )}
          />
        </motion.div>

        {/* Mobile Field */}
        <motion.div variants={itemVariants}>
          <FormField
            control={form.control}
            name="mobile"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Mobile Number</FormLabel>
                <div className="relative">
                  <Smartphone className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                  <FormControl>
                    <Input
                      placeholder="+1234567890"
                      className="pl-10 bg-white dark:bg-gray-800"
                      {...field}
                    />
                  </FormControl>
                </div>
                <FormMessage />
              </FormItem>
            )}
          />
        </motion.div>

        {/* Password Field */}
        <motion.div variants={itemVariants}>
          <FormField
            control={form.control}
            name="password"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Password</FormLabel>
                <div className="relative">
                  <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                  <FormControl>
                    <Input
                      type={showPassword ? "text" : "password"}
                      placeholder="••••••••"
                      className="pl-10 pr-10 bg-white dark:bg-gray-800"
                      {...field}
                    />
                  </FormControl>
                  <button
                    type="button"
                    className="absolute right-3 top-1/2 transform -translate-y-1/2"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <EyeOff className="cursor-pointer h-5 w-5 text-gray-400 hover:text-gray-500" />
                    ) : (
                      <Eye className="cursor-pointer h-5 w-5 text-emerald-400 hover:text-gray-500" />
                    )}
                  </button>
                </div>
                <FormMessage />
                <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                  Use 8+ characters with uppercase, lowercase, numbers & symbols
                </p>
              </FormItem>
            )}
          />
        </motion.div>

        {/* Confirm Password Field */}
        <motion.div variants={itemVariants}>
          <FormField
            control={form.control}
            name="confirmPassword"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Confirm Password</FormLabel>
                <div className="relative">
                  <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                  <FormControl>
                    <Input
                      type={showConfirmPassword ? "text" : "password"}
                      placeholder="••••••••"
                      className="pl-10 pr-10 bg-white dark:bg-gray-800"
                      {...field}
                    />
                  </FormControl>
                  <button
                    type="button"
                    className="absolute right-3 top-1/2 transform -translate-y-1/2"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  >
                    {showConfirmPassword ? (
                      <EyeOff className="cursor-pointer h-5 w-5 text-gray-400 hover:text-gray-500" />
                    ) : (
                      <Eye className="cursor-pointer h-5 w-5 text-emerald-400 hover:text-gray-500" />
                    )}
                  </button>
                </div>
                <FormMessage />
              </FormItem>
            )}
          />
        </motion.div>

        {/* Terms Checkbox */}
        <motion.div variants={itemVariants}>
          <FormField
            control={form.control}
            name="terms"
            render={({ field }) => (
              <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md p-2">
                <FormControl>
                  <Checkbox
                    checked={field.value}
                    onCheckedChange={field.onChange}
                    className="border-gray-300 cursor-pointer dark:border-gray-600 data-[state=checked]:bg-emerald-600 data-[state=checked]:border-emerald-600"
                  />
                </FormControl>
                <div className="space-y-1 leading-none">
                  <FormLabel className="cursor-pointer text-sm font-normal">
                    I accept the{" "}
                    <Link
                      href="/terms"
                      className="text-emerald-600 dark:text-emerald-400 hover:underline"
                    >
                      Terms and Conditions
                    </Link>
                  </FormLabel>
                </div>
              </FormItem>
            )}
          />
        </motion.div>

        {/* Submit Button */}
        <motion.div variants={itemVariants}>
          <Button
            type="submit"
            disabled={isLoading || !form.formState.isValid}
            className="w-full cursor-pointer dark:text-gray-50 bg-emerald-600 hover:bg-emerald-700"
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Creating account...
              </>
            ) : (
              <>
                Sign Up
                <ArrowRight className="ml-2 h-4 w-4" />
              </>
            )}
          </Button>
        </motion.div>

        {/* Login Link */}
        <motion.div
          variants={itemVariants}
          className="text-center text-sm text-gray-600 dark:text-gray-400"
        >
          Already have an account?{" "}
          <Link
            href="/login"
            className="font-medium text-emerald-600 dark:text-emerald-400 hover:text-emerald-800 dark:hover:text-emerald-300"
          >
            Sign in
          </Link>
        </motion.div>
      </form>
    </Form>
  );
}
