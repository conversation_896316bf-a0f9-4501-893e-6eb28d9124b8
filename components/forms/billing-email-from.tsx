"use client";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { motion } from "framer-motion";
import { Mail, Check, RefreshCw } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { BillingEmailValues } from "@/types/types";
import { billingEmailSchema } from "@/validations/validations";
import toast from "react-hot-toast";
import { useEffect, useState } from "react";
import { useBillingEmail } from "@/hooks/use-billing-email";
import { ErrorDialog } from "../error/error-dialog";

interface BillingEmailFormProps {
  defaultEmail?: string;
  onSubmit: (values: BillingEmailValues) => Promise<void> | void;
  isSubmitting?: boolean;
  headerIcon?: React.ReactNode;
  headerTitle?: string;
  description?: string;
  error?: Error | null;
  onErrorDismiss?: () => void;
}

export function BillingEmailForm({
  defaultEmail = "",
  onSubmit,
  isSubmitting = false,
  headerIcon = <Mail className="h-5 w-5 text-amber-600 dark:text-amber-400" />,
  headerTitle = "Billing Email",
  description = "By default, invoices are sent to the team creator's email. Enter a different email below for billing communications.",
  error,
  onErrorDismiss,
}: BillingEmailFormProps) {
  const [errorDialogOpen, setErrorDialogOpen] = useState<boolean>(false);
  const { data, isLoading, updateError } = useBillingEmail();

  const form = useForm<BillingEmailValues>({
    resolver: zodResolver(billingEmailSchema),
    defaultValues: {
      billingEmail: defaultEmail,
    },
    mode: "onChange",
    reValidateMode: "onChange",
  });

  // Update form when data loads
  useEffect(() => {
    if (data?.billingEmail) {
      form.reset({ billingEmail: data.billingEmail });
    }
  }, [data, form]);

  // Handle submission errors
  useEffect(() => {
    if (updateError) {
      setErrorDialogOpen(true);
    }
  }, [updateError]);

  async function handleSubmit(values: BillingEmailValues) {
    try {
      await onSubmit(values);
    } catch (error) {
      console.log(error);
      toast.error("Failed to update billing email");
    }
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, delay: 0.3 }}
      className="rounded-xl border border-gray-200 bg-white shadow-sm dark:border-gray-700 dark:bg-gray-800"
    >
      <div className="p-6">
        <div className="flex items-center space-x-3 mb-6">
          <div className="p-2 rounded-full bg-amber-100 dark:bg-amber-900/30">
            {headerIcon}
          </div>
          <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-100">
            {headerTitle}
          </h2>
        </div>

        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleSubmit)}
            className="space-y-6 relative"
          >
            {/* Loading overlay */}
            {isLoading && (
              <div className="absolute inset-0 bg-gray-200 dark:bg-gray-800 bg-opacity-20 dark:bg-opacity-20 flex items-center justify-center rounded-lg z-10">
                <span className="flex gap-1 items-center">
                  <RefreshCw className="h-8 w-8 animate-spin text-emerald-600 dark:text-emerald-400" />
                  Loading...
                </span>
              </div>
            )}

            
            <p className="text-sm text-gray-600 dark:text-gray-300">
              {description}
            </p>

            <FormField
              control={form.control}
              name="billingEmail"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Billing Email Address</FormLabel>
                  <FormControl>
                    <Input
                      type="email"
                      placeholder="<EMAIL>"
                      required
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="mt-6 flex justify-end">
              <Button
                type="submit"
                disabled={isSubmitting}
                className="bg-emerald-600 hover:bg-emerald-700 dark:bg-emerald-700 dark:hover:bg-emerald-800 cursor-pointer dark:text-white"
              >
                {isSubmitting ? (
                  <>
                    <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Check className="mr-2 h-4 w-4" />
                    Save Email
                  </>
                )}
              </Button>
            </div>
          </form>
        </Form>

        <ErrorDialog
          open={errorDialogOpen}
          onOpenChange={(open) => {
            setErrorDialogOpen(open);
            if (!open && onErrorDismiss) {
              onErrorDismiss();
            }
          }}
          title="Submission Error"
          description={error?.message || "Failed to save billing information"}
        />
      </div>
    </motion.div>
  );
}
