"use client";

import { Alert, AlertDescription, AlertTitle } from "../ui/alert";
import { AlertTriangle, Loader2 } from "lucide-react";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "../ui/dialog";
import { But<PERSON> } from "../ui/button";

interface ErrorDialogProps {
  error: Error;
  open: boolean;
  isFetching?: boolean | undefined;
  retry: () => void;
}

const ErrorDialog = ({ error, open, retry, isFetching }: ErrorDialogProps) => {
  return (
    <Dialog open={open}>
      <DialogContent
        className="sm:max-w-md"
        onPointerDownOutside={(e) => e.preventDefault()} // Prevent closing by clicking outside
        onEscapeKeyDown={(e) => e.preventDefault()} // Prevent closing with ESC key
        onInteractOutside={(e) => e.preventDefault()}
      >
        <DialogHeader>
          <DialogTitle className="text-red-600 dark:text-red-400">
            Error Loading Teams
          </DialogTitle>
        </DialogHeader>

        <Alert
          variant="destructive"
          className="border-red-500 bg-red-50 dark:bg-red-900/10"
        >
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>
            {error.message || "Failed to load your teams. Please try again."}
          </AlertDescription>
        </Alert>

        <div className="flex justify-end space-x-2 pt-4">
          <Button
            variant="destructive"
            onClick={retry}
            disabled={isFetching}
            className="bg-red-600 hover:bg-red-700 cursor-pointer"
          >
            {isFetching ? (
              <span className="flex gap-1 items-center justify-center">
                <Loader2 className="w-4 h-5 animate-spin" /> Retrying...
              </span>
            ) : (
              <span className="flex gap-1 items-center justify-center">
                <AlertTriangle className="h-4 w-4 mr-2" />
                Retry
              </span>
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ErrorDialog;
