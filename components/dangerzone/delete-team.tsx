import { useState } from "react";
import { Trash2, Loader2 } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  Di<PERSON>Header,
  <PERSON>alog<PERSON><PERSON>le,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { DeleteTeamValues, Team } from "@/types/types";
import { createDeleteTeamSchema } from "@/validations/validations";
import toast from "react-hot-toast";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "../ui/form";

interface DangerZoneProps {
  team: Team;
  onDelete: () => Promise<void>;
  isDeleting?: boolean;
}

export function DangerZone({ team, onDelete, isDeleting }: DangerZoneProps) {
  const [isOpen, setIsOpen] = useState(false);
  const form = useForm<DeleteTeamValues>({
    resolver: zodResolver(createDeleteTeamSchema(team.name)),
  });

  async function handleDelete() {
    try {
      await onDelete();
      toast.success("Team deleted successfully");
      setIsOpen(false);
    } catch (error) {
      console.log("unknow error occureed during form subbmission:", error);
      toast.error("Failed to delete team");
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <button className="flex gap-1 items-center w-full cursor-pointer text-left px-3 py-1.5 text-sm text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 rounded">
          <Trash2 className="h-4 w-4" />
          Delete Team
        </button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Confirm Team Deletion</DialogTitle>
          <DialogDescription>
            To confirm, type{" "}
            <span className="font-bold">&quot;delete {team.name}&quot;</span> in
            the box below
          </DialogDescription>
        </DialogHeader>

        <div className="py-4">
          <Form {...form}>
            <form
              onSubmit={form.handleSubmit(handleDelete)}
              className="space-y-4 py-4"
            >
              <FormField
                control={form.control}
                name="confirmation"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Confirmation</FormLabel>
                    <FormControl>
                      <Input placeholder={`delete ${team.name}`} {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </form>
          </Form>
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => setIsOpen(false)}
            disabled={isDeleting}
            className="cursor-pointer"
          >
            Cancel
          </Button>
          <Button
            variant="destructive"
            onClick={form.handleSubmit(handleDelete)}
            className="cursor-pointer"
            disabled={!form.formState.isValid || isDeleting}
          >
            {isDeleting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Deleting...
              </>
            ) : (
              <>
                <Trash2 className="mr-2 h-4 w-4" />
                Delete Team
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
