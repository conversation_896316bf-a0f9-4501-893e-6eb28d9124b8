"use client";
import { useState } from "react";
import { Co<PERSON>, Check } from "lucide-react";
import { motion } from "framer-motion";

interface CopyButtonProps {
  textToCopy: string;
  buttonId: string;
  classNames?: string;
}

export const CopyButton = ({
  textToCopy,
  buttonId,
  classNames = "",
}: CopyButtonProps) => {
  const [isCopied, setIsCopied] = useState(false);

  const copyToClipboard = async (text: string) => {
    await navigator.clipboard.writeText(text);
    setIsCopied(true);
    setTimeout(() => setIsCopied(false), 2000);
  };

  return (
    <motion.button
      initial={{ scale: 1 }}
      whileHover={{
        scale: 1.05,
        boxShadow: "0 4px 8px rgba(0, 0, 0, 0.1)",
      }}
      whileTap={{
        scale: 0.98,
        boxShadow: "0 2px 4px rgba(0, 0, 0, 0.1)",
      }}
      animate={{
        backgroundColor: isCopied
          ? ["#059669", "#047857"] // emerald-700 to emerald-800
          : ["#10b981", "#059669"], // emerald-500 to emerald-600
      }}
      transition={{
        scale: { type: "spring", stiffness: 300, damping: 10 },
        backgroundColor: { duration: 0.3 },
        boxShadow: { duration: 0.2 },
      }}
      onClick={() => copyToClipboard(textToCopy)}
      className={`px-3 py-1 rounded-full flex items-center gap-1 cursor-pointer
        text-white text-sm font-medium ${classNames}`}
      aria-label={`Copy ${buttonId}`}
    >
      <motion.div
        key={isCopied ? "checked" : "copy"}
        initial={{ scale: 0.8, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.8, opacity: 0 }}
        transition={{ duration: 0.2 }}
      >
        {isCopied ? (
          <Check className="w-3 h-3" />
        ) : (
          <Copy className="w-3 h-3" />
        )}
      </motion.div>
      <motion.span
        initial={{ x: 0 }}
        animate={{ x: isCopied ? [0, -2, 0] : 0 }}
        transition={{ duration: 0.3 }}
      >
        {isCopied ? "Copied!" : "Copy"}
      </motion.span>
    </motion.button>
  );
};
