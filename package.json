{"name": "<PERSON><PERSON><PERSON>-next", "version": "0.1.35", "description": "Build a custom ChatGPT chatbot for your business. Embed it on your site to handle customer support, generate leads, and engage users. Free trial included!", "keywords": ["ChatGPT", "business chatbot", "AI chatbot", "customer support bot", "lead generation", "website chatbot", "GPT chatbot", "custom chatbot", "AI assistant", "live chat alternative"], "author": "<PERSON>", "license": "ISC", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "test": "echo \"No tests yet\" && exit 0", "lint": "next lint", "prepare": "husky install", "release": "dotenv -e .env -- release-it --ci"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@langchain/community": "^0.3.47", "@langchain/core": "^0.3.61", "@langchain/langgraph": "^0.3.5", "@langchain/openai": "^0.5.16", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.2.3", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.3.1", "@tanstack/react-query": "^5.76.1", "@types/node-cron": "^3.0.11", "axios": "^1.9.0", "bcryptjs": "^3.0.2", "bullmq": "^5.56.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cookies-next": "^5.1.0", "critters": "^0.0.23", "date-fns": "^4.1.0", "framer-motion": "^12.9.2", "gsap": "^3.12.7", "ioredis": "^5.6.1", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.503.0", "mongoose": "^8.14.3", "next": "15.3.1", "next-auth": "^4.24.11", "next-themes": "^0.4.6", "node-cron": "^4.2.0", "nodemailer": "^6.10.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.56.1", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-intersection-observer": "^9.16.0", "react-syntax-highlighter": "^15.6.1", "stripe": "^18.2.1", "tailwind-merge": "^3.2.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.3"}, "devDependencies": {"@commitlint/cli": "^19.8.0", "@commitlint/config-conventional": "^19.8.0", "@eslint/eslintrc": "^3", "@release-it/conventional-changelog": "^10.0.1", "@tailwindcss/postcss": "^4", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^20", "@types/nodemailer": "^6.4.17", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-syntax-highlighter": "^15.5.13", "dotenv-cli": "^8.0.0", "eslint": "^9", "eslint-config-next": "15.3.1", "husky": "^8.0.0", "release-it": "^19.0.1", "tailwindcss": "^4", "tw-animate-css": "^1.2.8", "typescript": "^5"}, "release-it": {"git": {"commitMessage": "chore: release v${version}"}, "github": {"release": true}, "npm": {"publish": false}, "plugins": {"@release-it/conventional-changelog": {"infile": "CHANGELOG.md", "preset": {"name": "conventionalcommits", "types": [{"type": "feat", "section": "Features"}, {"type": "fix", "section": "Bug Fixes"}, {}]}}}}}