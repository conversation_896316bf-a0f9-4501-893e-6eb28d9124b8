import { NextRequest, NextResponse } from 'next/server'

export interface SecurityConfig {
  contentSecurityPolicy: string
  strictTransportSecurity: string
  xFrameOptions: string
  xContentTypeOptions: string
  referrerPolicy: string
  permissionsPolicy: string
}

export const defaultSecurityConfig: SecurityConfig = {
  contentSecurityPolicy: [
    "default-src 'self'",
    "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://vercel.live https://va.vercel-scripts.com",
    "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
    "font-src 'self' https://fonts.gstatic.com",
    "img-src 'self' data: https: blob:",
    "media-src 'self' https:",
    "connect-src 'self' https://api.openai.com https://api.anthropic.com https://api.groq.com wss:",
    "frame-src 'self' https://vercel.live",
    "worker-src 'self' blob:",
    "child-src 'self' blob:",
    "object-src 'none'",
    "base-uri 'self'",
    "form-action 'self'",
    "frame-ancestors 'none'",
    "upgrade-insecure-requests"
  ].join('; '),
  
  strictTransportSecurity: 'max-age=31536000; includeSubDomains; preload',
  xFrameOptions: 'DENY',
  xContentTypeOptions: 'nosniff',
  referrerPolicy: 'strict-origin-when-cross-origin',
  permissionsPolicy: [
    'camera=()',
    'microphone=()',
    'geolocation=()',
    'interest-cohort=()'
  ].join(', ')
}

export function applySecurityHeaders(
  response: NextResponse,
  config: Partial<SecurityConfig> = {}
): NextResponse {
  const securityConfig = { ...defaultSecurityConfig, ...config }

  // Content Security Policy
  response.headers.set('Content-Security-Policy', securityConfig.contentSecurityPolicy)

  // Strict Transport Security
  response.headers.set('Strict-Transport-Security', securityConfig.strictTransportSecurity)

  // X-Frame-Options
  response.headers.set('X-Frame-Options', securityConfig.xFrameOptions)

  // X-Content-Type-Options
  response.headers.set('X-Content-Type-Options', securityConfig.xContentTypeOptions)

  // Referrer Policy
  response.headers.set('Referrer-Policy', securityConfig.referrerPolicy)

  // Permissions Policy
  response.headers.set('Permissions-Policy', securityConfig.permissionsPolicy)

  // Additional security headers
  response.headers.set('X-XSS-Protection', '1; mode=block')
  response.headers.set('X-DNS-Prefetch-Control', 'off')
  response.headers.set('X-Download-Options', 'noopen')
  response.headers.set('X-Permitted-Cross-Domain-Policies', 'none')

  return response
}

export function createSecurityMiddleware(config?: Partial<SecurityConfig>) {
  return function securityMiddleware(request: NextRequest) {
    const response = NextResponse.next()
    return applySecurityHeaders(response, config)
  }
}

// Rate limiting configuration
export interface RateLimitConfig {
  windowMs: number
  maxRequests: number
  skipSuccessfulRequests?: boolean
  skipFailedRequests?: boolean
  keyGenerator?: (request: NextRequest) => string
}

export const defaultRateLimits: Record<string, RateLimitConfig> = {
  api: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 100,
    skipSuccessfulRequests: false
  },
  auth: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 5,
    skipSuccessfulRequests: true
  },
  upload: {
    windowMs: 60 * 60 * 1000, // 1 hour
    maxRequests: 10,
    skipSuccessfulRequests: false
  },
  webhook: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 60,
    skipSuccessfulRequests: false
  }
}

// Input validation and sanitization
export function sanitizeInput(input: string): string {
  return input
    .replace(/[<>]/g, '') // Remove potential HTML tags
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+=/gi, '') // Remove event handlers
    .trim()
}

export function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email) && email.length <= 254
}

export function validateUrl(url: string): boolean {
  try {
    const parsedUrl = new URL(url)
    return ['http:', 'https:'].includes(parsedUrl.protocol)
  } catch {
    return false
  }
}

// CSRF protection
export function generateCSRFToken(): string {
  return require('crypto').randomBytes(32).toString('hex')
}

export function validateCSRFToken(token: string, sessionToken: string): boolean {
  return token === sessionToken && token.length === 64
}

// API key validation
export function validateApiKey(apiKey: string, type: 'openai' | 'anthropic' | 'groq'): boolean {
  const patterns = {
    openai: /^sk-[a-zA-Z0-9]{48}$/,
    anthropic: /^sk-ant-[a-zA-Z0-9\-_]{95}$/,
    groq: /^gsk_[a-zA-Z0-9]{52}$/
  }
  
  return patterns[type]?.test(apiKey) || false
}

// Encryption key validation
export function validateEncryptionKey(key: string): boolean {
  try {
    const decoded = Buffer.from(key, 'base64')
    return decoded.length === 32 // 256 bits
  } catch {
    return false
  }
}

// Password strength validation
export function validatePasswordStrength(password: string): {
  isValid: boolean
  score: number
  feedback: string[]
} {
  const feedback: string[] = []
  let score = 0

  if (password.length >= 8) score += 1
  else feedback.push('Password must be at least 8 characters long')

  if (/[a-z]/.test(password)) score += 1
  else feedback.push('Password must contain lowercase letters')

  if (/[A-Z]/.test(password)) score += 1
  else feedback.push('Password must contain uppercase letters')

  if (/\d/.test(password)) score += 1
  else feedback.push('Password must contain numbers')

  if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) score += 1
  else feedback.push('Password must contain special characters')

  if (password.length >= 12) score += 1

  return {
    isValid: score >= 4,
    score,
    feedback
  }
}

// Environment-specific security configurations
export function getEnvironmentSecurityConfig(): Partial<SecurityConfig> {
  const isDevelopment = process.env.NODE_ENV === 'development'
  const isProduction = process.env.NODE_ENV === 'production'

  if (isDevelopment) {
    return {
      contentSecurityPolicy: [
        "default-src 'self'",
        "script-src 'self' 'unsafe-inline' 'unsafe-eval'",
        "style-src 'self' 'unsafe-inline'",
        "img-src 'self' data: https:",
        "connect-src 'self' ws: wss:",
        "font-src 'self' data:",
      ].join('; ')
    }
  }

  if (isProduction) {
    return {
      contentSecurityPolicy: [
        "default-src 'self'",
        "script-src 'self'",
        "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
        "font-src 'self' https://fonts.gstatic.com",
        "img-src 'self' data: https:",
        "connect-src 'self' https:",
        "frame-ancestors 'none'",
        "base-uri 'self'",
        "object-src 'none'"
      ].join('; ')
    }
  }

  return {}
}
