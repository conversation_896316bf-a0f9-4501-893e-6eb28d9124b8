# Testing Guide

This document outlines the testing strategy and practices for the Chatzuri Agents platform.

## 🧪 Testing Stack

- **Unit/Integration Tests**: Jest + React Testing Library
- **E2E Tests**: Playwright
- **Component Testing**: React Testing Library
- **API Testing**: Jest + <PERSON><PERSON> (Mock Service Worker)
- **Performance Testing**: Lighthouse CI

## 📁 Test Structure

```
frontend/
├── __tests__/                 # Global test utilities
├── components/
│   └── **/__tests__/         # Component tests
├── hooks/
│   └── __tests__/            # Hook tests
├── lib/
│   └── **/__tests__/         # Service/utility tests
├── app/
│   └── **/__tests__/         # Page and API route tests
├── e2e/                      # End-to-end tests
│   ├── global-setup.ts
│   ├── global-teardown.ts
│   └── *.spec.ts
└── test-utils/               # Shared test utilities
```

## 🚀 Running Tests

### Unit and Integration Tests

```bash
# Run all tests
npm run test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage

# Run specific test suites
npm run test:credentials
npm run test:hooks
npm run test:components
```

### End-to-End Tests

```bash
# Install Playwright browsers
npm run test:setup

# Run E2E tests
npm run test:e2e

# Run E2E tests with UI
npm run test:e2e:ui

# Run E2E tests in headed mode
npm run test:e2e:headed

# Debug E2E tests
npm run test:e2e:debug
```

## 📋 Test Categories

### 1. Unit Tests

Test individual functions, components, and modules in isolation.

**Examples:**
- Credential encryption/decryption
- Utility functions
- Component rendering
- Hook behavior

**Location:** `**/__tests__/*.test.ts(x)`

### 2. Integration Tests

Test interactions between multiple components or services.

**Examples:**
- API route handlers
- Database operations
- Service integrations
- Component interactions

**Location:** `**/__tests__/*.integration.test.ts(x)`

### 3. End-to-End Tests

Test complete user workflows from browser perspective.

**Examples:**
- User authentication flow
- Credential management workflow
- Workflow builder interactions
- Team management

**Location:** `e2e/*.spec.ts`

## 🔧 Test Utilities

### Custom Render Function

```typescript
// test-utils/render.tsx
import { render, RenderOptions } from '@testing-library/react'
import { ReactElement } from 'react'
import { ThemeProvider } from 'next-themes'

const AllTheProviders = ({ children }: { children: React.ReactNode }) => {
  return (
    <ThemeProvider attribute="class" defaultTheme="light">
      {children}
    </ThemeProvider>
  )
}

const customRender = (
  ui: ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>
) => render(ui, { wrapper: AllTheProviders, ...options })

export * from '@testing-library/react'
export { customRender as render }
```

### Mock Factories

```typescript
// test-utils/factories.ts
export const createMockCredential = (overrides = {}) => ({
  id: 'cred-123',
  name: 'Test Credential',
  appType: 'openai',
  isActive: true,
  teamId: 'team-123',
  createdAt: new Date(),
  updatedAt: new Date(),
  ...overrides
})

export const createMockIntegration = (overrides = {}) => ({
  id: 'openai',
  name: 'OpenAI',
  icon: '🤖',
  color: '#10A37F',
  category: 'ai_services',
  ...overrides
})
```

## 📊 Coverage Requirements

- **Statements**: 70%
- **Branches**: 70%
- **Functions**: 70%
- **Lines**: 70%

### Critical Components (90%+ coverage required):
- Credential encryption/decryption
- Authentication logic
- Payment processing
- Security-related functions

## 🎯 Testing Best Practices

### 1. Test Structure (AAA Pattern)

```typescript
test('should create credential successfully', async () => {
  // Arrange
  const mockCredentials = { api_key: 'test-key' }
  const createRequest = { name: 'Test', appType: 'openai', credentials: mockCredentials }
  
  // Act
  const result = await credentialManager.createCredential('team-123', 'user-123', createRequest)
  
  // Assert
  expect(result).toMatchObject({
    name: 'Test',
    appType: 'openai',
    isActive: true
  })
})
```

### 2. Component Testing

```typescript
test('should render credential selector with options', async () => {
  const user = userEvent.setup()
  const onChange = jest.fn()
  
  render(
    <CredentialSelector 
      teamId="team-123" 
      onChange={onChange} 
    />
  )
  
  await user.click(screen.getByRole('combobox'))
  
  expect(screen.getByText('OpenAI Production')).toBeInTheDocument()
  
  await user.click(screen.getByText('OpenAI Production'))
  
  expect(onChange).toHaveBeenCalledWith('cred-1')
})
```

### 3. API Route Testing

```typescript
test('POST /api/teams/[teamId]/credentials should create credential', async () => {
  const request = new NextRequest('http://localhost/api/teams/team-123/credentials', {
    method: 'POST',
    body: JSON.stringify({
      name: 'Test Credential',
      appType: 'openai',
      credentials: { api_key: 'test-key' }
    })
  })
  
  const response = await POST(request, { params: { teamId: 'team-123' } })
  
  expect(response.status).toBe(201)
  
  const data = await response.json()
  expect(data.name).toBe('Test Credential')
})
```

### 4. E2E Testing

```typescript
test('should create and use credential in workflow', async ({ page }) => {
  // Create credential
  await page.goto('/dashboard/team/test-team/credentials')
  await page.click('[data-testid="add-credential-button"]')
  await page.fill('[data-testid="credential-name"]', 'E2E Test Credential')
  await page.fill('[data-testid="api-key-input"]', 'sk-test123')
  await page.click('[data-testid="save-credential-button"]')
  
  // Use in workflow
  await page.goto('/dashboard/team/test-team/workflows/new')
  await page.click('[data-testid="add-ai-agent-node"]')
  await page.selectOption('[data-testid="credential-selector"]', 'E2E Test Credential')
  await page.click('[data-testid="save-workflow"]')
  
  await expect(page.getByText('Workflow saved successfully')).toBeVisible()
})
```

## 🔍 Debugging Tests

### Jest Debugging

```bash
# Debug specific test
npm run test -- --testNamePattern="should create credential" --verbose

# Run single test file
npm run test -- credentials.test.ts

# Debug with Node inspector
node --inspect-brk node_modules/.bin/jest --runInBand
```

### Playwright Debugging

```bash
# Debug mode with browser
npm run test:e2e:debug

# Run with trace viewer
npm run test:e2e -- --trace on

# Show test report
npm run test:e2e:report
```

## 📈 Continuous Integration

### GitHub Actions Example

```yaml
name: Tests
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run unit tests
        run: npm run test:ci
      
      - name: Run E2E tests
        run: npm run test:e2e
        env:
          PLAYWRIGHT_BASE_URL: http://localhost:3000
      
      - name: Upload coverage
        uses: codecov/codecov-action@v3
```

## 🚨 Common Issues

### 1. Test Timeouts
- Increase timeout for async operations
- Use `waitFor` for DOM updates
- Mock slow external services

### 2. Flaky Tests
- Avoid hardcoded delays
- Use proper waiting strategies
- Isolate test data

### 3. Memory Leaks
- Clear mocks after each test
- Cleanup event listeners
- Close database connections

## 📚 Resources

- [Jest Documentation](https://jestjs.io/docs/getting-started)
- [React Testing Library](https://testing-library.com/docs/react-testing-library/intro/)
- [Playwright Documentation](https://playwright.dev/docs/intro)
- [Testing Best Practices](https://kentcdodds.com/blog/common-mistakes-with-react-testing-library)
