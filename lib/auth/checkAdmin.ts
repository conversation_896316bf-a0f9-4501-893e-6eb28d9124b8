import { UserModel } from "@/models";

/**
 * Check if a user has admin or global admin privileges.
 * @param userId The ID of the user to verify.
 * @returns The user object if admin, otherwise null.
 */
export const checkIfAdmin = async (userId: string) => {
  if (!userId) return null;

  const user = await UserModel.findById(userId).select("isAdmin isGlobalAdmin");
  if (!user || (!user.isAdmin && !user.isGlobalAdmin)) {
    return null;
  }

  return user;
};
