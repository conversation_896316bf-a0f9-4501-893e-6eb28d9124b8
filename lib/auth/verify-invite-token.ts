import { verify } from "jsonwebtoken";
import TeamInvitationModel from "@/models/team-invitation";
import dbConnect from "@/lib/db/db";
import { createHash } from "crypto";
import { StatusEnum } from "@/enums/enums";

export async function verifyInviteToken(token: string, email: string) {
  await dbConnect();

  const tokenHash = createHash("sha256").update(token).digest("hex");
  const invitation = await TeamInvitationModel.findOne({
    tokenHash,
    email,
    status: StatusEnum.PENDING,
    expiresAt: { $gt: new Date() },
  });

  if (!invitation) return false;

  try {
    const emailHash = createHash("sha256")
      .update(email)
      .digest("hex")
      .substring(0, 32);
    const secret = process.env.INVITE_TOKEN_SECRET! + emailHash;

    const decoded = verify(token, secret) as {
      email: string;
      teamId: string;
      exp: number;
    };

    return (
      decoded.email === email && decoded.teamId === invitation.teamId.toString()
    );
  } catch {
    return false;
  }
}
