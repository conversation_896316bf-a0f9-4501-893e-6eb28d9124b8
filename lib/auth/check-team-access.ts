import dbConnect from "@/lib/db/db";
import { TeamModel, MemberModel } from "@/models";
import { RoleEnum, StatusEnum } from "@/enums/enums";

export async function checkTeamAccess(
  userId: string,
  teamId: string
): Promise<boolean> {
  await dbConnect();

  try {
    const directTeamAccess = await TeamModel.findOne({
      _id: teamId,
      $or: [{ ownerId: userId }, { admins: userId }],
    }).lean();

    if (directTeamAccess) return true;

    const memberAccess = await MemberModel.findOne({
      teamId,
      userId,
      role: RoleEnum.ADMIN,
      status: StatusEnum.ACCEPTED,
    }).lean();

    return !!memberAccess;
  } catch (error) {
    console.error("Error checking team access:", error);
    return false;
  }
}
