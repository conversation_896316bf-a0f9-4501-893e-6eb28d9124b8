import { NextAuthOptions, Session } from "next-auth";
import { User } from "next-auth";
import CredentialsProvider from "next-auth/providers/credentials";
import GoogleProvider from "next-auth/providers/google";
import { comparePassword, hashPassword } from "@/utils/hash-password";
import { UserModel } from "@/models";
import type { JWT } from "next-auth/jwt";
import { adminEmails } from "@/config/admin-emails";
import { PRODUCTION_URL, DEVELOPMENT_URL } from "@/utils/site/defaults";
import { Provider, Role } from "@/enums/enums";
import { capitalizeFirstLetter, currentYear } from "@/utils/functions";
import dbConnect from "../db/db";

export const authOptions: NextAuthOptions = {
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
      authorization: {
        params: {
          prompt: "consent",
          access_type: "offline",
          response_type: "code",
          scope: "openid email profile",
          redirect_uri:
            process.env.NODE_ENV === "production"
              ? PRODUCTION_URL
              : DEVELOPMENT_URL + "/api/auth/callback/google",
        },
      },
      profile(profile) {
        const isAdmin = adminEmails.includes(profile.email);
        return {
          id: profile.sub,
          name: profile.name || profile.given_name + " " + profile.family_name,
          email: profile.email,
          image: profile.picture,
          provider: Provider.GOOGLE,
          role: isAdmin ? Role.ADMIN : Role.USER,
          canLogin: true,
        } as User;
      },
    }),
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials, req) {
        if (!credentials?.email || !credentials?.password) {
          throw new Error("Please provide both email and password");
        }

        try {
          await dbConnect();
          const user = await UserModel.findOne({
            email: credentials.email.toLowerCase(),
          }).select("+password +passwordSalt");

          if (!user || !user.password) {
            throw new Error("Invalid credentials");
          }

          // Check if user can login
          if (!user.canLogin) {
            throw new Error("AccountDisabled");
          }

          const isMatch = await comparePassword(
            credentials.password,
            user.password
          );
          if (!isMatch) {
            throw new Error("Invalid credentials");
          }

          // Update last login info
          user.lastLogin = new Date();
          user.lastLoginIp = req.headers?.["x-forwarded-for"] as string;
          await user.save();

          return {
            id: user.id,
            name: user.name || undefined,
            email: user.email,
            image: user.image || undefined,
            role: user.role,
            provider: user.provider,
            canLogin: user.canLogin,
            isAdmin: user.role === Role.ADMIN,
            isGlobalAdmin: false,
          };
        } catch (error) {
          if (error instanceof Error && error.message === "AccountDisabled") {
            const url = new URL("/disabled", process.env.NEXTAUTH_URL);
            throw new Error(
              JSON.stringify({
                message:
                  "This account is disabled. Contact system admin for activation",
                url: url.toString(),
              })
            );
          }
          if (process.env.NODE_ENV === "production") {
            throw new Error("Invalid credentials");
          }
          throw error;
        }
      },
    }),
  ],
  session: {
    strategy: "jwt",
    maxAge: 24 * 60 * 60, // 1 day
  },
  secret: process.env.NEXTAUTH_SECRET!,
  debug: process.env.NODE_ENV === "development",
  callbacks: {
    async signIn({ user, account, profile }): Promise<string | boolean> {
      if (account?.provider === "google") {
        try {
          const email = user.email?.toLowerCase() || "";
          const isAdmin = adminEmails.includes(email);

          await dbConnect();
          let dbUser = await UserModel.findOne({ email });

          if (dbUser) {
            if (dbUser.provider !== "google") {
              return `/error?error=OAuthAccountNotLinked&message=This email is already registered with password. Please sign in with your password first, then you can link your Google account in account settings.`;
            }

            // Check if user can login
            if (!dbUser.canLogin) {
              throw new Error("AccountDisabled");
            }

            user.id = dbUser.id;
            user.role = dbUser.role;
          } else {
            // New Google user - create account
            const generatedPassword = isAdmin
              ? `Admin@${currentYear}`
              : `${capitalizeFirstLetter(
                  user.email?.split("@")[0] || ""
                )}@${currentYear}`;

            const hashed = await hashPassword(generatedPassword);

            dbUser = new UserModel({
              name: user.name || profile?.name || "Unknown",
              email,
              billingEmail: email, 
              image: profile?.image || user.image,
              provider: account.provider,
              role: isAdmin ? Role.ADMIN : Role.USER,
              password: hashed,
              canLogin: true,
              lastLogin: new Date(),
              lastLoginIp: account?.providerAccountId || "unknown",
            });

            await dbUser.save();
            user.id = dbUser.id;
            user.role = dbUser.role;
          }
          return true;
        } catch (error) {
          console.error("Google sign-in error:", error);
          if (error instanceof Error && error.message === "AccountDisabled") {
            return `/error?error=AccountDisabled&message=This account is disabled. Contact system admin for activation`;
          }
          return false;
        }
      }
      return true;
    },
    async session({ session, token }: { session: Session; token: JWT }) {
      if (token?.user) {
        session.user = {
          ...session.user,
          id: token.user.id,
          name: token.user.name || session.user?.name,
          email: token.user.email || session.user?.email,
          image: token.user.image || session.user?.image,
          role: token.user.role || Role.USER,
          provider: token.user.provider,
          canLogin: token.user.canLogin,
        };
      }
      return session;
    },
    async jwt({ token, user }) {
      // Initial sign in
      if (user) {
        token.user = {
          id: user.id,
          role: user.role,
          canLogin: user.canLogin,
          name: user.name ?? undefined,
          email: user.email ?? undefined,
          image: user.image ?? undefined,
          provider: user.provider,
          isAdmin: user.role === Role.ADMIN,
          isGlobalAdmin: false,
        };
      }
      return token;
    },
  },
  pages: {
    signIn: "/login",
    error: "/error",
  },
  events: {
    async signIn({ user }) {
      try {
        await dbConnect();
        // Update last login info in user document
        await UserModel.findByIdAndUpdate(user.id, {
          lastLogin: new Date(),
          $inc: { loginCount: 1 },
        });
      } catch (error) {
        console.error("Error updating login info:", error);
      }
    },
    async createUser({ user }) {
      console.log(`User created: ${user.email}`);
    },
  },
};
