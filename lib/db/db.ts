import mongoose from 'mongoose';

let isConnected = false;

const MONGODB_URI = process.env.MONGODB_URI as string;
const MONGODB_DATABASE_NAME = process.env.MONGODB_DATABASE_NAME as string;
const NODE_ENV = process.env.NODE_ENV as string;

if (!MONGODB_URI) {
  throw new Error('Please define the MONGODB_URI environment variable in .env.local');
}

export default async function dbConnect(): Promise<void> {
  if (isConnected) return;

  try {
    // mongoose.set('debug', NODE_ENV !== 'production');
    
    const connection = await mongoose.connect(MONGODB_URI, {
      dbName: MONGODB_DATABASE_NAME,
    });

    isConnected = connection.connections[0].readyState === 1;

    if (NODE_ENV !== 'production') {
      if (isConnected) {
        console.log('MongoDB connected');
      } else {
        console.log('MongoDB connection failed');
      }
    }
  } catch (error) {
    if (NODE_ENV !== 'production') {
      console.error('MongoDB connection error:', error);
    } else {
      console.error('MongoDB connection error occurred');
    }
    throw error;
  }
}