// Integration Registry for Managing Available Integrations

import { BaseIntegration } from './base-integration';
import { AppType, Integration } from '../types/integration-types';

// Import specific integrations
import { OpenAIIntegration } from '../ai-services/openai';
import { GroqIntegration } from '../ai-services/groq';
import { AnthropicIntegration } from '../ai-services/anthropic';
import { GoogleGeminiIntegration } from '../ai-services/google-gemini';
import { AirtableIntegration } from '../business-apps/airtable';
import { SlackIntegration } from '../business-apps/slack';

export class IntegrationRegistry {
  private static integrations: Map<AppType, BaseIntegration> = new Map();
  private static initialized = false;

  /**
   * Initialize the registry with all available integrations
   */
  static initialize(): void {
    if (this.initialized) return;

    // AI Services
    this.register(new OpenAIIntegration());
    this.register(new GroqIntegration());
    this.register(new AnthropicIntegration());
    this.register(new GoogleGeminiIntegration());

    // Business Apps
    this.register(new AirtableIntegration());
    this.register(new SlackIntegration());

    this.initialized = true;
    console.log(`🔌 Initialized ${this.integrations.size} integrations`);
  }

  /**
   * Register a new integration
   */
  static register(integration: BaseIntegration): void {
    this.integrations.set(integration.id, integration);
    console.log(`✅ Registered integration: ${integration.name}`);
  }

  /**
   * Get integration by app type
   */
  static getIntegration(appType: AppType): BaseIntegration | null {
    this.initialize();
    return this.integrations.get(appType) || null;
  }

  /**
   * Get all integrations
   */
  static getAllIntegrations(): BaseIntegration[] {
    this.initialize();
    return Array.from(this.integrations.values());
  }

  /**
   * Get integrations by category
   */
  static getIntegrationsByCategory(category: Integration['category']): BaseIntegration[] {
    this.initialize();
    return Array.from(this.integrations.values())
      .filter(integration => integration.category === category);
  }

  /**
   * Check if integration is supported
   */
  static isSupported(appType: AppType): boolean {
    this.initialize();
    return this.integrations.has(appType);
  }

  /**
   * Get integration metadata for UI
   */
  static getIntegrationMetadata(): Integration[] {
    this.initialize();
    return Array.from(this.integrations.values()).map(integration => ({
      id: integration.id,
      name: integration.name,
      description: integration.description,
      category: integration.category,
      icon: integration.icon,
      color: integration.color,
      website: integration.website,
      documentation: integration.documentation,
      requiredFields: integration.getRequiredFields(),
      actions: integration.getAvailableActions(),
      isActive: true
    }));
  }

  /**
   * Get AI service integrations
   */
  static getAIServices(): BaseIntegration[] {
    return this.getIntegrationsByCategory('ai_services');
  }

  /**
   * Get business app integrations
   */
  static getBusinessApps(): BaseIntegration[] {
    return this.getIntegrationsByCategory('business_apps');
  }

  /**
   * Search integrations by name or description
   */
  static searchIntegrations(query: string): BaseIntegration[] {
    this.initialize();
    const lowerQuery = query.toLowerCase();
    
    return Array.from(this.integrations.values())
      .filter(integration => 
        integration.name.toLowerCase().includes(lowerQuery) ||
        integration.description.toLowerCase().includes(lowerQuery)
      );
  }

  /**
   * Get integration statistics
   */
  static getStatistics(): {
    total: number;
    byCategory: Record<string, number>;
    aiServices: number;
    businessApps: number;
  } {
    this.initialize();
    const integrations = Array.from(this.integrations.values());
    
    const byCategory: Record<string, number> = {};
    integrations.forEach(integration => {
      byCategory[integration.category] = (byCategory[integration.category] || 0) + 1;
    });

    return {
      total: integrations.length,
      byCategory,
      aiServices: byCategory.ai_services || 0,
      businessApps: byCategory.business_apps || 0
    };
  }

  /**
   * Validate integration configuration
   */
  static validateIntegration(appType: AppType): {
    isValid: boolean;
    errors: string[];
  } {
    const integration = this.getIntegration(appType);
    const errors: string[] = [];

    if (!integration) {
      errors.push(`Integration not found: ${appType}`);
      return { isValid: false, errors };
    }

    // Validate required methods
    const requiredMethods = [
      'getRequiredFields',
      'getAvailableActions', 
      'validateCredentials',
      'executeAction'
    ];

    for (const method of requiredMethods) {
      if (typeof (integration as any)[method] !== 'function') {
        errors.push(`Missing required method: ${method}`);
      }
    }

    // Validate required fields
    try {
      const fields = integration.getRequiredFields();
      if (!Array.isArray(fields)) {
        errors.push('getRequiredFields must return an array');
      }
    } catch (error) {
      errors.push(`Error calling getRequiredFields: ${error}`);
    }

    // Validate actions
    try {
      const actions = integration.getAvailableActions();
      if (!Array.isArray(actions)) {
        errors.push('getAvailableActions must return an array');
      }
    } catch (error) {
      errors.push(`Error calling getAvailableActions: ${error}`);
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Get integration health status
   */
  static async getHealthStatus(): Promise<{
    healthy: AppType[];
    unhealthy: AppType[];
    errors: Record<AppType, string>;
  }> {
    this.initialize();
    const healthy: AppType[] = [];
    const unhealthy: AppType[] = [];
    const errors: Record<AppType, string> = {};

    for (const [appType, integration] of this.integrations) {
      try {
        const validation = this.validateIntegration(appType);
        if (validation.isValid) {
          healthy.push(appType);
        } else {
          unhealthy.push(appType);
          errors[appType] = validation.errors.join(', ');
        }
      } catch (error) {
        unhealthy.push(appType);
        errors[appType] = error instanceof Error ? error.message : 'Unknown error';
      }
    }

    return { healthy, unhealthy, errors };
  }

  /**
   * Reset registry (for testing)
   */
  static reset(): void {
    this.integrations.clear();
    this.initialized = false;
  }
}
