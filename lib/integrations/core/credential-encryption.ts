// Credential Encryption Service for Secure Storage

import crypto from 'crypto';
import { EncryptedData, EncryptionConfig, CredentialData } from '../types/integration-types';

export class CredentialEncryption {
  private static readonly CONFIG: EncryptionConfig = {
    algorithm: 'aes-256-gcm',
    keyDerivation: 'pbkdf2',
    iterations: 100000,
    saltLength: 32,
    ivLength: 16
  };

  /**
   * Get encryption key from environment
   */
  private static getEncryptionKey(): string {
    const key = process.env.CREDENTIAL_ENCRYPTION_KEY;
    if (!key) {
      throw new Error('CREDENTIAL_ENCRYPTION_KEY environment variable is required');
    }
    return key;
  }

  /**
   * Derive key from master key and salt using PBKDF2
   */
  private static deriveKey(masterKey: string, salt: Buffer): Buffer {
    return crypto.pbkdf2Sync(
      masterKey,
      salt,
      this.CONFIG.iterations,
      32, // 256 bits
      'sha256'
    );
  }

  /**
   * Encrypt credential data
   */
  static encrypt(data: CredentialData): string {
    try {
      const masterKey = this.getEncryptionKey();
      const salt = crypto.randomBytes(this.CONFIG.saltLength);
      const iv = crypto.randomBytes(this.CONFIG.ivLength);
      const key = this.deriveKey(masterKey, salt);

      // Convert data to JSON string
      const plaintext = JSON.stringify(data);

      // Create cipher
      const cipher = crypto.createCipherGCM(this.CONFIG.algorithm, key, iv);
      
      // Encrypt data
      let encrypted = cipher.update(plaintext, 'utf8', 'hex');
      encrypted += cipher.final('hex');
      
      // Get authentication tag
      const tag = cipher.getAuthTag();

      // Create encrypted data object
      const encryptedData: EncryptedData = {
        data: encrypted,
        iv: iv.toString('hex'),
        salt: salt.toString('hex'),
        tag: tag.toString('hex')
      };

      // Return base64 encoded encrypted data
      return Buffer.from(JSON.stringify(encryptedData)).toString('base64');

    } catch (error) {
      console.error('Encryption failed:', error);
      throw new Error('Failed to encrypt credential data');
    }
  }

  /**
   * Decrypt credential data
   */
  static decrypt(encryptedString: string): CredentialData {
    try {
      const masterKey = this.getEncryptionKey();

      // Decode base64 and parse encrypted data
      const encryptedData: EncryptedData = JSON.parse(
        Buffer.from(encryptedString, 'base64').toString('utf8')
      );

      // Convert hex strings back to buffers
      const salt = Buffer.from(encryptedData.salt, 'hex');
      const iv = Buffer.from(encryptedData.iv, 'hex');
      const tag = Buffer.from(encryptedData.tag, 'hex');
      const key = this.deriveKey(masterKey, salt);

      // Create decipher
      const decipher = crypto.createDecipherGCM(this.CONFIG.algorithm, key, iv);
      decipher.setAuthTag(tag);

      // Decrypt data
      let decrypted = decipher.update(encryptedData.data, 'hex', 'utf8');
      decrypted += decipher.final('utf8');

      // Parse JSON and return
      return JSON.parse(decrypted);

    } catch (error) {
      console.error('Decryption failed:', error);
      throw new Error('Failed to decrypt credential data');
    }
  }

  /**
   * Generate a new encryption key (for setup)
   */
  static generateEncryptionKey(): string {
    return crypto.randomBytes(32).toString('base64');
  }

  /**
   * Validate encryption key format
   */
  static validateEncryptionKey(key: string): boolean {
    try {
      const buffer = Buffer.from(key, 'base64');
      return buffer.length === 32; // 256 bits
    } catch {
      return false;
    }
  }

  /**
   * Test encryption/decryption with sample data
   */
  static testEncryption(): boolean {
    try {
      const testData: CredentialData = {
        api_key: 'test-key-123',
        secret: 'test-secret-456'
      };

      const encrypted = this.encrypt(testData);
      const decrypted = this.decrypt(encrypted);

      return JSON.stringify(testData) === JSON.stringify(decrypted);
    } catch {
      return false;
    }
  }

  /**
   * Securely compare two credential objects
   */
  static secureCompare(a: CredentialData, b: CredentialData): boolean {
    try {
      const aStr = JSON.stringify(a);
      const bStr = JSON.stringify(b);
      
      if (aStr.length !== bStr.length) {
        return false;
      }

      let result = 0;
      for (let i = 0; i < aStr.length; i++) {
        result |= aStr.charCodeAt(i) ^ bStr.charCodeAt(i);
      }

      return result === 0;
    } catch {
      return false;
    }
  }

  /**
   * Hash credential data for comparison (without decryption)
   */
  static hashCredentials(data: CredentialData): string {
    const dataString = JSON.stringify(data);
    return crypto.createHash('sha256').update(dataString).digest('hex');
  }

  /**
   * Sanitize credential data for logging (remove sensitive fields)
   */
  static sanitizeForLogging(data: CredentialData): Record<string, any> {
    const sensitiveFields = [
      'api_key', 'secret', 'password', 'token', 'access_token', 
      'refresh_token', 'private_key', 'client_secret'
    ];

    const sanitized: Record<string, any> = {};
    
    for (const [key, value] of Object.entries(data)) {
      if (sensitiveFields.some(field => key.toLowerCase().includes(field))) {
        sanitized[key] = value ? `***${value.slice(-4)}` : null;
      } else {
        sanitized[key] = value;
      }
    }

    return sanitized;
  }

  /**
   * Rotate encryption (re-encrypt with new key)
   */
  static rotateEncryption(encryptedString: string, newKey: string): string {
    // Temporarily store old key
    const oldKey = process.env.CREDENTIAL_ENCRYPTION_KEY;
    
    try {
      // Decrypt with old key
      const data = this.decrypt(encryptedString);
      
      // Set new key
      process.env.CREDENTIAL_ENCRYPTION_KEY = newKey;
      
      // Encrypt with new key
      const newEncrypted = this.encrypt(data);
      
      return newEncrypted;
    } finally {
      // Restore old key
      if (oldKey) {
        process.env.CREDENTIAL_ENCRYPTION_KEY = oldKey;
      }
    }
  }
}
