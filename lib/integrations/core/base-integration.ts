// Base Integration Class for Credential Management

import { 
  CredentialField, 
  Action, 
  CredentialData, 
  CredentialValidationResult,
  AppType 
} from '../types/integration-types';

export abstract class BaseIntegration {
  abstract readonly id: AppType;
  abstract readonly name: string;
  abstract readonly description: string;
  abstract readonly category: 'ai_services' | 'business_apps' | 'databases' | 'communication' | 'storage';
  abstract readonly icon: string;
  abstract readonly color: string;
  abstract readonly website?: string;
  abstract readonly documentation?: string;

  /**
   * Get required credential fields for this integration
   */
  abstract getRequiredFields(): CredentialField[];

  /**
   * Get available actions for this integration
   */
  abstract getAvailableActions(): Action[];

  /**
   * Validate credentials format and connectivity
   */
  abstract validateCredentials(credentials: CredentialData): Promise<CredentialValidationResult>;

  /**
   * Execute an action with the given parameters
   */
  abstract executeAction(action: string, params: any, credentials: CredentialData): Promise<any>;

  /**
   * Test connection with credentials
   */
  async testConnection(credentials: CredentialData): Promise<boolean> {
    try {
      const validation = await this.validateCredentials(credentials);
      return validation.isValid && (validation.connectionTest?.success ?? false);
    } catch (error) {
      console.error(`Connection test failed for ${this.name}:`, error);
      return false;
    }
  }

  /**
   * Get credential field by name
   */
  getField(fieldName: string): CredentialField | undefined {
    return this.getRequiredFields().find(field => field.name === fieldName);
  }

  /**
   * Validate individual field
   */
  validateField(fieldName: string, value: any): { isValid: boolean; error?: string } {
    const field = this.getField(fieldName);
    if (!field) {
      return { isValid: false, error: `Unknown field: ${fieldName}` };
    }

    // Check required
    if (field.required && (!value || value === '')) {
      return { isValid: false, error: `${field.label} is required` };
    }

    // Check validation rules
    if (value && field.validation) {
      const { pattern, minLength, maxLength } = field.validation;
      
      if (pattern && !new RegExp(pattern).test(value)) {
        return { isValid: false, error: `${field.label} format is invalid` };
      }
      
      if (minLength && value.length < minLength) {
        return { isValid: false, error: `${field.label} must be at least ${minLength} characters` };
      }
      
      if (maxLength && value.length > maxLength) {
        return { isValid: false, error: `${field.label} must be no more than ${maxLength} characters` };
      }
    }

    return { isValid: true };
  }

  /**
   * Validate all fields
   */
  validateAllFields(credentials: CredentialData): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    for (const field of this.getRequiredFields()) {
      const validation = this.validateField(field.name, credentials[field.name]);
      if (!validation.isValid && validation.error) {
        errors.push(validation.error);
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Get action by ID
   */
  getAction(actionId: string): Action | undefined {
    return this.getAvailableActions().find(action => action.id === actionId);
  }

  /**
   * Check if action is supported
   */
  supportsAction(actionId: string): boolean {
    return this.getAvailableActions().some(action => action.id === actionId);
  }

  /**
   * Get actions by category
   */
  getActionsByCategory(category: Action['category']): Action[] {
    return this.getAvailableActions().filter(action => action.category === category);
  }

  /**
   * Prepare credentials for API calls (e.g., add headers)
   */
  protected prepareCredentials(credentials: CredentialData): Record<string, any> {
    // Default implementation - can be overridden
    return credentials;
  }

  /**
   * Handle API errors consistently
   */
  protected handleApiError(error: any, action: string): never {
    console.error(`${this.name} API error in ${action}:`, error);
    
    if (error.response) {
      // HTTP error
      throw new Error(`${this.name} API error: ${error.response.status} ${error.response.statusText}`);
    } else if (error.request) {
      // Network error
      throw new Error(`${this.name} network error: Unable to connect`);
    } else {
      // Other error
      throw new Error(`${this.name} error: ${error.message || 'Unknown error'}`);
    }
  }

  /**
   * Rate limit handling
   */
  protected async handleRateLimit(retryAfter?: number): Promise<void> {
    const delay = retryAfter ? retryAfter * 1000 : 1000; // Default 1 second
    console.warn(`${this.name} rate limited, waiting ${delay}ms`);
    await new Promise(resolve => setTimeout(resolve, delay));
  }

  /**
   * Common HTTP request wrapper
   */
  protected async makeRequest(
    url: string, 
    options: RequestInit, 
    credentials: CredentialData
  ): Promise<Response> {
    const preparedCredentials = this.prepareCredentials(credentials);
    
    // Add authentication headers
    const headers = {
      'Content-Type': 'application/json',
      ...options.headers,
      ...this.getAuthHeaders(preparedCredentials)
    };

    const response = await fetch(url, {
      ...options,
      headers
    });

    // Handle rate limiting
    if (response.status === 429) {
      const retryAfter = response.headers.get('Retry-After');
      await this.handleRateLimit(retryAfter ? parseInt(retryAfter) : undefined);
      
      // Retry the request
      return this.makeRequest(url, options, credentials);
    }

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    return response;
  }

  /**
   * Get authentication headers - to be implemented by subclasses
   */
  protected abstract getAuthHeaders(credentials: Record<string, any>): Record<string, string>;

  /**
   * Get base URL for API calls - to be implemented by subclasses
   */
  protected abstract getBaseUrl(): string;
}
