// Credential Manager for CRUD Operations and Security

import { connectToDatabase } from '@/lib/mongodb';
import { CredentialEncryption } from './credential-encryption';
import { 
  Credential, 
  CredentialData, 
  AppType, 
  CreateCredentialRequest,
  UpdateCredentialRequest,
  CredentialListResponse,
  CredentialUsage,
  CredentialValidationResult
} from '../types/integration-types';
import { IntegrationRegistry } from './integration-registry';

export class CredentialManager {
  private static instance: CredentialManager;
  
  private constructor() {}

  static getInstance(): CredentialManager {
    if (!this.instance) {
      this.instance = new CredentialManager();
    }
    return this.instance;
  }

  /**
   * Create a new credential
   */
  async createCredential(
    teamId: string, 
    userId: string, 
    request: CreateCredentialRequest
  ): Promise<Credential> {
    try {
      const { db } = await connectToDatabase();
      
      // Validate credentials with integration
      const integration = IntegrationRegistry.getIntegration(request.appType);
      if (integration) {
        const validation = await integration.validateCredentials(request.credentials);
        if (!validation.isValid) {
          throw new Error(`Invalid credentials: ${validation.errors.join(', ')}`);
        }
      }

      // Encrypt credential data
      const encryptedCredentials = CredentialEncryption.encrypt(request.credentials);

      // Create credential document
      const credential: Omit<Credential, 'id'> = {
        name: request.name,
        appType: request.appType,
        teamId,
        encryptedCredentials,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
        createdBy: userId,
        usageCount: 0,
        metadata: request.metadata
      };

      const result = await db.collection('credentials').insertOne(credential);
      
      return {
        id: result.insertedId.toString(),
        ...credential
      };

    } catch (error) {
      console.error('Failed to create credential:', error);
      throw new Error('Failed to create credential');
    }
  }

  /**
   * Get credential by ID
   */
  async getCredential(credentialId: string, teamId: string): Promise<Credential | null> {
    try {
      const { db } = await connectToDatabase();
      
      const credential = await db.collection('credentials').findOne({
        _id: credentialId,
        teamId,
        isActive: true
      });

      if (!credential) return null;

      return {
        id: credential._id.toString(),
        name: credential.name,
        appType: credential.appType,
        teamId: credential.teamId,
        encryptedCredentials: credential.encryptedCredentials,
        isActive: credential.isActive,
        createdAt: credential.createdAt,
        updatedAt: credential.updatedAt,
        createdBy: credential.createdBy,
        lastUsedAt: credential.lastUsedAt,
        usageCount: credential.usageCount || 0,
        metadata: credential.metadata
      };

    } catch (error) {
      console.error('Failed to get credential:', error);
      return null;
    }
  }

  /**
   * Get decrypted credential data
   */
  async getCredentialData(credentialId: string, teamId: string): Promise<CredentialData | null> {
    try {
      const credential = await this.getCredential(credentialId, teamId);
      if (!credential) return null;

      // Decrypt credential data
      const credentialData = CredentialEncryption.decrypt(credential.encryptedCredentials);
      
      // Update last used timestamp
      await this.updateLastUsed(credentialId);

      return credentialData;

    } catch (error) {
      console.error('Failed to get credential data:', error);
      return null;
    }
  }

  /**
   * List credentials for a team
   */
  async listCredentials(
    teamId: string, 
    page: number = 1, 
    limit: number = 50,
    appType?: AppType
  ): Promise<CredentialListResponse> {
    try {
      const { db } = await connectToDatabase();
      
      const filter: any = { teamId, isActive: true };
      if (appType) {
        filter.appType = appType;
      }

      const skip = (page - 1) * limit;
      
      const [credentials, total] = await Promise.all([
        db.collection('credentials')
          .find(filter)
          .sort({ updatedAt: -1 })
          .skip(skip)
          .limit(limit)
          .toArray(),
        db.collection('credentials').countDocuments(filter)
      ]);

      return {
        credentials: credentials.map(cred => ({
          id: cred._id.toString(),
          name: cred.name,
          appType: cred.appType,
          teamId: cred.teamId,
          encryptedCredentials: cred.encryptedCredentials,
          isActive: cred.isActive,
          createdAt: cred.createdAt,
          updatedAt: cred.updatedAt,
          createdBy: cred.createdBy,
          lastUsedAt: cred.lastUsedAt,
          usageCount: cred.usageCount || 0,
          metadata: cred.metadata
        })),
        total,
        page,
        limit
      };

    } catch (error) {
      console.error('Failed to list credentials:', error);
      throw new Error('Failed to list credentials');
    }
  }

  /**
   * Update credential
   */
  async updateCredential(
    credentialId: string, 
    teamId: string, 
    request: UpdateCredentialRequest
  ): Promise<Credential | null> {
    try {
      const { db } = await connectToDatabase();
      
      const updateData: any = {
        updatedAt: new Date()
      };

      if (request.name) updateData.name = request.name;
      if (request.isActive !== undefined) updateData.isActive = request.isActive;
      if (request.metadata) updateData.metadata = request.metadata;

      // If updating credentials, encrypt them
      if (request.credentials) {
        // Get current credential to validate app type
        const current = await this.getCredential(credentialId, teamId);
        if (!current) return null;

        // Validate new credentials
        const integration = IntegrationRegistry.getIntegration(current.appType);
        if (integration) {
          const validation = await integration.validateCredentials(request.credentials);
          if (!validation.isValid) {
            throw new Error(`Invalid credentials: ${validation.errors.join(', ')}`);
          }
        }

        updateData.encryptedCredentials = CredentialEncryption.encrypt(request.credentials);
      }

      const result = await db.collection('credentials').findOneAndUpdate(
        { _id: credentialId, teamId },
        { $set: updateData },
        { returnDocument: 'after' }
      );

      if (!result.value) return null;

      return {
        id: result.value._id.toString(),
        name: result.value.name,
        appType: result.value.appType,
        teamId: result.value.teamId,
        encryptedCredentials: result.value.encryptedCredentials,
        isActive: result.value.isActive,
        createdAt: result.value.createdAt,
        updatedAt: result.value.updatedAt,
        createdBy: result.value.createdBy,
        lastUsedAt: result.value.lastUsedAt,
        usageCount: result.value.usageCount || 0,
        metadata: result.value.metadata
      };

    } catch (error) {
      console.error('Failed to update credential:', error);
      throw new Error('Failed to update credential');
    }
  }

  /**
   * Delete credential (soft delete)
   */
  async deleteCredential(credentialId: string, teamId: string): Promise<boolean> {
    try {
      // Check if credential is in use
      const usage = await this.getCredentialUsage(credentialId, teamId);
      if (usage.length > 0) {
        throw new Error('Cannot delete credential that is in use by workflows');
      }

      const { db } = await connectToDatabase();
      
      const result = await db.collection('credentials').updateOne(
        { _id: credentialId, teamId },
        { 
          $set: { 
            isActive: false, 
            updatedAt: new Date() 
          } 
        }
      );

      return result.modifiedCount > 0;

    } catch (error) {
      console.error('Failed to delete credential:', error);
      throw error;
    }
  }

  /**
   * Test credential connection
   */
  async testCredential(credentialId: string, teamId: string): Promise<CredentialValidationResult> {
    try {
      const credential = await this.getCredential(credentialId, teamId);
      if (!credential) {
        return {
          isValid: false,
          errors: ['Credential not found'],
          warnings: []
        };
      }

      const credentialData = CredentialEncryption.decrypt(credential.encryptedCredentials);
      const integration = IntegrationRegistry.getIntegration(credential.appType);
      
      if (!integration) {
        return {
          isValid: false,
          errors: [`Integration not found for ${credential.appType}`],
          warnings: []
        };
      }

      return await integration.validateCredentials(credentialData);

    } catch (error) {
      console.error('Failed to test credential:', error);
      return {
        isValid: false,
        errors: ['Failed to test credential connection'],
        warnings: []
      };
    }
  }

  /**
   * Get credential usage in workflows
   */
  async getCredentialUsage(credentialId: string, teamId: string): Promise<CredentialUsage[]> {
    try {
      const { db } = await connectToDatabase();
      
      // Find workflows that use this credential
      const workflows = await db.collection('workflows').find({
        teamId,
        'nodes.config.credentialId': credentialId
      }).toArray();

      const usage: CredentialUsage[] = [];

      for (const workflow of workflows) {
        for (const node of workflow.nodes) {
          if (node.config?.credentialId === credentialId) {
            usage.push({
              workflowId: workflow._id.toString(),
              workflowName: workflow.name,
              nodeId: node.id,
              nodeName: node.name,
              lastUsed: workflow.updatedAt,
              usageCount: 1 // Could be enhanced with actual usage tracking
            });
          }
        }
      }

      return usage;

    } catch (error) {
      console.error('Failed to get credential usage:', error);
      return [];
    }
  }

  /**
   * Update last used timestamp
   */
  private async updateLastUsed(credentialId: string): Promise<void> {
    try {
      const { db } = await connectToDatabase();
      
      await db.collection('credentials').updateOne(
        { _id: credentialId },
        { 
          $set: { lastUsedAt: new Date() },
          $inc: { usageCount: 1 }
        }
      );

    } catch (error) {
      console.error('Failed to update last used:', error);
      // Don't throw - this is not critical
    }
  }
}
