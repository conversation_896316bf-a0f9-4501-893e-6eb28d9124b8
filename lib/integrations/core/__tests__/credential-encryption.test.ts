import { CredentialEncryption } from '../credential-encryption'
import { CredentialData } from '../../types/integration-types'

describe('CredentialEncryption', () => {
  const testCredentials: CredentialData = {
    api_key: 'test-api-key-123',
    secret: 'test-secret-456',
    username: 'testuser',
    password: 'testpass123'
  }

  beforeEach(() => {
    // Set test encryption key
    process.env.CREDENTIAL_ENCRYPTION_KEY = 'dGVzdC1lbmNyeXB0aW9uLWtleS0zMi1ieXRlcw=='
  })

  afterEach(() => {
    jest.clearAllMocks()
  })

  describe('encrypt', () => {
    it('should encrypt credential data successfully', () => {
      const encrypted = CredentialEncryption.encrypt(testCredentials)
      
      expect(encrypted).toBeDefined()
      expect(typeof encrypted).toBe('string')
      expect(encrypted.length).toBeGreaterThan(0)
      expect(encrypted).not.toContain('test-api-key-123')
    })

    it('should produce different encrypted strings for same data', () => {
      const encrypted1 = CredentialEncryption.encrypt(testCredentials)
      const encrypted2 = CredentialEncryption.encrypt(testCredentials)
      
      expect(encrypted1).not.toBe(encrypted2)
    })

    it('should throw error when encryption key is missing', () => {
      delete process.env.CREDENTIAL_ENCRYPTION_KEY
      
      expect(() => {
        CredentialEncryption.encrypt(testCredentials)
      }).toThrow('CREDENTIAL_ENCRYPTION_KEY environment variable is required')
    })
  })

  describe('decrypt', () => {
    it('should decrypt credential data successfully', () => {
      const encrypted = CredentialEncryption.encrypt(testCredentials)
      const decrypted = CredentialEncryption.decrypt(encrypted)
      
      expect(decrypted).toEqual(testCredentials)
    })

    it('should throw error for invalid encrypted data', () => {
      expect(() => {
        CredentialEncryption.decrypt('invalid-encrypted-data')
      }).toThrow('Failed to decrypt credential data')
    })

    it('should throw error when encryption key is missing', () => {
      const encrypted = CredentialEncryption.encrypt(testCredentials)
      delete process.env.CREDENTIAL_ENCRYPTION_KEY
      
      expect(() => {
        CredentialEncryption.decrypt(encrypted)
      }).toThrow('CREDENTIAL_ENCRYPTION_KEY environment variable is required')
    })
  })

  describe('generateEncryptionKey', () => {
    it('should generate a valid base64 encryption key', () => {
      const key = CredentialEncryption.generateEncryptionKey()
      
      expect(key).toBeDefined()
      expect(typeof key).toBe('string')
      expect(CredentialEncryption.validateEncryptionKey(key)).toBe(true)
    })
  })

  describe('validateEncryptionKey', () => {
    it('should validate correct encryption key format', () => {
      const validKey = 'dGVzdC1lbmNyeXB0aW9uLWtleS0zMi1ieXRlcw=='
      expect(CredentialEncryption.validateEncryptionKey(validKey)).toBe(true)
    })

    it('should reject invalid encryption key format', () => {
      expect(CredentialEncryption.validateEncryptionKey('invalid-key')).toBe(false)
      expect(CredentialEncryption.validateEncryptionKey('')).toBe(false)
      expect(CredentialEncryption.validateEncryptionKey('short')).toBe(false)
    })
  })

  describe('testEncryption', () => {
    it('should pass encryption test with valid key', () => {
      expect(CredentialEncryption.testEncryption()).toBe(true)
    })

    it('should fail encryption test with invalid key', () => {
      process.env.CREDENTIAL_ENCRYPTION_KEY = 'invalid-key'
      expect(CredentialEncryption.testEncryption()).toBe(false)
    })
  })

  describe('secureCompare', () => {
    it('should return true for identical credential objects', () => {
      const creds1 = { api_key: 'test123' }
      const creds2 = { api_key: 'test123' }
      
      expect(CredentialEncryption.secureCompare(creds1, creds2)).toBe(true)
    })

    it('should return false for different credential objects', () => {
      const creds1 = { api_key: 'test123' }
      const creds2 = { api_key: 'test456' }
      
      expect(CredentialEncryption.secureCompare(creds1, creds2)).toBe(false)
    })
  })

  describe('sanitizeForLogging', () => {
    it('should sanitize sensitive fields', () => {
      const sanitized = CredentialEncryption.sanitizeForLogging(testCredentials)
      
      expect(sanitized.api_key).toBe('***-123')
      expect(sanitized.secret).toBe('***-456')
      expect(sanitized.password).toBe('***123')
      expect(sanitized.username).toBe('testuser') // Not sensitive
    })

    it('should handle null/undefined values', () => {
      const creds = { api_key: null, secret: undefined, username: 'test' }
      const sanitized = CredentialEncryption.sanitizeForLogging(creds)
      
      expect(sanitized.api_key).toBe(null)
      expect(sanitized.secret).toBe(null)
      expect(sanitized.username).toBe('test')
    })
  })

  describe('hashCredentials', () => {
    it('should generate consistent hash for same credentials', () => {
      const hash1 = CredentialEncryption.hashCredentials(testCredentials)
      const hash2 = CredentialEncryption.hashCredentials(testCredentials)
      
      expect(hash1).toBe(hash2)
      expect(hash1).toHaveLength(64) // SHA-256 hex length
    })

    it('should generate different hashes for different credentials', () => {
      const creds1 = { api_key: 'test123' }
      const creds2 = { api_key: 'test456' }
      
      const hash1 = CredentialEncryption.hashCredentials(creds1)
      const hash2 = CredentialEncryption.hashCredentials(creds2)
      
      expect(hash1).not.toBe(hash2)
    })
  })
})
