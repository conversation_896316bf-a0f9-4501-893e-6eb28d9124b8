import { CredentialManager } from '../credential-manager'
import { CredentialEncryption } from '../credential-encryption'
import { IntegrationRegistry } from '../integration-registry'
import { connectToDatabase } from '@/lib/mongodb'

// Mock dependencies
jest.mock('../credential-encryption')
jest.mock('../integration-registry')
jest.mock('@/lib/mongodb')

const mockDb = {
  collection: jest.fn(() => ({
    insertOne: jest.fn(),
    findOne: jest.fn(),
    find: jest.fn(() => ({
      sort: jest.fn(() => ({
        skip: jest.fn(() => ({
          limit: jest.fn(() => ({
            toArray: jest.fn()
          }))
        }))
      }))
    })),
    countDocuments: jest.fn(),
    findOneAndUpdate: jest.fn(),
    updateOne: jest.fn(),
  }))
}

const mockIntegration = {
  validateCredentials: jest.fn()
}

describe('CredentialManager', () => {
  let credentialManager: CredentialManager
  const testTeamId = 'team-123'
  const testUserId = 'user-456'
  const testCredentialId = 'cred-789'

  beforeEach(() => {
    credentialManager = CredentialManager.getInstance()
    
    // Mock database connection
    ;(connectToDatabase as jest.Mock).mockResolvedValue({ db: mockDb })
    
    // Mock encryption
    ;(CredentialEncryption.encrypt as jest.Mock).mockReturnValue('encrypted-data')
    ;(CredentialEncryption.decrypt as jest.Mock).mockReturnValue({
      api_key: 'test-key'
    })
    
    // Mock integration registry
    ;(IntegrationRegistry.getIntegration as jest.Mock).mockReturnValue(mockIntegration)
    mockIntegration.validateCredentials.mockResolvedValue({
      isValid: true,
      errors: [],
      warnings: []
    })
  })

  afterEach(() => {
    jest.clearAllMocks()
  })

  describe('createCredential', () => {
    const createRequest = {
      name: 'Test Credential',
      appType: 'openai' as const,
      credentials: { api_key: 'test-key' }
    }

    it('should create credential successfully', async () => {
      const mockInsertResult = { insertedId: 'new-id' }
      mockDb.collection().insertOne.mockResolvedValue(mockInsertResult)

      const result = await credentialManager.createCredential(
        testTeamId,
        testUserId,
        createRequest
      )

      expect(result).toMatchObject({
        id: 'new-id',
        name: 'Test Credential',
        appType: 'openai',
        teamId: testTeamId,
        createdBy: testUserId,
        isActive: true
      })

      expect(CredentialEncryption.encrypt).toHaveBeenCalledWith(createRequest.credentials)
      expect(mockIntegration.validateCredentials).toHaveBeenCalledWith(createRequest.credentials)
    })

    it('should throw error for invalid credentials', async () => {
      mockIntegration.validateCredentials.mockResolvedValue({
        isValid: false,
        errors: ['Invalid API key'],
        warnings: []
      })

      await expect(
        credentialManager.createCredential(testTeamId, testUserId, createRequest)
      ).rejects.toThrow('Invalid credentials: Invalid API key')
    })

    it('should handle database errors', async () => {
      mockDb.collection().insertOne.mockRejectedValue(new Error('DB Error'))

      await expect(
        credentialManager.createCredential(testTeamId, testUserId, createRequest)
      ).rejects.toThrow('Failed to create credential')
    })
  })

  describe('getCredential', () => {
    it('should return credential when found', async () => {
      const mockCredential = {
        _id: testCredentialId,
        name: 'Test Credential',
        appType: 'openai',
        teamId: testTeamId,
        encryptedCredentials: 'encrypted-data',
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
        createdBy: testUserId,
        usageCount: 5
      }

      mockDb.collection().findOne.mockResolvedValue(mockCredential)

      const result = await credentialManager.getCredential(testCredentialId, testTeamId)

      expect(result).toMatchObject({
        id: testCredentialId,
        name: 'Test Credential',
        appType: 'openai',
        teamId: testTeamId
      })
    })

    it('should return null when credential not found', async () => {
      mockDb.collection().findOne.mockResolvedValue(null)

      const result = await credentialManager.getCredential(testCredentialId, testTeamId)

      expect(result).toBeNull()
    })
  })

  describe('getCredentialData', () => {
    it('should return decrypted credential data', async () => {
      const mockCredential = {
        _id: testCredentialId,
        encryptedCredentials: 'encrypted-data',
        teamId: testTeamId,
        isActive: true
      }

      mockDb.collection().findOne.mockResolvedValue(mockCredential)
      mockDb.collection().updateOne.mockResolvedValue({ modifiedCount: 1 })

      const result = await credentialManager.getCredentialData(testCredentialId, testTeamId)

      expect(result).toEqual({ api_key: 'test-key' })
      expect(CredentialEncryption.decrypt).toHaveBeenCalledWith('encrypted-data')
      
      // Should update last used timestamp
      expect(mockDb.collection().updateOne).toHaveBeenCalledWith(
        { _id: testCredentialId },
        expect.objectContaining({
          $set: expect.objectContaining({ lastUsedAt: expect.any(Date) }),
          $inc: { usageCount: 1 }
        })
      )
    })

    it('should return null for non-existent credential', async () => {
      mockDb.collection().findOne.mockResolvedValue(null)

      const result = await credentialManager.getCredentialData(testCredentialId, testTeamId)

      expect(result).toBeNull()
    })
  })

  describe('listCredentials', () => {
    it('should return paginated credentials list', async () => {
      const mockCredentials = [
        {
          _id: 'cred-1',
          name: 'Credential 1',
          appType: 'openai',
          teamId: testTeamId,
          isActive: true
        }
      ]

      mockDb.collection().find().sort().skip().limit().toArray.mockResolvedValue(mockCredentials)
      mockDb.collection().countDocuments.mockResolvedValue(1)

      const result = await credentialManager.listCredentials(testTeamId, 1, 10)

      expect(result).toEqual({
        credentials: expect.arrayContaining([
          expect.objectContaining({
            id: 'cred-1',
            name: 'Credential 1'
          })
        ]),
        total: 1,
        page: 1,
        limit: 10
      })
    })

    it('should filter by app type when provided', async () => {
      mockDb.collection().find().sort().skip().limit().toArray.mockResolvedValue([])
      mockDb.collection().countDocuments.mockResolvedValue(0)

      await credentialManager.listCredentials(testTeamId, 1, 10, 'openai')

      expect(mockDb.collection().find).toHaveBeenCalledWith({
        teamId: testTeamId,
        isActive: true,
        appType: 'openai'
      })
    })
  })

  describe('updateCredential', () => {
    const updateRequest = {
      name: 'Updated Credential',
      isActive: false
    }

    it('should update credential successfully', async () => {
      const mockUpdatedCredential = {
        _id: testCredentialId,
        name: 'Updated Credential',
        isActive: false,
        updatedAt: new Date()
      }

      mockDb.collection().findOneAndUpdate.mockResolvedValue({
        value: mockUpdatedCredential
      })

      const result = await credentialManager.updateCredential(
        testCredentialId,
        testTeamId,
        updateRequest
      )

      expect(result).toMatchObject({
        id: testCredentialId,
        name: 'Updated Credential',
        isActive: false
      })
    })

    it('should return null when credential not found', async () => {
      mockDb.collection().findOneAndUpdate.mockResolvedValue({ value: null })

      const result = await credentialManager.updateCredential(
        testCredentialId,
        testTeamId,
        updateRequest
      )

      expect(result).toBeNull()
    })
  })

  describe('deleteCredential', () => {
    it('should soft delete credential when not in use', async () => {
      // Mock no usage
      mockDb.collection().find().toArray.mockResolvedValue([])
      mockDb.collection().updateOne.mockResolvedValue({ modifiedCount: 1 })

      const result = await credentialManager.deleteCredential(testCredentialId, testTeamId)

      expect(result).toBe(true)
      expect(mockDb.collection().updateOne).toHaveBeenCalledWith(
        { _id: testCredentialId, teamId: testTeamId },
        expect.objectContaining({
          $set: expect.objectContaining({
            isActive: false,
            updatedAt: expect.any(Date)
          })
        })
      )
    })

    it('should throw error when credential is in use', async () => {
      // Mock credential in use
      mockDb.collection().find().toArray.mockResolvedValue([
        { _id: 'workflow-1', nodes: [{ config: { credentialId: testCredentialId } }] }
      ])

      await expect(
        credentialManager.deleteCredential(testCredentialId, testTeamId)
      ).rejects.toThrow('Cannot delete credential that is in use by workflows')
    })
  })

  describe('testCredential', () => {
    it('should test credential successfully', async () => {
      const mockCredential = {
        _id: testCredentialId,
        appType: 'openai',
        encryptedCredentials: 'encrypted-data'
      }

      mockDb.collection().findOne.mockResolvedValue(mockCredential)

      const result = await credentialManager.testCredential(testCredentialId, testTeamId)

      expect(result).toEqual({
        isValid: true,
        errors: [],
        warnings: []
      })

      expect(mockIntegration.validateCredentials).toHaveBeenCalledWith({ api_key: 'test-key' })
    })

    it('should return error for non-existent credential', async () => {
      mockDb.collection().findOne.mockResolvedValue(null)

      const result = await credentialManager.testCredential(testCredentialId, testTeamId)

      expect(result).toEqual({
        isValid: false,
        errors: ['Credential not found'],
        warnings: []
      })
    })
  })
})
