import { OpenAIIntegration } from '../openai'
import { CredentialData } from '../../types/integration-types'

// Mock fetch globally
global.fetch = jest.fn()

describe('OpenAIIntegration', () => {
  let integration: OpenAIIntegration
  const validCredentials: CredentialData = {
    api_key: 'sk-1234567890abcdef1234567890abcdef1234567890abcdef',
    organization_id: 'org-1234567890abcdef'
  }

  beforeEach(() => {
    integration = new OpenAIIntegration()
    jest.clearAllMocks()
  })

  describe('basic properties', () => {
    it('should have correct integration properties', () => {
      expect(integration.id).toBe('openai')
      expect(integration.name).toBe('OpenAI')
      expect(integration.category).toBe('ai_services')
      expect(integration.icon).toBe('🤖')
      expect(integration.color).toBe('#10A37F')
    })
  })

  describe('getRequiredFields', () => {
    it('should return required credential fields', () => {
      const fields = integration.getRequiredFields()
      
      expect(fields).toHaveLength(3)
      expect(fields[0]).toMatchObject({
        name: 'api_key',
        label: 'API Key',
        type: 'password',
        required: true
      })
      expect(fields[1]).toMatchObject({
        name: 'organization_id',
        label: 'Organization ID',
        type: 'text',
        required: false
      })
    })
  })

  describe('getAvailableActions', () => {
    it('should return available actions', () => {
      const actions = integration.getAvailableActions()
      
      expect(actions).toHaveLength(3)
      expect(actions.map(a => a.id)).toEqual([
        'chat_completion',
        'text_embedding',
        'image_generation'
      ])
    })

    it('should have correct chat completion action structure', () => {
      const actions = integration.getAvailableActions()
      const chatAction = actions.find(a => a.id === 'chat_completion')
      
      expect(chatAction).toMatchObject({
        id: 'chat_completion',
        name: 'Chat Completion',
        category: 'ai',
        inputs: expect.arrayContaining([
          expect.objectContaining({
            name: 'messages',
            required: true
          })
        ])
      })
    })
  })

  describe('validateCredentials', () => {
    it('should validate correct credentials', async () => {
      // Mock successful API response
      ;(fetch as jest.Mock).mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({ data: [] })
      })

      const result = await integration.validateCredentials(validCredentials)

      expect(result.isValid).toBe(true)
      expect(result.errors).toHaveLength(0)
    })

    it('should reject missing API key', async () => {
      const invalidCredentials = { organization_id: 'org-123' }

      const result = await integration.validateCredentials(invalidCredentials)

      expect(result.isValid).toBe(false)
      expect(result.errors).toContain('API key is required')
    })

    it('should reject invalid API key format', async () => {
      const invalidCredentials = { api_key: 'invalid-key' }

      const result = await integration.validateCredentials(invalidCredentials)

      expect(result.isValid).toBe(false)
      expect(result.errors).toContain('Invalid API key format')
    })

    it('should warn about invalid organization ID format', async () => {
      const credentialsWithBadOrg = {
        ...validCredentials,
        organization_id: 'invalid-org'
      }

      // Mock successful API response
      ;(fetch as jest.Mock).mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({ data: [] })
      })

      const result = await integration.validateCredentials(credentialsWithBadOrg)

      expect(result.isValid).toBe(true)
      expect(result.warnings).toContain('Organization ID should start with "org-"')
    })

    it('should handle connection test failure', async () => {
      // Mock API error
      ;(fetch as jest.Mock).mockRejectedValue(new Error('Network error'))

      const result = await integration.validateCredentials(validCredentials)

      expect(result.isValid).toBe(false)
      expect(result.errors[0]).toContain('Connection test failed')
    })
  })

  describe('executeAction', () => {
    beforeEach(() => {
      // Mock successful API responses
      ;(fetch as jest.Mock).mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({
          choices: [{ message: { content: 'Test response' }, finish_reason: 'stop' }],
          usage: { total_tokens: 100 },
          model: 'gpt-4o-mini'
        })
      })
    })

    describe('chat_completion', () => {
      it('should execute chat completion successfully', async () => {
        const params = {
          messages: [{ role: 'user', content: 'Hello' }],
          model: 'gpt-4o',
          max_tokens: 100
        }

        const result = await integration.executeAction('chat_completion', params, validCredentials)

        expect(result).toMatchObject({
          content: 'Test response',
          model: 'gpt-4o-mini',
          finish_reason: 'stop'
        })

        expect(fetch).toHaveBeenCalledWith(
          'https://api.openai.com/v1/chat/completions',
          expect.objectContaining({
            method: 'POST',
            headers: expect.objectContaining({
              'Authorization': `Bearer ${validCredentials.api_key}`,
              'Content-Type': 'application/json'
            }),
            body: expect.stringContaining('"model":"gpt-4o"')
          })
        )
      })

      it('should handle string messages input', async () => {
        const params = { messages: 'Hello world' }

        await integration.executeAction('chat_completion', params, validCredentials)

        const requestBody = JSON.parse((fetch as jest.Mock).mock.calls[0][1].body)
        expect(requestBody.messages).toEqual([
          { role: 'user', content: 'Hello world' }
        ])
      })

      it('should include organization header when provided', async () => {
        const params = { messages: 'Hello' }

        await integration.executeAction('chat_completion', params, validCredentials)

        expect(fetch).toHaveBeenCalledWith(
          expect.any(String),
          expect.objectContaining({
            headers: expect.objectContaining({
              'OpenAI-Organization': validCredentials.organization_id
            })
          })
        )
      })
    })

    describe('text_embedding', () => {
      beforeEach(() => {
        ;(fetch as jest.Mock).mockResolvedValue({
          ok: true,
          json: () => Promise.resolve({
            data: [{ embedding: [0.1, 0.2, 0.3] }],
            usage: { total_tokens: 50 },
            model: 'text-embedding-3-small'
          })
        })
      })

      it('should execute text embedding successfully', async () => {
        const params = {
          input: 'Text to embed',
          model: 'text-embedding-3-large'
        }

        const result = await integration.executeAction('text_embedding', params, validCredentials)

        expect(result).toMatchObject({
          embedding: [0.1, 0.2, 0.3],
          model: 'text-embedding-3-small'
        })

        expect(fetch).toHaveBeenCalledWith(
          'https://api.openai.com/v1/embeddings',
          expect.objectContaining({
            method: 'POST',
            body: expect.stringContaining('"model":"text-embedding-3-large"')
          })
        )
      })
    })

    describe('image_generation', () => {
      beforeEach(() => {
        ;(fetch as jest.Mock).mockResolvedValue({
          ok: true,
          json: () => Promise.resolve({
            data: [{
              url: 'https://example.com/image.png',
              revised_prompt: 'A beautiful landscape'
            }]
          })
        })
      })

      it('should execute image generation successfully', async () => {
        const params = {
          prompt: 'A beautiful landscape',
          model: 'dall-e-3',
          size: '1024x1024'
        }

        const result = await integration.executeAction('image_generation', params, validCredentials)

        expect(result).toMatchObject({
          url: 'https://example.com/image.png',
          revised_prompt: 'A beautiful landscape'
        })

        expect(fetch).toHaveBeenCalledWith(
          'https://api.openai.com/v1/images/generations',
          expect.objectContaining({
            method: 'POST',
            body: expect.stringContaining('"model":"dall-e-3"')
          })
        )
      })
    })

    it('should throw error for unknown action', async () => {
      await expect(
        integration.executeAction('unknown_action', {}, validCredentials)
      ).rejects.toThrow('Unknown action: unknown_action')
    })
  })

  describe('error handling', () => {
    it('should handle API rate limiting', async () => {
      // Mock rate limit response
      ;(fetch as jest.Mock)
        .mockResolvedValueOnce({
          ok: false,
          status: 429,
          headers: new Map([['Retry-After', '60']])
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ data: [] })
        })

      // Should retry after rate limit
      const result = await integration.executeAction('chat_completion', {}, validCredentials)

      expect(fetch).toHaveBeenCalledTimes(2)
    })

    it('should handle HTTP errors', async () => {
      ;(fetch as jest.Mock).mockResolvedValue({
        ok: false,
        status: 401,
        statusText: 'Unauthorized'
      })

      await expect(
        integration.executeAction('chat_completion', {}, validCredentials)
      ).rejects.toThrow('HTTP 401: Unauthorized')
    })
  })
})
