// OpenAI Integration Implementation

import { BaseIntegration } from '../core/base-integration';
import { 
  CredentialField, 
  Action, 
  CredentialData, 
  CredentialValidationResult,
  AppType 
} from '../types/integration-types';
import { CREDENTIAL_FIELDS, VALIDATION_PATTERNS } from '../types/credential-types';

export class OpenAIIntegration extends BaseIntegration {
  readonly id: AppType = 'openai';
  readonly name = 'OpenAI';
  readonly description = 'Access GPT models and AI capabilities from OpenAI';
  readonly category = 'ai_services' as const;
  readonly icon = '🤖';
  readonly color = '#10A37F';
  readonly website = 'https://openai.com';
  readonly documentation = 'https://platform.openai.com/docs';

  getRequiredFields(): CredentialField[] {
    return CREDENTIAL_FIELDS.openai;
  }

  getAvailableActions(): Action[] {
    return [
      {
        id: 'chat_completion',
        name: 'Chat Completion',
        description: 'Generate text using GPT models',
        category: 'ai',
        inputs: [
          {
            name: 'messages',
            label: 'Messages',
            type: 'textarea',
            required: true,
            description: 'Array of messages or single message text'
          },
          {
            name: 'model',
            label: 'Model',
            type: 'select',
            required: false,
            options: [
              { label: 'GPT-4o', value: 'gpt-4o' },
              { label: 'GPT-4o Mini', value: 'gpt-4o-mini' },
              { label: 'GPT-4 Turbo', value: 'gpt-4-turbo' },
              { label: 'GPT-4', value: 'gpt-4' },
              { label: 'GPT-3.5 Turbo', value: 'gpt-3.5-turbo' }
            ]
          },
          {
            name: 'max_tokens',
            label: 'Max Tokens',
            type: 'text',
            required: false,
            description: 'Maximum number of tokens to generate'
          },
          {
            name: 'temperature',
            label: 'Temperature',
            type: 'text',
            required: false,
            description: 'Sampling temperature (0-2)'
          }
        ],
        outputs: {
          content: 'string',
          usage: 'object',
          model: 'string',
          finish_reason: 'string'
        }
      },
      {
        id: 'text_embedding',
        name: 'Text Embedding',
        description: 'Generate embeddings for text',
        category: 'ai',
        inputs: [
          {
            name: 'input',
            label: 'Input Text',
            type: 'textarea',
            required: true,
            description: 'Text to generate embeddings for'
          },
          {
            name: 'model',
            label: 'Model',
            type: 'select',
            required: false,
            options: [
              { label: 'text-embedding-3-large', value: 'text-embedding-3-large' },
              { label: 'text-embedding-3-small', value: 'text-embedding-3-small' },
              { label: 'text-embedding-ada-002', value: 'text-embedding-ada-002' }
            ]
          }
        ],
        outputs: {
          embedding: 'array',
          usage: 'object',
          model: 'string'
        }
      },
      {
        id: 'image_generation',
        name: 'Image Generation',
        description: 'Generate images using DALL-E',
        category: 'ai',
        inputs: [
          {
            name: 'prompt',
            label: 'Prompt',
            type: 'textarea',
            required: true,
            description: 'Description of the image to generate'
          },
          {
            name: 'model',
            label: 'Model',
            type: 'select',
            required: false,
            options: [
              { label: 'DALL-E 3', value: 'dall-e-3' },
              { label: 'DALL-E 2', value: 'dall-e-2' }
            ]
          },
          {
            name: 'size',
            label: 'Size',
            type: 'select',
            required: false,
            options: [
              { label: '1024x1024', value: '1024x1024' },
              { label: '1792x1024', value: '1792x1024' },
              { label: '1024x1792', value: '1024x1792' }
            ]
          }
        ],
        outputs: {
          url: 'string',
          revised_prompt: 'string'
        }
      }
    ];
  }

  async validateCredentials(credentials: CredentialData): Promise<CredentialValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Validate API key format
    if (!credentials.api_key) {
      errors.push('API key is required');
    } else if (!VALIDATION_PATTERNS.openai_api_key.test(credentials.api_key)) {
      errors.push('Invalid API key format');
    }

    // Validate organization ID if provided
    if (credentials.organization_id && !credentials.organization_id.startsWith('org-')) {
      warnings.push('Organization ID should start with "org-"');
    }

    // Test connection if API key is valid
    let connectionTest;
    if (errors.length === 0) {
      try {
        connectionTest = await this.testConnection(credentials);
      } catch (error) {
        errors.push(`Connection test failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      connectionTest
    };
  }

  async executeAction(action: string, params: any, credentials: CredentialData): Promise<any> {
    switch (action) {
      case 'chat_completion':
        return this.chatCompletion(params, credentials);
      case 'text_embedding':
        return this.textEmbedding(params, credentials);
      case 'image_generation':
        return this.imageGeneration(params, credentials);
      default:
        throw new Error(`Unknown action: ${action}`);
    }
  }

  protected getAuthHeaders(credentials: Record<string, any>): Record<string, string> {
    const headers: Record<string, string> = {
      'Authorization': `Bearer ${credentials.api_key}`
    };

    if (credentials.organization_id) {
      headers['OpenAI-Organization'] = credentials.organization_id;
    }

    return headers;
  }

  protected getBaseUrl(): string {
    return 'https://api.openai.com/v1';
  }

  private async chatCompletion(params: any, credentials: CredentialData): Promise<any> {
    const { messages, model = 'gpt-4o-mini', max_tokens, temperature } = params;

    // Format messages
    const formattedMessages = Array.isArray(messages) 
      ? messages 
      : [{ role: 'user', content: messages }];

    const requestBody: any = {
      model,
      messages: formattedMessages
    };

    if (max_tokens) requestBody.max_tokens = parseInt(max_tokens);
    if (temperature !== undefined) requestBody.temperature = parseFloat(temperature);

    const response = await this.makeRequest(
      `${this.getBaseUrl()}/chat/completions`,
      {
        method: 'POST',
        body: JSON.stringify(requestBody)
      },
      credentials
    );

    const data = await response.json();
    
    return {
      content: data.choices[0]?.message?.content || '',
      usage: data.usage,
      model: data.model,
      finish_reason: data.choices[0]?.finish_reason
    };
  }

  private async textEmbedding(params: any, credentials: CredentialData): Promise<any> {
    const { input, model = 'text-embedding-3-small' } = params;

    const response = await this.makeRequest(
      `${this.getBaseUrl()}/embeddings`,
      {
        method: 'POST',
        body: JSON.stringify({
          model,
          input
        })
      },
      credentials
    );

    const data = await response.json();
    
    return {
      embedding: data.data[0]?.embedding || [],
      usage: data.usage,
      model: data.model
    };
  }

  private async imageGeneration(params: any, credentials: CredentialData): Promise<any> {
    const { prompt, model = 'dall-e-3', size = '1024x1024' } = params;

    const response = await this.makeRequest(
      `${this.getBaseUrl()}/images/generations`,
      {
        method: 'POST',
        body: JSON.stringify({
          model,
          prompt,
          size,
          n: 1
        })
      },
      credentials
    );

    const data = await response.json();
    
    return {
      url: data.data[0]?.url || '',
      revised_prompt: data.data[0]?.revised_prompt
    };
  }

  private async testConnection(credentials: CredentialData): Promise<{ success: boolean; responseTime?: number; message?: string }> {
    const startTime = Date.now();
    
    try {
      const response = await this.makeRequest(
        `${this.getBaseUrl()}/models`,
        { method: 'GET' },
        credentials
      );

      const responseTime = Date.now() - startTime;
      
      if (response.ok) {
        return {
          success: true,
          responseTime,
          message: 'Connection successful'
        };
      } else {
        return {
          success: false,
          responseTime,
          message: `HTTP ${response.status}: ${response.statusText}`
        };
      }
    } catch (error) {
      return {
        success: false,
        responseTime: Date.now() - startTime,
        message: error instanceof Error ? error.message : 'Connection failed'
      };
    }
  }
}
