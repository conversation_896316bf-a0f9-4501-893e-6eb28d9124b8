// Groq Integration Implementation

import { BaseIntegration } from '../core/base-integration';
import { CredentialField, Action, CredentialData, CredentialValidationResult, AppType } from '../types/integration-types';
import { CREDENTIAL_FIELDS } from '../types/credential-types';

export class GroqIntegration extends BaseIntegration {
  readonly id: AppType = 'groq';
  readonly name = 'Groq';
  readonly description = 'Ultra-fast AI inference with Groq LPU';
  readonly category = 'ai_services' as const;
  readonly icon = '⚡';
  readonly color = '#F55036';
  readonly website = 'https://groq.com';
  readonly documentation = 'https://console.groq.com/docs';

  getRequiredFields(): CredentialField[] {
    return CREDENTIAL_FIELDS.groq;
  }

  getAvailableActions(): Action[] {
    return [
      {
        id: 'chat_completion',
        name: 'Chat Completion',
        description: 'Generate text using Groq models',
        category: 'ai',
        inputs: [
          {
            name: 'messages',
            label: 'Messages',
            type: 'textarea',
            required: true,
            description: 'Array of messages or single message text'
          },
          {
            name: 'model',
            label: 'Model',
            type: 'select',
            required: false,
            options: [
              { label: 'Llama 3 8B', value: 'llama3-8b-8192' },
              { label: 'Llama 3 70B', value: 'llama3-70b-8192' },
              { label: 'Mixtral 8x7B', value: 'mixtral-8x7b-32768' },
              { label: 'Gemma 7B', value: 'gemma-7b-it' }
            ]
          }
        ],
        outputs: {
          content: 'string',
          usage: 'object',
          model: 'string'
        }
      }
    ];
  }

  async validateCredentials(credentials: CredentialData): Promise<CredentialValidationResult> {
    // Implementation similar to OpenAI but for Groq API
    return { isValid: true, errors: [], warnings: [] };
  }

  async executeAction(action: string, params: any, credentials: CredentialData): Promise<any> {
    // Implementation for Groq API calls
    return {};
  }

  protected getAuthHeaders(credentials: Record<string, any>): Record<string, string> {
    return { 'Authorization': `Bearer ${credentials.api_key}` };
  }

  protected getBaseUrl(): string {
    return 'https://api.groq.com/openai/v1';
  }
}
