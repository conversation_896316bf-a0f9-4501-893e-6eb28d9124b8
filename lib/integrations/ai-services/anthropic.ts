// Anthropic Integration Implementation

import { BaseIntegration } from '../core/base-integration';
import { CredentialField, Action, CredentialData, CredentialValidationResult, AppType } from '../types/integration-types';
import { CREDENTIAL_FIELDS } from '../types/credential-types';

export class AnthropicIntegration extends BaseIntegration {
  readonly id: AppType = 'anthropic';
  readonly name = 'Anthropic';
  readonly description = 'Claude AI models for advanced reasoning and analysis';
  readonly category = 'ai_services' as const;
  readonly icon = '🧠';
  readonly color = '#D97706';
  readonly website = 'https://anthropic.com';
  readonly documentation = 'https://docs.anthropic.com';

  getRequiredFields(): CredentialField[] {
    return CREDENTIAL_FIELDS.anthropic;
  }

  getAvailableActions(): Action[] {
    return [
      {
        id: 'chat_completion',
        name: 'Chat Completion',
        description: 'Generate text using Claude models',
        category: 'ai',
        inputs: [
          {
            name: 'messages',
            label: 'Messages',
            type: 'textarea',
            required: true,
            description: 'Array of messages or single message text'
          }
        ],
        outputs: {
          content: 'string',
          usage: 'object',
          model: 'string'
        }
      }
    ];
  }

  async validateCredentials(credentials: CredentialData): Promise<CredentialValidationResult> {
    return { isValid: true, errors: [], warnings: [] };
  }

  async executeAction(action: string, params: any, credentials: CredentialData): Promise<any> {
    return {};
  }

  protected getAuthHeaders(credentials: Record<string, any>): Record<string, string> {
    return { 
      'x-api-key': credentials.api_key,
      'anthropic-version': '2023-06-01'
    };
  }

  protected getBaseUrl(): string {
    return 'https://api.anthropic.com';
  }
}
