// Integration Types for Credential Management System

export interface CredentialField {
  name: string;
  label: string;
  type: 'text' | 'password' | 'email' | 'url' | 'select' | 'textarea';
  required: boolean;
  placeholder?: string;
  description?: string;
  options?: Array<{ label: string; value: string }>;
  validation?: {
    pattern?: string;
    minLength?: number;
    maxLength?: number;
  };
}

export interface Action {
  id: string;
  name: string;
  description: string;
  category: 'ai' | 'data' | 'communication' | 'automation' | 'storage';
  inputs: CredentialField[];
  outputs: Record<string, any>;
}

export interface Integration {
  id: string;
  name: string;
  description: string;
  category: 'ai_services' | 'business_apps' | 'databases' | 'communication' | 'storage';
  icon: string;
  color: string;
  website?: string;
  documentation?: string;
  requiredFields: CredentialField[];
  actions: Action[];
  isActive: boolean;
}

export interface Credential {
  id: string;
  name: string;
  appType: AppType;
  teamId: string;
  encryptedCredentials: string; // AES-256 encrypted JSON
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
  lastUsedAt?: Date;
  usageCount: number;
  metadata?: {
    version?: string;
    environment?: 'development' | 'staging' | 'production';
    tags?: string[];
  };
}

export type AppType = 
  | 'openai'
  | 'groq' 
  | 'anthropic'
  | 'google_gemini'
  | 'fireworks_ai'
  | 'mistral_ai'
  | 'airtable'
  | 'slack'
  | 'google_sheets'
  | 'whatsapp'
  | 'custom';

export interface CredentialData {
  // AI Services
  api_key?: string;
  organization_id?: string;
  project_id?: string;
  model?: string;
  endpoint?: string;
  
  // OAuth
  access_token?: string;
  refresh_token?: string;
  expires_at?: Date;
  scope?: string[];
  
  // Basic Auth
  username?: string;
  password?: string;
  
  // Custom fields
  [key: string]: any;
}

export interface CredentialValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  connectionTest?: {
    success: boolean;
    responseTime?: number;
    message?: string;
  };
}

export interface CredentialUsage {
  workflowId: string;
  workflowName: string;
  nodeId: string;
  nodeName: string;
  lastUsed: Date;
  usageCount: number;
}

// Encryption interfaces
export interface EncryptionConfig {
  algorithm: 'aes-256-gcm';
  keyDerivation: 'pbkdf2';
  iterations: number;
  saltLength: number;
  ivLength: number;
}

export interface EncryptedData {
  data: string;
  iv: string;
  salt: string;
  tag: string;
}

// API interfaces
export interface CreateCredentialRequest {
  name: string;
  appType: AppType;
  credentials: CredentialData;
  metadata?: Credential['metadata'];
}

export interface UpdateCredentialRequest {
  name?: string;
  credentials?: CredentialData;
  isActive?: boolean;
  metadata?: Credential['metadata'];
}

export interface CredentialListResponse {
  credentials: Credential[];
  total: number;
  page: number;
  limit: number;
}

export interface CredentialTestRequest {
  appType: AppType;
  credentials: CredentialData;
}

// Integration registry
export interface IntegrationRegistry {
  [key: string]: Integration;
}

// Node configuration with credentials
export interface NodeCredentialConfig {
  credentialId?: string;
  credentialType?: AppType;
  overrideCredentials?: CredentialData;
}

// Workflow execution context
export interface ExecutionCredentialContext {
  credentials: Map<string, CredentialData>;
  getCredential: (credentialId: string) => Promise<CredentialData | null>;
  validateCredential: (credentialId: string, appType: AppType) => Promise<boolean>;
}
