// Credential Type Definitions for Specific Integrations

import { CredentialField, CredentialData } from './integration-types';

// AI Service Credentials
export interface OpenAICredentials extends CredentialData {
  api_key: string;
  organization_id?: string;
  model?: 'gpt-4' | 'gpt-4-turbo' | 'gpt-3.5-turbo' | 'gpt-4o' | 'gpt-4o-mini';
  max_tokens?: number;
  temperature?: number;
}

export interface GroqCredentials extends CredentialData {
  api_key: string;
  model?: 'llama3-8b-8192' | 'llama3-70b-8192' | 'mixtral-8x7b-32768' | 'gemma-7b-it';
  max_tokens?: number;
  temperature?: number;
}

export interface AnthropicCredentials extends CredentialData {
  api_key: string;
  model?: 'claude-3-5-sonnet-20241022' | 'claude-3-opus-20240229' | 'claude-3-haiku-20240307';
  max_tokens?: number;
  temperature?: number;
  version?: string;
}

export interface GoogleGeminiCredentials extends CredentialData {
  api_key: string;
  project_id?: string;
  model?: 'gemini-1.5-pro' | 'gemini-1.5-flash' | 'gemini-pro';
  location?: string;
}

export interface FireworksAICredentials extends CredentialData {
  api_key: string;
  model?: string;
  endpoint?: string;
}

export interface MistralAICredentials extends CredentialData {
  api_key: string;
  model?: 'mistral-large-latest' | 'mistral-medium-latest' | 'mistral-small-latest';
  endpoint?: string;
}

// Business Application Credentials
export interface AirtableCredentials extends CredentialData {
  api_key: string;
  base_id: string;
  workspace_id?: string;
}

export interface SlackCredentials extends CredentialData {
  bot_token: string;
  app_token?: string;
  workspace_id: string;
  signing_secret?: string;
  client_id?: string;
  client_secret?: string;
}

export interface GoogleSheetsCredentials extends CredentialData {
  access_token: string;
  refresh_token: string;
  client_id: string;
  client_secret: string;
  expires_at: Date;
  scope: string[];
}

export interface WhatsAppCredentials extends CredentialData {
  access_token: string;
  phone_number_id: string;
  business_account_id: string;
  app_id: string;
  app_secret?: string;
  webhook_verify_token?: string;
}

// Credential Field Definitions
export const CREDENTIAL_FIELDS: Record<string, CredentialField[]> = {
  openai: [
    {
      name: 'api_key',
      label: 'API Key',
      type: 'password',
      required: true,
      placeholder: 'sk-...',
      description: 'Your OpenAI API key from platform.openai.com'
    },
    {
      name: 'organization_id',
      label: 'Organization ID',
      type: 'text',
      required: false,
      placeholder: 'org-...',
      description: 'Optional organization ID for team accounts'
    },
    {
      name: 'model',
      label: 'Default Model',
      type: 'select',
      required: false,
      options: [
        { label: 'GPT-4o', value: 'gpt-4o' },
        { label: 'GPT-4o Mini', value: 'gpt-4o-mini' },
        { label: 'GPT-4 Turbo', value: 'gpt-4-turbo' },
        { label: 'GPT-4', value: 'gpt-4' },
        { label: 'GPT-3.5 Turbo', value: 'gpt-3.5-turbo' }
      ]
    }
  ],
  
  groq: [
    {
      name: 'api_key',
      label: 'API Key',
      type: 'password',
      required: true,
      placeholder: 'gsk_...',
      description: 'Your Groq API key from console.groq.com'
    },
    {
      name: 'model',
      label: 'Default Model',
      type: 'select',
      required: false,
      options: [
        { label: 'Llama 3 8B', value: 'llama3-8b-8192' },
        { label: 'Llama 3 70B', value: 'llama3-70b-8192' },
        { label: 'Mixtral 8x7B', value: 'mixtral-8x7b-32768' },
        { label: 'Gemma 7B', value: 'gemma-7b-it' }
      ]
    }
  ],
  
  anthropic: [
    {
      name: 'api_key',
      label: 'API Key',
      type: 'password',
      required: true,
      placeholder: 'sk-ant-...',
      description: 'Your Anthropic API key from console.anthropic.com'
    },
    {
      name: 'model',
      label: 'Default Model',
      type: 'select',
      required: false,
      options: [
        { label: 'Claude 3.5 Sonnet', value: 'claude-3-5-sonnet-20241022' },
        { label: 'Claude 3 Opus', value: 'claude-3-opus-20240229' },
        { label: 'Claude 3 Haiku', value: 'claude-3-haiku-20240307' }
      ]
    }
  ],
  
  google_gemini: [
    {
      name: 'api_key',
      label: 'API Key',
      type: 'password',
      required: true,
      placeholder: 'AIza...',
      description: 'Your Google AI API key from aistudio.google.com'
    },
    {
      name: 'project_id',
      label: 'Project ID',
      type: 'text',
      required: false,
      placeholder: 'my-project-123',
      description: 'Google Cloud Project ID (for Vertex AI)'
    }
  ],
  
  airtable: [
    {
      name: 'api_key',
      label: 'API Key',
      type: 'password',
      required: true,
      placeholder: 'pat...',
      description: 'Your Airtable Personal Access Token'
    },
    {
      name: 'base_id',
      label: 'Base ID',
      type: 'text',
      required: true,
      placeholder: 'app...',
      description: 'The ID of your Airtable base'
    }
  ],
  
  slack: [
    {
      name: 'bot_token',
      label: 'Bot Token',
      type: 'password',
      required: true,
      placeholder: 'xoxb-...',
      description: 'Your Slack Bot User OAuth Token'
    },
    {
      name: 'workspace_id',
      label: 'Workspace ID',
      type: 'text',
      required: true,
      placeholder: 'T...',
      description: 'Your Slack workspace ID'
    },
    {
      name: 'signing_secret',
      label: 'Signing Secret',
      type: 'password',
      required: false,
      description: 'For webhook verification (optional)'
    }
  ],
  
  whatsapp: [
    {
      name: 'access_token',
      label: 'Access Token',
      type: 'password',
      required: true,
      description: 'WhatsApp Business API access token'
    },
    {
      name: 'phone_number_id',
      label: 'Phone Number ID',
      type: 'text',
      required: true,
      description: 'WhatsApp Business phone number ID'
    },
    {
      name: 'business_account_id',
      label: 'Business Account ID',
      type: 'text',
      required: true,
      description: 'WhatsApp Business account ID'
    }
  ]
};

// Validation patterns
export const VALIDATION_PATTERNS = {
  openai_api_key: /^sk-[a-zA-Z0-9]{48}$/,
  groq_api_key: /^gsk_[a-zA-Z0-9]{52}$/,
  anthropic_api_key: /^sk-ant-[a-zA-Z0-9\-_]{95}$/,
  google_api_key: /^AIza[a-zA-Z0-9\-_]{35}$/,
  airtable_token: /^pat[a-zA-Z0-9]{14}\.[a-zA-Z0-9]{16}$/,
  slack_bot_token: /^xoxb-[0-9]{11,13}-[0-9]{11,13}-[a-zA-Z0-9]{24}$/,
  email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  url: /^https?:\/\/.+/
};
