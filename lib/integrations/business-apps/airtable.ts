// Airtable Integration Implementation

import { BaseIntegration } from '../core/base-integration';
import { CredentialField, Action, CredentialData, CredentialValidationResult, AppType } from '../types/integration-types';
import { CREDENTIAL_FIELDS } from '../types/credential-types';

export class AirtableIntegration extends BaseIntegration {
  readonly id: AppType = 'airtable';
  readonly name = 'Airtable';
  readonly description = 'Cloud-based database and spreadsheet platform';
  readonly category = 'business_apps' as const;
  readonly icon = '📊';
  readonly color = '#18BFFF';
  readonly website = 'https://airtable.com';
  readonly documentation = 'https://airtable.com/developers/web/api/introduction';

  getRequiredFields(): CredentialField[] {
    return CREDENTIAL_FIELDS.airtable;
  }

  getAvailableActions(): Action[] {
    return [
      {
        id: 'list_records',
        name: 'List Records',
        description: 'Retrieve records from a table',
        category: 'data',
        inputs: [
          {
            name: 'table_name',
            label: 'Table Name',
            type: 'text',
            required: true,
            description: 'Name of the table to query'
          }
        ],
        outputs: {
          records: 'array',
          total: 'number'
        }
      },
      {
        id: 'create_record',
        name: 'Create Record',
        description: 'Create a new record in a table',
        category: 'data',
        inputs: [
          {
            name: 'table_name',
            label: 'Table Name',
            type: 'text',
            required: true,
            description: 'Name of the table'
          },
          {
            name: 'fields',
            label: 'Fields',
            type: 'textarea',
            required: true,
            description: 'JSON object with field values'
          }
        ],
        outputs: {
          id: 'string',
          fields: 'object',
          created_time: 'string'
        }
      }
    ];
  }

  async validateCredentials(credentials: CredentialData): Promise<CredentialValidationResult> {
    return { isValid: true, errors: [], warnings: [] };
  }

  async executeAction(action: string, params: any, credentials: CredentialData): Promise<any> {
    return {};
  }

  protected getAuthHeaders(credentials: Record<string, any>): Record<string, string> {
    return { 'Authorization': `Bearer ${credentials.api_key}` };
  }

  protected getBaseUrl(): string {
    return 'https://api.airtable.com/v0';
  }
}
