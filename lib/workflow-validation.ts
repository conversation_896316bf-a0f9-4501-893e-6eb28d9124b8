// Workflow validation utilities

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  canExecuteAutomatically: boolean;
}

// Node type definitions
export const NODE_TYPES = {
  TRIGGERS: ['webhook', 'schedule', 'message', 'email_received', 'file_upload'],
  AI_AGENTS: ['ai_agent', 'conversation_agent', 'research_agent'], // All AI agent variants
  AI_TOOLS: ['web_search', 'calculator', 'code_interpreter', 'image_generator', 'email_composer', 'memory'],
  APP_ACTIONS: ['api_call', 'database_query', 'send_email', 'file_save', 'webhook_call', 'response'],
  LOGIC: ['condition', 'filter', 'transform', 'merge', 'split', 'delay'],
  MEMORY: ['memory', 'vector_store', 'session_data', 'workflow_variables']
} as const;

// Connection rules - what each node type can connect TO
export const CONNECTION_RULES = {
  triggers: ['ai_agent', 'logic', 'app_actions'],
  ai_agent: ['ai_agent', 'logic', 'app_actions', 'ai_tools'], // All AI agents can connect to each other, logic, actions, and tools
  ai_tools: [], // Cannot connect to anything (return to parent agent)
  app_actions: ['ai_agent', 'logic', 'app_actions'],
  logic: ['ai_agent', 'logic', 'app_actions'],
  memory: ['ai_agent'] // As sub-node, or standalone
} as const;

// Get node category
export function getNodeCategory(nodeType: string): string {
  if (NODE_TYPES.TRIGGERS.includes(nodeType as any)) return 'triggers';
  if (NODE_TYPES.AI_AGENTS.includes(nodeType as any)) return 'ai_agent'; // All AI agent variants return 'ai_agent'
  if (NODE_TYPES.AI_TOOLS.includes(nodeType as any)) return 'ai_tools';
  if (NODE_TYPES.APP_ACTIONS.includes(nodeType as any)) return 'app_actions';
  if (NODE_TYPES.LOGIC.includes(nodeType as any)) return 'logic';
  if (NODE_TYPES.MEMORY.includes(nodeType as any)) return 'memory';
  return 'unknown';
}

// Helper function to check if a node is an AI agent
export function isAIAgent(nodeType: string): boolean {
  return NODE_TYPES.AI_AGENTS.includes(nodeType as any);
}

// Check if a connection is valid
export function isValidConnection(sourceNodeType: string, targetNodeType: string): boolean {
  const sourceCategory = getNodeCategory(sourceNodeType);
  const targetCategory = getNodeCategory(targetNodeType);

  // RULE: Nothing can connect TO trigger nodes (triggers are entry points)
  if (NODE_TYPES.TRIGGERS.includes(targetNodeType as any)) {
    return false;
  }

  // Get allowed target categories for source
  const allowedTargets = CONNECTION_RULES[sourceCategory as keyof typeof CONNECTION_RULES] || [];

  return allowedTargets.includes(targetCategory as any);
}

// Check if workflow has at least one trigger
export function hasTrigger(nodes: any[]): boolean {
  return nodes.some(node => NODE_TYPES.TRIGGERS.includes(node.type));
}

// Check if trigger is connected to at least one node
export function isTriggerConnected(nodes: any[], edges: any[]): boolean {
  const triggerNodes = nodes.filter(node => NODE_TYPES.TRIGGERS.includes(node.type));
  
  if (triggerNodes.length === 0) return false;
  
  // Check if at least one trigger has outgoing connections
  return triggerNodes.some(trigger => 
    edges.some(edge => edge.source === trigger.id)
  );
}

// Find nodes that have no incoming connections (potential entry points)
export function findEntryNodes(nodes: any[], edges: any[]): any[] {
  const nodesWithIncoming = new Set(edges.map(edge => edge.target));
  return nodes.filter(node => !nodesWithIncoming.has(node.id));
}

// Check if all trigger nodes are properly positioned (no incoming connections)
export function validateTriggerPositioning(nodes: any[], edges: any[]): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];
  const triggerNodes = nodes.filter(node => NODE_TYPES.TRIGGERS.includes(node.type));
  const nodesWithIncoming = new Set(edges.map(edge => edge.target));

  triggerNodes.forEach(trigger => {
    if (nodesWithIncoming.has(trigger.id)) {
      errors.push(`Trigger node "${trigger.data?.label || trigger.type}" has incoming connections. Triggers must be workflow entry points.`);
    }
  });

  return {
    isValid: errors.length === 0,
    errors
  };
}

// Validate entire workflow
export function validateWorkflow(nodes: any[], edges: any[]): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];
  
  // Check for triggers
  const hasValidTrigger = hasTrigger(nodes);
  const triggerConnected = isTriggerConnected(nodes, edges);
  
  if (!hasValidTrigger) {
    warnings.push('No trigger found. Workflow can only be executed manually.');
  } else if (!triggerConnected) {
    warnings.push('Trigger is not connected to any nodes. Workflow can only be executed manually.');
  }
  
  // Validate trigger positioning (no incoming connections)
  const triggerValidation = validateTriggerPositioning(nodes, edges);
  if (!triggerValidation.isValid) {
    errors.push(...triggerValidation.errors);
  }

  // Validate all connections
  const invalidConnections: string[] = [];
  edges.forEach(edge => {
    const sourceNode = nodes.find(n => n.id === edge.source);
    const targetNode = nodes.find(n => n.id === edge.target);

    if (!sourceNode || !targetNode) {
      errors.push(`Invalid edge: missing source or target node`);
      return;
    }

    if (!isValidConnection(sourceNode.type, targetNode.type)) {
      const sourceCategory = getNodeCategory(sourceNode.type);
      const targetCategory = getNodeCategory(targetNode.type);
      invalidConnections.push(
        `${sourceNode.data?.label || sourceNode.type} (${sourceCategory}) cannot connect to ${targetNode.data?.label || targetNode.type} (${targetCategory})`
      );
    }
  });

  if (invalidConnections.length > 0) {
    errors.push(...invalidConnections);
  }
  
  // Check for orphaned nodes (no connections at all)
  const connectedNodes = new Set([
    ...edges.map(e => e.source),
    ...edges.map(e => e.target)
  ]);
  
  const orphanedNodes = nodes.filter(node => !connectedNodes.has(node.id));
  if (orphanedNodes.length > 0) {
    warnings.push(`${orphanedNodes.length} unconnected nodes found`);
  }
  
  // Check for cycles (basic check)
  const hasCycles = detectCycles(nodes, edges);
  if (hasCycles) {
    warnings.push('Potential infinite loop detected in workflow');
  }
  
  const canExecuteAutomatically = hasValidTrigger && triggerConnected;
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    canExecuteAutomatically
  };
}

// Basic cycle detection
function detectCycles(nodes: any[], edges: any[]): boolean {
  const visited = new Set<string>();
  const recursionStack = new Set<string>();
  
  function dfs(nodeId: string): boolean {
    if (recursionStack.has(nodeId)) return true; // Cycle found
    if (visited.has(nodeId)) return false;
    
    visited.add(nodeId);
    recursionStack.add(nodeId);
    
    const outgoingEdges = edges.filter(edge => edge.source === nodeId);
    for (const edge of outgoingEdges) {
      if (dfs(edge.target)) return true;
    }
    
    recursionStack.delete(nodeId);
    return false;
  }
  
  for (const node of nodes) {
    if (!visited.has(node.id)) {
      if (dfs(node.id)) return true;
    }
  }
  
  return false;
}

// Get connection validation message
export function getConnectionValidationMessage(sourceNodeType: string, targetNodeType: string): string {
  const sourceCategory = getNodeCategory(sourceNodeType);
  const targetCategory = getNodeCategory(targetNodeType);

  if (isValidConnection(sourceNodeType, targetNodeType)) {
    // Special message for AI agent to AI agent connections
    if (isAIAgent(sourceNodeType) && isAIAgent(targetNodeType)) {
      return `✅ Valid AI agent chain: ${sourceNodeType} → ${targetNodeType}`;
    }
    return `✅ Valid connection: ${sourceCategory} → ${targetCategory}`;
  }

  // Provide specific error messages
  if (NODE_TYPES.TRIGGERS.includes(targetNodeType as any)) {
    return `❌ Cannot connect to trigger nodes. Triggers (${targetNodeType}) are workflow entry points and cannot receive connections.`;
  }

  if (sourceCategory === 'ai_tools') {
    return `❌ AI tools cannot connect directly to other nodes. They must return to their parent AI agent.`;
  }

  if (targetCategory === 'ai_tools' && sourceCategory !== 'ai_agent') {
    return `❌ Only AI agents can connect to AI tools. ${sourceCategory} cannot directly use AI tools.`;
  }

  // Special message for AI agent connections
  if (isAIAgent(sourceNodeType)) {
    return `❌ AI agents (${sourceNodeType}) can connect to: other AI agents, logic nodes, actions, and AI tools.`;
  }

  const allowedTargets = CONNECTION_RULES[sourceCategory as keyof typeof CONNECTION_RULES] || [];
  return `❌ ${sourceCategory} can only connect to: ${allowedTargets.join(', ')}`;
}
