import { Queue, Worker, Job } from 'bullmq';
import Redis from 'ioredis';
import { LangGraphExecutionEngine, WorkflowDefinition } from './langgraph-engine';
import { connectToDatabase } from '@/lib/mongodb';
import { ObjectId } from 'mongodb';
import { getRedisConnectionWithValidation, testRedisConnection } from './redis-connection';

// Redis connection configuration
const redisConfig = {
  host: process.env.REDIS_HOST || 'localhost',
  port: parseInt(process.env.REDIS_PORT || '6379'),
  password: process.env.REDIS_PASSWORD,

  // Connection resilience settings
  connectTimeout: 10000, // 10 seconds
  commandTimeout: 5000,  // 5 seconds
  enableReadyCheck: false,
  maxRetriesPerRequest: 3,

  // Reconnection settings
  lazyConnect: true,
  keepAlive: 30000, // 30 seconds
  family: 4, // Force IPv4

  // Error handling
  retryDelayOnClusterDown: 300,
  retryDelayOnFailover: 100,

  // TLS for secure connections (if using rediss://)
  ...(process.env.REDIS_HOST?.includes('aivencloud.com') && {
    tls: {
      rejectUnauthorized: false, // For cloud Redis instances
    }
  }),
};

// Job data interfaces
export interface WorkflowExecutionJob {
  workflowId: string;
  executionId: string;
  userId: string;
  input: Record<string, any>;
  mode: 'sync' | 'async';
}

export interface NodeTestJob {
  workflowId: string;
  nodeId: string;
  userId: string;
  input: Record<string, any>;
  variables: Record<string, any>;
}

export class QueueManager {
  private redis!: Redis;
  private workflowQueue!: Queue;
  private nodeTestQueue!: Queue;
  private workflowWorker!: Worker;
  private nodeTestWorker!: Worker;
  private executionEngine: LangGraphExecutionEngine;
  public initialized = false;

  constructor() {
    console.log('🚀 Initializing Queue Manager');

    // Initialize execution engine
    this.executionEngine = new LangGraphExecutionEngine();

    // Initialize async components
    this.initialize().catch(error => {
      console.error('❌ Failed to initialize Queue Manager:', error);
    });
  }

  /**
   * Async initialization method
   */
  private async initialize(): Promise<void> {
    try {
      console.log('🔄 Setting up Redis connection...');

      // Get validated Redis connection (throws error if fails)
      this.redis = await getRedisConnectionWithValidation();
      console.log('✅ Redis connection established');

      // Initialize queues
      this.initializeQueues();

      // Initialize workers
      this.initializeWorkers();

      this.initialized = true;
      console.log('✅ Queue Manager initialized successfully');

    } catch (error) {
      console.error('❌ Queue Manager initialization failed:', error);
      console.error('💡 Please check your Redis configuration:');
      console.error('   - REDIS_URL or REDIS_HOST/REDIS_PORT/REDIS_PASSWORD');
      console.error('   - Redis server is running and accessible');
      throw error; // Re-throw to prevent silent failures
    }
  }

  /**
   * Initialize queues
   */
  private initializeQueues(): void {
    // Get retry configuration from environment
    const retryAttempts = parseInt(process.env.QUEUE_RETRY_ATTEMPTS || '3');
    const retryDelay = parseInt(process.env.QUEUE_RETRY_DELAY || '2000');
    const isDevelopment = process.env.NODE_ENV === 'development';

    console.log(`🔧 Queue retry config: attempts=${retryAttempts}, delay=${retryDelay}ms, dev=${isDevelopment}`);

    this.workflowQueue = new Queue('workflow-execution', {
      connection: this.redis,
      defaultJobOptions: {
        removeOnComplete: 100,
        removeOnFail: 50,
        attempts: retryAttempts,
        backoff: retryAttempts > 0 ? {
          type: 'exponential',
          delay: retryDelay,
        } : undefined,
      },
    });

    this.nodeTestQueue = new Queue('node-test', {
      connection: this.redis,
      defaultJobOptions: {
        removeOnComplete: 50,
        removeOnFail: 25,
        attempts: Math.max(1, Math.min(retryAttempts, 2)), // Node tests: max 2 attempts, min 1
        backoff: retryAttempts > 0 ? {
          type: 'exponential',
          delay: Math.min(retryDelay, 1000), // Node tests: shorter delay
        } : undefined,
      },
    });

    if (isDevelopment && retryAttempts === 0) {
      console.log('🚫 Development mode: Queue retries disabled');
    }
  }



  /**
   * Initialize queue workers
   */
  private initializeWorkers(): void {
    console.log('👷 Initializing queue workers');

    // Workflow execution worker
    this.workflowWorker = new Worker(
      'workflow-execution',
      async (job: Job<WorkflowExecutionJob>) => {
        return await this.processWorkflowExecution(job);
      },
      {
        connection: this.redis,
        concurrency: 5, // Process up to 5 workflows concurrently
      }
    );

    // Node test worker
    this.nodeTestWorker = new Worker(
      'node-test',
      async (job: Job<NodeTestJob>) => {
        return await this.processNodeTest(job);
      },
      {
        connection: this.redis,
        concurrency: 10, // Process up to 10 node tests concurrently
      }
    );

    // Set up event listeners
    this.setupEventListeners();
    
    console.log('✅ Queue workers initialized');
  }

  /**
   * Set up event listeners for monitoring
   */
  private setupEventListeners(): void {
    console.log('📡 Setting up queue event listeners...');

    // Attach event listeners to both workers
    this.attachWorkflowWorkerEvents();
    this.attachNodeTestWorkerEvents();
  }

  /**
   * Restart workflow worker when it gets stuck
   */
  private async restartWorkflowWorker(): Promise<void> {
    try {
      console.log('🔄 Closing stuck workflow worker...');
      await this.workflowWorker.close();

      console.log('🚀 Creating new workflow worker...');
      this.workflowWorker = new Worker(
        'workflow-execution',
        async (job: Job<WorkflowExecutionJob>) => {
          return await this.processWorkflowExecution(job);
        },
        {
          connection: this.redis,
          concurrency: 5, // Process up to 5 workflows concurrently
        }
      );

      // Re-attach event listeners
      this.attachWorkflowWorkerEvents();

      console.log('✅ Workflow worker restarted successfully');
    } catch (error) {
      console.error('❌ Failed to restart workflow worker:', error);
    }
  }

  /**
   * Restart node test worker when it gets stuck
   */
  private async restartNodeTestWorker(): Promise<void> {
    try {
      console.log('🔄 Closing stuck node test worker...');
      await this.nodeTestWorker.close();

      console.log('🚀 Creating new node test worker...');
      this.nodeTestWorker = new Worker(
        'node-test',
        async (job: Job<NodeTestJob>) => {
          return await this.processNodeTest(job);
        },
        {
          connection: this.redis,
          concurrency: 10, // Process up to 10 node tests concurrently
        }
      );

      // Re-attach event listeners
      this.attachNodeTestWorkerEvents();

      console.log('✅ Node test worker restarted successfully');
    } catch (error) {
      console.error('❌ Failed to restart node test worker:', error);
    }
  }

  /**
   * Attach event listeners to workflow worker
   */
  private attachWorkflowWorkerEvents(): void {
    this.workflowWorker.on('completed', (job) => {
      console.log(`✅ Workflow job ${job.id} completed successfully`);
    });

    this.workflowWorker.on('failed', (job, err) => {
      console.error(`❌ Workflow job ${job?.id} failed:`, err.message);
    });

    this.workflowWorker.on('error', (err) => {
      console.error('❌ Workflow worker error:', err.message);

      // If it's a timeout error, restart the worker
      if (err.message.includes('Command timed out') || err.message.includes('timeout')) {
        console.log('🔄 Restarting workflow worker due to timeout...');
        this.restartWorkflowWorker();
      }
    });

    this.workflowWorker.on('stalled', (jobId) => {
      console.warn(`⚠️ Workflow job ${jobId} stalled - will be retried`);
    });
  }

  /**
   * Attach event listeners to node test worker
   */
  private attachNodeTestWorkerEvents(): void {
    this.nodeTestWorker.on('completed', (job) => {
      console.log(`✅ Node test job ${job.id} completed successfully`);
    });

    this.nodeTestWorker.on('failed', (job, err) => {
      console.error(`❌ Node test job ${job?.id} failed:`, err.message);
    });

    this.nodeTestWorker.on('error', (err) => {
      console.error('❌ Node test worker error:', err.message);

      // If it's a timeout error, restart the worker
      if (err.message.includes('Command timed out') || err.message.includes('timeout')) {
        console.log('🔄 Restarting node test worker due to timeout...');
        this.restartNodeTestWorker();
      }
    });

    this.nodeTestWorker.on('stalled', (jobId) => {
      console.warn(`⚠️ Node test job ${jobId} stalled - will be retried`);
    });
  }

  /**
   * Queue a workflow for execution
   */
  async queueWorkflowExecution(
    workflowId: string,
    executionId: string,
    userId: string,
    input: Record<string, any>,
    mode: 'sync' | 'async' = 'async'
  ): Promise<Job<WorkflowExecutionJob>> {
    console.log(`📋 Queueing workflow execution: ${workflowId}`);
    console.log(`🆔 Execution ID: ${executionId}`);
    console.log(`👤 User ID: ${userId}`);
    console.log(`⚙️ Mode: ${mode}`);

    const jobData: WorkflowExecutionJob = {
      workflowId,
      executionId,
      userId,
      input,
      mode,
    };

    const job = await this.workflowQueue.add(
      'execute-workflow',
      jobData,
      {
        jobId: executionId, // Use execution ID as job ID for tracking
        priority: mode === 'sync' ? 10 : 5, // Higher priority for sync execution
      }
    );

    console.log(`✅ Workflow queued with job ID: ${job.id}`);
    return job;
  }

  /**
   * Queue a node for testing
   */
  async queueNodeTest(
    workflowId: string,
    nodeId: string,
    userId: string,
    input: Record<string, any>,
    variables: Record<string, any> = {}
  ): Promise<Job<NodeTestJob>> {
    console.log(`🔧 Queueing node test: ${nodeId} in workflow ${workflowId}`);

    const jobData: NodeTestJob = {
      workflowId,
      nodeId,
      userId,
      input,
      variables,
    };

    const job = await this.nodeTestQueue.add(
      'test-node',
      jobData,
      {
        priority: 15, // High priority for testing
      }
    );

    console.log(`✅ Node test queued with job ID: ${job.id}`);
    return job;
  }

  /**
   * Process workflow execution job
   */
  private async processWorkflowExecution(job: Job<WorkflowExecutionJob>): Promise<any> {
    const { workflowId, executionId, userId, input, mode } = job.data;
    
    console.log(`🎯 Processing workflow execution job: ${job.id}`);
    console.log(`📋 Workflow ID: ${workflowId}`);
    console.log(`🆔 Execution ID: ${executionId}`);

    try {
      // Update job progress
      await job.updateProgress(10);

      // Fetch workflow definition from database
      const { db } = await connectToDatabase();
      const workflow = await db.collection('workflows').findOne({
        _id: new ObjectId(workflowId)
      });

      if (!workflow) {
        throw new Error(`Workflow not found: ${workflowId}`);
      }

      await job.updateProgress(20);

      // Execute the workflow
      console.log(`🚀 Starting workflow execution: ${workflow.name}`);
      const result = await this.executionEngine.executeWorkflow(
        workflow as unknown as WorkflowDefinition,
        input,
        executionId,
        userId
      );

      await job.updateProgress(100);

      console.log(`✅ Workflow execution completed: ${executionId}`);
      return result;

    } catch (error) {
      console.error(`❌ Workflow execution failed: ${executionId}`, error);
      
      // Update execution status in database
      try {
        const { db } = await connectToDatabase();
        await db.collection('workflow_executions').updateOne(
          { _id: new ObjectId(executionId) },
          {
            $set: {
              status: 'failed',
              error: error instanceof Error ? error.message : 'Unknown error',
              endTime: new Date(),
              updatedAt: new Date(),
            }
          }
        );
      } catch (dbError) {
        console.error(`❌ Failed to update execution status:`, dbError);
      }

      throw error;
    }
  }

  /**
   * Process node test job
   */
  private async processNodeTest(job: Job<NodeTestJob>): Promise<any> {
    const { workflowId, nodeId, userId, input, variables } = job.data;
    
    console.log(`🔧 Processing node test job: ${job.id}`);
    console.log(`📋 Workflow ID: ${workflowId}`);
    console.log(`🔧 Node ID: ${nodeId}`);

    try {
      // Fetch workflow definition from database
      const { db } = await connectToDatabase();
      const workflow = await db.collection('workflows').findOne({
        _id: new ObjectId(workflowId)
      });

      if (!workflow) {
        throw new Error(`Workflow not found: ${workflowId}`);
      }

      // Find the specific node
      const node = workflow.nodes.find((n: any) => n.id === nodeId);
      if (!node) {
        throw new Error(`Node not found: ${nodeId}`);
      }

      // Execute the node
      console.log(`🔧 Testing node: ${node.name} (${node.type})`);
      const result = await this.executionEngine.executeNode(node, input, variables);

      console.log(`✅ Node test completed: ${nodeId}`);
      return result;

    } catch (error) {
      console.error(`❌ Node test failed: ${nodeId}`, error);
      throw error;
    }
  }

  /**
   * Get job status
   */
  async getJobStatus(jobId: string, queueType: 'workflow' | 'node-test'): Promise<any> {
    const queue = queueType === 'workflow' ? this.workflowQueue : this.nodeTestQueue;
    const job = await queue.getJob(jobId);
    
    if (!job) {
      return null;
    }

    return {
      id: job.id,
      name: job.name,
      data: job.data,
      progress: job.progress,
      returnvalue: job.returnvalue,
      failedReason: job.failedReason,
      processedOn: job.processedOn,
      finishedOn: job.finishedOn,
      opts: job.opts,
    };
  }

  /**
   * Cancel a job by execution ID
   */
  async cancelJob(executionId: string): Promise<boolean> {
    try {
      // Check workflow queue first
      const workflowJobs = await this.workflowQueue.getJobs(['waiting', 'active', 'delayed']);
      for (const job of workflowJobs) {
        if (job.data.executionId === executionId) {
          await job.remove();
          console.log(`✅ Cancelled workflow job: ${job.id} for execution: ${executionId}`);
          return true;
        }
      }

      // Check node test queue
      const nodeTestJobs = await this.nodeTestQueue.getJobs(['waiting', 'active', 'delayed']);
      for (const job of nodeTestJobs) {
        if (job.data.executionId === executionId) {
          await job.remove();
          console.log(`✅ Cancelled node test job: ${job.id} for execution: ${executionId}`);
          return true;
        }
      }

      console.log(`⚠️ No job found for execution: ${executionId}`);
      return false;

    } catch (error) {
      console.error(`❌ Failed to cancel job for execution ${executionId}:`, error);
      return false;
    }
  }

  /**
   * Clean up resources
   */
  async cleanup(): Promise<void> {
    console.log('🧹 Cleaning up Queue Manager');
    
    await this.workflowWorker.close();
    await this.nodeTestWorker.close();
    await this.workflowQueue.close();
    await this.nodeTestQueue.close();
    await this.redis.quit();
    
    console.log('✅ Queue Manager cleanup completed');
  }
}

// Singleton instance
let queueManager: QueueManager | null = null;
let initializationPromise: Promise<QueueManager> | null = null;

export async function getQueueManager(): Promise<QueueManager> {
  if (queueManager && queueManager.initialized) {
    return queueManager;
  }

  if (!initializationPromise) {
    initializationPromise = initializeQueueManager();
  }

  return await initializationPromise;
}

async function initializeQueueManager(): Promise<QueueManager> {
  if (!queueManager) {
    queueManager = new QueueManager();
  }

  // Wait for initialization to complete
  while (!queueManager.initialized) {
    await new Promise(resolve => setTimeout(resolve, 100));
  }

  return queueManager;
}
