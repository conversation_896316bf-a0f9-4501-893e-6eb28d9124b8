import { StateGraph, END, START } from "@langchain/langgraph";
import { BaseMessage, HumanMessage, AIMessage } from "@langchain/core/messages";
import { ChatOpenAI } from "@langchain/openai";
import { TavilySearchResults } from "@langchain/community/tools/tavily_search";
import { MemorySaver } from "@langchain/langgraph";
import { connectToDatabase } from "@/lib/mongodb";
import { CredentialManager } from "@/lib/integrations/core/credential-manager";

// Types for our workflow execution
export interface WorkflowState {
  messages: BaseMessage[];
  variables: Record<string, any>;
  currentNode: string;
  executionId: string;
  workflowId: string;
  userId: string;
  status: 'running' | 'completed' | 'failed' | 'paused';
  error?: string;
  nodeResults: Record<string, any>;
  startTime: Date;
  endTime?: Date;
}

export interface NodeConfig {
  id: string;
  type: string;
  name: string;
  config: Record<string, any>;
  position: { x: number; y: number };
  connections: Array<{ target: string; condition?: string }>;
}

export interface WorkflowDefinition {
  _id: string;
  name: string;
  nodes: NodeConfig[];
  edges: Array<{ source: string; target: string; condition?: string }>;
  variables: Record<string, any>;
  settings: Record<string, any>;
}

export class LangGraphExecutionEngine {
  private llm: ChatOpenAI;
  private searchTool: TavilySearchResults;
  private memory: MemorySaver;
  private activeTriggerNodeId?: string;
  private credentialManager: CredentialManager;

  constructor() {
    console.log("🚀 Initializing LangGraph Execution Engine");
    
    // Initialize OpenAI LLM
    this.llm = new ChatOpenAI({
      modelName: "gpt-4o",
      temperature: 0.7,
      openAIApiKey: process.env.OPENAI_API_KEY,
    });

    // Initialize Tavily search tool
    this.searchTool = new TavilySearchResults({
      maxResults: 5,
      apiKey: process.env.TAVILY_API_KEY,
    });

    // Initialize memory for conversation persistence
    this.memory = new MemorySaver();

    // Initialize credential manager
    this.credentialManager = CredentialManager.getInstance();

    console.log("✅ LangGraph Engine initialized successfully");
  }

  /**
   * Execute a complete workflow
   */
  async executeWorkflow(
    workflowDefinition: WorkflowDefinition,
    input: Record<string, any>,
    executionId: string,
    userId: string
  ): Promise<WorkflowState> {
    console.log(`🎯 Starting workflow execution: ${workflowDefinition.name}`);
    console.log(`📋 Execution ID: ${executionId}`);
    console.log(`👤 User ID: ${userId}`);
    console.log(`🔧 Input:`, input);

    const startTime = new Date();
    
    // Initialize workflow state
    const initialState: WorkflowState = {
      messages: input.message ? [new HumanMessage(input.message)] : [],
      variables: { ...workflowDefinition.variables, ...input },
      currentNode: this.findStartNode(workflowDefinition),
      executionId,
      workflowId: workflowDefinition._id,
      userId,
      status: 'running',
      nodeResults: {},
      startTime,
    };

    console.log(`🏁 Starting from node: ${initialState.currentNode}`);

    try {
      // Save initial execution state
      await this.saveExecutionState(initialState);

      // Build and execute the LangGraph
      const graph = await this.buildLangGraph(workflowDefinition);
      const finalState = await graph.invoke(initialState, {
        configurable: { thread_id: executionId }
      });

      // Mark as completed
      finalState.status = 'completed';
      finalState.endTime = new Date();

      console.log(`✅ Workflow execution completed successfully`);
      console.log(`⏱️ Execution time: ${finalState.endTime.getTime() - startTime.getTime()}ms`);

      // Save final state
      await this.saveExecutionState(finalState);

      return finalState;

    } catch (error) {
      console.error(`❌ Workflow execution failed:`, error);
      
      const failedState: WorkflowState = {
        ...initialState,
        status: 'failed',
        error: error instanceof Error ? error.message : 'Unknown error',
        endTime: new Date(),
      };

      await this.saveExecutionState(failedState);
      throw error;
    }
  }

  /**
   * Execute a single node
   */
  async executeNode(
    nodeConfig: NodeConfig,
    input: Record<string, any>,
    variables: Record<string, any> = {}
  ): Promise<any> {
    console.log(`🔧 Excecuting node: ${nodeConfig.name} (${nodeConfig.type})`);
    console.log(`📥 Input:`, input);
    console.log(`🔄 Variables:`, variables);

    const startTime = Date.now();

    try {
      let result;

      switch (nodeConfig.type) {
        case 'conversation_agent':
          result = await this.executeConversationAgent(nodeConfig, input, variables);
          break;
        case 'message':
          result = await this.executeMessageNode(nodeConfig, input, variables);
          break;
        case 'web_search':
          result = await this.executeWebSearch(nodeConfig, input, variables);
          break;
        case 'memory_store':
          result = await this.executeMemoryStore(nodeConfig, input, variables);
          break;
        case 'memory_recall':
          result = await this.executeMemoryRecall(nodeConfig, input, variables);
          break;
        case 'condition':
          result = await this.executeCondition(nodeConfig, input, variables);
          break;
        case 'variable_set':
          result = await this.executeVariableSet(nodeConfig, input, variables);
          break;
        case 'webhook':
          result = await this.executeWebhookNode(nodeConfig, input, variables);
          break;
        case 'response':
          result = await this.executeResponseNode(nodeConfig, input, variables);
          break;
        case 'api_call':
          result = await this.executeApiCall(nodeConfig, input, variables);
          break;
        case 'calculator':
          result = await this.executeCalculator(nodeConfig, input, variables);
          break;
        case 'ai_agent':
          result = await this.executeAiAgent(nodeConfig, input, variables);
          break;
        case 'research_agent':
          result = await this.executeResearchAgent(nodeConfig, input, variables);
          break;
        case 'email':
          result = await this.executeEmail(nodeConfig, input, variables);
          break;
        case 'database':
          result = await this.executeDatabase(nodeConfig, input, variables);
          break;
        default:
          throw new Error(`Unknown node type: ${nodeConfig.type}`);
      }

      const executionTime = Date.now() - startTime;
      console.log(`✅ Node execution completed in ${executionTime}ms`);
      console.log(`📤 Result:`, result);

      return {
        success: true,
        result,
        executionTime,
        timestamp: new Date().toISOString(),
      };

    } catch (error) {
      const executionTime = Date.now() - startTime;
      console.error(`❌ Node execution failed after ${executionTime}ms:`, error);
      
      throw {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        executionTime,
        timestamp: new Date().toISOString(),
      };
    }
  }

  /**
   * Filter triggers and clean up graph structure
   */
  private filterTriggersAndCleanGraph(workflowDefinition: WorkflowDefinition): {
    filteredNodes: any[];
    filteredEdges: any[];
  } {
    console.log('🔧 Filtering triggers and cleaning graph structure');

    const triggerTypes = ['webhook', 'message', 'schedule'];
    const allNodes = workflowDefinition.nodes || [];
    const allEdges = workflowDefinition.edges || [];

    // Find all trigger nodes
    const triggerNodes = allNodes.filter(node => triggerTypes.includes(node.type));

    // Determine active trigger based on execution context
    const activeTrigger = this.determineActiveTrigger(triggerNodes);

    if (!activeTrigger) {
      console.warn('⚠️ No active trigger found, including all nodes');
      return {
        filteredNodes: allNodes,
        filteredEdges: allEdges
      };
    }

    console.log(`🎯 Active trigger: ${activeTrigger.name} (${activeTrigger.type})`);

    // Filter out inactive triggers
    const inactiveTriggerIds = triggerNodes
      .filter(trigger => trigger.id !== activeTrigger.id)
      .map(trigger => trigger.id);

    console.log(`🗑️ Removing ${inactiveTriggerIds.length} inactive triggers:`, inactiveTriggerIds);

    // Remove inactive trigger nodes
    const filteredNodes = allNodes.filter(node => !inactiveTriggerIds.includes(node.id));

    // Remove edges connected to inactive triggers
    const filteredEdges = allEdges.filter(edge =>
      !inactiveTriggerIds.includes(edge.source) &&
      !inactiveTriggerIds.includes(edge.target)
    );

    // Validate graph integrity
    this.validateGraphIntegrity(filteredNodes, filteredEdges);

    console.log(`✅ Graph cleaned: ${filteredNodes.length} nodes, ${filteredEdges.length} edges`);

    return {
      filteredNodes,
      filteredEdges
    };
  }

  /**
   * Determine which trigger is active based on execution context
   */
  private determineActiveTrigger(triggerNodes: any[]): any | null {
    if (triggerNodes.length === 0) return null;
    if (triggerNodes.length === 1) return triggerNodes[0];

    // Check if we have an active trigger ID set during execution
    if (this.activeTriggerNodeId) {
      const activeTrigger = triggerNodes.find(t => t.id === this.activeTriggerNodeId);
      if (activeTrigger) return activeTrigger;
    }

    // Fallback: return the first trigger (should be set properly during execution)
    console.warn('⚠️ Multiple triggers found but no active trigger specified, using first trigger');
    return triggerNodes[0];
  }

  /**
   * Validate graph integrity after filtering
   */
  private validateGraphIntegrity(nodes: any[], edges: any[]): void {
    const nodeIds = new Set(nodes.map(n => n.id));

    // Check for orphaned edges
    const orphanedEdges = edges.filter(edge =>
      !nodeIds.has(edge.source) || !nodeIds.has(edge.target)
    );

    if (orphanedEdges.length > 0) {
      console.error('❌ Found orphaned edges after filtering:', orphanedEdges);
      throw new Error(`Graph integrity validation failed: ${orphanedEdges.length} orphaned edges found`);
    }

    // Check for isolated nodes (nodes with no connections)
    const connectedNodeIds = new Set();
    edges.forEach(edge => {
      connectedNodeIds.add(edge.source);
      connectedNodeIds.add(edge.target);
    });

    const isolatedNodes = nodes.filter(node =>
      !connectedNodeIds.has(node.id) &&
      !['webhook', 'message', 'schedule'].includes(node.type) // Triggers can be isolated [UPDATE, TRIGGERS/ACTIONS CAN ISOLATE]
    );

    if (isolatedNodes.length > 0) {
      console.warn('⚠️ Found isolated nodes (may be intentional):', isolatedNodes.map(n => n.name));
    }

    console.log('✅ Graph integrity validation passed');
  }

  /**
   * Build a LangGraph from workflow definition with trigger filtering
   */
  private async buildLangGraph(workflowDefinition: WorkflowDefinition) {
    console.log(`🏗️ Building LangGraph for workflow: ${workflowDefinition.name}`);

    // Filter triggers and clean up graph
    const { filteredNodes, filteredEdges } = this.filterTriggersAndCleanGraph(workflowDefinition);

    console.log(`🔧 Filtered ${workflowDefinition.nodes.length} nodes to ${filteredNodes.length} active nodes`);

    const workflow = new StateGraph({
      channels: {
        messages: { value: (x: BaseMessage[], y: BaseMessage[]) => x.concat(y) },
        variables: { value: (x: Record<string, any>, y: Record<string, any>) => ({ ...x, ...y }) },
        currentNode: { value: (x: string, y: string) => y || x },
        executionId: { value: (x: string) => x },
        workflowId: { value: (x: string) => x },
        userId: { value: (x: string) => x },
        status: { value: (x: string, y: string) => y || x },
        error: { value: (x: string, y: string) => y || x },
        nodeResults: { value: (x: Record<string, any>, y: Record<string, any>) => ({ ...x, ...y }) },
        startTime: { value: (x: Date) => x },
        endTime: { value: (x: Date, y: Date) => y || x },
      }
    });

    // Add filtered nodes to the graph
    for (const node of filteredNodes) {
      console.log(`➕ Adding node: ${node.name} (${node.type})`);

      workflow.addNode(node.id, async (state: WorkflowState) => {
        console.log(`🔄 Executing node: ${node.name}`);

        try {
          const result = await this.executeNodeInGraph(node, state);

          // Update state with node result
          const updatedState = {
            ...state,
            currentNode: node.id,
            nodeResults: {
              ...state.nodeResults,
              [node.id]: result
            }
          };

          // Save intermediate state
          await this.saveExecutionState(updatedState);

          return updatedState;

        } catch (error) {
          console.error(`❌ Node ${node.name} failed:`, error);
          throw error;
        }
      });
    }

    // Add edges to the graph
    for (const edge of workflowDefinition.edges) {
      console.log(`🔗 Adding edge: ${edge.source} → ${edge.target}`);
      
      if (edge.condition) {
        // Conditional edge
        workflow.addConditionalEdges(
          edge.source,
          (state: WorkflowState) => this.evaluateCondition(edge.condition!, state),
          { true: edge.target, false: END }
        );
      } else {
        // Direct edge
        workflow.addEdge(edge.source, edge.target);
      }
    }

    // Set entry point
    const startNode = this.findStartNode(workflowDefinition);
    workflow.setEntryPoint(startNode);

    console.log(`✅ LangGraph built successfully with ${workflowDefinition.nodes.length} nodes and ${workflowDefinition.edges.length} edges`);

    return workflow.compile({ checkpointer: this.memory });
  }

  /**
   * Find the start node in the workflow
   */
  private findStartNode(workflowDefinition: WorkflowDefinition): string {
    // Look for a node with no incoming edges
    const targetNodes = new Set(workflowDefinition.edges.map(e => e.target));
    const startNode = workflowDefinition.nodes.find(node => !targetNodes.has(node.id));
    
    if (!startNode) {
      throw new Error("No start node found in workflow");
    }
    
    console.log(`🏁 Found start node: ${startNode.name} (${startNode.id})`);
    return startNode.id;
  }

  /**
   * Execute a node within the graph context
   */
  private async executeNodeInGraph(node: NodeConfig, state: WorkflowState): Promise<any> {
    const input = {
      messages: state.messages,
      variables: state.variables,
      previousResults: state.nodeResults,
    };

    return await this.executeNode(node, input, state.variables);
  }

  /**
   * Save execution state to database
   */
  private async saveExecutionState(state: WorkflowState): Promise<void> {
    try {
      const { db } = await connectToDatabase();

      await db.collection('workflow_executions').updateOne(
        { executionId: state.executionId },
        {
          $set: {
            ...state,
            updatedAt: new Date(),
          }
        },
        { upsert: true }
      );

      console.log(`💾 Execution state saved: ${state.executionId} (${state.status})`);

    } catch (error) {
      console.error(`❌ Failed to save execution state:`, error);
      // Don't throw here to avoid breaking the workflow
    }
  }

  /**
   * Execute message node (simple message output)
   */
  private async executeMessageNode(
    node: NodeConfig,
    input: Record<string, any>,
    variables: Record<string, any>
  ): Promise<any> {
    console.log(`💬 Executing message node: ${node.name}`);
    console.log(`🔧 Node structure:`, JSON.stringify(node, null, 2));

    // Handle different node config structures
    const config = node.config || (node as any).data?.config || {};
    const messageText = config.message || config.text || input.message || "Default message";

    console.log(`⚙️ Config:`, config);
    console.log(`💬 Message text:`, messageText);

    // Replace variables in message text
    let processedMessage = messageText;
    for (const [key, value] of Object.entries(variables)) {
      processedMessage = processedMessage.replace(new RegExp(`{{${key}}}`, 'g'), String(value));
    }

    // Replace input variables
    for (const [key, value] of Object.entries(input)) {
      processedMessage = processedMessage.replace(new RegExp(`{{${key}}}`, 'g'), String(value));
    }

    console.log(`✅ Processed message: ${processedMessage}`);

    return {
      success: true,
      result: processedMessage,
      type: 'message',
      timestamp: new Date().toISOString(),
      executionTime: 1 // Message nodes are instant
    };
  }

  /**
   * Execute conversation agent node
   */
  private async executeConversationAgent(
    node: NodeConfig,
    input: Record<string, any>,
    variables: Record<string, any>
  ): Promise<any> {
    console.log(`🤖 Executing conversation agent: ${node.name}`);
    console.log(`🔧 Node structure:`, JSON.stringify(node, null, 2));

    // Handle different node config structures
    const config = node.config || (node as any).data?.config || {};
    const messages = input.messages || [];
    const userMessage = input.message || variables.message || "Hello";

    console.log(`⚙️ Config:`, config);

    // Build system prompt
    let systemPrompt = config.systemPrompt || config.prompt || "You are a helpful AI assistant.";

    // Replace variables in system prompt
    for (const [key, value] of Object.entries(variables)) {
      systemPrompt = systemPrompt.replace(new RegExp(`{{${key}}}`, 'g'), String(value));
    }

    console.log(`📝 System prompt: ${systemPrompt}`);
    console.log(`💬 User message: ${userMessage}`);

    // Prepare messages for LLM
    const llmMessages = [
      new HumanMessage(systemPrompt),
      ...messages,
      new HumanMessage(userMessage)
    ];

    // Call LLM
    const response = await this.llm.invoke(llmMessages);

    console.log(`🤖 AI Response: ${response.content}`);

    return {
      response: response.content,
      model: config.model || "gpt-4o",
      systemPrompt,
      userMessage,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Execute web search node
   */
  private async executeWebSearch(
    node: NodeConfig,
    input: Record<string, any>,
    variables: Record<string, any>
  ): Promise<any> {
    console.log(`🔍 Executing web search: ${node.name}`);

    const query = input.query || variables.query || node.config.query || "";
    const maxResults = node.config.maxResults || 5;

    console.log(`🔎 Search query: ${query}`);
    console.log(`📊 Max results: ${maxResults}`);

    if (!query) {
      throw new Error("No search query provided");
    }

    // Perform search
    const searchResults = await this.searchTool.invoke({ query });

    console.log(`📋 Found ${searchResults.length} search results`);

    return {
      query,
      results: searchResults.slice(0, maxResults),
      resultCount: searchResults.length,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Execute memory store node
   */
  private async executeMemoryStore(
    node: NodeConfig,
    input: Record<string, any>,
    variables: Record<string, any>
  ): Promise<any> {
    console.log(`💾 Executing memory store: ${node.name}`);

    const content = input.content || variables.content || "";
    const key = input.key || variables.key || node.config.key || `memory_${Date.now()}`;
    const category = node.config.category || "general";

    console.log(`🔑 Memory key: ${key}`);
    console.log(`📂 Category: ${category}`);
    console.log(`📝 Content: ${content.substring(0, 100)}...`);

    try {
      const { db } = await connectToDatabase();

      await db.collection('agent_memories').insertOne({
        key,
        content,
        category,
        userId: variables.userId,
        workflowId: variables.workflowId,
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      console.log(`✅ Memory stored successfully`);

      return {
        key,
        content,
        category,
        stored: true,
        timestamp: new Date().toISOString(),
      };

    } catch (error) {
      console.error(`❌ Failed to store memory:`, error);
      throw error;
    }
  }

  /**
   * Execute memory recall node
   */
  private async executeMemoryRecall(
    node: NodeConfig,
    input: Record<string, any>,
    variables: Record<string, any>
  ): Promise<any> {
    console.log(`🧠 Executing memory recall: ${node.name}`);

    const key = input.key || variables.key || node.config.key;
    const category = node.config.category || "general";
    const limit = node.config.limit || 10;

    console.log(`🔑 Memory key: ${key}`);
    console.log(`📂 Category: ${category}`);
    console.log(`📊 Limit: ${limit}`);

    try {
      const { db } = await connectToDatabase();

      const query: any = {
        userId: variables.userId,
        category,
      };

      if (key) {
        query.key = key;
      }

      const memories = await db.collection('agent_memories')
        .find(query)
        .sort({ createdAt: -1 })
        .limit(limit)
        .toArray();

      console.log(`🧠 Found ${memories.length} memories`);

      return {
        key,
        category,
        memories: memories.map(m => ({
          key: m.key,
          content: m.content,
          createdAt: m.createdAt,
        })),
        count: memories.length,
        timestamp: new Date().toISOString(),
      };

    } catch (error) {
      console.error(`❌ Failed to recall memory:`, error);
      throw error;
    }
  }

  /**
   * Execute condition node
   */
  private async executeCondition(
    node: NodeConfig,
    input: Record<string, any>,
    variables: Record<string, any>
  ): Promise<any> {
    console.log(`🔀 Executing condition: ${node.name}`);

    const condition = node.config.condition || "";
    const leftValue = this.resolveValue(node.config.leftValue, variables);
    const operator = node.config.operator || "equals";
    const rightValue = this.resolveValue(node.config.rightValue, variables);

    console.log(`📊 Condition: ${leftValue} ${operator} ${rightValue}`);

    let result = false;

    switch (operator) {
      case 'equals':
        result = leftValue === rightValue;
        break;
      case 'not_equals':
        result = leftValue !== rightValue;
        break;
      case 'greater_than':
        result = Number(leftValue) > Number(rightValue);
        break;
      case 'less_than':
        result = Number(leftValue) < Number(rightValue);
        break;
      case 'contains':
        result = String(leftValue).includes(String(rightValue));
        break;
      case 'not_contains':
        result = !String(leftValue).includes(String(rightValue));
        break;
      case 'is_empty':
        result = !leftValue || leftValue === "";
        break;
      case 'is_not_empty':
        result = !!leftValue && leftValue !== "";
        break;
      case 'regex':
        result = this.evaluateRegex(String(leftValue), String(rightValue));
        break;
      case 'javascript':
        result = this.evaluateJavaScript(node.config.expression, { leftValue, rightValue, ...variables });
        break;
      case 'in_array':
        result = Array.isArray(rightValue) ? rightValue.includes(leftValue) : false;
        break;
      case 'not_in_array':
        result = Array.isArray(rightValue) ? !rightValue.includes(leftValue) : true;
        break;
      case 'starts_with':
        result = String(leftValue).startsWith(String(rightValue));
        break;
      case 'ends_with':
        result = String(leftValue).endsWith(String(rightValue));
        break;
      case 'length_equals':
        result = String(leftValue).length === Number(rightValue);
        break;
      case 'length_greater':
        result = String(leftValue).length > Number(rightValue);
        break;
      case 'length_less':
        result = String(leftValue).length < Number(rightValue);
        break;
      case 'greater_equal':
        result = Number(leftValue) >= Number(rightValue);
        break;
      case 'less_equal':
        result = Number(leftValue) <= Number(rightValue);
        break;
      default:
        throw new Error(`Unknown operator: ${operator}`);
    }

    console.log(`✅ Condition result: ${result}`);

    return {
      condition,
      leftValue,
      operator,
      rightValue,
      result,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Evaluate regex pattern
   */
  private evaluateRegex(text: string, pattern: string): boolean {
    try {
      const regex = new RegExp(pattern, 'i'); // Case insensitive by default
      return regex.test(text);
    } catch (error) {
      console.error(`❌ Invalid regex pattern: ${pattern}`, error);
      return false;
    }
  }

  /**
   * Evaluate JavaScript expression safely
   */
  private evaluateJavaScript(expression: string, context: Record<string, any>): boolean {
    try {
      // Create a safe evaluation context
      const safeContext = {
        leftValue: context.leftValue,
        rightValue: context.rightValue,
        variables: context,
        // Add safe utility functions
        String: String,
        Number: Number,
        Boolean: Boolean,
        Array: Array,
        Math: Math,
        Date: Date,
        JSON: JSON
      };

      // Create function with limited scope
      const func = new Function(...Object.keys(safeContext), `return ${expression}`);
      const result = func(...Object.values(safeContext));

      return Boolean(result);
    } catch (error) {
      console.error(`❌ JavaScript evaluation failed: ${expression}`, error);
      return false;
    }
  }

  /**
   * Execute variable set node
   */
  private async executeVariableSet(
    node: NodeConfig,
    input: Record<string, any>,
    variables: Record<string, any>
  ): Promise<any> {
    console.log(`📝 Executing variable set: ${node.name}`);

    const assignments = node.config.assignments || [];
    const newVariables: Record<string, any> = {};

    for (const assignment of assignments) {
      const key = assignment.key;
      const value = this.resolveValue(assignment.value, variables);

      newVariables[key] = value;
      console.log(`📝 Set variable: ${key} = ${value}`);
    }

    return {
      assignments: newVariables,
      timestamp: new Date().toISOString(),
    };
  }


/**
 * Execute webhook node
 * This node primarily passes through the incoming webhook data
 * and can optionally extract/transform specific parts into variables.
 */
private async executeWebhookNode(
  node: NodeConfig,
  input: Record<string, any>,
  variables: Record<string, any>
): Promise<any> {
  console.log(`🎣 Executing webhook node: ${node.name}`);
  console.log(`🔧 Node config:`, node.config);

  // The webhook data is already part of the initial 'input' and 'variables'
  // when the workflow is triggered by the webhook.
  // This node's role is often just to confirm reception and potentially
  // expose specific parts of the webhook payload as top-level variables
  // for easier access by subsequent nodes.

  const webhookPayload = variables.webhook_payload || input.webhook?.body || {};
  const webhookHeaders = variables.webhook_headers || input.webhook?.headers || {};

  console.log("Webhook payload received:", webhookPayload);
  console.log("Webhook headers received:", webhookHeaders);

  // You might want to process node.config to determine what data to extract
  // For instance, if node.config has "extractBodyToVariable: 'my_webhook_data'",
  // you could add it to the state.
  const extractedVariables: Record<string, any> = {};

  // Example: If you want to explicitly expose the 'message' from the webhook body
  // as a separate variable accessible as 'webhookMessage'
  if (typeof webhookPayload === 'object' && webhookPayload !== null && 'message' in webhookPayload) {
      extractedVariables['webhookMessage'] = webhookPayload.message;
  }

  return {
    success: true,
    receivedPayload: webhookPayload,
    receivedHeaders: webhookHeaders,
    extractedVariables: extractedVariables, // These will be merged into the global 'variables' state
    timestamp: new Date().toISOString(),

  };
}

  /**
   * Resolve a value that might contain variable references
   */
  private resolveValue(value: any, variables: Record<string, any>): any {
    if (typeof value !== 'string') {
      return value;
    }

    // Replace variable references like {{variableName}}
    return value.replace(/\{\{(\w+)\}\}/g, (match, varName) => {
      return variables[varName] !== undefined ? variables[varName] : match;
    });
  }

  /**
   * Evaluate a condition for conditional edges
   */
  private evaluateCondition(condition: string, state: WorkflowState): string {
    // Simple condition evaluation - can be extended
    try {
      // Replace variables in condition
      let evaluatedCondition = condition;
      for (const [key, value] of Object.entries(state.variables)) {
        evaluatedCondition = evaluatedCondition.replace(
          new RegExp(`{{${key}}}`, 'g'),
          String(value)
        );
      }

      // Evaluate the condition (basic implementation)
      // In production, use a safer expression evaluator
      const result = eval(evaluatedCondition);
      return result ? 'true' : 'false';

    } catch (error) {
      console.error(`❌ Failed to evaluate condition: ${condition}`, error);
      return 'false';
    }
  }

  /**
   * Execute API call node with credential support
   */
  private async executeApiCall(
    node: NodeConfig,
    input: Record<string, any>,
    variables: Record<string, any>
  ): Promise<any> {
    console.log(`🌐 Executing API call: ${node.name}`);

    const config = node.config;
    const url = this.resolveValue(config.url, variables);
    const method = config.method || 'GET';
    const headers = config.headers || {};
    const timeout = config.timeout || 30000;

    // Resolve headers with variables
    const resolvedHeaders: Record<string, string> = {};
    for (const [key, value] of Object.entries(headers)) {
      resolvedHeaders[key] = this.resolveValue(value, variables);
    }

    // Add credential-based authentication if specified
    if (config.credentialId && variables.teamId) {
      try {
        const credentialData = await this.credentialManager.getCredentialData(
          config.credentialId,
          variables.teamId
        );

        if (credentialData) {
          // Add authentication headers based on credential type
          if (credentialData.api_key) {
            resolvedHeaders['Authorization'] = `Bearer ${credentialData.api_key}`;
          }
          if (credentialData.access_token) {
            resolvedHeaders['Authorization'] = `Bearer ${credentialData.access_token}`;
          }
          if (credentialData.username && credentialData.password) {
            const auth = Buffer.from(`${credentialData.username}:${credentialData.password}`).toString('base64');
            resolvedHeaders['Authorization'] = `Basic ${auth}`;
          }

          console.log(`🔐 Applied credentials for API call: ${config.credentialId}`);
        }
      } catch (error) {
        console.warn(`⚠️ Failed to load credentials for API call:`, error);
        // Continue without credentials
      }
    }

    // Prepare request body
    let body = null;
    if (config.body && ['POST', 'PUT', 'PATCH'].includes(method.toUpperCase())) {
      body = typeof config.body === 'string'
        ? this.resolveValue(config.body, variables)
        : JSON.stringify(config.body);
    }

    try {
      const response = await fetch(url, {
        method: method.toUpperCase(),
        headers: {
          'Content-Type': 'application/json',
          ...resolvedHeaders
        },
        body,
        signal: AbortSignal.timeout(timeout)
      });

      const responseData = await response.json();

      return {
        success: response.ok,
        status: response.status,
        statusText: response.statusText,
        data: responseData,
        headers: Object.fromEntries(response.headers.entries())
      };

    } catch (error) {
      console.error(`❌ API call failed:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'API call failed',
        status: 0
      };
    }
  }

  /**
   * Execute calculator node
   */
  private async executeCalculator(
    node: NodeConfig,
    input: Record<string, any>,
    variables: Record<string, any>
  ): Promise<any> {
    console.log(`🧮 Executing calculator: ${node.name}`);

    const config = node.config;
    const expression = this.resolveValue(config.expression, variables);
    const precision = config.precision || 10;

    try {
      // Safe expression evaluation (basic math only)
      const result = this.evaluateExpression(expression);
      const roundedResult = Number(result.toFixed(precision));

      console.log(`🧮 Calculation: ${expression} = ${roundedResult}`);

      return {
        expression,
        result: roundedResult,
        precision
      };

    } catch (error) {
      console.error(`❌ Calculator error:`, error);
      return {
        expression,
        result: null,
        error: error instanceof Error ? error.message : 'Calculation failed'
      };
    }
  }

  /**
   * Execute AI agent node with credential support
   */
  private async executeAiAgent(
    node: NodeConfig,
    input: Record<string, any>,
    variables: Record<string, any>
  ): Promise<any> {
    console.log(`🤖 Executing AI agent: ${node.name}`);

    const config = node.config;
    const prompt = this.resolveValue(config.prompt || config.systemPrompt, variables);
    const userMessage = this.resolveValue(input.message || input.text || JSON.stringify(input), variables);

    try {
      // Get credentials if specified
      let apiKey = process.env.OPENAI_API_KEY; // Default fallback
      let baseUrl = 'https://api.openai.com/v1';

      if (config.credentialId && variables.teamId) {
        try {
          const credentialData = await this.credentialManager.getCredentialData(
            config.credentialId,
            variables.teamId
          );

          if (credentialData?.api_key) {
            apiKey = credentialData.api_key;

            // Set base URL based on credential type or model
            if (config.model?.startsWith('claude-')) {
              baseUrl = 'https://api.anthropic.com';
            } else if (config.model?.includes('llama') || config.model?.includes('mixtral')) {
              baseUrl = 'https://api.groq.com/openai/v1';
            } else if (config.model?.startsWith('gemini')) {
              baseUrl = 'https://generativelanguage.googleapis.com/v1beta';
            }

            console.log(`🔐 Using credentials for AI agent: ${config.credentialId}`);
          }
        } catch (error) {
          console.warn(`⚠️ Failed to load AI credentials:`, error);
          // Continue with default credentials
        }
      }

      // Create LLM instance with credentials
      const llm = new ChatOpenAI({
        modelName: config.model || 'gpt-4o-mini',
        temperature: config.temperature || 0.7,
        openAIApiKey: apiKey,
        configuration: {
          baseURL: baseUrl
        }
      });

      // Prepare messages
      const messages = [
        { role: 'system', content: prompt },
        { role: 'user', content: userMessage }
      ];

      // Call the LLM
      const response = await llm.invoke(messages);

      return {
        response: response.content,
        model: config.model || 'gpt-4o-mini',
        usage: response.usage_metadata || {}
      };

    } catch (error) {
      console.error(`❌ AI agent error:`, error);
      return {
        response: 'I encountered an error processing your request.',
        error: error instanceof Error ? error.message : 'AI agent failed'
      };
    }
  }

  /**
   * Execute research agent node
   */
  private async executeResearchAgent(
    node: NodeConfig,
    input: Record<string, any>,
    variables: Record<string, any>
  ): Promise<any> {
    console.log(`🔍 Executing research agent: ${node.name}`);

    const query = this.resolveValue(input.query || input.message, variables);
    const maxResults = node.config.maxResults || 5;

    try {
      // First, perform web search
      const searchResults = await this.executeWebSearch({
        ...node,
        config: { query, maxResults }
      }, { query }, variables);

      // Then, analyze results with AI
      const analysisPrompt = `Analyze the following search results and provide a comprehensive summary:

Search Query: ${query}

Results:
${searchResults.results?.map((r: any, i: number) =>
  `${i + 1}. ${r.title}\n${r.snippet}\nURL: ${r.url}\n`
).join('\n')}

Please provide a detailed analysis and summary of the key findings.`;

      const aiResponse = await this.executeAiAgent({
        ...node,
        config: {
          prompt: 'You are a research analyst. Provide comprehensive analysis of search results.',
          model: node.config.model || 'gpt-4'
        }
      }, { message: analysisPrompt }, variables);

      return {
        query,
        searchResults: searchResults.results,
        analysis: aiResponse.response,
        summary: aiResponse.response
      };

    } catch (error) {
      console.error(`❌ Research agent error:`, error);
      return {
        query,
        error: error instanceof Error ? error.message : 'Research failed'
      };
    }
  }

  /**
   * Execute email node
   */
  private async executeEmail(
    node: NodeConfig,
    input: Record<string, any>,
    variables: Record<string, any>
  ): Promise<any> {
    console.log(`📧 Executing email: ${node.name}`);

    const config = node.config;
    const to = this.resolveValue(config.to, variables);
    const subject = this.resolveValue(config.subject, variables);
    const body = this.resolveValue(config.body || config.template, variables);

    try {
      // This would integrate with your email service (SendGrid, etc.)
      console.log(`📧 Sending email to: ${to}`);
      console.log(`📧 Subject: ${subject}`);
      console.log(`📧 Body: ${body}`);

      // Placeholder for actual email sending
      const emailResult = {
        success: true,
        messageId: `email_${Date.now()}`,
        to,
        subject,
        sentAt: new Date().toISOString()
      };

      return emailResult;

    } catch (error) {
      console.error(`❌ Email error:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Email failed',
        to,
        subject
      };
    }
  }

  /**
   * Execute database node
   */
  private async executeDatabase(
    node: NodeConfig,
    input: Record<string, any>,
    variables: Record<string, any>
  ): Promise<any> {
    console.log(`🗄️ Executing database: ${node.name}`);

    const config = node.config;
    const operation = config.operation || 'select';
    const table = config.table;

    try {
      // This would integrate with your database service
      console.log(`🗄️ Database operation: ${operation} on ${table}`);

      // Placeholder for actual database operations
      const dbResult = {
        success: true,
        operation,
        table,
        rowsAffected: 1,
        data: operation === 'select' ? [{ id: 1, example: 'data' }] : null,
        executedAt: new Date().toISOString()
      };

      return dbResult;

    } catch (error) {
      console.error(`❌ Database error:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Database operation failed',
        operation,
        table
      };
    }
  }

  /**
   * Execute response node
   */
  private async executeResponseNode(
    node: NodeConfig,
    input: Record<string, any>,
    variables: Record<string, any>
  ): Promise<any> {
    console.log(`💬 Executing response: ${node.name}`);

    const config = node.config;
    const message = this.resolveValue(config.message || config.template, variables);
    const responseType = config.responseType || 'text';

    try {
      const response = {
        message,
        responseType,
        timestamp: new Date().toISOString(),
        variables: variables
      };

      console.log(`💬 Response: ${message}`);

      return response;

    } catch (error) {
      console.error(`❌ Response error:`, error);
      return {
        message: 'Error generating response',
        error: error instanceof Error ? error.message : 'Response failed',
        responseType
      };
    }
  }

  /**
   * Safe expression evaluation for calculator
   */
  private evaluateExpression(expression: string): number {
    // Remove any non-math characters for security
    const sanitized = expression.replace(/[^0-9+\-*/.() ]/g, '');

    // Use Function constructor for safe evaluation (limited scope)
    try {
      return new Function('return ' + sanitized)();
    } catch {
      throw new Error('Invalid mathematical expression');
    }
  }


}
