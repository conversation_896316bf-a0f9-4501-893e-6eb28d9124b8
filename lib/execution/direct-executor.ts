import { LangGraphExecutionEngine, WorkflowDefinition, NodeConfig } from './langgraph-engine';
import { connectToDatabase } from '@/lib/mongodb';
import { ObjectId } from 'mongodb';

/**
 * Direct execution without queues - for immediate testing and development
 */
export class DirectExecutor {
  private executionEngine: LangGraphExecutionEngine;

  constructor() {
    console.log('🚀 Initializing Direct Executor');
    this.executionEngine = new LangGraphExecutionEngine();
    console.log('✅ Direct Executor initialized successfully');
  }

  /**
   * Execute a workflow directly without queuing
   */
  async executeWorkflow(
    workflowId: string,
    executionId: string,
    userId: string,
    input: Record<string, any>
  ): Promise<any> {
    console.log(`🎯 Direct workflow execution: ${workflowId}`);
    console.log(`📋 Execution ID: ${executionId}`);
    console.log(`👤 User ID: ${userId}`);
    console.log(`🔧 Input:`, input);

    try {
      // Fetch workflow definition from database
      const { db } = await connectToDatabase();
      const workflow = await db.collection('workflows').findOne({
        _id: new ObjectId(workflowId)
      });

      if (!workflow) {
        throw new Error(`Workflow not found: ${workflowId}`);
      }

      console.log(`🚀 Starting direct workflow execution: ${workflow.name}`);

      // Create initial execution record with unique ObjectId
      const initialExecution = {
        _id: new ObjectId(), // Let MongoDB generate a unique ObjectId
        workflowId,
        executionId,
        status: 'running',
        triggerType: 'manual',
        triggerData: input,
        startTime: new Date(),
        variables: { ...workflow.variables, ...input },
        userId,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      // Save initial execution record
      await db.collection('workflow_executions').insertOne(initialExecution);
      console.log(`💾 Initial execution record saved: ${executionId}`);

      // Execute the workflow using LangGraph
      const result = await this.executionEngine.executeWorkflow(
        workflow as unknown as WorkflowDefinition,
        input,
        executionId,
        userId
      );

      console.log(`✅ Direct workflow execution completed: ${executionId}`);
      return result;

    } catch (error) {
      console.error(`❌ Direct workflow execution failed: ${executionId}`, error);
      
      // Update execution status in database
      try {
        const { db } = await connectToDatabase();
        await db.collection('workflow_executions').updateOne(
          { executionId: executionId },
          {
            $set: {
              status: 'failed',
              error: error instanceof Error ? error.message : 'Unknown error',
              endTime: new Date(),
              updatedAt: new Date(),
            }
          }
        );
      } catch (dbError) {
        console.error(`❌ Failed to update execution status:`, dbError);
      }

      throw error;
    }
  }

  
  /**
   * Test a single node directly without queuing
   */
  async testNode(
    workflowId: string,
    nodeId: string,
    userId: string,
    input: Record<string, any>,
    variables: Record<string, any> = {}
  ): Promise<any> {
    console.log(`🔧 Direct node test: ${nodeId} in workflow ${workflowId}`);
    console.log(`👤 User ID: ${userId}`);
    console.log(`🔧 Input:`, input);
    console.log(`📊 Variables:`, variables);

    try {
      // Fetch workflow definition from database
      const { db } = await connectToDatabase();
      const workflow = await db.collection('workflows').findOne({
        _id: new ObjectId(workflowId)
      });

      if (!workflow) {
        throw new Error(`Workflow not found: ${workflowId}`);
      }

      // Find the specific node
      const node = workflow.nodes.find((n: any) => n.id === nodeId);
      if (!node) {
        throw new Error(`Node not found: ${nodeId}`);
      }

      console.log(`🔧 Testing node directly: ${node.data?.label || node.name || nodeId} (${node.type})`);
      console.log(`🔧 Full node structure:`, JSON.stringify(node, null, 2));

      // Convert node to the expected format - handle different structures
      const nodeConfig = {
        id: node.id,
        type: node.type,
        name: node.data?.label || node.data?.name || node.name || nodeId,
        config: {
          // Handle different config structures
          ...node.data?.config,
          ...node.config,
          // Common properties that might be at different levels
          message: node.data?.config?.message || node.data?.message || node.message,
          text: node.data?.config?.text || node.data?.text || node.text,
          prompt: node.data?.config?.prompt || node.data?.prompt || node.prompt,
          systemPrompt: node.data?.config?.systemPrompt || node.data?.systemPrompt || node.systemPrompt,
        },
        position: node.position || { x: 0, y: 0 },
        connections: []
      };

      console.log(`🔧 Converted node config:`, JSON.stringify(nodeConfig, null, 2));

      // Execute the node using LangGraph
      const result = await this.executionEngine.executeNode(nodeConfig, input, variables);

      console.log(`✅ Direct node test completed: ${nodeId}`);
      return result;

    } catch (error) {
      console.error(`❌ Direct node test failed: ${nodeId}`, error);
      throw error;
    }
  }

  /**
   * Get execution status from database
   */
  async getExecutionStatus(executionId: string): Promise<any> {
    try {
      const { db } = await connectToDatabase();
      
      const execution = await db.collection('workflow_executions').findOne({
        executionId: executionId
      });

      if (!execution) {
        return {
          found: false,
          error: 'Execution not found'
        };
      }

      return {
        found: true,
        executionId: execution.executionId,
        status: execution.status,
        startTime: execution.startTime,
        endTime: execution.endTime,
        variables: execution.variables,
        nodeResults: execution.nodeResults,
        error: execution.error,
        progress: this.calculateProgress(execution),
      };

    } catch (error) {
      console.error(`❌ Failed to get execution status:`, error);
      throw error;
    }
  }

  /**
   * Calculate execution progress
   */
  private calculateProgress(execution: any): number {
    if (execution.status === 'completed') return 100;
    if (execution.status === 'failed') return 0;
    if (execution.status === 'running') {
      // Simple progress calculation based on node results
      const totalNodes = execution.nodeResults ? Object.keys(execution.nodeResults).length : 0;
      const workflowNodes = execution.variables?.totalNodes || 1;
      return Math.min(95, (totalNodes / workflowNodes) * 100);
    }
    return 0;
  }

  /**
   * Cancel a running execution
   */
  async cancelExecution(executionId: string): Promise<boolean> {
    try {
      const { db } = await connectToDatabase();

      // Check if execution exists and is cancellable
      const execution = await db.collection('workflow_executions').findOne({
        executionId: executionId
      });

      if (!execution) {
        return false;
      }

      // Only cancel if execution is still running or queued
      if (!['running', 'queued'].includes(execution.status)) {
        return false;
      }

      // Update execution status to cancelled
      const result = await db.collection('workflow_executions').updateOne(
        { executionId: executionId },
        {
          $set: {
            status: 'cancelled',
            endTime: new Date(),
            error: {
              message: 'Execution cancelled by user',
              code: 'USER_CANCELLED'
            }
          }
        }
      );

      return result.modifiedCount > 0;

    } catch (error) {
      console.error(`❌ Failed to cancel execution:`, error);
      return false;
    }
  }
}

// Singleton instance
let directExecutor: DirectExecutor | null = null;

export function getDirectExecutor(): DirectExecutor {
  if (!directExecutor) {
    directExecutor = new DirectExecutor();
  }
  return directExecutor;
}
