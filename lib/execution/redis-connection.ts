import Redis from 'ioredis';

let redisInstance: Redis | null = null;
let connectionAttempts = 0;
const MAX_CONNECTION_ATTEMPTS = 3;

/**
 * Create a robust Redis connection with error handling
 */
export function createRedisConnection(): Redis {
  if (redisInstance && redisInstance.status === 'ready') {
    return redisInstance;
  }

  console.log('🔄 Creating Redis connection...');

  // Priority: Redis URL first, then fallback to host/port/password
  const redisUrl = process.env.REDIS_URL;
  const redisHost = process.env.REDIS_HOST;
  const redisPort = process.env.REDIS_PORT;
  const redisPassword = process.env.REDIS_PASSWORD;

  let redis: Redis;

  if (redisUrl) {
    console.log('📡 Connecting to Redis using URL:', redisUrl.replace(/:[^:@]*@/, ':***@'));
    redis = new Redis(redisUrl, {
      connectTimeout: 10000,
      commandTimeout: 5000,
      enableReadyCheck: false,
      maxRetriesPerRequest: null, // Required for BullMQ
      lazyConnect: true,
      keepAlive: 30000,
      family: 4,
    });
  } else if (redisHost) {
    console.log(`📡 Connecting to Redis using host/port: ${redisHost}:${redisPort || 6379}`);
    redis = new Redis({
      host: redisHost,
      port: parseInt(redisPort || '6379'),
      password: redisPassword,
      connectTimeout: 10000,
      commandTimeout: 5000,
      enableReadyCheck: false,
      maxRetriesPerRequest: null, // Required for BullMQ
      lazyConnect: true,
      keepAlive: 30000,
      family: 4,
    });
  } else {
    throw new Error('❌ Redis configuration missing: Please provide either REDIS_URL or REDIS_HOST');
  }

  // Set up event listeners
  redis.on('connect', () => {
    console.log('✅ Redis connected successfully');
    connectionAttempts = 0;
  });

  redis.on('ready', () => {
    console.log('🚀 Redis ready for commands');
  });

  redis.on('error', (error) => {
    console.error('❌ Redis connection error:', error.message);
    connectionAttempts++;
    
    if (connectionAttempts >= MAX_CONNECTION_ATTEMPTS) {
      console.error('💥 Max Redis connection attempts reached. Falling back to mock mode.');
    }
  });

  redis.on('close', () => {
    console.log('🔌 Redis connection closed');
  });

  redis.on('reconnecting', () => {
    console.log('🔄 Redis reconnecting...');
  });

  redisInstance = redis;
  return redis;
}

/**
 * Get the current Redis instance or create a new one
 */
export function getRedisConnection(): Redis {
  if (!redisInstance) {
    return createRedisConnection();
  }
  return redisInstance;
}

/**
 * Test Redis connection with detailed error reporting
 */
export async function testRedisConnection(): Promise<boolean> {
  try {
    console.log('🔍 Testing Redis connection...');
    const redis = getRedisConnection();

    // Connect if not already connected
    if (redis.status !== 'ready') {
      await redis.connect();
    }

    // Test with ping
    const result = await redis.ping();
    if (result === 'PONG') {
      console.log('✅ Redis connection test successful');
      return true;
    } else {
      console.error('❌ Redis ping returned unexpected result:', result);
      return false;
    }
  } catch (error: any) {
    console.error('❌ Redis connection test failed:');
    console.error(`   Error: ${error.message}`);
    console.error(`   Code: ${error.code}`);

    if (error.code === 'ECONNREFUSED') {
      console.error('   💡 Redis server is not running or unreachable');
    } else if (error.code === 'ENOTFOUND') {
      console.error('   💡 Redis host not found - check REDIS_HOST/REDIS_URL');
    } else if (error.code === 'ECONNRESET') {
      console.error('   💡 Connection was reset - check Redis server status');
    } else if (error.message.includes('AUTH')) {
      console.error('   💡 Authentication failed - check REDIS_PASSWORD');
    }

    return false;
  }
}

/**
 * Close Redis connection
 */
export async function closeRedisConnection(): Promise<void> {
  if (redisInstance) {
    console.log('🔌 Closing Redis connection...');
    await redisInstance.quit();
    redisInstance = null;
    console.log('✅ Redis connection closed');
  }
}

/**
 * Mock Redis implementation for fallback
 */
export class MockRedis {
  private data: Map<string, any> = new Map();
  
  async ping(): Promise<string> {
    return 'PONG';
  }
  
  async set(key: string, value: any): Promise<string> {
    this.data.set(key, value);
    return 'OK';
  }
  
  async get(key: string): Promise<any> {
    return this.data.get(key) || null;
  }
  
  async del(key: string): Promise<number> {
    const existed = this.data.has(key);
    this.data.delete(key);
    return existed ? 1 : 0;
  }
  
  async exists(key: string): Promise<number> {
    return this.data.has(key) ? 1 : 0;
  }
  
  async keys(pattern: string): Promise<string[]> {
    const keys = Array.from(this.data.keys());
    if (pattern === '*') {
      return keys;
    }
    // Simple pattern matching
    const regex = new RegExp(pattern.replace(/\*/g, '.*'));
    return keys.filter(key => regex.test(key));
  }
  
  // Mock event emitter methods
  on(event: string, callback: Function): this {
    console.log(`📝 Mock Redis: Registered listener for ${event}`);
    return this;
  }
  
  emit(event: string, ...args: any[]): boolean {
    console.log(`📡 Mock Redis: Emitted ${event}`, args);
    return true;
  }
  
  async quit(): Promise<string> {
    console.log('🔌 Mock Redis: Connection closed');
    this.data.clear();
    return 'OK';
  }
}

/**
 * Get Redis connection with proper error handling
 */
export async function getRedisConnectionWithValidation(): Promise<Redis> {
  const redis = getRedisConnection();

  // Test the connection
  const isConnected = await testRedisConnection();

  if (!isConnected) {
    throw new Error('❌ Failed to establish Redis connection. Please check your Redis configuration.');
  }

  return redis;
}
