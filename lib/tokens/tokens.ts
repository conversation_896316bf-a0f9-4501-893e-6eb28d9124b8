import { sign } from "jsonwebtoken";
import crypto from "crypto";

export function generateInviteToken(email: string, teamId: string) {
  const baseSecret = process.env.INVITE_TOKEN_SECRET!; 
    const emailHash = crypto
    .createHash('sha256')
    .update(email)
    .digest('hex')
    .substring(0, 32);

  const secret = baseSecret + emailHash;

  return sign(
    {
      email,      
      teamId,
      timestamp: Date.now(),
    },
    secret,
    { expiresIn: "7d" }
  );
}