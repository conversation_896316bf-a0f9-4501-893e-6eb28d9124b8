export enum Role {
  USER = "user",
  ADMIN = "admin",
  BOT_OWNER = "botOwner",
}

export enum Provider {
  credentials = "credentials",
  GOOGLE = "google",
  GITHUB = "github",
  LOCAL = "local",
}

export enum RoleEnum {
  MEMBER = "member",
  ADMIN = "admin",
  OWNER = "owner",
}

export enum StatusEnum {
  INVITED = "invited",
  ACCEPTED = "accepted",
  REJECTED = "rejected",
  ACTIVE = "active",
  REMOVED = "removed",
  PENDING = "pending",
  CANCELLED = "CANCELLED",
}

export enum Theme {
  LIGHT = "light",
  DARK = "dark",
}

export enum Align {
  LEFT = "left",
  RIGHT = "right",
}

export enum TeamPlanEnum {
  FREE = "free",
  HOBBY = "hobby",
  PRO = "pro",
  ENTERPRISE = "enterprise",
}
