import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
import { getToken } from "next-auth/jwt";

export const config = {
  matcher: [
    "/dashboard", 
    "/dashboard/:path*", 
    "/api/dashboard/:path*", 
  ],
};

export async function middleware(request: NextRequest) {
  try {
    const token = await getToken({
      req: request,
      secret: process.env.NEXTAUTH_SECRET,
    });

    const { pathname } = request.nextUrl;

    // Debug logging in development
    if (process.env.NODE_ENV === "development") {
      console.log(`Middleware: ${pathname}, Token: ${token ? 'exists' : 'none'}`);
    }

    // Allow access to login page and auth callbacks
    if (pathname.startsWith("/login") || pathname.startsWith("/api/auth")) {
      return NextResponse.next();
    }

    if (!token && pathname.startsWith("/dashboard")) {
      const loginUrl = new URL("/login", request.url);
      loginUrl.searchParams.set("callbackUrl", pathname);
      return NextResponse.redirect(loginUrl);
    }

    if (!token && pathname.startsWith("/api/dashboard")) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    if (token?.role !== "admin" && pathname.startsWith("/dashboard/admin")) {
      return NextResponse.redirect(
        new URL("/dashboard/unauthorized", request.url)
      );
    }

    return NextResponse.next();
  } catch (error) {
    console.error("Middleware error:", error);
    // If there's an error with token verification, redirect to login
    if (request.nextUrl.pathname.startsWith("/dashboard")) {
      const loginUrl = new URL("/login", request.url);
      loginUrl.searchParams.set("callbackUrl", request.nextUrl.pathname);
      return NextResponse.redirect(loginUrl);
    }
    return NextResponse.next();
  }
}
