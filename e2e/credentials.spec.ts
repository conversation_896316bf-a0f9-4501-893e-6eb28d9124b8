import { test, expect } from '@playwright/test'

// Use authenticated state
test.use({ storageState: 'e2e/auth.json' })

test.describe('Credential Management', () => {
  const testTeamId = 'test-team-e2e'

  test.beforeEach(async ({ page }) => {
    await page.goto(`/dashboard/team/${testTeamId}/credentials`)
  })

  test('should display credentials page correctly', async ({ page }) => {
    // Check page title and navigation
    await expect(page.locator('h1')).toContainText('Team Credentials')
    await expect(page.getByText('Back to Team')).toBeVisible()
    await expect(page.getByText('Add Credential')).toBeVisible()

    // Check statistics cards
    await expect(page.getByText('Total')).toBeVisible()
    await expect(page.getByText('Active')).toBeVisible()
    await expect(page.getByText('AI Services')).toBeVisible()
    await expect(page.getByText('Business Apps')).toBeVisible()

    // Check search and filter controls
    await expect(page.getByPlaceholder('Search credentials...')).toBeVisible()
    await expect(page.getByText('All Categories')).toBeVisible()
    await expect(page.getByText('Refresh')).toBeVisible()
  })

  test('should create new OpenAI credential', async ({ page }) => {
    // Click add credential button
    await page.click('[data-testid="add-credential-button"]')

    // Should open create modal
    await expect(page.getByText('Create Credential')).toBeVisible()

    // Select OpenAI integration
    await page.click('[data-testid="integration-openai"]')
    await page.click('[data-testid="continue-button"]')

    // Fill credential details
    await page.fill('[data-testid="credential-name"]', 'Test OpenAI E2E')
    await page.fill('[data-testid="api-key-input"]', 'sk-test1234567890abcdef1234567890abcdef1234567890abcdef')
    await page.fill('[data-testid="organization-id-input"]', 'org-test123')

    // Test credential connection
    await page.click('[data-testid="test-credential-button"]')
    
    // Wait for test result
    await expect(page.getByText('Connection test passed')).toBeVisible({ timeout: 10000 })

    // Save credential
    await page.click('[data-testid="save-credential-button"]')

    // Should close modal and show success message
    await expect(page.getByText('Credential created successfully')).toBeVisible()
    
    // Should appear in credentials list
    await expect(page.getByText('Test OpenAI E2E')).toBeVisible()
    await expect(page.getByText('OpenAI')).toBeVisible()
    await expect(page.getByText('Active')).toBeVisible()
  })

  test('should edit existing credential', async ({ page }) => {
    // Find and click edit button for first credential
    const firstEditButton = page.locator('[data-testid^="edit-credential-"]').first()
    await firstEditButton.click()

    // Should open edit modal
    await expect(page.getByText('Edit Credential')).toBeVisible()

    // Update credential name
    const nameInput = page.locator('[data-testid="credential-name"]')
    await nameInput.clear()
    await nameInput.fill('Updated Credential Name')

    // Save changes
    await page.click('[data-testid="save-changes-button"]')

    // Should show success message
    await expect(page.getByText('Credential updated successfully')).toBeVisible()

    // Should reflect changes in list
    await expect(page.getByText('Updated Credential Name')).toBeVisible()
  })

  test('should test credential connection', async ({ page }) => {
    // Find and click test button for first credential
    const firstTestButton = page.locator('[data-testid^="test-credential-"]').first()
    await firstTestButton.click()

    // Should show loading state
    await expect(page.locator('[data-testid="testing-spinner"]')).toBeVisible()

    // Should show test result
    await expect(page.getByText(/Test (passed|failed)/)).toBeVisible({ timeout: 10000 })
  })

  test('should filter credentials by search', async ({ page }) => {
    // Type in search box
    await page.fill('[data-testid="search-input"]', 'OpenAI')

    // Should filter results
    await expect(page.getByText('OpenAI')).toBeVisible()
    
    // Clear search
    await page.fill('[data-testid="search-input"]', '')
    
    // Should show all credentials again
    await page.waitForTimeout(500) // Wait for debounce
  })

  test('should filter credentials by category', async ({ page }) => {
    // Select AI Services category
    await page.selectOption('[data-testid="category-filter"]', 'ai_services')

    // Should show only AI service credentials
    await expect(page.getByText('AI Services')).toBeVisible()

    // Reset to all categories
    await page.selectOption('[data-testid="category-filter"]', 'all')
  })

  test('should delete credential with confirmation', async ({ page }) => {
    // Count initial credentials
    const initialCount = await page.locator('[data-testid^="credential-item-"]').count()

    // Find and click delete button for last credential
    const lastDeleteButton = page.locator('[data-testid^="delete-credential-"]').last()
    await lastDeleteButton.click()

    // Should show confirmation dialog
    await expect(page.getByText('Delete Credential')).toBeVisible()
    await expect(page.getByText('Are you sure you want to delete')).toBeVisible()

    // Confirm deletion
    await page.click('[data-testid="confirm-delete-button"]')

    // Should show success message
    await expect(page.getByText('Credential deleted successfully')).toBeVisible()

    // Should have one less credential
    const finalCount = await page.locator('[data-testid^="credential-item-"]').count()
    expect(finalCount).toBe(initialCount - 1)
  })

  test('should handle credential creation errors', async ({ page }) => {
    // Click add credential button
    await page.click('[data-testid="add-credential-button"]')

    // Select OpenAI integration
    await page.click('[data-testid="integration-openai"]')
    await page.click('[data-testid="continue-button"]')

    // Fill with invalid API key
    await page.fill('[data-testid="credential-name"]', 'Invalid Credential')
    await page.fill('[data-testid="api-key-input"]', 'invalid-key')

    // Test credential connection
    await page.click('[data-testid="test-credential-button"]')
    
    // Should show error
    await expect(page.getByText(/Invalid API key format|Connection test failed/)).toBeVisible({ timeout: 10000 })

    // Save button should be disabled or show error
    const saveButton = page.locator('[data-testid="save-credential-button"]')
    await expect(saveButton).toBeDisabled()
  })

  test('should refresh credentials list', async ({ page }) => {
    // Click refresh button
    await page.click('[data-testid="refresh-button"]')

    // Should show loading state briefly
    await expect(page.locator('[data-testid="loading-spinner"]')).toBeVisible()

    // Should reload credentials
    await expect(page.locator('[data-testid="loading-spinner"]')).not.toBeVisible()
  })

  test('should show empty state when no credentials exist', async ({ page }) => {
    // Navigate to a team with no credentials (or delete all)
    await page.goto(`/dashboard/team/empty-team/credentials`)

    // Should show empty state
    await expect(page.getByText('No credentials found')).toBeVisible()
    await expect(page.getByText('Create your first credential')).toBeVisible()
  })

  test('should navigate back to team page', async ({ page }) => {
    // Click back to team button
    await page.click('[data-testid="back-to-team-button"]')

    // Should navigate to team page
    await expect(page).toHaveURL(`/dashboard/team/${testTeamId}`)
  })

  test('should be responsive on mobile', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 })

    // Check that page is still usable
    await expect(page.getByText('Team Credentials')).toBeVisible()
    await expect(page.getByText('Add Credential')).toBeVisible()

    // Statistics should stack vertically
    const statsCards = page.locator('[data-testid="stats-card"]')
    const firstCard = statsCards.first()
    const secondCard = statsCards.nth(1)
    
    const firstCardBox = await firstCard.boundingBox()
    const secondCardBox = await secondCard.boundingBox()
    
    // Second card should be below first card (stacked)
    expect(secondCardBox?.y).toBeGreaterThan(firstCardBox?.y || 0)
  })

  test('should handle network errors gracefully', async ({ page }) => {
    // Intercept and fail API requests
    await page.route('/api/teams/*/credentials', route => {
      route.abort('failed')
    })

    // Reload page
    await page.reload()

    // Should show error state
    await expect(page.getByText(/Failed to load|Error loading/)).toBeVisible()
  })
})

test.describe('Credential Integration with Workflows', () => {
  const testTeamId = 'test-team-e2e'

  test('should use credential in workflow node configuration', async ({ page }) => {
    // Navigate to workflow builder
    await page.goto(`/dashboard/team/${testTeamId}/workflows`)
    
    // Create or open existing workflow
    await page.click('[data-testid="create-workflow-button"]')
    await page.fill('[data-testid="workflow-name"]', 'Credential Test Workflow')
    await page.click('[data-testid="create-workflow-submit"]')

    // Add AI agent node
    await page.click('[data-testid="add-node-button"]')
    await page.click('[data-testid="node-type-ai-agent"]')

    // Should see credential selector
    await expect(page.locator('[data-testid="credential-selector"]')).toBeVisible()

    // Select a credential
    await page.selectOption('[data-testid="credential-selector"]', { label: /OpenAI/ })

    // Should show credential info
    await expect(page.getByText('OpenAI')).toBeVisible()

    // Save node configuration
    await page.click('[data-testid="save-node-config"]')

    // Save workflow
    await page.click('[data-testid="save-workflow"]')

    // Should show success message
    await expect(page.getByText('Workflow saved successfully')).toBeVisible()
  })
})
