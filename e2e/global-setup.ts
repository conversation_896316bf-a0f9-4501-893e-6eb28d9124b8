import { chromium, FullConfig } from '@playwright/test'

async function globalSetup(config: FullConfig) {
  console.log('🚀 Starting global setup...')

  // Start browser for authentication
  const browser = await chromium.launch()
  const page = await browser.newPage()

  try {
    // Navigate to login page
    await page.goto('/auth/signin')

    // Perform authentication (adjust based on your auth system)
    await page.fill('[data-testid="email-input"]', process.env.TEST_USER_EMAIL || '<EMAIL>')
    await page.fill('[data-testid="password-input"]', process.env.TEST_USER_PASSWORD || 'testpassword')
    await page.click('[data-testid="signin-button"]')

    // Wait for successful login
    await page.waitForURL('/dashboard')

    // Save authentication state
    await page.context().storageState({ path: 'e2e/auth.json' })

    console.log('✅ Authentication setup complete')

    // Setup test data
    await setupTestData(page)

  } catch (error) {
    console.error('❌ Global setup failed:', error)
    throw error
  } finally {
    await browser.close()
  }
}

async function setupTestData(page: any) {
  console.log('📊 Setting up test data...')

  try {
    // Create test team if it doesn't exist
    const testTeamId = 'test-team-e2e'
    
    // Navigate to teams page
    await page.goto('/dashboard')
    
    // Check if test team exists, create if not
    const teamExists = await page.locator(`[data-testid="team-${testTeamId}"]`).count() > 0
    
    if (!teamExists) {
      // Create test team
      await page.click('[data-testid="create-team-button"]')
      await page.fill('[data-testid="team-name-input"]', 'E2E Test Team')
      await page.fill('[data-testid="team-url-input"]', testTeamId)
      await page.click('[data-testid="create-team-submit"]')
      
      console.log('✅ Test team created')
    }

    // Setup test credentials
    await setupTestCredentials(page, testTeamId)

    // Setup test workflows
    await setupTestWorkflows(page, testTeamId)

    console.log('✅ Test data setup complete')

  } catch (error) {
    console.error('❌ Test data setup failed:', error)
    // Don't throw here, tests should handle missing data gracefully
  }
}

async function setupTestCredentials(page: any, teamId: string) {
  console.log('🔐 Setting up test credentials...')

  try {
    // Navigate to credentials page
    await page.goto(`/dashboard/team/${teamId}/credentials`)

    // Check if test credential exists
    const credentialExists = await page.locator('[data-testid="credential-test-openai"]').count() > 0

    if (!credentialExists) {
      // Create test OpenAI credential
      await page.click('[data-testid="add-credential-button"]')
      
      // Select OpenAI integration
      await page.click('[data-testid="integration-openai"]')
      await page.click('[data-testid="continue-button"]')
      
      // Fill credential details
      await page.fill('[data-testid="credential-name"]', 'Test OpenAI Credential')
      await page.fill('[data-testid="api-key-input"]', 'sk-test1234567890abcdef1234567890abcdef1234567890abcdef')
      
      // Test and save
      await page.click('[data-testid="test-credential-button"]')
      await page.waitForSelector('[data-testid="test-success"]')
      await page.click('[data-testid="save-credential-button"]')
      
      console.log('✅ Test credential created')
    }

  } catch (error) {
    console.warn('⚠️ Could not setup test credentials:', error.message)
  }
}

async function setupTestWorkflows(page: any, teamId: string) {
  console.log('🔄 Setting up test workflows...')

  try {
    // Navigate to workflows page
    await page.goto(`/dashboard/team/${teamId}/workflows`)

    // Check if test workflow exists
    const workflowExists = await page.locator('[data-testid="workflow-test-e2e"]').count() > 0

    if (!workflowExists) {
      // Create test workflow
      await page.click('[data-testid="create-workflow-button"]')
      await page.fill('[data-testid="workflow-name"]', 'E2E Test Workflow')
      await page.fill('[data-testid="workflow-description"]', 'Workflow for end-to-end testing')
      await page.click('[data-testid="create-workflow-submit"]')
      
      // Add basic nodes for testing
      await addTestWorkflowNodes(page)
      
      console.log('✅ Test workflow created')
    }

  } catch (error) {
    console.warn('⚠️ Could not setup test workflows:', error.message)
  }
}

async function addTestWorkflowNodes(page: any) {
  // Add webhook trigger
  await page.click('[data-testid="add-node-trigger"]')
  await page.click('[data-testid="node-type-webhook"]')
  
  // Configure webhook
  await page.fill('[data-testid="webhook-path"]', '/test-webhook')
  await page.click('[data-testid="save-node"]')
  
  // Add AI agent node
  await page.click('[data-testid="add-node-ai-agent"]')
  await page.click('[data-testid="node-type-ai-agent"]')
  
  // Configure AI agent with test credential
  await page.selectOption('[data-testid="credential-selector"]', 'Test OpenAI Credential')
  await page.fill('[data-testid="system-prompt"]', 'You are a helpful assistant for testing.')
  await page.click('[data-testid="save-node"]')
  
  // Save workflow
  await page.click('[data-testid="save-workflow"]')
}

export default globalSetup
