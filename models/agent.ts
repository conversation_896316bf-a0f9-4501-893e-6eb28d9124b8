// Define interface for Agent document
import { Align, Theme } from "@/enums/enums";
import mongoose, { Schema, Document, Model } from "mongoose";


export interface AgentSettings {
  maxTokens?: number;
  topP?: number;
  frequencyPenalty?: number;
  presencePenalty?: number;
    responseTime?: number;
      language?: string;

  [key: string]: any;
}



// Define interface for Bot document
export interface IAgent extends Document {
  userId: mongoose.Types.ObjectId;
  teamId: mongoose.Types.ObjectId;
  name: string;
  description?: string | null;
  model: string;
  temperature: number;
  systemPrompt: string;
  tools: string[];
  settings: Map<string, any> | Record<string, any>;
  
  // Agent-specific capabilities
  capabilities: string[];
  maxTokens?: number;
  topP?: number;
  frequencyPenalty?: number;
  presencePenalty?: number;
  
  // Workflow ownership
  workflowIds: mongoose.Types.ObjectId[];
  
  // Inherited from chatbot for consistency
  language?: string;
  modelName: string;
  instructions?: string | null;
  initialMessages?: string | null;
  suggestedMessages?: string | null;
  messagePlaceholder?: string;
  footer: string;
  theme: Theme;
  isPublic: boolean;
  allowedDomains: string[];
  ipLimit: number;
  ipLimitTimeframe: number;
  ipLimitMessage?: string | null;
  displayName?: string | null;
  iconPath?: string | null;
  profilePicturePath?: string | null;
  customerMessagecolorAsChatbotHeader: boolean;
  customerMessagecolor: string;
  chatBubbleColor: string;
  alignChatBubble: Align;
  autoShowInitialDelay: number;
  collectFeedback: boolean;
  regenerateMessage: boolean;
  isActive: boolean;
  trainingData?: mongoose.Types.ObjectId | null;
  leadTitle: string;
  leadTitleEnabled: boolean;
  leadEmail: string;
  leadEmailEnabled: boolean;
  leadName: string;
  leadNameEnabled: boolean;
  leadPhone: string;
  leadPhoneEnabled: boolean;
  leadMessage: string;
  leadMessageEnabled: boolean;
  leadsNotificationEmail: string;
  leadsNotificationEmailEnabled: boolean;
  conversationsNotificationEmail: string;
  conversationsNotificationEmailEnabled: boolean;
  customDomains: string;
  customDomainsEnabled: boolean;
  webhooks: string;
  webhooksEnabled: boolean;
  isTrained: boolean;
  lastTrainedAt?: Date | null;
  lastUsed?: Date | null;
  createdAt: Date;
  updatedAt: Date;
  config: AgentSettings;
}

const AgentSchema: Schema = new Schema({
  userId: {
    type: Schema.Types.ObjectId,
    ref: "User",
    required: true,
    index: true,
  },
  teamId: {
    type: Schema.Types.ObjectId,
    ref: "Team",
    required: true,
    index: true,
  },
  name: {
    type: String,
    required: true,
    index: true,
  },
  description: {
    type: String,
    default: null,
  },
  language: {
    type: String,
    default: "en",
  },
  modelName: {
    type: String,
    default: "gpt-4",
  },
  temperature: {
    type: Number,
    default: 0.7,
  },
  settings: {
    type: Map,
    of: Schema.Types.Mixed,
    default: {},
  },
  instructions: {
    type: String,
    default: null,
  },
  initialMessages: {
    type: String,
    default: null,
  },
  suggestedMessages: {
    type: String,
    default: null,
  },
  messagePlaceholder: {
    type: String,
    default: "Message...",
  },
  footer: {
    type: String,
    default: "",
  },
  theme: {
    type: String,
    enum: Object.values(Theme),
    default: Theme.LIGHT,
    required: true,
  },
  isPublic: {
    type: Boolean,
    default: true,
    index: true,
  },
  allowedDomains: {
    type: [String],
    default: [],
  },
  ipLimit: {
    type: Number,
    default: 0,
  },
  ipLimitTimeframe: {
    type: Number,
    default: 60,
  },
  ipLimitMessage: {
    type: String,
    default: null,
  },
  displayName: {
    type: String,
    default: null,
  },
  iconPath: {
    type: String,
    default: null,
  },
  profilePicturePath: {
    type: String,
    default: null,
  },
  customerMessagecolorAsChatbotHeader: {
    type: Boolean,
    default: false,
  },
  customerMessagecolor: {
    type: String,
    default: "#000000",
    validate: {
      validator: function (v: string) {
        return /^#[0-9A-F]{6}$/i.test(v);
      },
      message: (props: { value: string }) =>
        `${props.value} is not a valid hex color code!`,
    },
  },
  chatBubbleColor: {
    type: String,
    default: "#000000",
    validate: {
      validator: function (v: string) {
        return /^#[0-9A-F]{6}$/i.test(v);
      },
      message: (props: { value: string }) =>
        `${props.value} is not a valid hex color code!`,
    },
  },
  alignChatBubble: {
    type: String,
    enum: Object.values(Align),
    default: Align.RIGHT,
    required: true,
  },
  autoShowInitialDelay: {
    type: Number,
    default: 3,
  },
  collectFeedback: {
    type: Boolean,
    default: true,
  },
  regenerateMessage: {
    type: Boolean,
    default: true,
  },
  isActive: {
    type: Boolean,
    default: true,
    index: true,
  },
  trainingData: {
    type: Schema.Types.ObjectId,
    ref: "AgentTrainingData",
  },
  leadTitle: {
    type: String,
    default: "null",
  },
  leadTitleEnabled: {
    type: Boolean,
    default: true,
  },
  leadEmail: {
    type: String,
    default: "null",
  },
  leadEmailEnabled: {
    type: Boolean,
    default: false,
  },
  leadName: {
    type: String,
    default: "null",
  },
  leadNameEnabled: {
    type: Boolean,
    default: false,
  },
  leadPhone: {
    type: String,
    default: "null",
  },
  leadPhoneEnabled: {
    type: Boolean,
    default: false,
  },
  leadMessage: {
    type: String,
    default: "null",
  },
  leadMessageEnabled: {
    type: Boolean,
    default: false,
  },
  leadsNotificationEmail: {
    type: String,
    default: "null",
  },
  leadsNotificationEmailEnabled: {
    type: Boolean,
    default: false,
  },
  conversationsNotificationEmail: {
    type: String,
    default: "null",
  },
  conversationsNotificationEmailEnabled: {
    type: Boolean,
    default: false,
  },
  customDomains: {
    type: String,
    default: "null",
  },
  customDomainsEnabled: {
    type: Boolean,
    default: false,
  },
  webhooks: {
    type: String,
    default: "null",
  },
  webhooksEnabled: {
    type: Boolean,
    default: false,
  },
  isTrained: {
    type: Boolean,
    default: false,
    index: true,
  },
  lastTrainedAt: {
    type: Date,
    default: null,
  },
  lastUsed: {
    type: Date,
    default: Date.now,
    index: true,
    get: (date: Date) => date?.toISOString()?.split("T")[0], 
    set: (date: Date | string) => new Date(date), 
  },
  createdAt: {
    type: Date,
    default: Date.now,
    index: true,
  },
  updatedAt: {
    type: Date,
    default: Date.now,
    index: true,
  },
  config: {
    language: { type: String, default: "en" },
    responseTime: { type: Number, default: 1000 },
  },
});

// Middleware to update the updatedAt field on save
AgentSchema.pre<IAgent>("save", function (next) {
  this.updatedAt = new Date();
  next();
});

if (mongoose.models && mongoose.models.Agent) {
  delete mongoose.models.Agent;
}

const AgentModel: Model<IAgent> =
  mongoose.models.Agent || mongoose.model<IAgent>("Agent", AgentSchema);

export default AgentModel;
