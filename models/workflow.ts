import mongoose, { Schema, Document, Model } from "mongoose";

export interface WorkflowNode {
  id: string;
  type: string;
  subtype?: string;
  position: { x: number; y: number };
  data: {
    label: string;
    config: Record<string, any>;
  };
}

export interface WorkflowEdge {
  id: string;
  source: string;
  target: string;
  sourceHandle?: string;
  targetHandle?: string;
  data?: {
    condition?: string;
    label?: string;
  };
}

// Define interface for Workflow document
export interface IWorkflow extends Document {
  userId: mongoose.Types.ObjectId;
  teamId: mongoose.Types.ObjectId;
  name: string;
  description?: string | null;
  version: string;
  nodes: WorkflowNode[];
  edges: WorkflowEdge[];
  triggers: string[];
  status: 'draft' | 'active' | 'inactive';
  metadata?: Record<string, any>;

  // Execution tracking
  lastExecuted?: Date | null;
  executionCount: number;

  // Settings
  isPublic: boolean;
  isActive: boolean;
  executionMode: 'direct' | 'async';

  // Timestamps
  createdAt: Date;
  updatedAt: Date;
}

const WorkflowNodeSchema = new Schema({
  id: { type: String, required: true },
  type: { type: String, required: true },
  subtype: { type: String },
  position: {
    x: { type: Number, required: true },
    y: { type: Number, required: true }
  },
  data: {
    label: { type: String, required: true },
    config: { type: Schema.Types.Mixed, default: {} }
  }
}, { _id: false });

const WorkflowEdgeSchema = new Schema({
  id: { type: String, required: true },
  source: { type: String, required: true },
  target: { type: String, required: true },
  sourceHandle: { type: String },
  targetHandle: { type: String },
  data: {
    condition: { type: String },
    label: { type: String }
  }
}, { _id: false });

const WorkflowSchema: Schema = new Schema({
  userId: {
    type: Schema.Types.ObjectId,
    ref: "User",
    required: true,
    index: true,
  },
  teamId: {
    type: Schema.Types.ObjectId,
    ref: "Team",
    required: true,
    index: true,
  },
  name: {
    type: String,
    required: true,
    index: true,
  },
  description: {
    type: String,
    default: null,
  },
  version: {
    type: String,
    default: "1.0.0",
  },
  nodes: {
    type: [WorkflowNodeSchema],
    default: [],
  },
  edges: {
    type: [WorkflowEdgeSchema],
    default: [],
  },
  triggers: {
    type: [String],
    default: [],
  },
  status: {
    type: String,
    enum: ['draft', 'active', 'inactive'],
    default: 'draft',
    index: true,
  },
  metadata: {
    type: Map,
    of: Schema.Types.Mixed,
    default: {},
  },
  
  // Execution tracking
  lastExecuted: {
    type: Date,
    default: null,
  },
  executionCount: {
    type: Number,
    default: 0,
  },
  
  // Settings
  isPublic: {
    type: Boolean,
    default: false,
    index: true,
  },
  isActive: {
    type: Boolean,
    default: true,
    index: true,
  },
  executionMode: {
    type: String,
    enum: ['direct', 'async'],
    default: 'direct',
    index: true,
  },
  
  // Timestamps
  createdAt: {
    type: Date,
    default: Date.now,
  },
  updatedAt: {
    type: Date,
    default: Date.now,
  },
});

// Middleware to update the updatedAt field on save
WorkflowSchema.pre<IWorkflow>("save", function (next) {
  this.updatedAt = new Date();
  next();
});

// Indexes for performance
WorkflowSchema.index({ agentId: 1, isActive: 1 });
WorkflowSchema.index({ teamId: 1, status: 1 });
WorkflowSchema.index({ userId: 1, createdAt: -1 });
WorkflowSchema.index({ name: "text", description: "text" });

if (mongoose.models && mongoose.models.Workflow) {
  delete mongoose.models.Workflow;
}

const WorkflowModel: Model<IWorkflow> =
  mongoose.models.Workflow || mongoose.model<IWorkflow>("Workflow", WorkflowSchema);

export default WorkflowModel;
