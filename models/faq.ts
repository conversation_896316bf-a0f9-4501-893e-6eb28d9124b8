import mongoose, { Document, Model, Schema } from "mongoose";

export interface IFAQ extends Document {
  question: string;
  answer: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

const faqSchema: Schema = new mongoose.Schema({
  question: {
    type: String,
    required: true,
    trim: true,
    minlength: 10,
    maxlength: 200,
  },
  answer: {
    type: String,
    required: true,
    trim: true,
    minlength: 20,
    maxlength: 1000,
  },
  isActive: {
    type: Boolean,
    default: true,
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
  updatedAt: {
    type: Date,
    default: Date.now,
  },
});

faqSchema.set("toJSON", {
  transform: function (doc, ret) {
    ret.id = ret._id.toString();
    delete ret._id;
    delete ret.__v;
    return ret;
  },
});

if (mongoose.models && mongoose.models.FAQ) {
  delete mongoose.models.FAQ;
}


const FAQModel: Model<IFAQ> =
  mongoose.models.FAQ || mongoose.model<IFAQ>("FAQ", faqSchema);
export default FAQModel;