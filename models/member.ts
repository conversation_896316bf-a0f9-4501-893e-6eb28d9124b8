import { <PERSON>Enum, StatusEnum } from "@/enums/enums";
import mongoose, { Document, Model, Schema } from "mongoose";
import { IUser } from "./user";
import { ITeam } from "./teams";

export interface IMember extends Document {
  userId?: mongoose.Types.ObjectId | IUser | null;
  teamId: mongoose.Types.ObjectId | ITeam;
  role: RoleEnum;
  avatar?: string;
  name?: string | null;
  invitedBy?: mongoose.Types.ObjectId;
  email: string;
  invitedAt: Date;
  acceptedAt?: Date;
  status: StatusEnum;
  createdAt: Date;
  updatedAt: Date;
}

export interface PopulatedMember extends Omit<IMember, "userId"> {
  userId: IUser | null;
}

const memberSchema: Schema = new mongoose.Schema(
  {
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      default: null,
      required: false,
    },
     avatar: {
      type: String,
      default: null,
    },
    teamId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Team",
      required: true,
    },
    role: {
      type: String,
      enum: Object.values(RoleEnum),
      default: RoleEnum.MEMBER,
      required: true,
    },
    name: {
      type: String,
      default: null,
      index: true,
    },
    invitedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      index: true,
    },
    email: {
      type: String,
      required: true,
      index: true,
    },
    invitedAt: {
      type: Date,
      default: Date.now,
    },
    acceptedAt: {
      type: Date,
    },
    status: {
      type: String,
      enum: Object.values(StatusEnum),
      default: StatusEnum.INVITED,
      required: true,
      index: true,
    },
  },
  {
    timestamps: true,
    toJSON: {
      virtuals: true,
      transform: function (doc, ret) {
        ret.id = ret._id.toString();
        delete ret._id;
        delete ret.__v;
        return ret;
      },
    },
  }
);

if (mongoose.models && mongoose.models.Member) {
  delete mongoose.models.Member;
}

const MemberModel: Model<IMember> =
  mongoose.models.Member || mongoose.model<IMember>("Member", memberSchema);
export default MemberModel;
