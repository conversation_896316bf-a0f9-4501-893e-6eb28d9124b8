import mongoose, { Document, Schema } from "mongoose";

export interface IPlan extends Document {
  name: string;
  description?: string;
  priceMonthly: number;
  priceYearly: number;
  discountPercentage?: number;
  maxMembers: number;
  maxChatbots: number;
  maxTrainingChars: number;
  maxTrainingLinks: number;
  monthlyMessageCredits: number;
  models: string[];
  features: {
    hasBasicAnalytics: boolean;
    hasApiAccess: boolean;
    hasCustomBranding: boolean;
    hasCustomDomains: boolean;
    hasNotifications: boolean;
    [key: string]: boolean;
  };
  isActive: boolean;
  isDefault: boolean;
  createdAt: Date;
  updatedAt: Date;
}

const planSchema = new Schema<IPlan>(
  {
    name: {
      type: String,
      required: true,
      unique: true,
      trim: true,
    },
    description: {
      type: String,
      default: "",
    },
    priceMonthly: {
      type: Number,
      required: true,
      min: 0,
    },
    priceYearly: {
      type: Number,
      required: true,
      min: 0,
    },
    discountPercentage: {
      type: Number,
      default: 0,
      min: 0,
      max: 100,
    },
    maxMembers: {
      type: Number,
      required: true,
      min: 1,
    },
    maxChatbots: {
      type: Number,
      required: true,
      min: 1,
    },
    maxTrainingChars: {
      type: Number,
      required: true,
      min: 0,
    },
    maxTrainingLinks: {
      type: Number,
      required: true,
      min: 0,
    },
    monthlyMessageCredits: {
      type: Number,
      required: true,
      min: 0,
    },
    models: {
      type: [String],
      required: true,
      default: ["GPT-3.5"],
    },
    features: {
      hasBasicAnalytics: {
        type: Boolean,
        default: false,
      },
      hasApiAccess: {
        type: Boolean,
        default: false,
      },
      hasCustomBranding: {
        type: Boolean,
        default: false,
      },
      hasCustomDomains: {
        type: Boolean,
        default: false,
      },
      hasNotifications: {
        type: Boolean,
        default: false,
      },
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    isDefault: {
      type: Boolean,
      default: false,
    },
  },
  {
    timestamps: true,
    toJSON: {
      virtuals: true,
      transform: (doc, ret) => {
        ret.id = ret._id;
        delete ret._id;
        delete ret.__v;
        return ret;
      },
    },
  }
);

// Ensure only one default plan exists
planSchema.pre("save", async function (next) {
  if (this.isDefault) {
    try {
      await mongoose.model("Plan").updateMany(
        { _id: { $ne: this._id } },
        { $set: { isDefault: false } }
      );
    } catch (err: unknown) {
      next(err as Error);
    }
  }
  next();
});

if (mongoose.models && mongoose.models.Plan) {
  delete mongoose.models.Plan;
}

const PlanModel = mongoose.models?.Plan || mongoose.model<IPlan>("Plan", planSchema);
export default PlanModel;