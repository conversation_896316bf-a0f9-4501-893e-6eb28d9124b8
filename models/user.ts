import { Provider, Role } from "@/enums/enums";
import mongoose, { Document, Schema, Types } from "mongoose";

// Interface for User document

interface IPaymentMethod {
  id: string;
  brand: string;
  last4: string;
  status: "active" | "inactive";
  expMonth: number;
  expYear: number;
  name: string;
  createdAt: Date;
}

interface IUser extends Document {
  nickname?: string;
  name?: string;
  email: string;
  billingEmail?: string;
  userName?: string;
  role: Role;
  avatar?: string;
  social?: string;
  fname?: string;
  mname?: string;
  taxType?: string;
  taxId?: string;
  lname?: string;
  password?: string;
  resetPasswordToken?: string;
  resetPasswordExpires?: Date;
  apiKey?: string;
  signature?: string;
  mobile?: string;
  balance?: Types.Decimal128;
  total_recharge?: Types.Decimal128;
  address?: string;
  postal_code?: string;
  city?: string;
  country?: string;
  state?: string;
  about?: string;
  nationalId?: string;
  image?: string;
  lastLogin?: Date;
  lastLoginIp?: string;
  emailVerified?: boolean;
  isManager: boolean;
  isAdmin: boolean;
  isGlobalManager: boolean;
  isGlobalAdmin: boolean;
  detail?: string;
  canLogin: boolean;
  otp?: string;
  otpVerified: boolean;
  isNotified: number;
  multiFactor: boolean;
  referralCode?: string;
  tourCount?: number;
  receiveMessages: boolean;
  createdBy?: number;
  updatedBy?: number;
  provider?: Provider | string;
  emailVerificationToken?: string;
  emailVerificationExpires?: Date;
  stripeCustomerId?: string;
  paymentMethods?: IPaymentMethod[];
  preferences: {
    receiveNewsNotifications: boolean;
  };
  rememberToken?: string;
  updatedAt: Date;
  createdAt: Date;
}

// Schema definition
const userSchema: Schema<IUser> = new mongoose.Schema(
  {
    nickname: {
      type: String,
      default: null,
    },
    name: {
      type: String,
      default: null,
    },
    email: {
      type: String,
      required: true,
      unique: true,
      trim: true,
      lowercase: true,
      index: true,
      validate: {
        validator: (v: string) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(v),
        message: (props: mongoose.ValidatorProps) => `${props.value} is not a valid email address!`,
      },
    },
    billingEmail: {
      type: String,
      required: true,
      trim: true,
      lowercase: true,
      index: true,
      validate: {
        validator: (v: string) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(v),
        message: (props: mongoose.ValidatorProps) => `${props.value} is not a valid email address!`,
      },
    },
    userName: {
      type: String,
      default: null,
      trim: true,
      index: true,
    },
    role: {
      type: String,
      enum: Object.values(Role),
      default: Role.USER,
      index: true,
    },
    avatar: {
      type: String,
      default: null,
    },
    social: {
      type: String,
      default: null,
    },
    fname: {
      type: String,
      default: null,
    },
    mname: {
      type: String,
      default: null,
    },
    lname: {
      type: String,
      default: null,
    },
    password: {
      type: String,
      default: null,
      required: true,
      select: false, // Security: don't return password by default
    },
    resetPasswordToken: {
      type: String,
      default: null,
      select: false, // Security: don't return token by default
    },
    resetPasswordExpires: {
      type: Date,
      default: null,
    },
    taxType: {
      type: String,
      trim: true,
    },
    taxId: {
      type: String,
      trim: true,
    },
    apiKey: {
      type: String,
      unique: true,
      sparse: true,
      maxlength: 80,
      default: undefined,
      select: false,
    },
    signature: {
      type: String,
      default: null,
    },
    mobile: {
      type: String,
      trim: true,
      maxlength: [20, "Mobile number cannot be longer than 20 characters"],
      minlength: [6, "Mobile number must have at least 6 characters"],
      required: [true, "Mobile number is required"],
      default: "07123456789",
      index: true,
      validate: {
        validator: function (v) {
          return /^[+]?[(]?[0-9]{1,4}[)]?[-\s./0-9]*$/.test(v);
        },
        message: (props) => `${props.value} is not a valid mobile number!`,
      },
      unique: true,
    },
    emailVerified: {
      type: Boolean,
      default: false,
    },
    emailVerificationToken: {
      type: String,
      default: null,
      select: false,
    },
    emailVerificationExpires: {
      type: Date,
      default: null,
    },
    balance: {
      type: mongoose.Schema.Types.Decimal128,
      default: 0.0,
    },
    total_recharge: {
      type: mongoose.Schema.Types.Decimal128,
      default: null,
    },
    address: {
      type: String,
      default: null,
    },
    postal_code: {
      type: String,
      default: null,
    },
    city: {
      type: String,
      default: null,
    },
    country: {
      type: String,
      default: null,
    },
    state: {
      type: String,
      default: null,
    },
    about: {
      type: String,
      default: null,
    },
    nationalId: {
      type: String,
      sparse: true,
      maxlength: 15,
      default: null,
      select: false, // Security: don't return national ID by default
    },
    image: {
      type: String,
      default: null,
    },
    lastLogin: {
      type: Date,
      default: null,
    },
    lastLoginIp: {
      type: String,
      default: null,
      select: false, // Security consideration
    },
    isManager: {
      type: Boolean,
      default: false,
    },
    isAdmin: {
      type: Boolean,
      default: false,
    },
    isGlobalManager: {
      type: Boolean,
      default: false,
    },
    isGlobalAdmin: {
      type: Boolean,
      default: false,
    },
    detail: {
      type: String,
      default: null,
    },
    canLogin: {
      type: Boolean,
      default: true,
    },
    otp: {
      type: String,
      default: null,
      select: false, // Security: don't return OTP by default
    },
    otpVerified: {
      type: Boolean,
      default: false,
    },
    isNotified: {
      type: Number,
      default: 0,
    },
    multiFactor: {
      type: Boolean,
      default: false,
    },
    referralCode: {
      type: String,
      default: null,
      index: true, // Added index for referral queries
    },
    tourCount: {
      type: Number,
      default: null,
    },
    receiveMessages: {
      type: Boolean,
      default: true,
    },
    createdBy: {
      type: Number,
      default: null,
    },
    updatedBy: {
      type: Number,
      default: null,
    },
    stripeCustomerId: {
      type: String,
      index: true,
    },
    paymentMethods: [
      {
        id: String,
        brand: String,
        last4: String,
        status: {
          type: String,
          enum: ["active", "inactive"],
          default: "inactive",
        },
        expMonth: Number,
        expYear: Number,
        name: String,
        createdAt: {
          type: Date,
          default: Date.now,
        },
      },
    ],
    provider: {
      type: String,
      enum: Object.values(Provider),
      default: Provider.LOCAL,
    },
    preferences: {
      receiveNewsNotifications: {
        type: Boolean,
        default: true,
      },
    },
    rememberToken: {
      type: String,
      default: null,
      select: false, // Security: don't return token by default
    },
  },
  {
    timestamps: true,
    toJSON: {
      virtuals: true,
      transform: (doc, ret) => {
        // Remove sensitive fields when converting to JSON
        delete ret.password;
        delete ret.passwordSalt;
        delete ret.resetPasswordToken;
        delete ret.apiKey;
        delete ret.nationalId;
        delete ret.otp;
        delete ret.rememberToken;
        return ret;
      },
    },
  }
);

userSchema.pre('save', function(next) {
  if (!this.billingEmail && this.email) {
    this.billingEmail = this.email;
  }
  next();
});

// Indexes for better query performance
userSchema.index({ role: 1, isAdmin: 1 }); // For role-based admin queries
userSchema.index({ createdAt: -1 }); // For sorting by newest users

if (mongoose.models && mongoose.models.User) {
  delete mongoose.models.User;
}

const UserModel =
  mongoose.models?.User || mongoose.model<IUser>("User", userSchema);
export default UserModel;
export type { IUser, IPaymentMethod };
