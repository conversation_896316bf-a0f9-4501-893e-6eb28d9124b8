import { ContactFormData } from "@/types/types";
import mongoose, { Schema, model } from "mongoose";

export interface ContactDocument extends ContactFormData {
  _id: string;
  createdAt: Date;
  updatedAt: Date;
  status: "pending" | "resolved" | "spam";
}

const ContactSchema = new Schema<ContactDocument>(
  {
    title: { type: String, required: true },
    chatbotId: { type: String },
    email: { type: String, required: true },
    message: { type: String, required: true },
    status: {
      type: String,
      enum: ["pending", "resolved", "spam"],
      default: "pending",
    },
  },
  {
    timestamps: true,
  }
);

if (mongoose.models && mongoose.models.Contact) {
  delete mongoose.models.Contact;
}

const ContactModel =
  mongoose.models?.ContactModel ||
  model<ContactDocument>("Contact", ContactSchema);
export default ContactModel;
