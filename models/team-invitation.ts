import mongoose, { Document, Model, Schema } from "mongoose";
import { StatusEnum } from "@/enums/enums";

export interface ITeamInvitation extends Document {
  teamId: mongoose.Types.ObjectId;
  email: string;
  token: string;
  expiresAt: Date;
  invitedBy: mongoose.Types.ObjectId;
  status: StatusEnum;
  createdAt: Date;
  updatedAt: Date;
}

const teamInvitationSchema: Schema = new mongoose.Schema(
  {
    teamId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Team",
      required: true,
    },
    email: {
      type: String,
      required: true,
      index: true,
    },
    token: {
      type: String,
      required: true,
      unique: true,
    },
    tokenHash: {
      type: String,
      required: true,
      unique: true,
    },
    expiresAt: {
      type: Date,
      required: true,
    },
    invitedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    status: {
      type: String,
      enum: Object.values(StatusEnum),
      default: StatusEnum.PENDING,
    },
  },
  {
    timestamps: true,
    toJSON: {
      virtuals: true,
      transform: function (doc, ret) {
        ret.id = ret._id.toString();
        delete ret._id;
        delete ret.__v;
        return ret;
      },
    },
  }
);

if (mongoose.models && mongoose.models.TeamInvitation) {
  delete mongoose.models.TeamInvitation;
}

const TeamInvitationModel: Model<ITeamInvitation> =
  mongoose.models.TeamInvitation ||
  mongoose.model<ITeamInvitation>("TeamInvitation", teamInvitationSchema);

export default TeamInvitationModel;