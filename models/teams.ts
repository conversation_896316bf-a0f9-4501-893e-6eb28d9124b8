import mongoose, { Document, Model, Schema } from "mongoose";
import MemberModel, { IMember } from "./member";
import { RoleEnum, StatusEnum,TeamPlanEnum  } from "@/enums/enums";
import UserModel from "./user";

export interface ITeam extends Document {
  name: string;
  url: string;
  description: string;
  uid: mongoose.Types.ObjectId;
  ownerId: mongoose.Types.ObjectId;
  apiKey?: string | null;
  openAiOrg?: string | null;
  openAiKey?: string | null;
  metaData?: string | null;
  isFavorite: boolean;
  members: IMember[];
  membersCount: number;
  plan: TeamPlanEnum; 
  avatar?: string; // 
  color: string;
  createdAt: Date;
  updatedAt: Date;
}

const teamSchema: Schema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    index: true,
  },
  avatar: {
    type: String,
    default: null,
  },
  url: {
    type: String,
    unique: true,
    required: true,
  },
  description: {
    type: String,
    required: true,
  },
  uid: {
    type: mongoose.Schema.Types.ObjectId,
    auto: true,
  },
  ownerId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "User",
    required: true,
  },
  apiKey: {
    type: String,
    default: null,
    unique: true,
  },
  openAiOrg: {
    type: String,
    default: null,
  },
  openAiKey: {
    type: String,
    default: null,
  },
  metaData: {
    type: String,
    default: null,
  },
  isFavorite: {
    type: Boolean,
    default: false,
    index: true,
  },
  membersCount: {
    type: Number,
    default: 1, //default is teh owner of teh team
    min: 1,
  },
  color: {
    type: String,
    default: "#3b82f6", // Default to blue-500
    validate: {
      validator: (v: string) => /^#([0-9A-F]{3}){1,2}$/i.test(v),
      message: (props: mongoose.ValidatorProps) => `${props.value} is not a valid hex color code!`,
    },
  },
   plan: {
    type: String,
    enum: Object.values(TeamPlanEnum),
    default: TeamPlanEnum.FREE,
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
  updatedAt: {
    type: Date,
    default: Date.now,
  },
});

// Add a virtual for members (not stored in DB)
teamSchema.virtual("members", {
  ref: "Member",
  localField: "_id",
  foreignField: "teamId",
  justOne: false,
  options: { sort: { createdAt: -1 } },
});

// Cascade delete middleware
teamSchema.pre(
  "deleteOne",
  { document: true, query: false },
  async function (next) {
    const teamId = this._id;
    try {
      // Delete all members associated with this team
      await MemberModel.deleteMany({ teamId });
      next();
    } catch (error: unknown) {
      next(error as Error);
    }
  }
);

// Include owner as a member when team is created
teamSchema.pre("save", async function (next) {
  if (this.isNew) {
    try {
      await MemberModel.create({
        teamId: this._id,
        userId: this.ownerId,
        role: RoleEnum.OWNER,
        status: StatusEnum.ACCEPTED,
        name: (await UserModel.findById(this.ownerId))?.name,
        avatar:
          (await UserModel.findById(this.ownerId))?.image ||
          "/avatars/default-avatar.png",
        email: (await UserModel.findById(this.ownerId))?.email,
      });
      this.membersCount = 1;
    } catch (error) {
      next(error as Error);
    }
  }
  next();
});

// Add toJSON option to include virtuals
teamSchema.set("toJSON", {
  virtuals: true,
  transform: function (doc, ret) {
    ret.id = ret._id.toString();
    delete ret._id;
    delete ret.__v;
    return ret;
  },
});

teamSchema.set("toObject", { virtuals: true });

if (mongoose.models && mongoose.models.Team) {
  delete mongoose.models.Team;
}

const TeamModel: Model<ITeam> =
  mongoose.models.Team || mongoose.model<ITeam>("Team", teamSchema);
export default TeamModel;
