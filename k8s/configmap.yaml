apiVersion: v1
kind: ConfigMap
metadata:
  name: chatzuri-config
  namespace: chatzuri-prod
data:
  NODE_ENV: "production"
  NEXT_PUBLIC_APP_URL: "https://app.chatzuri.com"
  NEXT_PUBLIC_API_URL: "https://api.chatzuri.com"
  LOG_LEVEL: "info"
  LOG_FORMAT: "json"
  FEATURE_ADVANCED_WORKFLOWS: "true"
  FEATURE_REAL_TIME_COLLABORATION: "false"
  QUEUE_CONCURRENCY: "5"
  QUEUE_MAX_RETRIES: "3"
  BACKUP_SCHEDULE: "0 2 * * *"
  AUDIT_LOG_RETENTION_DAYS: "2555"
  GDPR_COMPLIANCE: "true"
  SOC2_COMPLIANCE: "true"
  CORS_ORIGINS: "https://app.chatzuri.com,https://chatzuri.com"
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: nginx-config
  namespace: chatzuri-prod
data:
  nginx.conf: |
    events {
        worker_connections 1024;
    }
    
    http {
        upstream app {
            server chatzuri-app:3000;
        }
        
        # Rate limiting
        limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
        limit_req_zone $binary_remote_addr zone=login:10m rate=5r/m;
        
        # Security headers
        add_header X-Frame-Options DENY;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";
        add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline';";
        
        server {
            listen 80;
            server_name app.chatzuri.com;
            return 301 https://$server_name$request_uri;
        }
        
        server {
            listen 443 ssl http2;
            server_name app.chatzuri.com;
            
            ssl_certificate /etc/nginx/ssl/cert.pem;
            ssl_certificate_key /etc/nginx/ssl/key.pem;
            ssl_protocols TLSv1.2 TLSv1.3;
            ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
            ssl_prefer_server_ciphers off;
            
            # API rate limiting
            location /api/ {
                limit_req zone=api burst=20 nodelay;
                proxy_pass http://app;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
            }
            
            # Auth rate limiting
            location /auth/ {
                limit_req zone=login burst=5 nodelay;
                proxy_pass http://app;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
            }
            
            # Static files
            location /_next/static/ {
                expires 1y;
                add_header Cache-Control "public, immutable";
                proxy_pass http://app;
            }
            
            # Default location
            location / {
                proxy_pass http://app;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                
                # WebSocket support
                proxy_http_version 1.1;
                proxy_set_header Upgrade $http_upgrade;
                proxy_set_header Connection "upgrade";
            }
        }
    }
