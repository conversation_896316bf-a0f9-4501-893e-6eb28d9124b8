apiVersion: apps/v1
kind: Deployment
metadata:
  name: chatzuri-app
  namespace: chatzuri-prod
  labels:
    app: chatzuri-app
    version: v1
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: chatzuri-app
  template:
    metadata:
      labels:
        app: chatzuri-app
        version: v1
    spec:
      containers:
      - name: chatzuri-app
        image: your-registry/chatzuri-agents:latest
        ports:
        - containerPort: 3000
          name: http
        env:
        - name: NODE_ENV
          valueFrom:
            configMapKeyRef:
              name: chatzuri-config
              key: NODE_ENV
        - name: NEXT_PUBLIC_APP_URL
          valueFrom:
            configMapKeyRef:
              name: chatzuri-config
              key: NEXT_PUBLIC_APP_URL
        - name: MONGODB_URI
          valueFrom:
            secretKeyRef:
              name: chatzuri-secrets
              key: mongodb-uri
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: chatzuri-secrets
              key: redis-url
        - name: NEXTAUTH_SECRET
          valueFrom:
            secretKeyRef:
              name: chatzuri-secrets
              key: nextauth-secret
        - name: CREDENTIAL_ENCRYPTION_KEY
          valueFrom:
            secretKeyRef:
              name: chatzuri-secrets
              key: credential-encryption-key
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /api/health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /api/health
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        securityContext:
          runAsNonRoot: true
          runAsUser: 1001
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop:
            - ALL
        volumeMounts:
        - name: tmp
          mountPath: /tmp
        - name: logs
          mountPath: /app/logs
      volumes:
      - name: tmp
        emptyDir: {}
      - name: logs
        emptyDir: {}
      securityContext:
        fsGroup: 1001
      serviceAccountName: chatzuri-app
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: chatzuri-nginx
  namespace: chatzuri-prod
  labels:
    app: chatzuri-nginx
spec:
  replicas: 2
  selector:
    matchLabels:
      app: chatzuri-nginx
  template:
    metadata:
      labels:
        app: chatzuri-nginx
    spec:
      containers:
      - name: nginx
        image: nginx:alpine
        ports:
        - containerPort: 80
        - containerPort: 443
        volumeMounts:
        - name: nginx-config
          mountPath: /etc/nginx/nginx.conf
          subPath: nginx.conf
        - name: ssl-certs
          mountPath: /etc/nginx/ssl
          readOnly: true
        resources:
          requests:
            memory: "64Mi"
            cpu: "50m"
          limits:
            memory: "128Mi"
            cpu: "100m"
        livenessProbe:
          httpGet:
            path: /health
            port: 80
          initialDelaySeconds: 10
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 80
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: nginx-config
        configMap:
          name: nginx-config
      - name: ssl-certs
        secret:
          secretName: ssl-certs
