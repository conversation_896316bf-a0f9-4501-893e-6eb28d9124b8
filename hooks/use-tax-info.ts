import { useMutation, useQuery } from "@tanstack/react-query";

export const useTaxInfo = () => {
  const query = useQuery({
    queryKey: ["taxInfo"],
    queryFn: async () => {
      const response = await fetch("/api/billing/tax");
      if (!response.ok) {
        throw new Error("Failed to fetch tax information");
      }
      return response.json();
    },
  });

  const mutation = useMutation({
    mutationFn: async (values: { type: string; id: string }) => {
      const response = await fetch("/api/billing/tax", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(values),
      });
      if (!response.ok) {
        throw new Error("Failed to update tax information");
      }
      return response.json();
    },
  });

  return {
    data: query.data?.data,
    isLoading: query.isLoading,
    isError: query.isError,
    error: query.error,
    updateTaxInfo: mutation.mutateAsync,
    isUpdating: mutation.isPending,
    updateError: mutation.error,
  };
};
