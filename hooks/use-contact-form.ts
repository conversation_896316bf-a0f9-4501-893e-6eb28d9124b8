"use client";
import { useState } from "react";
import axios from "axios";
import toast from "react-hot-toast";
import { ContactFormData } from "@/types/types";

export function useContactForm() {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [ticketId, setTicketId] = useState<string | null>(null);

  const submitContactForm = async (data: ContactFormData) => {
    setIsSubmitting(true);
    try {
      const response = await axios.post("/api/contact", data);
      setTicketId(response.data.ticketId);
      toast.success("Message submitted successfully!");
      return response.data;
    } catch (error) {
      const errorMessage = axios.isAxiosError(error)
        ? error.response?.data?.message || "Failed to submit message"
        : "Failed to submit message";

      toast.error(errorMessage);
      throw error;
    } finally {
      setIsSubmitting(false);
    }
  };

  return { submitContactForm, isSubmitting, ticketId };
}
