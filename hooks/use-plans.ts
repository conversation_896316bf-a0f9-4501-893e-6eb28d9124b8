import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import axios from "axios";
import { Plan } from "@/types/types";
import { useSession } from "next-auth/react";

const API_URL = "/api/plans";

export const usePlans = () => {
  const queryClient = useQueryClient();
  const { data: session } = useSession();

  // Fetch all plans
  const fetchPlans = async (): Promise<Plan[]> => {
    const response = await axios.get(API_URL);
    return response.data;
  };

  // Create a new plan
  const createPlan = async (planData: Partial<Plan>): Promise<Plan> => {
    const response = await axios.post(API_URL, planData);
    return response.data;
  };

  // Update a plan
  const updatePlan = async ({
    id,
    ...planData
  }: Partial<Plan> & { id: string }): Promise<Plan> => {
    if (!id) throw new Error("Plan ID is required");
    const response = await axios.put(`${API_URL}/${id}`, planData);
    return response.data;
  };

  // Delete a plan
  const deletePlan = async (id: string): Promise<void> => {
    if (!id) throw new Error("Plan ID is required");
    await axios.delete(`${API_URL}/${id}`);
  };

  // Query for getting all plans
  const {
    data: plans,
    isLoading,
    error,
    refetch,
  } = useQuery<Plan[], Error>({
    queryKey: ["plans"],
    queryFn: fetchPlans,
    enabled: !!session?.user,
  });

  // Mutation for creating a plan
  const createMutation = useMutation<Plan, Error, Partial<Plan>>({
    mutationFn: createPlan,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["plans"] });
    },
  });

  // Mutation for updating a plan
  const updateMutation = useMutation<
    Plan,
    Error,
    Partial<Plan> & { id: string }
  >({
    mutationFn: updatePlan,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["plans"] });
    },
  });

  // Mutation for deleting a plan
  const deleteMutation = useMutation<void, Error, string>({
    mutationFn: deletePlan,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["plans"] });
    },
  });

  return {
    plans,
    isLoading,
    error,
    refetch,
    createPlan: createMutation.mutateAsync,
    isCreating: createMutation.isPending,
    updatePlan: updateMutation.mutateAsync,
    isUpdating: updateMutation.isPending,
    deletePlan: deleteMutation.mutateAsync,
    isDeleting: deleteMutation.isPending,
  };
};
