import { useQuery, useMutation, useQueryClient, keepPreviousData } from "@tanstack/react-query";
import { toast } from "react-hot-toast";
import { PaginationData } from "@/types/types";
import { IMember } from "@/models/member";

interface TeamMembersQueryProps {
  slug: string;
  initialPagination?: PaginationData;
}

export interface TeamMembersResponse {
  data: IMember[];
  pagination: PaginationData;
}

export const useTeamMembers = ({
  slug,
  initialPagination = { page: 1, limit: 10, total: 0, totalPages: 0 },
}: TeamMembersQueryProps) => {
  const queryClient = useQueryClient();

  // Helper function to invalidate all related queries
  const invalidateTeamQueries = () => {
    queryClient.invalidateQueries({
      queryKey: ["team-members", slug],
      exact: false, // Invalidate all paginated versions
    });
    queryClient.invalidateQueries({
      queryKey: ["team-invitations", slug],
      exact: false,
    });
    // If you have a combined members+invitations query
    queryClient.invalidateQueries({
      queryKey: ["team-users", slug],
      exact: false,
    });
  };

  const {
    data: membersData,
    isLoading,           
    error,
  } = useQuery<TeamMembersResponse>({
    queryKey: ["team-members", slug, initialPagination],
    queryFn: async () => {
      if (!slug) throw new Error("Team slug is required");
      const res = await fetch(
        `/api/teams/${slug}/members?page=${initialPagination.page}&limit=${initialPagination.limit}`
      );
      if (!res.ok) throw new Error("Failed to fetch members");
      return res.json();
    },
    placeholderData: keepPreviousData,
  });

  // Mutation for inviting a new member
  const inviteMember = useMutation({
    mutationFn: async (email: string) => {
      const response = await fetch(`/api/teams/${slug}/invite`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ email }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to send invitation");
      }
      return await response.json();
    },
    onSuccess: () => {
      toast.success("Invitation sent successfully");
      invalidateTeamQueries();
    },
    onError: (error: Error) => {
      toast.error(error.message || "Failed to send invitation");
    },
  });

  // Mutation for updating member role
  const updateMemberRole = useMutation({
    mutationFn: async ({ memberId, role }: { memberId: string; role: "member" | "admin" }) => {
      const response = await fetch(`/api/teams/${slug}/members/${memberId}/role`, {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ role }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to update role");
      }
      return await response.json();
    },
    onSuccess: () => {
      toast.success("Role updated successfully");
      invalidateTeamQueries();
    },
    onError: (error: Error) => {
      toast.error(error.message || "Failed to update role");
    },
  });

  // Mutation for removing a member
  const removeMember = useMutation({
    mutationFn: async (memberId: string) => {
      const response = await fetch(`/api/teams/${slug}/members/${memberId}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to remove member");
      }
      return memberId;
    },
    onSuccess: () => {
      toast.success("Member removed successfully");
      invalidateTeamQueries();
    },
    onError: (error: Error) => {
      toast.error(error.message || "Failed to remove member");
    },
  });

  // Mutation for canceling an invitation
  const cancelInvite = useMutation({
    mutationFn: async (invitationId: string) => {
      if (!invitationId) throw new Error('Invitation id is required');
      const response = await fetch(`/api/teams/${slug}/invite/${invitationId}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to cancel invitation");
      }
      return invitationId;
    },
    onSuccess: () => {
      toast.success("Invitation cancelled successfully");
      invalidateTeamQueries();
    },
    onError: (error: Error) => {
      toast.error(error.message || "Failed to cancel invitation");
    },
  });

  return {
    // Query data
    members: membersData?.data || [],
    pagination: membersData?.pagination || initialPagination,
    isLoading,
    error,

    // Mutations
    inviteMember,
    updateMemberRole,
    removeMember,
    cancelInvite,

    // Utility function
    invalidate: invalidateTeamQueries,
  };
};