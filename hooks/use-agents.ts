"use client";

import { IAgent } from "@/models/agent";
import {
  useInfiniteQuery,
  useMutation,
  useQuery,
  useQueryClient,
} from "@tanstack/react-query";
import axios from "axios";
import toast from "react-hot-toast";

interface PaginatedAgents {
  data: IAgent[];
  nextCursor: string | null;
}

export function useAgents(teamSlug: string, teamId: string, limit = 10) {
  if (!teamSlug) throw new Error("Teams url is required");
  return useInfiniteQuery<PaginatedAgents>({
    queryKey: ["agents", teamId],
    queryFn: async ({ pageParam }) => {
      const { data } = await axios.get(
        `/api/teams/${teamSlug}/agents?teamId=${teamId}&limit=${limit}&cursor=${
          pageParam || ""
        }`
      );
      return data;
    },
    getNextPageParam: (lastPage) => lastPage.nextCursor,
    enabled: !!teamId,
    initialPageParam: undefined,
  });
}

export function useAgent(teamSlug: string, agentId: string) {
  if (!teamSlug) throw new Error("Teams url is required");
  return useQuery<IAgent>({
    queryKey: ["agent", agentId],
    queryFn: async () => {
      const { data } = await axios.get(
        `/api/teams/${teamSlug}/agents/${agentId}`
      );
      return data;
    },
    enabled: !!agentId,
    staleTime: 1000 * 60 * 5, // 5 minutes cache
  });
}

export function useCreateAgent(teamSlug: string) {
  const queryClient = useQueryClient();
  if (!teamSlug) throw new Error("Teams url is required");
  return useMutation({
    mutationFn: (agentData: { name: string; teamId: string }) =>
      axios.post(`/api/teams/${teamSlug}/agents`, agentData),
    onSuccess: (response, variables) => {
      // Invalidate the agents list to refresh the data
      queryClient.invalidateQueries({ queryKey: ["agents", variables.teamId] });

      // Also invalidate any related queries
      queryClient.invalidateQueries({ queryKey: ["teams"] });

      toast.success("Agent created successfully!");

      // Return the created agent data for potential use
      return response.data;
    },
    onError: (error: unknown) => {
      let errorMessage = "Failed to create agent";
      if (axios.isAxiosError(error) && error.response?.data?.message) {
        errorMessage = error.response.data.message;
      }
      toast.error(errorMessage);
    },
  });
}

export function useUpdateAgent(teamSlug: string) {
  const queryClient = useQueryClient();
  if (!teamSlug) throw new Error("Teams url is required");

  return useMutation({
    mutationFn: ({
      id,
      ...agentData
    }: {
      id: string;
      name: string;
      teamId: string;
    }) => axios.patch(`/api/teams/${teamSlug}/agents/${id}`, agentData),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ["agents", variables.teamId] });
      toast.success("Agent updated successfully!");
    },
    onError: (error: unknown) => {
      let errorMessage = "Failed to update agent";
      if (axios.isAxiosError(error) && error.response?.data?.message) {
        errorMessage = error.response.data.message;
      }
      toast.error(errorMessage);
    },
  });
}

export function useDeleteAgent(teamSlug: string) {
  const queryClient = useQueryClient();
  if (!teamSlug) throw new Error("Teams url is required");
  return useMutation({
    mutationFn: ({ id, teamId }: { id: string; teamId: string }) =>
      axios.delete(`/api/teams/${teamSlug}/agents/${id}?teamId=${teamId}`),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ["agents", variables.teamId] });
      toast.success("Agent deleted successfully!");
    },
    onError: (error: unknown) => {
      let errorMessage = "Failed to delete agent";
      if (axios.isAxiosError(error) && error.response?.data?.message) {
        errorMessage = error.response.data.message;
      }
      toast.error(errorMessage);
    },
  });
}
