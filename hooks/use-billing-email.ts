import { useMutation, useQuery } from '@tanstack/react-query';

export const useBillingEmail = () => {
  const query = useQuery({
    queryKey: ['billingEmail'],
    queryFn: async () => {
      const response = await fetch('/api/billing/email');
      if (!response.ok) {
        throw new Error('Failed to fetch billing email');
      }
      return response.json();
    },
  });

  const mutation = useMutation({
    mutationFn: async (email: string) => {
      const response = await fetch('/api/billing/email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ billingEmail: email }),
      });
      if (!response.ok) {
        throw new Error('Failed to update billing email');
      }
      return response.json();
    },
  });

  return {
    data: query.data?.data,
    isLoading: query.isLoading,
    isError: query.isError,
    error: query.error,
    updateEmail: mutation.mutateAsync,
    isUpdating: mutation.isPending,
    updateError: mutation.error,
  };
};