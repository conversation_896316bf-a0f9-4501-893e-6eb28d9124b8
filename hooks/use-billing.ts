import { useQuery, useMutation } from '@tanstack/react-query';
import { BillingFormValues } from '@/types/types';

export const useBillingData = () => {
  return useQuery({
    queryKey: ['billing'],
    queryFn: async () => {
      const response = await fetch('/api/billing');
      if (!response.ok) {
        throw new Error('Failed to fetch billing data');
      }
      return response.json();
    },
  });
};

export const useUpdateBilling = () => {
  return useMutation({
    mutationFn: async (values: BillingFormValues) => {
      const response = await fetch('/api/billing', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(values),
      });
      if (!response.ok) {
        throw new Error('Failed to update billing data');
      }
      return response.json();
    },
  });
};