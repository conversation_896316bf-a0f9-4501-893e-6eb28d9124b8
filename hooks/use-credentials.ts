// Custom hook for credential management

import { useState, useEffect, useCallback } from 'react';
import { toast } from 'react-hot-toast';
import { 
  Credential, 
  AppType, 
  CreateCredentialRequest, 
  UpdateCredentialRequest,
  CredentialListResponse,
  CredentialValidationResult
} from '@/lib/integrations/types/integration-types';

interface UseCredentialsOptions {
  teamId: string;
  appType?: AppType;
  autoLoad?: boolean;
}

export function useCredentials({ teamId, appType, autoLoad = true }: UseCredentialsOptions) {
  const [credentials, setCredentials] = useState<Credential[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [total, setTotal] = useState(0);

  // Load credentials
  const loadCredentials = useCallback(async (page = 1, limit = 50) => {
    if (!teamId) return;

    setLoading(true);
    setError(null);

    try {
      const params = new URLSearchParams({
        teamId,
        page: page.toString(),
        limit: limit.toString()
      });

      if (appType) {
        params.append('appType', appType);
      }

      const response = await fetch(`/api/credentials?${params}`);
      
      if (!response.ok) {
        throw new Error('Failed to load credentials');
      }

      const data: CredentialListResponse = await response.json();
      setCredentials(data.credentials);
      setTotal(data.total);

    } catch (error) {
      const message = error instanceof Error ? error.message : 'Failed to load credentials';
      setError(message);
      toast.error(message);
    } finally {
      setLoading(false);
    }
  }, [teamId, appType]);

  // Create credential
  const createCredential = useCallback(async (request: CreateCredentialRequest): Promise<Credential | null> => {
    if (!teamId) return null;

    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/credentials', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          teamId,
          ...request
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create credential');
      }

      const credential: Credential = await response.json();
      
      // Add to local state
      setCredentials(prev => [credential, ...prev]);
      setTotal(prev => prev + 1);
      
      toast.success('Credential created successfully');
      return credential;

    } catch (error) {
      const message = error instanceof Error ? error.message : 'Failed to create credential';
      setError(message);
      toast.error(message);
      return null;
    } finally {
      setLoading(false);
    }
  }, [teamId]);

  // Update credential
  const updateCredential = useCallback(async (
    credentialId: string, 
    request: UpdateCredentialRequest
  ): Promise<Credential | null> => {
    if (!teamId) return null;

    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/credentials', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          credentialId,
          teamId,
          ...request
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update credential');
      }

      const updatedCredential: Credential = await response.json();
      
      // Update local state
      setCredentials(prev => 
        prev.map(cred => 
          cred.id === credentialId ? updatedCredential : cred
        )
      );
      
      toast.success('Credential updated successfully');
      return updatedCredential;

    } catch (error) {
      const message = error instanceof Error ? error.message : 'Failed to update credential';
      setError(message);
      toast.error(message);
      return null;
    } finally {
      setLoading(false);
    }
  }, [teamId]);

  // Delete credential
  const deleteCredential = useCallback(async (credentialId: string): Promise<boolean> => {
    if (!teamId) return false;

    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/credentials?credentialId=${credentialId}&teamId=${teamId}`, {
        method: 'DELETE'
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete credential');
      }

      // Remove from local state
      setCredentials(prev => prev.filter(cred => cred.id !== credentialId));
      setTotal(prev => prev - 1);
      
      toast.success('Credential deleted successfully');
      return true;

    } catch (error) {
      const message = error instanceof Error ? error.message : 'Failed to delete credential';
      setError(message);
      toast.error(message);
      return false;
    } finally {
      setLoading(false);
    }
  }, [teamId]);

  // Test credential
  const testCredential = useCallback(async (credentialId: string): Promise<CredentialValidationResult | null> => {
    if (!teamId) return null;

    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/credentials/test', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          credentialId,
          teamId
        })
      });

      if (!response.ok) {
        throw new Error('Failed to test credential');
      }

      const result: CredentialValidationResult = await response.json();
      
      if (result.isValid) {
        toast.success('Credential connection successful');
      } else {
        toast.error(`Connection failed: ${result.errors.join(', ')}`);
      }

      return result;

    } catch (error) {
      const message = error instanceof Error ? error.message : 'Failed to test credential';
      setError(message);
      toast.error(message);
      return null;
    } finally {
      setLoading(false);
    }
  }, [teamId]);

  // Test new credentials (before saving)
  const testNewCredentials = useCallback(async (
    appType: AppType, 
    credentials: Record<string, any>
  ): Promise<CredentialValidationResult | null> => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/credentials/test', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          appType,
          credentials
        })
      });

      if (!response.ok) {
        throw new Error('Failed to test credentials');
      }

      const result: CredentialValidationResult = await response.json();
      return result;

    } catch (error) {
      const message = error instanceof Error ? error.message : 'Failed to test credentials';
      setError(message);
      toast.error(message);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  // Get credential by ID
  const getCredential = useCallback((credentialId: string): Credential | undefined => {
    return credentials.find(cred => cred.id === credentialId);
  }, [credentials]);

  // Get credentials by app type
  const getCredentialsByAppType = useCallback((appType: AppType): Credential[] => {
    return credentials.filter(cred => cred.appType === appType);
  }, [credentials]);

  // Auto-load on mount
  useEffect(() => {
    if (autoLoad && teamId) {
      loadCredentials();
    }
  }, [autoLoad, teamId, loadCredentials]);

  return {
    credentials,
    loading,
    error,
    total,
    loadCredentials,
    createCredential,
    updateCredential,
    deleteCredential,
    testCredential,
    testNewCredentials,
    getCredential,
    getCredentialsByAppType,
    refresh: () => loadCredentials()
  };
}
