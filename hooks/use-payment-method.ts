import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "react-hot-toast";

export const usePaymentMethods = () => {
  const queryClient = useQueryClient();

  // Fetch payment methods
  const { data, isLoading, isError, error } = useQuery({
    queryKey: ["paymentMethods"],
    queryFn: async () => {
      const response = await fetch("/api/payment-methods");
      if (!response.ok) {
        throw new Error("Failed to fetch payment methods");
      }
      return response.json();
    },
  });

  // Add payment method
  const addPaymentMethod = useMutation({
    mutationFn: async (data: { paymentMethodId: string; cardName: string }) => {
      const response = await fetch("/api/payment-methods", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error("Failed to save payment method");
      }
      return response.json();
    },
    onSuccess: () => {
      toast.success("Payment method added successfully");
      queryClient.invalidateQueries({ queryKey: ["paymentMethods"] });
    },
    onError: (error: Error) => {
      toast.error(error.message || "Failed to add payment method");
    },
  });

  // Delete payment method
  const deletePaymentMethod = useMutation({
    mutationFn: async (paymentMethodId: string) => {
      const response = await fetch(`/api/payment-methods/${paymentMethodId}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        throw new Error("Failed to delete payment method");
      }
      return response.json();
    },
    onSuccess: () => {
      toast.success("Payment method deleted successfully");
      queryClient.invalidateQueries({ queryKey: ["paymentMethods"] });
    },
    onError: (error: Error) => {
      toast.error(error.message || "Failed to delete payment method");
    },
  });

  // Toggle payment method status
  const updatePaymentMethodStatus = useMutation({
    mutationFn: async ({ id, status }: { id: string; status: string }) => {
      const response = await fetch(`/api/payment-methods/${id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ status }),
      });

      if (!response.ok) {
        throw new Error("Failed to update payment method status");
      }
      return response.json();
    },
    onSuccess: (_, { status }) => {
      toast.success(
        `Card ${status !== 'active' ? "activated" : "deactivated"} successfully`
      );
      queryClient.invalidateQueries({ queryKey: ["paymentMethods"] });
    },
    onError: (error: Error) => {
      toast.error(error.message || "Failed to update status");
    },
  });

  return {
    data,
    isLoading,
    isError,
    error,
    addPaymentMethod: addPaymentMethod.mutateAsync,
    isAdding: addPaymentMethod.isPending,
    deletePaymentMethod: deletePaymentMethod.mutateAsync,
    isDeleting: deletePaymentMethod.isPending,
    updatePaymentMethodStatus: updatePaymentMethodStatus.mutateAsync,
    isUpdatingStatus: updatePaymentMethodStatus.isPending,
  };
};
