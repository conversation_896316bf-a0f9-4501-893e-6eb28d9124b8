import { renderHook, act, waitFor } from '@testing-library/react'
import { useCredentials } from '../use-credentials'
import toast from 'react-hot-toast'

// Mock dependencies
jest.mock('react-hot-toast')
global.fetch = jest.fn()

const mockToast = toast as jest.Mocked<typeof toast>

describe('useCredentials', () => {
  const teamId = 'team-123'
  
  const mockCredentials = [
    {
      id: 'cred-1',
      name: 'OpenAI Production',
      appType: 'openai' as const,
      isActive: true,
      lastUsedAt: new Date('2024-01-01'),
      usageCount: 5,
      teamId: 'team-123',
      encryptedCredentials: 'encrypted',
      createdAt: new Date(),
      updatedAt: new Date(),
      createdBy: 'user-1'
    }
  ]

  beforeEach(() => {
    jest.clearAllMocks()
    
    // Mock successful API responses by default
    ;(fetch as jest.Mock).mockResolvedValue({
      ok: true,
      json: () => Promise.resolve({
        credentials: mockCredentials,
        total: 1
      })
    })
  })

  describe('initialization', () => {
    it('should initialize with default state', () => {
      const { result } = renderHook(() => useCredentials({ teamId }))
      
      expect(result.current.credentials).toEqual([])
      expect(result.current.loading).toBe(true)
      expect(result.current.error).toBeNull()
      expect(result.current.total).toBe(0)
    })

    it('should load credentials on mount', async () => {
      const { result } = renderHook(() => useCredentials({ teamId }))
      
      await waitFor(() => {
        expect(result.current.loading).toBe(false)
      })
      
      expect(result.current.credentials).toEqual(mockCredentials)
      expect(result.current.total).toBe(1)
    })

    it('should not load credentials when teamId is not provided', () => {
      renderHook(() => useCredentials({ teamId: '' }))
      
      expect(fetch).not.toHaveBeenCalled()
    })
  })

  describe('loadCredentials', () => {
    it('should load credentials successfully', async () => {
      const { result } = renderHook(() => useCredentials({ teamId }))
      
      await act(async () => {
        await result.current.loadCredentials()
      })
      
      expect(fetch).toHaveBeenCalledWith(
        `/api/teams/${teamId}/credentials?page=1&limit=50`,
        expect.objectContaining({
          method: 'GET',
          headers: expect.objectContaining({
            'Content-Type': 'application/json'
          })
        })
      )
      
      expect(result.current.credentials).toEqual(mockCredentials)
    })

    it('should handle API errors', async () => {
      ;(fetch as jest.Mock).mockResolvedValue({
        ok: false,
        status: 500,
        statusText: 'Internal Server Error'
      })

      const { result } = renderHook(() => useCredentials({ teamId }))
      
      await waitFor(() => {
        expect(result.current.loading).toBe(false)
      })
      
      expect(result.current.error).toBe('Failed to load credentials')
      expect(mockToast.error).toHaveBeenCalledWith('Failed to load credentials')
    })

    it('should filter by appType when provided', async () => {
      const { result } = renderHook(() => 
        useCredentials({ teamId, appType: 'openai' })
      )
      
      await waitFor(() => {
        expect(result.current.loading).toBe(false)
      })
      
      expect(fetch).toHaveBeenCalledWith(
        `/api/teams/${teamId}/credentials?page=1&limit=50&appType=openai`,
        expect.any(Object)
      )
    })
  })

  describe('createCredential', () => {
    const createRequest = {
      name: 'New Credential',
      appType: 'openai' as const,
      credentials: { api_key: 'test-key' }
    }

    it('should create credential successfully', async () => {
      const newCredential = { ...mockCredentials[0], id: 'cred-2', name: 'New Credential' }
      
      ;(fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(newCredential)
      })

      const { result } = renderHook(() => useCredentials({ teamId }))
      
      await act(async () => {
        const created = await result.current.createCredential(createRequest)
        expect(created).toEqual(newCredential)
      })
      
      expect(fetch).toHaveBeenCalledWith(
        `/api/teams/${teamId}/credentials`,
        expect.objectContaining({
          method: 'POST',
          body: JSON.stringify(createRequest)
        })
      )
      
      expect(mockToast.success).toHaveBeenCalledWith('Credential created successfully')
    })

    it('should handle creation errors', async () => {
      ;(fetch as jest.Mock).mockResolvedValue({
        ok: false,
        status: 400,
        json: () => Promise.resolve({ error: 'Invalid credentials' })
      })

      const { result } = renderHook(() => useCredentials({ teamId }))
      
      await act(async () => {
        const created = await result.current.createCredential(createRequest)
        expect(created).toBeNull()
      })
      
      expect(mockToast.error).toHaveBeenCalledWith('Invalid credentials')
    })
  })

  describe('updateCredential', () => {
    const updateRequest = {
      name: 'Updated Credential',
      isActive: false
    }

    it('should update credential successfully', async () => {
      const updatedCredential = { ...mockCredentials[0], ...updateRequest }
      
      ;(fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(updatedCredential)
      })

      const { result } = renderHook(() => useCredentials({ teamId }))
      
      await act(async () => {
        const updated = await result.current.updateCredential('cred-1', updateRequest)
        expect(updated).toEqual(updatedCredential)
      })
      
      expect(fetch).toHaveBeenCalledWith(
        `/api/teams/${teamId}/credentials/cred-1`,
        expect.objectContaining({
          method: 'PUT',
          body: JSON.stringify(updateRequest)
        })
      )
      
      expect(mockToast.success).toHaveBeenCalledWith('Credential updated successfully')
    })
  })

  describe('deleteCredential', () => {
    it('should delete credential successfully', async () => {
      ;(fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({ success: true })
      })

      const { result } = renderHook(() => useCredentials({ teamId }))
      
      await act(async () => {
        const deleted = await result.current.deleteCredential('cred-1')
        expect(deleted).toBe(true)
      })
      
      expect(fetch).toHaveBeenCalledWith(
        `/api/teams/${teamId}/credentials/cred-1`,
        expect.objectContaining({
          method: 'DELETE'
        })
      )
      
      expect(mockToast.success).toHaveBeenCalledWith('Credential deleted successfully')
    })

    it('should handle deletion errors', async () => {
      ;(fetch as jest.Mock).mockResolvedValue({
        ok: false,
        status: 409,
        json: () => Promise.resolve({ error: 'Credential is in use' })
      })

      const { result } = renderHook(() => useCredentials({ teamId }))
      
      await act(async () => {
        const deleted = await result.current.deleteCredential('cred-1')
        expect(deleted).toBe(false)
      })
      
      expect(mockToast.error).toHaveBeenCalledWith('Credential is in use')
    })
  })

  describe('testCredential', () => {
    it('should test credential successfully', async () => {
      const testResult = {
        isValid: true,
        errors: [],
        warnings: []
      }
      
      ;(fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(testResult)
      })

      const { result } = renderHook(() => useCredentials({ teamId }))
      
      await act(async () => {
        const result_test = await result.current.testCredential('cred-1')
        expect(result_test).toEqual(testResult)
      })
      
      expect(fetch).toHaveBeenCalledWith(
        `/api/teams/${teamId}/credentials/cred-1/test`,
        expect.objectContaining({
          method: 'POST'
        })
      )
      
      expect(mockToast.success).toHaveBeenCalledWith('Credential test passed')
    })

    it('should handle test failures', async () => {
      const testResult = {
        isValid: false,
        errors: ['Invalid API key'],
        warnings: []
      }
      
      ;(fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(testResult)
      })

      const { result } = renderHook(() => useCredentials({ teamId }))
      
      await act(async () => {
        const result_test = await result.current.testCredential('cred-1')
        expect(result_test).toEqual(testResult)
      })
      
      expect(mockToast.error).toHaveBeenCalledWith('Credential test failed: Invalid API key')
    })
  })

  describe('testNewCredentials', () => {
    const testCredentials = {
      appType: 'openai' as const,
      credentials: { api_key: 'test-key' }
    }

    it('should test new credentials successfully', async () => {
      const testResult = {
        isValid: true,
        errors: [],
        warnings: []
      }
      
      ;(fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(testResult)
      })

      const { result } = renderHook(() => useCredentials({ teamId }))
      
      await act(async () => {
        const result_test = await result.current.testNewCredentials(testCredentials)
        expect(result_test).toEqual(testResult)
      })
      
      expect(fetch).toHaveBeenCalledWith(
        `/api/teams/${teamId}/credentials/test`,
        expect.objectContaining({
          method: 'POST',
          body: JSON.stringify(testCredentials)
        })
      )
    })
  })

  describe('utility functions', () => {
    it('should get credential by id', async () => {
      const { result } = renderHook(() => useCredentials({ teamId }))
      
      await waitFor(() => {
        expect(result.current.loading).toBe(false)
      })
      
      const credential = result.current.getCredential('cred-1')
      expect(credential).toEqual(mockCredentials[0])
    })

    it('should get credentials by app type', async () => {
      const { result } = renderHook(() => useCredentials({ teamId }))
      
      await waitFor(() => {
        expect(result.current.loading).toBe(false)
      })
      
      const openaiCredentials = result.current.getCredentialsByAppType('openai')
      expect(openaiCredentials).toEqual([mockCredentials[0]])
    })

    it('should refresh credentials', async () => {
      const { result } = renderHook(() => useCredentials({ teamId }))
      
      await act(async () => {
        await result.current.refresh()
      })
      
      // Should call loadCredentials again
      expect(fetch).toHaveBeenCalledTimes(2) // Once on mount, once on refresh
    })
  })
})
