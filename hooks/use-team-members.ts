import { useQuery, useQueryClient } from "@tanstack/react-query";
import { PaginationData } from "@/types/types";
import { IMember } from "@/models/member";

interface Props {
  slug: string;
  initialPagination?: PaginationData;
}

export interface TeamMembersResponse {
  data: IMember[];
  pagination: PaginationData;
}

export const useTeamMembers = (props: Props) => {
  const queryClient = useQueryClient();

  const {
    data: membersData,
    isLoading,
    error,
  } = useQuery<TeamMembersResponse>({
    queryKey: ["team-members", props.slug, props.initialPagination],
    queryFn: async () => {
      if (!props.slug) return null;

      const response = await fetch(
        `/api/teams/${props.slug}/members?page=${
          props.initialPagination?.page ?? 1
        }&limit=${props.initialPagination?.limit ?? 10}`
      );
      if (!response.ok) throw new Error("Failed to fetch members");
      return await response.json();
    },
  });

  

  return {
    members: membersData?.data || [],
    pagination: membersData?.pagination || props.initialPagination,
    isLoading,
    error,
    /**
     * Invalidates the query for team members. This is useful when you've changed a team member
     * and want to refetch the data.
     */
    invalidate: () =>
      queryClient.invalidateQueries({ queryKey: ["team-members"] }),
  };
};
