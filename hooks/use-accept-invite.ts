import { useMutation } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import { toast } from "react-hot-toast";

export const useAcceptInvite = () => {
  const router = useRouter();

  return useMutation({
    mutationFn: async (token: string) => {
      const response = await fetch(`/api/invite/${token}`);
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message);
      }
      return await response.json();
    },
    onSuccess: (data) => {
      if (data.redirect) {
        router.push(data.redirect);
      }
      toast.success(data.message || "Successfully joined the team");
    },
    onError: (error: Error) => {
      toast.error(error.message || "Failed to accept invitation");
    },
  });
};
