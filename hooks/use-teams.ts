"use client";

import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import axios, { AxiosError } from "axios";
import {
  Team,
  CreateTeamDto,
  UpdateTeamDto,
  TeamsApiResponse,
  TeamApiResponse,
  TeamsQueryResult,
} from "@/types/types";
import { useSession } from "next-auth/react";

const API_URL = "/api/teams";

export function useTeams(page: number = 1, limit: number = 10) {
  const { data: session } = useSession();

  return useQuery<
    TeamsApiResponse,
    AxiosError<Pick<TeamsApiResponse, "success" | "error">>
  >({
    queryKey: ["teams", session?.user?.id, page, limit],
    queryFn: async () => {
      const { data } = await axios.get<TeamsApiResponse>(
        `${API_URL}?page=${page}&limit=${limit}`
      );
      if (!data.success) throw new AxiosError(data.error);
      return data;
    },
    staleTime: 1000 * 60 * 5,
  });
}

export function useTeam(slug: string) {
  return useQuery<
    { data: TeamApiResponse["data"]; status: number },
    AxiosError<TeamApiResponse>
  >({
    queryKey: ["team", slug],
    queryFn: async () => {
      if (!slug) throw new Error("slug is required");

      const response = await axios.get<TeamApiResponse>(`${API_URL}/${slug}`);
      const { data, status } = response;

      if (!data.success) {
        throw new AxiosError(
          data.error || "Request failed",
          undefined,
          response.config,
          undefined,
          {
            data,
            status,
            statusText: response.statusText,
            headers: response.headers,
            config: response.config,
          }
        );
      }

      return { data: data.data, status };
    },
    enabled: !!slug,
  });
}

export function useCreateTeam() {
  const queryClient = useQueryClient();
  const { data: session } = useSession();

  return useMutation<
    Team,
    AxiosError<TeamsApiResponse>,
    CreateTeamDto,
    { previousTeams?: TeamsQueryResult }
  >({
    mutationFn: async (teamData) => {
      const { data } = await axios.post<TeamsApiResponse>(API_URL, teamData);
      if (!data.success) throw new AxiosError(data.error);
      return Array.isArray(data.data) ? data.data[0] : data.data;
    },
    onMutate: async (newTeamData) => {
      // Cancel outgoing queries
      await queryClient.cancelQueries({
        queryKey: ["teams", session?.user?.id],
      });

      // Get previous data
      const previousTeams = queryClient.getQueryData<TeamsQueryResult>([
        "teams",
        session?.user?.id,
        1,
      ]);

      // Optimistically update first page
      queryClient.setQueryData<TeamsQueryResult>(
        ["teams", session?.user?.id, 1],
        (old) => {
          if (!old) {
            return {
              teams: [
                {
                  ...newTeamData,
                  _id: "temp-id",
                  uid: "temp-uid",
                  createdAt: new Date().toISOString(),
                  updatedAt: new Date().toISOString(),
                } as unknown as Team,
              ],
              pagination: {
                total: 1,
                page: 1,
                limit: 10,
                totalPages: 1,
              },
            };
          }

          return {
            teams: [
              {
                ...newTeamData,
                _id: "temp-id",
                uid: "temp-uid",
              } as unknown as Team,
              ...old.teams,
            ],
            pagination: {
              ...old.pagination,
              total: old.pagination.total + 1,
              totalPages: Math.ceil(
                (old.pagination.total + 1) / old.pagination.limit
              ),
            },
          };
        }
      );

      return { previousTeams };
    },
    onError: (error, variables, context) => {
      // Rollback on error
      if (context?.previousTeams) {
        queryClient.setQueryData(
          ["teams", session?.user?.id, 1],
          context.previousTeams
        );
      }
    },
    onSettled: () => {
      // Invalidate all team queries
      queryClient.invalidateQueries({
        queryKey: ["teams", session?.user?.id],
      });
    },
  });
}

export function useUpdateTeam() {
  const queryClient = useQueryClient();
  const { data: session } = useSession();

  return useMutation<
    Team,
    AxiosError<TeamApiResponse>,
    { slug: string; data: UpdateTeamDto }
  >({
    mutationFn: async ({ slug, data }) => {
      if (!slug) throw new Error("slug is required");
      const { data: response } = await axios.put<TeamApiResponse>(
        `${API_URL}/${slug}`,
        data
      );
      if (!response.success) throw new AxiosError(response.error);
      return response.data?.currentTeam as Team;
    },
    onSuccess: (_, variables) => {
      queryClient
        .invalidateQueries({
          queryKey: ["teams", session?.user?.id],
        })
        .catch(console.error);

      queryClient
        .invalidateQueries({
          queryKey: ["team", variables.slug],
        })
        .catch(console.error);
    },
    onError: (error) => {
      console.error("Mutation error:", error);
    },
  });
}

export function useDeleteTeam() {
  const queryClient = useQueryClient();
  const { data: session } = useSession();

  return useMutation<
    void,
    AxiosError<TeamApiResponse>,
    string,
    { previousPages?: [unknown, TeamsApiResponse | undefined][] }
  >({
    mutationFn: async (slug) => {
      if (!slug) throw new Error("slug is required");
      const { data } = await axios.delete<TeamApiResponse>(`${API_URL}/${slug}`);
      if (!data.success) throw new AxiosError(data.error);
    },
    onMutate: async (slug) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({
        queryKey: ["teams", session?.user?.id],
      });

      // Snapshot the previous value for all pages
      const previousPages = queryClient.getQueriesData<TeamsApiResponse>({
        queryKey: ["teams", session?.user?.id],
      });

      // Optimistically remove the team from all pages
      previousPages.forEach(([queryKey, data]) => {
        if (!data) return;

        // Handle both single team and array cases
        const teamsArray = Array.isArray(data.data) 
          ? data.data 
          : data.data 
            ? [data.data] 
            : [];

        const updatedTeams = teamsArray.filter((team) => team.url !== slug);

        queryClient.setQueryData<TeamsApiResponse>(queryKey, {
          ...data,
          data: updatedTeams, // This matches your TeamsApiResponse structure
          pagination: {
            ...data.pagination,
            total: data.pagination.total - 1,
            totalPages: Math.ceil(
              (data.pagination.total - 1) / data.pagination.limit
            ),
          },
        });
      });

      return { previousPages };
    },
    onError: (err, slug, context) => {
      // Rollback to previous value on error
      if (context?.previousPages) {
        context.previousPages.forEach(([queryKey, data]) => {
          if (data) {
            queryClient.setQueryData(queryKey as readonly unknown[], data);
          }
        });
      }
    },
    onSettled: () => {
      // Force refresh all team queries
      queryClient.invalidateQueries({
        queryKey: ["teams", session?.user?.id],
        exact: false,
      });
    },
  });
}
