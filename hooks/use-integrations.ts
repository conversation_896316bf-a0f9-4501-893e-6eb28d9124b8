// Custom hook for integration management

import { useState, useEffect, useCallback } from 'react';
import { Integration, AppType } from '@/lib/integrations/types/integration-types';

interface UseIntegrationsOptions {
  category?: Integration['category'];
  search?: string;
  autoLoad?: boolean;
}

export function useIntegrations({ category, search, autoLoad = true }: UseIntegrationsOptions = {}) {
  const [integrations, setIntegrations] = useState<Integration[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [categories, setCategories] = useState<string[]>([]);

  // Load integrations
  const loadIntegrations = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const params = new URLSearchParams();
      if (category) params.append('category', category);
      if (search) params.append('search', search);

      const response = await fetch(`/api/integrations?${params}`);
      
      if (!response.ok) {
        throw new Error('Failed to load integrations');
      }

      const data = await response.json();
      setIntegrations(data.integrations);
      setCategories(data.categories);

    } catch (error) {
      const message = error instanceof Error ? error.message : 'Failed to load integrations';
      setError(message);
    } finally {
      setLoading(false);
    }
  }, [category, search]);

  // Get integration by app type
  const getIntegration = useCallback((appType: AppType): Integration | undefined => {
    return integrations.find(integration => integration.id === appType);
  }, [integrations]);

  // Get integrations by category
  const getIntegrationsByCategory = useCallback((cat: Integration['category']): Integration[] => {
    return integrations.filter(integration => integration.category === cat);
  }, [integrations]);

  // Get AI service integrations
  const getAIServices = useCallback((): Integration[] => {
    return getIntegrationsByCategory('ai_services');
  }, [getIntegrationsByCategory]);

  // Get business app integrations
  const getBusinessApps = useCallback((): Integration[] => {
    return getIntegrationsByCategory('business_apps');
  }, [getIntegrationsByCategory]);

  // Search integrations
  const searchIntegrations = useCallback((query: string): Integration[] => {
    const lowerQuery = query.toLowerCase();
    return integrations.filter(integration =>
      integration.name.toLowerCase().includes(lowerQuery) ||
      integration.description.toLowerCase().includes(lowerQuery)
    );
  }, [integrations]);

  // Auto-load on mount
  useEffect(() => {
    if (autoLoad) {
      loadIntegrations();
    }
  }, [autoLoad, loadIntegrations]);

  return {
    integrations,
    loading,
    error,
    categories,
    loadIntegrations,
    getIntegration,
    getIntegrationsByCategory,
    getAIServices,
    getBusinessApps,
    searchIntegrations,
    refresh: loadIntegrations
  };
}
