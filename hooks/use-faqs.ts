"use client";

import { FAQFormData } from "@/types/types";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import axios from "axios";
import toast from "react-hot-toast";

export function useFAQs() {
  return useQuery({
    queryKey: ["faqs"],
    queryFn: async () => {
      const { data } = await axios.get("/api/faqs");
      return data;
    },
  });
}

export function useCreateFAQ() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (faqData: FAQFormData) => axios.post("/api/faqs", faqData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["faqs"] });
      toast.success("FAQ created successfully!");
    },
    //eslint-disable-next-line @typescript-eslint/no-explicit-any
    onError: (error: any) => {
      const errorMessage =
        error.response?.data?.message || "Failed to create FAQ";
      toast.error(errorMessage);
    },
  });
}

export function useUpdateFAQ() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: ({ id, ...faqData }: FAQFormData & { id: string }) =>
      axios.patch(`/api/faqs/${id}`, faqData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["faqs"] });
      toast.success("FAQ updated successfully!");
    },
    //eslint-disable-next-line  @typescript-eslint/no-explicit-any
    onError: (error: any) => {
      const errorMessage =
        error.response?.data?.message || "Failed to create FAQ";
      toast.error(errorMessage);
    },
  });
}

export function useDeleteFAQ() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (id: string) => axios.delete(`/api/faqs/${id}`),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["faqs"] });
      toast.success("FAQ deleted successfully!");
    },
    //eslint-disable-next-line  @typescript-eslint/no-explicit-any
    onError: (error: any) => {
      const errorMessage =
        error.response?.data?.message || "Failed to create FAQ";
      toast.error(errorMessage);
    },
  });
}
