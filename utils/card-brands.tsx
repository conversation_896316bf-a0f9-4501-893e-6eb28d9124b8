import { cardBrands } from "@/components/cards/payment-cards";
import Image from "next/image";

export function CardBrandIcon({
  brand,
  className,
}: {
  brand: string;
  className?: string;
}) {
  const iconSrc = cardBrands[brand.toLowerCase()] || "/cards/credit-card.svg";

  return (
    <Image
      src={iconSrc}
      alt={brand}
      className={className}
      width={40}
      height={24}
      onError={(e) => {
        (e.target as HTMLImageElement).src = "/icons/credit-card.svg";
      }}
    />
  );
}
