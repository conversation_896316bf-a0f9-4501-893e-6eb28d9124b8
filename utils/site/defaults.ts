import { Metadata } from "next";
import defaults from "@/config/metadata-defaults.json";
import { SiteMetadata } from "@/types/types";

const openGraphTypes = [
  "website",
  "article",
  "book",
  "profile",
  "music.song",
  "music.album",
  "music.playlist",
  "music.radio_station",
  "video.movie",
  "video.episode",
  "video.tv_show",
  "video.other",
] as const;

type OpenGraphType = (typeof openGraphTypes)[number];

const isValidOgType = (value: any): value is OpenGraphType =>
  openGraphTypes.includes(value);

// Type guard for metadata defaults
function isSiteMetadata(data: any): data is SiteMetadata {
  return (
    data?.base?.title !== undefined &&
    data?.urls?.production !== undefined &&
    data?.social?.openGraph !== undefined
  );
}

// Validate the imported defaults
if (!isSiteMetadata(defaults)) {
  throw new Error("Invalid metadata defaults configuration");
}

// Main metadata generation function
export const getDefaultMetadata = (): Metadata => {
  const { base, urls, social, icons, manifest, category } =
    defaults as unknown as SiteMetadata;

  return {
    title: base.title,
    description: base.description,
    keywords: base.keywords,
    authors: [{ name: base.author }],
    generator: "Next.js",
    referrer: "origin-when-cross-origin",
    creator: base.author,
    publisher: base.author,
    formatDetection: {
      email: false,
      address: false,
      telephone: false,
    },
    metadataBase: new URL(base.metadataBase || urls.production),
    alternates: {
      canonical: urls.alternates?.canonical || "/",
      languages: urls.alternates?.languages || { "en-US": "/en-US" },
    },
    openGraph: {
      title: social.openGraph.title,
      description: social.openGraph.description,
      url: social.openGraph.url,
      siteName: social.openGraph.siteName,
      images: social.openGraph.images.map((img) => ({
        url: img.url,
        width: img.width,
        height: img.height,
        alt: img.alt,
      })),
      locale: social.openGraph.locale,
      type: isValidOgType(social.openGraph.type)
        ? social.openGraph.type
        : "website",
    },
    twitter: {
      card: social.twitter.card as
        | "summary"
        | "summary_large_image"
        | "app"
        | "player",
      title: social.twitter.title || base.title,
      description: social.twitter.description || base.description,
      creator: social.twitter.handle,
      images: social.twitter.images,
    },
    robots: base.robots || {
      index: true,
      follow: true,
    },
    icons: {
      icon: icons.icon.map((icon) =>
        typeof icon === "string"
          ? { url: icon }
          : { url: icon.url, sizes: icon.sizes, type: icon.type }
      ),
      apple: icons.apple ? [{ url: icons.apple }] : undefined,
      other: icons.other.map((other) => ({
        rel: other.rel,
        url: other.url,
        ...(other.color && { color: other.color }),
      })),
    },
    manifest: manifest,
    category: category || "technology",
  };
};

// URL exports with type safety
export const PRODUCTION_URL: string = defaults.urls.production;
export const DEVELOPMENT_URL: string = defaults.urls.development;
