export const generateVerificationEmailTemplate = ({
  name,
  verifyUrl,
  expiresInMinutes = 30,
}: {
  name: string;
  verifyUrl: string;
  expiresInMinutes?: number;
}) => {
  return `
      <!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>✨ Verify Your Email - Chatzuri AI</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary: #10b981;
            --primary-dark: #059669;
            --gradient: linear-gradient(135deg, #10b981 0%, #0ea5e9 100%);
            --glow: 0 0 20px rgba(16, 185, 129, 0.3);
        }
        
        body {
            font-family: 'Poppins', Arial, sans-serif;
            background-color: #f8fafc;
            margin: 0;
            padding: 0;
            color: #1e293b;
        }
        
        .container {
            max-width: 600px;
            margin: 2rem auto;
            background: white;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(0, 0, 0, 0.05);
        }
        
        .header {
            background: var(--gradient);
            padding: 40px 0;
            text-align: center;
            position: relative;
        }
        
        .header::after {
            content: "";
            position: absolute;
            bottom: -50px;
            left: 0;
            right: 0;
            height: 50px;
            background: url('data:image/svg+xml;utf8,<svg viewBox="0 0 1200 120" xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="none"><path d="M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z" fill="white" opacity=".25"/><path d="M0,0V15.81C13,36.92,27.64,56.86,47.69,72.05,99.41,111.27,165,111,224.58,91.58c31.15-10.15,60.09-26.07,89.67-39.8,40.92-19,84.73-46,130.83-49.67,36.26-2.85,70.9,9.42,98.6,31.56,31.77,25.39,62.32,62,103.63,73,40.44,10.79,81.35-6.69,119.13-24.28s75.16-39,116.92-43.05c59.73-5.85,113.28,22.88,168.9,38.84,30.2,8.66,59,6.17,87.09-7.5,22.43-10.89,48-26.93,60.65-49.24V0Z" fill="white" opacity=".5"/><path d="M0,0V5.63C149.93,59,314.09,71.32,475.83,42.57c43-7.64,84.23-20.12,127.61-26.46,59-8.63,112.48,12.24,165.56,35.4C827.93,77.22,886,95.24,951.2,90c86.53-7,172.46-45.71,248.8-84.81V0Z" fill="white"/></svg>');
            background-size: cover;
        }
        
        .logo {
            height: 40px;
            filter: drop-shadow(0 2px 5px rgba(0,0,0,0.1));
        }
        
        .content {
            padding: 40px;
            text-align: center;
        }
        
        h1 {
            color: #0f172a;
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 20px;
            background: var(--gradient);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            display: inline-block;
        }
        
        p {
            line-height: 1.6;
            margin-bottom: 25px;
            color: #475569;
        }
        
        .btn {
            display: inline-block;
            background: var(--gradient);
            color: white !important;
            text-decoration: none;
            padding: 16px 32px;
            border-radius: 50px;
            font-weight: 600;
            margin: 25px 0;
            box-shadow: var(--glow);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(16, 185, 129, 0.4);
        }
        
        .btn::after {
            content: "";
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(
                to bottom right,
                rgba(255, 255, 255, 0.3),
                rgba(255, 255, 255, 0)
            );
            transform: rotate(30deg);
            transition: all 0.3s ease;
        }
        
        .btn:hover::after {
            left: 100%;
        }
        
        .expiry {
            background: #f1f5f9;
            display: inline-block;
            padding: 8px 16px;
            border-radius: 50px;
            font-size: 14px;
            color: #64748b;
            margin: 15px 0;
        }
        
        .footer {
            text-align: center;
            padding: 30px;
            color: #64748b;
            font-size: 14px;
            border-top: 1px solid #f1f5f9;
        }
        
        .social-icons {
            margin: 25px 0;
        }
        
        .social-icon {
            display: inline-block;
            margin: 0 10px;
            transition: transform 0.3s ease;
        }
        
        .social-icon:hover {
            transform: translateY(-3px);
        }
        
        .ai-badge {
            background: #f0fdf4;
            color: #065f46;
            padding: 8px 16px;
            border-radius: 50px;
            font-size: 12px;
            font-weight: 600;
            display: inline-block;
            margin-bottom: 20px;
            border: 1px solid #d1fae5;
        }
        
        @keyframes float {
            0% { transform: translateY(0px); }
            50% { transform: translateY(-5px); }
            100% { transform: translateY(0px); }
        }
        
        .floating {
            animation: float 3s ease-in-out infinite;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <img src="https://i.postimg.cc/ZYMrvF0R/chatzuri-logo-trans-rect.png" alt="Chatzuri AI" class="logo floating">
        </div>
        
        <div class="content">
            <span class="ai-badge">AI-Powered Chat Solutions</span>
            <h1>Verify Your Email</h1>
            
            <p>Hello ${name || "there"},</p>
            
            <p>Welcome to <strong>Chatzuri AI</strong>! We're thrilled to have you on board. To complete your registration and unlock the full power of AI chatbots for your business, please verify your email address.</p>
            
            <div class="expiry">Link expires in ${expiresInMinutes} minutes</div>
            <br/>
            <a href="${verifyUrl}" class="btn">Verify Email Address</a>
            
            <p>If you didn't create this account, you can safely ignore this email or let us know.</p>
            
            <div class="social-icons">
                <a href="https://twitter.com/chatzuri" class="social-icon">
                    <img src="https://i.postimg.cc/WDMxkrhV/twitter.png" alt="Twitter" width="24">
                </a>
                <a href="https://linkedin.com/company/chatzuri" class="social-icon">
                    <img src="https://i.postimg.cc/hJFHvM4M/linkedin.png" alt="LinkedIn" width="24">
                </a>
                <a href="https://facebook.com/chatzuri" class="social-icon">
                    <img src="https://i.postimg.cc/BjPrJR3X/facebook.png" alt="Facebook" width="24">
                </a>
            </div>
        </div>
        
        <div class="footer">
            <p>© ${new Date().getFullYear()} Chatzuri AI. All rights reserved.</p>
            <p>
                <a href="https://chatzuri.com/privacy" style="color: #64748b; text-decoration: underline;">Privacy Policy</a> | 
                <a href="https://chatzuri.com/terms" style="color: #64748b; text-decoration: underline;">Terms of Service</a>
            </p>
            <p>Chatzuri Support Inc., AI chatbos & Agents Platform</p>
        </div>
    </div>
</body>
</html>`;
};
