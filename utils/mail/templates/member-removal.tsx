import { Team } from "@/types/types";

export const generateRemovalEmail = (team: Team,ownerEmail: string) => `
  <!DOCTYPE html>
  <html>
    <head>
      <meta charset="UTF-8" />
      <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      <title>Chatzuri Team Update</title>
      <style>
        body {
          font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
          line-height: 1.6;
          color: #334155;
          max-width: 600px;
          margin: 0 auto;
          padding: 20px;
          background-color: #f8fafc;
        }
        .header {
          text-align: center;
          padding: 20px 0;
        }
        .logo {
          height: 50px;
          margin-bottom: 20px;
        }
        .content {
          background: linear-gradient(135deg, #ffffff 0%, #fef2f2 100%);
          padding: 40px;
          border-radius: 12px;
          margin: 20px 0;
          box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.05), 0 2px 4px -1px rgba(0, 0, 0, 0.03);
          border: 1px solid rgba(254, 226, 226, 0.5);
        }
        h2 {
          color: #991b1b;
          font-weight: 700;
          margin-top: 0;
          font-size: 24px;
        }
        .button {
          display: inline-block;
          padding: 14px 28px;
          background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
          color: white !important;
          text-decoration: none;
          border-radius: 8px;
          font-weight: 600;
          margin: 25px 0;
          box-shadow: 0 4px 6px -1px rgba(239, 68, 68, 0.2), 0 2px 4px -1px rgba(239, 68, 68, 0.1);
          transition: all 0.3s ease;
          border: none;
          font-size: 16px;
        }
        .button:hover {
          transform: translateY(-2px);
          box-shadow: 0 10px 15px -3px rgba(239, 68, 68, 0.3), 0 4px 6px -2px rgba(239, 68, 68, 0.15);
        }
        .footer {
          text-align: center;
          font-size: 13px;
          color: #64748b;
          margin-top: 30px;
          line-height: 1.5;
        }
        .highlight {
          font-weight: 600;
          color: #991b1b;
        }
        .divider {
          height: 1px;
          background: linear-gradient(90deg, rgba(254, 226, 226, 0) 0%, rgba(254, 226, 226, 1) 50%, rgba(254, 226, 226, 0) 100%);
          margin: 25px 0;
        }
        .team-card {
          background: rgba(254, 226, 226, 0.3);
          border-radius: 8px;
          padding: 15px;
          margin: 20px 0;
          display: flex;
          align-items: center;
        }
        .team-icon {
          width: 40px;
          height: 40px;
          background: linear-gradient(135deg, #f87171 0%, #ef4444 100%);
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 15px;
          color: white;
          font-weight: bold;
        }
        .notice {
          background-color: #fef2f2;
          border-left: 4px solid #ef4444;
          padding: 12px 16px;
          margin: 20px 0;
          border-radius: 0 4px 4px 0;
        }
      </style>
    </head>
    <body>
      <div class="header">
        <img
          src="https://i.postimg.cc/ZYMrvF0R/chatzuri-logo-trans-rect.png"
          alt="Chatzuri Logo"
          class="logo"
        />
      </div>

      <div class="content">
        <h2>You've Been Removed From <span class="highlight">${team.name}</span></h2>
        <p>
          We're writing to inform you that you're no longer a member of the team
          <strong>${team.name}</strong> on Chatzuri. This change was made by the team administrator.
        </p>

        <div class="team-card">
          <div class="team-icon">${team.name.charAt(0).toUpperCase()}</div>
          <div>
            <div style="font-weight: 600; color: #991b1b;">${team.name}</div>
            <div style="font-size: 13px; color: #64748b;">Team on Chatzuri</div>
          </div>
        </div>

        <div class="notice">
          <p><strong>What this means:</strong></p>
          <ul style="padding-left: 20px; margin: 8px 0;">
            <li>You can no longer access this team's resources</li>
            <li>Your team-specific data has been archived</li>
            <li>You won't receive future team notifications</li>
          </ul>
        </div>

        <div class="divider"></div>

        <p>
          If this was a mistake or you'd like more information, please reply to this email
          or contact the team administrator directly at
          <a href="mailto:${ownerEmail}">${ownerEmail}</a>.
        </p>

        <div style="text-align: center; margin-top: 30px;">
          <a href="${process.env.NEXT_PUBLIC_APP_URL}" class="button">Return to Chatzuri</a>
        </div>
      </div>

      <div class="footer">
        <p>© ${new Date().getFullYear()} Chatzuri. All rights reserved.</p>
        <p>
          This is an automated notification. Please do not reply directly to this email.
        </p>
      </div>
    </body>
  </html>
`;