export function generatePasswordResetTemplate({
  name,
  resetUrl,
}: {
  name: string;
  resetUrl: string;
}) {
  return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Your Password - Chatzuri</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        
        body {
            font-family: 'Inter', Arial, sans-serif;
            background-color: #f8fafc;
            margin: 0;
            padding: 0;
            color: #334155;
        }
        
        .container {
            max-width: 600px;
            margin: 0 auto;
        }
        
        .header {
            background: linear-gradient(135deg, #10b981 0%, #0ea5e9 100%);
            padding: 40px 0;
            text-align: center;
            border-radius: 12px 12px 0 0;
            position: relative;
        }
        
        .header::after {
            content: "";
            position: absolute;
            bottom: -10px;
            left: 0;
            right: 0;
            height: 20px;
            background: url('data:image/svg+xml;utf8,<svg viewBox="0 0 1200 120" xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="none"><path d="M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z" fill="%23ffffff" opacity=".25"/><path d="M0,0V15.81C13,36.92,27.64,56.86,47.69,72.05,99.41,111.27,165,111,224.58,91.58c31.15-10.15,60.09-26.07,89.67-39.8,40.92-19,84.73-46,130.83-49.67,36.26-2.85,70.9,9.42,98.6,31.56,31.77,25.39,62.32,62,103.63,73,40.44,10.79,81.35-6.69,119.13-24.28s75.16-39,116.92-43.05c59.73-5.85,113.28,22.88,168.9,38.84,30.2,8.66,59,6.17,87.09-7.5,22.43-10.89,48-26.93,60.65-49.24V0Z" fill="%23ffffff" opacity=".5"/><path d="M0,0V5.63C149.93,59,314.09,71.32,475.83,42.57c43-7.64,84.23-20.12,127.61-26.46,59-8.63,112.48,12.24,165.56,35.4C827.93,77.22,886,95.24,951.2,90c86.53-7,172.46-45.71,248.8-84.81V0Z" fill="%23ffffff"/></svg>');
            background-size: cover;
        }
        
        .logo {
            height: 40px;
            filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1));
        }
        
        .content {
            background-color: #ffffff;
            padding: 40px;
            border-radius: 0 0 12px 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
        }
        
        h1 {
            color: #0f172a;
            font-size: 24px;
            font-weight: 700;
            margin-top: 0;
            margin-bottom: 20px;
            background: linear-gradient(135deg, #10b981 0%, #0ea5e9 100%);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        p {
            line-height: 1.6;
            margin-bottom: 20px;
            color: #475569;
        }
        
        .btn {
            display: inline-block;
            background: linear-gradient(135deg, #10b981 0%, #0ea5e9 100%);
            color: white !important;
            text-decoration: none;
            padding: 16px 32px;
            border-radius: 50px;
            font-weight: 600;
            margin: 25px 0;
            box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(16, 185, 129, 0.4);
        }
        
        .expiry-notice {
            background-color: #f1f5f9;
            padding: 12px;
            border-radius: 6px;
            text-align: center;
            margin: 20px 0;
            font-size: 14px;
            color: #64748b;
        }
        
        .footer {
            text-align: center;
            padding: 25px;
            color: #64748b;
            font-size: 14px;
            border-top: 1px solid #f1f5f9;
        }
        
        .security-note {
            font-size: 13px;
            color: #94a3b8;
            margin-top: 30px;
            padding-top: 15px;
            border-top: 1px solid #f1f5f9;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <img src="https://i.postimg.cc/ZYMrvF0R/chatzuri-logo-trans-rect.png" alt="Chatzuri Logo" class="logo">
        </div>
        
        <div class="content">
            <h1>Password Reset Request</h1>
            
            <p>Hello ${name},</p>
            
            <p>We received a request to reset your Chatzuri account password. Click the button below to create a new secure password:</p>
            
            <div style="text-align: center;">
                <a href="${resetUrl}" class="btn">Reset Password</a>
            </div>
            <br/>
            <div class="expiry-notice">
                ⏳ This link will expire in <strong>30 minutes</strong>
            </div>
            
            <p>If you didn't request this password reset, please ignore this email or contact our support team if you have any concerns.</p>
            
            <div class="security-note">
                <strong>Security Tip:</strong> Never share your password with anyone. Chatzuri will never ask for your password via email.
            </div>
        </div>
        
        <div class="footer">
            <p>© ${new Date().getFullYear()} Chatzuri. All rights reserved.</p>
            <p>Chatzuri Support Inc., AI chatbos & Agents Platform</p>
        </div>
    </div>
</body>
</html>`;
}
