export const generateInviteEmail = (teamName: string, inviteLink: string) => `
<!DOCTYPE html>
<html>
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Chatzuri Team Invitation</title>
    <style>
      body {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
        line-height: 1.6;
        color: #334155;
        max-width: 600px;
        margin: 0 auto;
        padding: 20px;
        background-color: #f8fafc;
      }
      .header {
        text-align: center;
        padding: 20px 0;
      }
      .logo {
        height: 50px;
        margin-bottom: 20px;
      }
      .content {
        background: linear-gradient(135deg, #ffffff 0%, #f0fdf4 100%);
        padding: 40px;
        border-radius: 12px;
        margin: 20px 0;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.05), 0 2px 4px -1px rgba(0, 0, 0, 0.03);
        border: 1px solid rgba(209, 250, 229, 0.5);
      }
      h2 {
        color: #064e3b;
        font-weight: 700;
        margin-top: 0;
        font-size: 24px;
      }
      .button {
        display: inline-block;
        padding: 14px 28px;
        background: linear-gradient(135deg, #059669 0%, #10b981 100%);
        color: white !important;
        text-decoration: none;
        border-radius: 8px;
        font-weight: 600;
        margin: 25px 0;
        box-shadow: 0 4px 6px -1px rgba(5, 150, 105, 0.2), 0 2px 4px -1px rgba(5, 150, 105, 0.1);
        transition: all 0.3s ease;
        border: none;
        font-size: 16px;
      }
      .button:hover, .button:active, .button:focus {
        color: white !important;
        transform: translateY(-2px);
        box-shadow: 0 10px 15px -3px rgba(5, 150, 105, 0.3), 0 4px 6px -2px rgba(5, 150, 105, 0.15);
      }
      .footer {
        text-align: center;
        font-size: 13px;
        color: #64748b;
        margin-top: 30px;
        line-height: 1.5;
      }
      .highlight {
        font-weight: 600;
        color: #065f46;
      }
      .divider {
        height: 1px;
        background: linear-gradient(90deg, rgba(209, 250, 229, 0) 0%, rgba(209, 250, 229, 1) 50%, rgba(209, 250, 229, 0) 100%);
        margin: 25px 0;
      }
      .team-card {
        background: rgba(209, 250, 229, 0.3);
        border-radius: 8px;
        padding: 15px;
        margin: 20px 0;
        display: flex;
        align-items: center;
      }
     .team-icon {
        width: 40px;
        height: 40px;
        background: linear-gradient(135deg, #34d399 0%, #10b981 100%);
        border-radius: 8px;

        display: flex;
        align-items: center;  
        justify-content: center; 

        margin-right: 15px;
        color: #fff;
        font-weight: bold;
        font-size: 18px;
        line-height: 1; 
      }
    </style>
  </head>
  <body>
    <div class="header">
      <img
        src="https://i.postimg.cc/ZYMrvF0R/chatzuri-logo-trans-rect.png"
        alt="Chatzuri Logo"
        class="logo"
      />
    </div>

    <div class="content">
      <h2>You've Been Invited to Join <span class="highlight">${teamName.charAt(0).toUpperCase() + teamName.slice(1)}</span></h2>
      <p>
        A team member has invited you to collaborate on Chatzuri. Accept this
        invitation to start working together on exciting projects.
      </p>

      <div class="team-card">
        <div class="team-icon">${teamName
          .charAt(0)
          .toUpperCase()}</div>
        <div>
          <div style="font-weight: 600; color: #064e3b;">${teamName.charAt(0).toUpperCase() + teamName.slice(1)}</div>
          <div style="font-size: 13px; color: #64748b;">Team on Chatzuri</div>
        </div>
      </div>

      <div class="divider"></div>

      <div style="text-align: center;">
        <a href="${inviteLink}" class="button" style="color: white !important;">Join Team Now</a>
      </div>

      <p style="font-size: 14px; color: #64748b; text-align: center;">
        This invitation will expire in 7 days. If you didn't request this
        invitation, you can safely ignore this email.
      </p>
    </div>

    <div class="footer">
      <p>© ${new Date().getFullYear()} Chatzuri. All rights reserved.</p>
      <p>
        If you're having trouble with the button above, copy and paste this URL
        into your browser:
      </p>
      <p style="word-break: break-all;"><small>${inviteLink}</small></p>
    </div>
  </body>
</html>
`;
