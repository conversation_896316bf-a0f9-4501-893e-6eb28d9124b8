import { SendEmailTypes } from "@/types/types";
import nodemailer from "nodemailer";
import SMTPTransport from "nodemailer/lib/smtp-transport";
import { generateContactEmailTemplate } from "./templates/contact";

const transport = nodemailer.createTransport({
  host: process.env.MAIL_HOST,
  port: process.env.MAIL_PORT,
  secure: true,
  auth: {
    user: process.env.MAIL_USER,
    pass: process.env.MAIL_PASSWORD,
  },
} as SMTPTransport.Options);

export const sendEmail = async (dto: SendEmailTypes) => {
  const { sender, recipients, subject, message, chatbotId, replyTo,html} = dto;
  const htmlContent = html || generateContactEmailTemplate({
    businessName: process.env.MAIL_NAME as string,
    clientName: sender.name,
    clientEmail: replyTo || "",
    subject,
    message,
    chatbotId,
  });
  return await transport.sendMail({
    from: `"${sender.name}" <${sender.address}>`,
    sender,
    to: recipients,
    subject,
    html: htmlContent,
    text: message,
    replyTo,
  });
};
