const EXCHANGE_RATE_API_KEY = process.env.NEXT_PUBLIC_EXCHANGE_RATE_API_KEY;
const CACHE_DURATION = 3600000; // 1 hour in milliseconds

let exchangeRates = {
  rates: { USD: 1, KES: 130 }, // Default fallback rates
  lastUpdated: 0,
};

export async function getExchangeRates() {
  if (Date.now() - exchangeRates.lastUpdated < CACHE_DURATION) {
    return exchangeRates.rates;
  }

  try {
    const response = await fetch(
      `https://v6.exchangerate-api.com/v6/${EXCHANGE_RATE_API_KEY}/latest/USD`
    );
    const data = await response.json();

    if (data.result === "success") {
      exchangeRates = {
        rates: data.conversion_rates,
        lastUpdated: Date.now(),
      };
      return exchangeRates.rates;
    }
  } catch (error) {
    console.error("Failed to fetch exchange rates:", error);
  }

  return exchangeRates.rates;
}

export interface ExchangeRates {
  [currency: string]: number;
}

export function convertCurrency(
  amount: number,
  fromCurrency: string,
  toCurrency: string,
  rates: ExchangeRates
): number {
  if (fromCurrency === toCurrency) return amount;
  if (!rates[fromCurrency] || !rates[toCurrency]) return amount;

  // Convert to USD first if necessary
  const amountInUSD =
    fromCurrency === "USD" ? amount : amount / rates[fromCurrency];

  // Convert from USD to target currency
  return toCurrency === "USD" ? amountInUSD : amountInUSD * rates[toCurrency];
}
