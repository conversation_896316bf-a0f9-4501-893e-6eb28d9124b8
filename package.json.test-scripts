# Add these scripts to your package.json

"scripts": {
  # Unit and Integration Tests
  "test": "jest",
  "test:watch": "jest --watch",
  "test:coverage": "jest --coverage",
  "test:ci": "jest --ci --coverage --watchAll=false",
  "test:unit": "jest --testPathPattern=__tests__",
  "test:integration": "jest --testPathPattern=integration",
  
  # E2E Tests
  "test:e2e": "playwright test",
  "test:e2e:ui": "playwright test --ui",
  "test:e2e:headed": "playwright test --headed",
  "test:e2e:debug": "playwright test --debug",
  "test:e2e:report": "playwright show-report",
  
  # Test Utilities
  "test:setup": "playwright install",
  "test:clean": "rm -rf coverage test-results playwright-report",
  "test:all": "npm run test:ci && npm run test:e2e",
  
  # Specific Test Suites
  "test:credentials": "jest --testPathPattern=credentials",
  "test:hooks": "jest --testPathPattern=hooks",
  "test:components": "jest --testPathPattern=components",
  "test:api": "jest --testPathPattern=api",
  "test:pages": "jest --testPathPattern=page",
  
  # Performance and Load Testing
  "test:lighthouse": "lighthouse http://localhost:3000 --output=json --output-path=./test-results/lighthouse.json",
  "test:bundle": "npm run build && bundlesize",
  
  # Test Data Management
  "test:seed": "node scripts/seed-test-data.js",
  "test:reset": "node scripts/reset-test-data.js"
}
