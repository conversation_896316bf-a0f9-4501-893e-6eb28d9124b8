#!/usr/bin/env node

// Test script for workflow execution system
const { workflowExecutionService } = require('../lib/workflow-execution/service');
const { executionEngine } = require('../lib/workflow-execution/execution-engine');
const { createVariableStore } = require('../lib/workflow-execution/variable-store');

async function testVariableStore() {
  console.log('🧪 Testing Variable Store...');
  
  const store = createVariableStore('test_execution_123');
  
  // Test basic operations
  await store.set('user_name', '<PERSON>', 'test_node');
  await store.set('user_age', 30, 'test_node');
  await store.merge({ city: 'New York', country: 'USA' }, 'test_node');
  
  const allVars = await store.getAll();
  console.log('✅ Variables stored:', allVars);
  
  // Test template resolution
  const template = 'Hello {{user_name}}, you are {{user_age}} years old and live in {{city}}, {{country}}!';
  const resolved = await store.resolveTemplate(template);
  console.log('✅ Template resolved:', resolved);
  
  // Test history
  const history = await store.getHistory('user_name');
  console.log('✅ Variable history:', history);
  
  // Cleanup
  await store.cleanup();
  console.log('✅ Variable store test completed\n');
}

async function testQueueManager() {
  console.log('🧪 Testing Queue Manager...');
  
  try {
    // Initialize the service
    await workflowExecutionService.initialize();
    
    // Get queue stats
    const stats = await workflowExecutionService.getQueueStats();
    console.log('✅ Queue stats:', stats);
    
    console.log('✅ Queue manager test completed\n');
  } catch (error) {
    console.error('❌ Queue manager test failed:', error.message);
  }
}

async function testMockWorkflow() {
  console.log('🧪 Testing Mock Workflow Execution...');
  
  // Create a simple mock workflow
  const mockWorkflow = {
    _id: 'test_workflow_123',
    name: 'Test Workflow',
    nodes: [
      {
        id: 'node_1',
        type: 'webhook',
        data: {
          label: 'Test Webhook',
          config: { method: 'POST', path: '/test' }
        }
      },
      {
        id: 'node_2',
        type: 'response',
        data: {
          label: 'Test Response',
          config: { message: 'Hello from test workflow!' }
        }
      }
    ],
    edges: [
      {
        id: 'edge_1',
        source: 'node_1',
        target: 'node_2'
      }
    ],
    userId: 'test_user',
    teamId: 'test_team'
  };
  
  try {
    // Test node execution
    const nodeResult = await executionEngine.executeNode(
      'test_workflow_123',
      'node_1',
      { test: true, message: 'Test input' },
      { test_var: 'test_value' },
      'test_user',
      'test_team'
    );
    
    console.log('✅ Node execution result:', {
      success: nodeResult.success,
      hasOutput: !!nodeResult.output,
      hasVariables: !!nodeResult.variables,
      hasLogs: !!nodeResult.logs
    });
    
    if (nodeResult.success) {
      console.log('✅ Node output:', nodeResult.output);
      console.log('✅ Node variables:', nodeResult.variables);
    } else {
      console.log('❌ Node error:', nodeResult.error);
    }
    
    console.log('✅ Mock workflow test completed\n');
  } catch (error) {
    console.error('❌ Mock workflow test failed:', error.message);
  }
}

async function runTests() {
  console.log('🚀 Starting Workflow Execution System Tests\n');
  
  try {
    await testVariableStore();
    await testQueueManager();
    await testMockWorkflow();
    
    console.log('🎉 All tests completed successfully!');
  } catch (error) {
    console.error('💥 Test suite failed:', error);
  } finally {
    // Cleanup
    try {
      await workflowExecutionService.shutdown();
    } catch (error) {
      console.error('Error during cleanup:', error.message);
    }
    process.exit(0);
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  runTests();
}

module.exports = {
  testVariableStore,
  testQueueManager,
  testMockWorkflow,
  runTests
};
