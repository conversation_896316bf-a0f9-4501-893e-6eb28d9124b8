/**
 * Migration script to move data from 'chatbots' collection to 'agents' collection
 * Run this script to migrate existing chatbot data to the new agent model
 */

const mongoose = require('mongoose');

// Connect to MongoDB
async function connectDB() {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/your-database');
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
}

async function migrateChatbotsToAgents() {
  try {
    const db = mongoose.connection.db;
    
    // Check if chatbots collection exists
    const collections = await db.listCollections().toArray();
    const chatbotsExists = collections.some(col => col.name === 'chatbots');
    const agentsExists = collections.some(col => col.name === 'agents');
    
    console.log('📊 Collection status:');
    console.log('  - chatbots collection exists:', chatbotsExists);
    console.log('  - agents collection exists:', agentsExists);
    
    if (!chatbotsExists) {
      console.log('ℹ️  No chatbots collection found. Nothing to migrate.');
      return;
    }
    
    // Get all documents from chatbots collection
    const chatbots = await db.collection('chatbots').find({}).toArray();
    console.log(`📋 Found ${chatbots.length} chatbots to migrate`);
    
    if (chatbots.length === 0) {
      console.log('ℹ️  No chatbots found. Nothing to migrate.');
      return;
    }
    
    // Check if agents already exist (to avoid duplicates)
    let existingAgents = [];
    if (agentsExists) {
      existingAgents = await db.collection('agents').find({}).toArray();
      console.log(`📋 Found ${existingAgents.length} existing agents`);
    }
    
    // Create a set of existing agent IDs to avoid duplicates
    const existingAgentIds = new Set(existingAgents.map(agent => agent._id.toString()));
    
    // Filter out chatbots that already exist as agents
    const chatbotsToMigrate = chatbots.filter(chatbot => 
      !existingAgentIds.has(chatbot._id.toString())
    );
    
    console.log(`🔄 Will migrate ${chatbotsToMigrate.length} unique chatbots`);
    
    if (chatbotsToMigrate.length === 0) {
      console.log('ℹ️  All chatbots have already been migrated. Nothing to do.');
      return;
    }
    
    // Insert chatbots into agents collection
    if (chatbotsToMigrate.length > 0) {
      await db.collection('agents').insertMany(chatbotsToMigrate);
      console.log(`✅ Successfully migrated ${chatbotsToMigrate.length} chatbots to agents collection`);
    }
    
    // Optional: You can uncomment the following lines to backup and remove the chatbots collection
    // WARNING: This will permanently delete the chatbots collection
    /*
    console.log('🗄️  Creating backup of chatbots collection...');
    await db.collection('chatbots_backup').insertMany(chatbots);
    console.log('✅ Backup created as chatbots_backup collection');
    
    console.log('🗑️  Dropping chatbots collection...');
    await db.collection('chatbots').drop();
    console.log('✅ Chatbots collection dropped');
    */
    
    console.log('🎉 Migration completed successfully!');
    console.log('💡 Note: The original chatbots collection is preserved for safety.');
    console.log('💡 You can manually drop it after verifying the migration worked correctly.');
    
  } catch (error) {
    console.error('❌ Migration error:', error);
    throw error;
  }
}

async function main() {
  try {
    await connectDB();
    await migrateChatbotsToAgents();
  } catch (error) {
    console.error('❌ Script failed:', error);
    process.exit(1);
  } finally {
    await mongoose.disconnect();
    console.log('👋 Disconnected from MongoDB');
  }
}

// Run the migration
if (require.main === module) {
  main();
}

module.exports = { migrateChatbotsToAgents };
