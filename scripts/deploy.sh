#!/bin/bash

# Production Deployment Script for Chatzuri Agents
# Usage: ./scripts/deploy.sh [environment] [version]

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
ENVIRONMENT="${1:-production}"
VERSION="${2:-latest}"
REGISTRY="ghcr.io"
IMAGE_NAME="chatzuri/chatzuri-agents"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Validation functions
validate_environment() {
    case "$ENVIRONMENT" in
        production|staging|development)
            log_info "Deploying to environment: $ENVIRONMENT"
            ;;
        *)
            log_error "Invalid environment: $ENVIRONMENT. Must be one of: production, staging, development"
            exit 1
            ;;
    esac
}

validate_prerequisites() {
    log_info "Validating prerequisites..."
    
    # Check required tools
    local required_tools=("docker" "kubectl" "helm")
    for tool in "${required_tools[@]}"; do
        if ! command -v "$tool" &> /dev/null; then
            log_error "$tool is required but not installed"
            exit 1
        fi
    done
    
    # Check environment variables
    local required_vars=("KUBE_CONFIG" "DOCKER_REGISTRY_TOKEN")
    for var in "${required_vars[@]}"; do
        if [[ -z "${!var:-}" ]]; then
            log_error "Environment variable $var is required"
            exit 1
        fi
    done
    
    log_success "Prerequisites validated"
}

# Build and push Docker image
build_and_push_image() {
    log_info "Building and pushing Docker image..."
    
    cd "$PROJECT_ROOT"
    
    # Build image
    docker build \
        --platform linux/amd64,linux/arm64 \
        --tag "$REGISTRY/$IMAGE_NAME:$VERSION" \
        --tag "$REGISTRY/$IMAGE_NAME:latest" \
        .
    
    # Login to registry
    echo "$DOCKER_REGISTRY_TOKEN" | docker login "$REGISTRY" --username "$DOCKER_REGISTRY_USER" --password-stdin
    
    # Push image
    docker push "$REGISTRY/$IMAGE_NAME:$VERSION"
    
    if [[ "$VERSION" != "latest" ]]; then
        docker push "$REGISTRY/$IMAGE_NAME:latest"
    fi
    
    log_success "Docker image built and pushed: $REGISTRY/$IMAGE_NAME:$VERSION"
}

# Deploy to Kubernetes
deploy_to_kubernetes() {
    log_info "Deploying to Kubernetes..."
    
    # Setup kubectl
    echo "$KUBE_CONFIG" | base64 -d > /tmp/kubeconfig
    export KUBECONFIG=/tmp/kubeconfig
    
    # Validate cluster connection
    if ! kubectl cluster-info &> /dev/null; then
        log_error "Cannot connect to Kubernetes cluster"
        exit 1
    fi
    
    # Set namespace based on environment
    local namespace="chatzuri-$ENVIRONMENT"
    
    # Create namespace if it doesn't exist
    kubectl create namespace "$namespace" --dry-run=client -o yaml | kubectl apply -f -
    
    # Update image in deployment
    sed -i.bak "s|your-registry/chatzuri-agents:latest|$REGISTRY/$IMAGE_NAME:$VERSION|g" "$PROJECT_ROOT/k8s/deployment.yaml"
    
    # Apply Kubernetes manifests
    kubectl apply -f "$PROJECT_ROOT/k8s/" -n "$namespace"
    
    # Wait for deployment to complete
    kubectl rollout status deployment/chatzuri-app -n "$namespace" --timeout=600s
    
    # Restore original deployment file
    mv "$PROJECT_ROOT/k8s/deployment.yaml.bak" "$PROJECT_ROOT/k8s/deployment.yaml"
    
    log_success "Deployment completed successfully"
}

# Run health checks
run_health_checks() {
    log_info "Running health checks..."
    
    local health_url
    case "$ENVIRONMENT" in
        production)
            health_url="https://app.chatzuri.com/api/health"
            ;;
        staging)
            health_url="https://staging.chatzuri.com/api/health"
            ;;
        development)
            health_url="https://dev.chatzuri.com/api/health"
            ;;
    esac
    
    # Wait for service to be ready
    local max_attempts=30
    local attempt=1
    
    while [[ $attempt -le $max_attempts ]]; do
        log_info "Health check attempt $attempt/$max_attempts..."
        
        if curl -f -s "$health_url" > /dev/null; then
            log_success "Health check passed"
            return 0
        fi
        
        sleep 10
        ((attempt++))
    done
    
    log_error "Health checks failed after $max_attempts attempts"
    return 1
}

# Run smoke tests
run_smoke_tests() {
    log_info "Running smoke tests..."
    
    cd "$PROJECT_ROOT"
    
    # Set test environment variables
    export PLAYWRIGHT_BASE_URL="https://${ENVIRONMENT}.chatzuri.com"
    export TEST_ENVIRONMENT="$ENVIRONMENT"
    
    # Run critical path tests
    npm run test:e2e:smoke
    
    log_success "Smoke tests passed"
}

# Database migrations
run_migrations() {
    log_info "Running database migrations..."
    
    # Run migration script
    if [[ -f "$PROJECT_ROOT/scripts/migrate.js" ]]; then
        node "$PROJECT_ROOT/scripts/migrate.js" --environment="$ENVIRONMENT"
        log_success "Database migrations completed"
    else
        log_warning "No migration script found, skipping migrations"
    fi
}

# Backup before deployment
create_backup() {
    log_info "Creating backup before deployment..."
    
    # Create database backup
    if [[ -f "$PROJECT_ROOT/scripts/backup.js" ]]; then
        node "$PROJECT_ROOT/scripts/backup.js" --environment="$ENVIRONMENT" --tag="pre-deploy-$(date +%Y%m%d-%H%M%S)"
        log_success "Backup created successfully"
    else
        log_warning "No backup script found, skipping backup"
    fi
}

# Rollback function
rollback_deployment() {
    log_warning "Rolling back deployment..."
    
    export KUBECONFIG=/tmp/kubeconfig
    local namespace="chatzuri-$ENVIRONMENT"
    
    # Rollback to previous version
    kubectl rollout undo deployment/chatzuri-app -n "$namespace"
    kubectl rollout status deployment/chatzuri-app -n "$namespace" --timeout=300s
    
    log_success "Rollback completed"
}

# Cleanup function
cleanup() {
    log_info "Cleaning up..."
    
    # Remove temporary files
    rm -f /tmp/kubeconfig
    
    # Logout from Docker registry
    docker logout "$REGISTRY" || true
    
    log_success "Cleanup completed"
}

# Main deployment function
main() {
    log_info "Starting deployment process..."
    log_info "Environment: $ENVIRONMENT"
    log_info "Version: $VERSION"
    
    # Trap cleanup on exit
    trap cleanup EXIT
    
    # Trap rollback on error (only for production)
    if [[ "$ENVIRONMENT" == "production" ]]; then
        trap 'log_error "Deployment failed, initiating rollback..."; rollback_deployment' ERR
    fi
    
    # Deployment steps
    validate_environment
    validate_prerequisites
    
    # Only create backup and run migrations for production
    if [[ "$ENVIRONMENT" == "production" ]]; then
        create_backup
        run_migrations
    fi
    
    build_and_push_image
    deploy_to_kubernetes
    
    # Wait a bit for services to stabilize
    sleep 30
    
    run_health_checks
    
    # Run smoke tests for production and staging
    if [[ "$ENVIRONMENT" == "production" || "$ENVIRONMENT" == "staging" ]]; then
        run_smoke_tests
    fi
    
    log_success "Deployment completed successfully!"
    log_info "Application is now running at: https://${ENVIRONMENT}.chatzuri.com"
}

# Script entry point
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
