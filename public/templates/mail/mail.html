<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>New Inquiry - ${businessName}</title>
    <style>
      @import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap");

      body {
        font-family: "Inter", Arial, sans-serif;
        background-color: #f8fafc;
        margin: 0;
        padding: 0;
        color: #334155;
      }

      .container {
        max-width: 600px;
        margin: 0 auto;
      }

      .header {
        background: linear-gradient(135deg, #10b981 0%, #0ea5e9 100%);
        padding: 40px 0;
        text-align: center;
        border-radius: 8px 8px 0 0;
        position: relative;
        overflow: hidden;
      }

      .header::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100" preserveAspectRatio="none"><path d="M0,0 L100,0 L100,100 L0,100 Z" fill="rgba(255,255,255,0.1)" /></svg>');
        background-size: 50px 50px;
        opacity: 0.3;
      }

      .logo {
        height: 40px;
        filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
        position: relative;
      }

      .content {
        background-color: #ffffff;
        padding: 40px;
        border-radius: 0 0 8px 8px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
      }

      h1 {
        color: #0f172a;
        font-size: 24px;
        font-weight: 700;
        margin-top: 0;
        margin-bottom: 25px;
        background: linear-gradient(135deg, #10b981 0%, #0ea5e9 100%);
        -webkit-background-clip: text;
        background-clip: text;
        -webkit-text-fill-color: transparent;
        display: inline-block;
      }

      .alert {
        background-color: #f0fdf4;
        border-left: 4px solid #10b981;
        padding: 16px;
        margin-bottom: 25px;
        border-radius: 0 6px 6px 0;
      }

      .details-card {
        background-color: #ffffff;
        border-radius: 8px;
        padding: 0;
        margin-bottom: 25px;
        border: 1px solid #e2e8f0;
        overflow: hidden;
      }

      .detail-row {
        display: flex;
        padding: 16px 20px;
        border-bottom: 1px solid #f1f5f9;
      }

      .detail-row:last-child {
        border-bottom: none;
      }

      .detail-label {
        font-weight: 600;
        color: #475569;
        min-width: 120px;
      }

      .detail-value {
        color: #1e293b;
        flex: 1;
      }

      .message-content {
        background-color: #f8fafc;
        padding: 20px;
        border-radius: 6px;
        margin-top: 10px;
        line-height: 1.6;
      }

      .footer {
        text-align: center;
        padding: 25px;
        color: #64748b;
        font-size: 14px;
        border-top: 1px solid #f1f5f9;
      }

      .badge {
        display: inline-block;
        background-color: #e0f2fe;
        color: #0369a1;
        padding: 4px 10px;
        border-radius: 50px;
        font-size: 12px;
        font-weight: 600;
        margin-left: 8px;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="header">
        <img
          src="https://i.postimg.cc/ZYMrvF0R/chatzuri-logo-trans-rect.png"
          alt="Chatzuri Logo"
          class="logo"
        />
      </div>

      <div class="content">
        <h1>New Customer Inquiry</h1>

        <div class="alert">
          You've received a new message via the ${businessName} contact form.
          Please respond within 24 hours.
        </div>

        <div class="details-card">
          <div class="detail-row">
            <div class="detail-label">Subject</div>
            <div class="detail-value">${subject}</div>
          </div>
          <div class="detail-row">
            <div class="detail-label">From</div>
            <div class="detail-value">${clientName}</div>
          </div>
          <div class="detail-row">
            <div class="detail-label">Email</div>
            <div class="detail-value">
              <a
                href="mailto:${clientEmail}"
                style="color: #10b981; text-decoration: none"
                >${clientEmail}</a
              >
            </div>
          </div>
          ${chatbotId ? `
          <div class="detail-row">
            <div class="detail-label">Chatbot</div>
            <div class="detail-value">
              ${chatbotId} <span class="badge">AI Assistant</span>
            </div>
          </div>
          ` : ""}
          <div
            class="detail-row"
            style="flex-direction: column; align-items: flex-start"
          >
            <div class="detail-label">Message</div>
            <div class="message-content">${message}</div>
          </div>
        </div>

        <p style="text-align: center; margin-top: 30px">
          <a
            href="mailto:${clientEmail}"
            style="
              background: linear-gradient(135deg, #10b981 0%, #0ea5e9 100%);
              color: white;
              padding: 12px 24px;
              border-radius: 6px;
              text-decoration: none;
              font-weight: 600;
              display: inline-block;
            "
            >Reply to Customer</a
          >
        </p>
      </div>

      <div class="footer">
        <p>
          © ${new Date().getFullYear()} ${businessName}. All rights reserved.
        </p>
        <p>Chatzuri Support Inc., AI chatbos & Agents Platform</p>
      </div>
    </div>
  </body>
</html>
