(function() {
  'use strict';

  // Get configuration from script attributes
  function getConfig() {
    const script = document.querySelector('script[data-workflow-id]');
    if (!script) {
      console.error('Chat Widget: data-workflow-id attribute is required');
      return null;
    }

    // Get base URL from script attribute or try to detect from script src
    let baseUrl = script.getAttribute('data-base-url');

    if (!baseUrl) {
      // Try to extract base URL from the script src
      const scriptSrc = script.src || script.getAttribute('src');
      if (scriptSrc) {
        try {
          const url = new URL(scriptSrc);
          baseUrl = `${url.protocol}//${url.host}`;
        } catch (e) {
          console.warn('Chat Widget: Could not determine base URL from script src');
        }
      }
    }

    if (!baseUrl) {
      console.error('Chat Widget: data-base-url attribute is required when script src cannot be determined');
      return null;
    }

    return {
      baseUrl: baseUrl,
      workflowId: script.getAttribute('data-workflow-id'),
      mode: script.getAttribute('data-mode') || 'bubble',
      position: script.getAttribute('data-position') || 'bottom-right',
      width: script.getAttribute('data-width') || '400px',
      height: script.getAttribute('data-height') || '600px',
      autoOpen: script.getAttribute('data-auto-open') === 'true',
      primaryColor: script.getAttribute('data-primary-color') || '#007bff',
      fontFamily: script.getAttribute('data-font-family') || 'system-ui, -apple-system, sans-serif'
    };
  }

  // Configuration defaults
  const WIDGET_CONFIG = {
    defaultPosition: 'bottom-right',
    defaultWidth: '400px',
    defaultHeight: '600px',
    zIndex: 9999
  };

  // Create chat widget
  function createChatWidget(config) {
    // Create container
    const container = document.createElement('div');
    container.id = 'chat-widget-container';
    container.style.cssText = `
      position: fixed;
      ${getPositionStyles(config.position)}
      width: ${config.width};
      height: ${config.height};
      z-index: ${WIDGET_CONFIG.zIndex};
      font-family: ${config.fontFamily};
      box-shadow: 0 4px 20px rgba(0,0,0,0.15);
      border-radius: 12px;
      overflow: hidden;
      transition: all 0.3s ease;
      ${config.mode === 'bubble' && !config.autoOpen ? 'display: none;' : ''}
    `;

    // Create iframe
    const iframe = document.createElement('iframe');
    iframe.src = `${config.baseUrl}/embed/chat/${config.workflowId}?mode=${config.mode}`;
    iframe.style.cssText = `
      width: 100%;
      height: 100%;
      border: none;
      border-radius: 12px;
    `;
    iframe.allow = 'microphone; camera; clipboard-write';

    container.appendChild(iframe);

    // Create chat bubble if in bubble mode
    if (config.mode === 'bubble') {
      const bubble = createChatBubble(config);
      document.body.appendChild(bubble);
    }

    document.body.appendChild(container);

    // Auto-open if configured
    if (config.autoOpen) {
      setTimeout(() => {
        showWidget();
      }, 1000);
    }

    return { container, iframe };
  }

  // Create chat bubble toggle
  function createChatBubble(config) {
    const bubble = document.createElement('div');
    bubble.id = 'chat-widget-bubble';
    bubble.style.cssText = `
      position: fixed;
      ${getPositionStyles(config.position, true)}
      width: 60px;
      height: 60px;
      background-color: ${config.primaryColor};
      border-radius: 50%;
      cursor: pointer;
      z-index: ${WIDGET_CONFIG.zIndex + 1};
      box-shadow: 0 4px 20px rgba(0,0,0,0.3);
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;
    `;

    // Add chat icon
    bubble.innerHTML = `
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
    `;

    // Add hover effect
    bubble.addEventListener('mouseenter', () => {
      bubble.style.transform = 'scale(1.1)';
    });

    bubble.addEventListener('mouseleave', () => {
      bubble.style.transform = 'scale(1)';
    });

    // Add click handler
    bubble.addEventListener('click', () => {
      toggleWidget();
    });

    return bubble;
  }

  // Get position styles based on configuration
  function getPositionStyles(position, isBubble = false) {
    const offset = isBubble ? '20px' : '20px';
    
    switch (position) {
      case 'bottom-left':
        return `bottom: ${offset}; left: ${offset};`;
      case 'bottom-right':
        return `bottom: ${offset}; right: ${offset};`;
      case 'top-left':
        return `top: ${offset}; left: ${offset};`;
      case 'top-right':
        return `top: ${offset}; right: ${offset};`;
      default:
        return `bottom: ${offset}; right: ${offset};`;
    }
  }

  // Show widget
  function showWidget() {
    const container = document.getElementById('chat-widget-container');
    const bubble = document.getElementById('chat-widget-bubble');
    
    if (container) {
      container.style.display = 'block';
      container.style.opacity = '0';
      container.style.transform = 'translateY(20px)';
      
      setTimeout(() => {
        container.style.opacity = '1';
        container.style.transform = 'translateY(0)';
      }, 10);
    }
    
    if (bubble) {
      bubble.style.display = 'none';
    }
  }

  // Hide widget
  function hideWidget() {
    const container = document.getElementById('chat-widget-container');
    const bubble = document.getElementById('chat-widget-bubble');
    
    if (container) {
      container.style.opacity = '0';
      container.style.transform = 'translateY(20px)';
      
      setTimeout(() => {
        container.style.display = 'none';
      }, 300);
    }
    
    if (bubble) {
      bubble.style.display = 'flex';
    }
  }

  // Toggle widget visibility
  function toggleWidget() {
    const container = document.getElementById('chat-widget-container');
    if (container && container.style.display !== 'none') {
      hideWidget();
    } else {
      showWidget();
    }
  }

  // Add close button to widget
  function addCloseButton(container, config) {
    if (config.mode !== 'bubble') return;

    const closeButton = document.createElement('div');
    closeButton.style.cssText = `
      position: absolute;
      top: 1px;
      right: 1px;
      width: 24px;
      height: 24px;
      background-color: rgba(0,0,0,0.5);
      border-radius: 50%;
      cursor: pointer;
      z-index: ${WIDGET_CONFIG.zIndex + 200};
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;
    `;

    closeButton.innerHTML = `
      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M18 6L6 18M6 6l12 12" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
    `;

    closeButton.addEventListener('click', hideWidget);
    closeButton.addEventListener('mouseenter', () => {
      closeButton.style.backgroundColor = 'rgba(0,0,0,0.7)';
    });
    closeButton.addEventListener('mouseleave', () => {
      closeButton.style.backgroundColor = 'rgba(0,0,0,0.5)';
    });

    container.appendChild(closeButton);
  }

  // Initialize widget
  function init() {
    // Wait for DOM to be ready
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', init);
      return;
    }

    const config = getConfig();
    if (!config) return;

    // Check if widget already exists
    if (document.getElementById('chat-widget-container')) {
      console.warn('Chat Widget: Widget already initialized');
      return;
    }

    try {
      const widget = createChatWidget(config);
      
      // Add close button for bubble mode
      if (config.mode === 'bubble') {
        addCloseButton(widget.container, config);
      }

      // Expose global functions
      window.ChatWidget = {
        show: showWidget,
        hide: hideWidget,
        toggle: toggleWidget,
        config: config
      };

      console.log('Chat Widget: Initialized successfully', config);

    } catch (error) {
      console.error('Chat Widget: Failed to initialize', error);
    }
  }

  // Start initialization
  init();

})();
