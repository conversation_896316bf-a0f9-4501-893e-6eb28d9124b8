// Test to verify the response structure
const response = {
  data: {
    data: {
      _id: "507f1f77bcf86cd799439011",
      name: "Test Agent",
      teamId: "507f1f77bcf86cd799439012",
      userId: "507f1f77bcf86cd799439013",
      createdAt: new Date(),
      updatedAt: new Date()
    }
  },
  status: 201,
  statusText: 'Created'
};

console.log("Full response:", response);
console.log("Response data:", response.data);
console.log("Agent data:", response.data?.data);
console.log("Agent ID:", response.data?.data?._id);

const agentId = response.data?.data?._id;
const teamSlug = "test-team";

if (agentId) {
  const navigationUrl = `/dashboard/team/${teamSlug}/agent/${agentId}/playground`;
  console.log("Navigation URL:", navigationUrl);
} else {
  console.error("Agent ID is undefined");
}
