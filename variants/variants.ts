export const itemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: {
      ease: "easeOut",
      duration: 0.5,
    },
  },
};

export const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2,
      delayChildren: 0.2,
    },
  },
};

export const cardVariants = {
  hover: {
    y: -10,
    scale: 1.02,
    boxShadow: "0 15px 30px -10px rgba(5, 150, 105, 0.3)",
    transition: {
      type: "spring",
      stiffness: 300,
    },
  },
};
export const mergedVariants = {
  ...itemVariants,
  ...cardVariants,
};

export const logoVariants = {
  hover: {
    scale: 1.05,
    transition: { type: "spring", stiffness: 300 },
  },
  tap: { scale: 0.95 },
};
