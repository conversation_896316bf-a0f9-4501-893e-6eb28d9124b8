import { z } from "zod";

export const affiliatesFormSchema = z.object({
  name: z.string().min(2, {
    message: "Name must be at least 2 characters.",
  }),
  email: z.string().email({
    message: "Please enter a valid email address.",
  }),
  message: z.string().min(10, {
    message: "Message must be at least 10 characters.",
  }),
});

export const contactFormSchema = z.object({
  title: z.string().min(1, "Subject is required"),
  chatbotId: z.string().optional(),
  email: z.string().email("Invalid email address"),
  message: z.string().min(1, "Message is required"),
});

export const LoginFormSchema = z.object({
  email: z.string().email("Please enter a valid email address"),
  password: z.string().min(1, "Password is required"),
  remember: z.boolean().optional(),
});

export const ForgotPasswordSchema = z.object({
  email: z.string().email("Please enter a valid email address"),
});

export const RegisterSchema = z
  .object({
    email: z.string().email("Please enter a valid email address"),
    mobile: z
      .string()
      .min(10, "Phone number must be at least 10 digits")
      .max(15, "Phone number can't be longer than 15 digits")
      .regex(/^[0-9+]+$/, "Phone number can only contain numbers and +"),
    password: z
      .string()
      .min(8, "Password must be at least 8 characters")
      .regex(/[a-z]/, "Password must contain at least one lowercase letter")
      .regex(/[A-Z]/, "Password must contain at least one uppercase letter")
      .regex(/[0-9]/, "Password must contain at least one number")
      .regex(
        /[^a-zA-Z0-9]/,
        "Password must contain at least one special character"
      ),
    confirmPassword: z.string(),
    terms: z.boolean().refine((val) => val === true, {
      message: "You must accept the terms and conditions",
    }),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords don't match",
    path: ["confirmPassword"],
  });

// Base schema for create/update
export const baseTeamFormSchema = z.object({
  name: z
    .string()
    .min(2, "Team name must be at least 2 characters")
    .max(50, "Team name must be less than 50 characters"),
  url: z
    .string()
    .min(2, "URL must be at least 2 characters")
    .max(30, "URL must be less than 30 characters")
    .regex(
      /^[a-z0-9-]+$/,
      "URL can only contain lowercase letters, numbers, and hyphens"
    ),
  description: z
    .string()
    .min(10, "Some description is required")
    .max(200, "Description must be less than 200 characters"),
  color: z
    .string()
    .regex(/^#[0-9A-F]{6}$/i, "Invalid color code")
    .optional(),
});

// Extended schema for edit mode
export const teamFormSchema = baseTeamFormSchema.extend({
  openAiKey: z
    .string()
    .regex(/^sk-[a-zA-Z0-9]+$/, "Invalid OpenAI key format")
    .nullable()
    .optional(),
  metaData: z
    .string()
    .refine((val) => {
      if (!val) return true;
      try {
        JSON.parse(val);
        return true;
      } catch {
        return false;
      }
    }, "Must be valid JSON")
    .nullable()
    .optional(),
  isFavorite: z.boolean().optional(),
});

export const openAiKeySchema = z.object({
  openAiOrg: z.string().min(1, "Organization ID is required"),
  openAiKey: z
    .string()
    .min(1, "API Key is required")
    .min(32, "API Key must be at least 32 characters long")
    .regex(
      /^sk-[a-zA-Z0-9]{32,}$|^sk_[a-zA-Z0-9]{32,}$/,
      "Must start with 'sk-' or 'sk_' followed by at least 32 alphanumeric characters"
    ),
});

export const billingFormSchema = z.object({
  organizationName: z.string().min(1, "Organization name is required"),
  countryOrRegion: z.string().min(1, "Country/Region is required"),
  addressLine1: z.string().min(1, "Address is required"),
  addressLine2: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  postalCode: z.string().optional(),
});

export const taxFormSchema = z.object({
  type: z.string().min(1, "Tax type is required"),
  id: z.string().min(1, "Tax ID is required"),
});

export const billingEmailSchema = z.object({
  billingEmail: z.string().email("Please enter a valid email address"),
});

export const createDeleteTeamSchema = (teamName: string) => {
  return z.object({
    confirmation: z.string().refine((val) => val === `delete ${teamName}`, {
      message: "Confirmation text must match exactly",
    }),
  });
};

export const cardFormSchema = z.object({
  name: z.string().min(1, "Name is required"),
});

export const faqSchema = z.object({
  question: z.string().min(10).max(200),
  answer: z.string().min(20).max(1000),
});

export const formSchema = z.object({
  chatbotName: z
    .string()
    .min(2, "Name must be at least 2 characters")
    .max(50, "Name must be less than 50 characters")
    .regex(/^[a-zA-Z0-9 ]+$/, "Only letters, numbers and spaces are allowed"),
});

export const leadsFormSchema = z
  .object({
    leadTitle: z.string().min(3, "Title must be at least 3 characters"),
    leadName: z.string().optional(),
    leadNameEnabled: z.boolean(),
    leadEmail: z.string().email("Invalid email format").optional(),
    leadEmailEnabled: z.boolean(),
    leadPhone: z.string().optional(),
    leadPhoneEnabled: z.boolean(),
  })
  .refine(
    (data) => {
      return (
        data.leadNameEnabled || data.leadEmailEnabled || data.leadPhoneEnabled
      );
    },
    {
      message: "At least one field (besides title) must be enabled",
      path: ["leadNameEnabled"],
    }
  );

export const notificationsFormSchema = z
  .object({
    leadsNotificationEmail: z.string().email("Invalid email format").optional(),
    leadsNotificationEnabled: z.boolean(),
    conversationsNotificationEmail: z
      .string()
      .email("Invalid email format")
      .optional(),
    conversationsNotificationEnabled: z.boolean(),
  })
  .refine(
    (data) => {
      if (data.leadsNotificationEnabled && !data.leadsNotificationEmail) {
        return false;
      }
      if (
        data.conversationsNotificationEnabled &&
        !data.conversationsNotificationEmail
      ) {
        return false;
      }
      return true;
    },
    {
      message: "Email is required when notification is enabled",
      path: ["leadsNotificationEmail"],
    }
  );

export const webhookFormSchema = z
  .object({
    webhookUrl: z.string().url("Invalid URL format").optional(),
    webhookEnabled: z.boolean(),
  })
  .refine(
    (data) => {
      if (data.webhookEnabled && !data.webhookUrl) {
        return false;
      }
      return true;
    },
    {
      message: "Webhook URL is required when enabled",
      path: ["webhookUrl"],
    }
  );

export const domainFormSchema = z
  .object({
    customDomain: z
      .string()
      .min(4, "Domain must be at least 4 characters")
      .optional(),
    customDomainEnabled: z.boolean(),
  })
  .refine(
    (data) => {
      if (data.customDomainEnabled && !data.customDomain) {
        return false;
      }
      return true;
    },
    {
      message: "Domain is required when enabled",
      path: ["customDomain"],
    }
  );

export const securityFormSchema = z.object({
  isPublic: z.boolean(),
  allowedDomains: z.string().default(""),
  ipLimit: z.number({ invalid_type_error: "Must be a number" }).min(0),
  ipLimitMessage: z.string().min(5, "Message must be at least 5 characters"),
});
