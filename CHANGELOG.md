# Changelog

## <small>0.1.35 (2025-06-29)</small>

## <small>0.1.34 (2025-06-29)</small>

* Adds error handling for FAQ update and delete actions ([f96661b](https://github.com/lawrencekm/chatzuri/commit/f96661b))
* Enhances caching mechanism for billing and tax information ([724ce48](https://github.com/lawrencekm/chatzuri/commit/724ce48))
* Refactor team dashboard to replace chatbots with agents ([7719a6c](https://github.com/lawrencekm/chatzuri/commit/7719a6c))
* Refactors agent dashboard components and improves UI interactions ([f83566b](https://github.com/lawrencekm/chatzuri/commit/f83566b))

## <small>0.1.33 (2025-06-05)</small>

* Enhances security and UI consistency in auth components ([d139002](https://github.com/kevykibbz/chatzuri/commit/d139002))
* Fixes indentation issues and updates user registration ([5a3ecaa](https://github.com/kevykibbz/chatzuri/commit/5a3ecaa))

## <small>0.1.32 (2025-06-05)</small>

* Enhances billing forms with error handling and loading states ([9373535](https://github.com/kevykibbz/chatzuri/commit/9373535))
* Updates form component to use useRouter for navigation ([914188e](https://github.com/kevykibbz/chatzuri/commit/914188e))
* Updates router redirection in LoginForm ([6da85df](https://github.com/kevykibbz/chatzuri/commit/6da85df))

## <small>0.1.31 (2025-05-31)</small>

* Fix:Indentation for readibility ([5f2afc7](https://github.com/kevykibbz/chatzuri/commit/5f2afc7))
* Update team-related components and API routes ([51c374a](https://github.com/kevykibbz/chatzuri/commit/51c374a))

## <small>0.1.30 (2025-05-21)</small>

* Adds "critters" package for improving performance and optimizing web page rendering ([96c4b4f](https://github.com/kevykibbz/chatzuri/commit/96c4b4f))
* Refactors remote image patterns and adds domain whitelist ([6df32ee](https://github.com/kevykibbz/chatzuri/commit/6df32ee)), closes [#123](https://github.com/kevykibbz/chatzuri/issues/123)
* Refactors team member profile images in user dashboard ([799f216](https://github.com/kevykibbz/chatzuri/commit/799f216))
* Update dashboard page to fetch teams dynamically and improve error handling ([752db37](https://github.com/kevykibbz/chatzuri/commit/752db37))
* Updates code for user authentication and profile UI ([2da7abe](https://github.com/kevykibbz/chatzuri/commit/2da7abe))
* Updates registration timeout and user profile image rendering ([624c876](https://github.com/kevykibbz/chatzuri/commit/624c876))

## <small>0.1.29 (2025-05-15)</small>

* Adds CodeBlock component to BlogPage ([3a82f45](https://github.com/kevykibbz/chatzuri/commit/3a82f45)), closes [#123](https://github.com/kevykibbz/chatzuri/issues/123)
* Adds container and item variants for header animation ([a7dc46f](https://github.com/kevykibbz/chatzuri/commit/a7dc46f))
* Adds image placeholder and blur effect to authentication pages ([fb0644f](https://github.com/kevykibbz/chatzuri/commit/fb0644f)), closes [#789](https://github.com/kevykibbz/chatzuri/issues/789)
* Refactors AuthHeader component and adds mobile menu functionality ([b5287b4](https://github.com/kevykibbz/chatzuri/commit/b5287b4))
* Updates font imports and metadata configuration ([358c004](https://github.com/kevykibbz/chatzuri/commit/358c004))
* Updates metadata for Chatzuri website ([93760a1](https://github.com/kevykibbz/chatzuri/commit/93760a1)), closes [#123](https://github.com/kevykibbz/chatzuri/issues/123)

## <small>0.1.28 (2025-05-10)</small>

* Refactors TeamsPage component and updates Lucide icons ([6be7c73](https://github.com/kevykibbz/chatzuri/commit/6be7c73))

## <small>0.1.27 (2025-05-09)</small>

* Refactors sidebar component for mobile view ([74ce4cd](https://github.com/kevykibbz/chatzuri/commit/74ce4cd))

## <small>0.1.26 (2025-05-04)</small>

* Refactors sidebar component for improved mobile view layout and styling.\n\nUpdates the sidebar comp ([b032494](https://github.com/kevykibbz/chatzuri/commit/b032494))

## <small>0.1.25 (2025-05-04)</small>

* Refactors sidebar component for improved mobile view layout and styling ([2f7e57e](https://github.com/kevykibbz/chatzuri/commit/2f7e57e))

## <small>0.1.24 (2025-05-03)</small>

* Fix:Mobile docs sidebar positioning ([5bed4c1](https://github.com/kevykibbz/chatzuri/commit/5bed4c1))
* Fix:Mobile docs sidebar positioning ([88ec65a](https://github.com/kevykibbz/chatzuri/commit/88ec65a))

## <small>0.1.23 (2025-05-03)</small>

* Adds dark mode toggle functionality to the header component ([42022ad](https://github.com/kevykibbz/chatzuri/commit/42022ad)), closes [#123](https://github.com/kevykibbz/chatzuri/issues/123)
* Refactors cookie consent UI for improved user experience ([dfe9932](https://github.com/kevykibbz/chatzuri/commit/dfe9932))
* type-safe useState for seting scrolled variable ([40d2630](https://github.com/kevykibbz/chatzuri/commit/40d2630))
* Updates CreateChatbotPage component ([78f4617](https://github.com/kevykibbz/chatzuri/commit/78f4617))

## <small>0.1.22 (2025-05-01)</small>

* Adds smooth scroll behavior and updates mobile menu button functionality ([eac0ee1](https://github.com/kevykibbz/chatzuri/commit/eac0ee1)), closes [#123](https://github.com/kevykibbz/chatzuri/issues/123)
* Removes unnecessary code for copying to clipboard in chatbot pages ([9e44b47](https://github.com/kevykibbz/chatzuri/commit/9e44b47))
* Summary: Adds CopyButton component for easy text copying ([988b460](https://github.com/kevykibbz/chatzuri/commit/988b460))

## <small>0.1.21 (2025-04-30)</small>

* Updates page and header components ([e3c6096](https://github.com/kevykibbz/chatzuri/commit/e3c6096))

## <small>0.1.20 (2025-04-30)</small>

* Refactors code and updates page names ([4fd3bf7](https://github.com/kevykibbz/chatzuri/commit/4fd3bf7))
* Updates background color and image in global CSS ([4e9f44b](https://github.com/kevykibbz/chatzuri/commit/4e9f44b)), closes [#789](https://github.com/kevykibbz/chatzuri/issues/789)

## <small>0.1.19 (2025-04-29)</small>

* Updates package dependencies and refines user experience ([3bfcce6](https://github.com/kevykibbz/chatzuri/commit/3bfcce6))

## <small>0.1.18 (2025-04-29)</small>

* fix:rectify tags alignment problem ([b09c571](https://github.com/kevykibbz/chatzuri/commit/b09c571))

## <small>0.1.17 (2025-04-29)</small>

* chore: release v0.1.10 ([c598a9e](https://github.com/kevykibbz/chatzuri/commit/c598a9e))
* chore: release v0.1.11 ([a2850bf](https://github.com/kevykibbz/chatzuri/commit/a2850bf))
* chore: release v0.1.12 ([39b7394](https://github.com/kevykibbz/chatzuri/commit/39b7394))
* chore: release v0.1.13 ([6725a60](https://github.com/kevykibbz/chatzuri/commit/6725a60))
* chore: release v0.1.14 ([a8f7f7c](https://github.com/kevykibbz/chatzuri/commit/a8f7f7c))
* chore: release v0.1.15 ([6ec6c1e](https://github.com/kevykibbz/chatzuri/commit/6ec6c1e))
* chore: release v0.1.16 ([1836615](https://github.com/kevykibbz/chatzuri/commit/1836615))
* chore: release v0.1.4 ([6ce20c9](https://github.com/kevykibbz/chatzuri/commit/6ce20c9))
* chore: release v0.1.5 ([6e197fc](https://github.com/kevykibbz/chatzuri/commit/6e197fc))
* chore: release v0.1.6 ([f6251fe](https://github.com/kevykibbz/chatzuri/commit/f6251fe))
* chore: release v0.1.7 ([73b9909](https://github.com/kevykibbz/chatzuri/commit/73b9909))
* chore: release v0.1.8 ([4547658](https://github.com/kevykibbz/chatzuri/commit/4547658))
* chore: release v0.1.9 ([a89bedf](https://github.com/kevykibbz/chatzuri/commit/a89bedf))
* Improves user experience by displaying changes instantly in the browser ([98ed2db](https://github.com/kevykibbz/chatzuri/commit/98ed2db))

## <small>0.1.16 (2025-04-29)</small>

* chore: release v0.1.10 ([c598a9e](https://github.com/kevykibbz/chatzuri/commit/c598a9e))
* chore: release v0.1.11 ([a2850bf](https://github.com/kevykibbz/chatzuri/commit/a2850bf))
* chore: release v0.1.12 ([39b7394](https://github.com/kevykibbz/chatzuri/commit/39b7394))
* chore: release v0.1.13 ([6725a60](https://github.com/kevykibbz/chatzuri/commit/6725a60))
* chore: release v0.1.14 ([a8f7f7c](https://github.com/kevykibbz/chatzuri/commit/a8f7f7c))
* chore: release v0.1.15 ([6ec6c1e](https://github.com/kevykibbz/chatzuri/commit/6ec6c1e))
* chore: release v0.1.4 ([6ce20c9](https://github.com/kevykibbz/chatzuri/commit/6ce20c9))
* chore: release v0.1.5 ([6e197fc](https://github.com/kevykibbz/chatzuri/commit/6e197fc))
* chore: release v0.1.6 ([f6251fe](https://github.com/kevykibbz/chatzuri/commit/f6251fe))
* chore: release v0.1.7 ([73b9909](https://github.com/kevykibbz/chatzuri/commit/73b9909))
* chore: release v0.1.8 ([4547658](https://github.com/kevykibbz/chatzuri/commit/4547658))
* chore: release v0.1.9 ([a89bedf](https://github.com/kevykibbz/chatzuri/commit/a89bedf))
* Improves user experience by displaying changes instantly in the browser ([98ed2db](https://github.com/kevykibbz/chatzuri/commit/98ed2db))

## <small>0.1.15 (2025-04-29)</small>

* chore: release v0.1.10 ([c598a9e](https://github.com/kevykibbz/chatzuri/commit/c598a9e))
* chore: release v0.1.11 ([a2850bf](https://github.com/kevykibbz/chatzuri/commit/a2850bf))
* chore: release v0.1.12 ([39b7394](https://github.com/kevykibbz/chatzuri/commit/39b7394))
* chore: release v0.1.13 ([6725a60](https://github.com/kevykibbz/chatzuri/commit/6725a60))
* chore: release v0.1.14 ([a8f7f7c](https://github.com/kevykibbz/chatzuri/commit/a8f7f7c))
* chore: release v0.1.4 ([6ce20c9](https://github.com/kevykibbz/chatzuri/commit/6ce20c9))
* chore: release v0.1.5 ([6e197fc](https://github.com/kevykibbz/chatzuri/commit/6e197fc))
* chore: release v0.1.6 ([f6251fe](https://github.com/kevykibbz/chatzuri/commit/f6251fe))
* chore: release v0.1.7 ([73b9909](https://github.com/kevykibbz/chatzuri/commit/73b9909))
* chore: release v0.1.8 ([4547658](https://github.com/kevykibbz/chatzuri/commit/4547658))
* chore: release v0.1.9 ([a89bedf](https://github.com/kevykibbz/chatzuri/commit/a89bedf))
* Improves user experience by displaying changes instantly in the browser ([98ed2db](https://github.com/kevykibbz/chatzuri/commit/98ed2db))

## <small>0.1.14 (2025-04-29)</small>

* chore: release v0.1.10 ([c598a9e](https://github.com/kevykibbz/chatzuri/commit/c598a9e))
* chore: release v0.1.11 ([a2850bf](https://github.com/kevykibbz/chatzuri/commit/a2850bf))
* chore: release v0.1.12 ([39b7394](https://github.com/kevykibbz/chatzuri/commit/39b7394))
* chore: release v0.1.13 ([6725a60](https://github.com/kevykibbz/chatzuri/commit/6725a60))
* chore: release v0.1.4 ([6ce20c9](https://github.com/kevykibbz/chatzuri/commit/6ce20c9))
* chore: release v0.1.5 ([6e197fc](https://github.com/kevykibbz/chatzuri/commit/6e197fc))
* chore: release v0.1.6 ([f6251fe](https://github.com/kevykibbz/chatzuri/commit/f6251fe))
* chore: release v0.1.7 ([73b9909](https://github.com/kevykibbz/chatzuri/commit/73b9909))
* chore: release v0.1.8 ([4547658](https://github.com/kevykibbz/chatzuri/commit/4547658))
* chore: release v0.1.9 ([a89bedf](https://github.com/kevykibbz/chatzuri/commit/a89bedf))
* Improves user experience by displaying changes instantly in the browser ([98ed2db](https://github.com/kevykibbz/chatzuri/commit/98ed2db))

## <small>0.1.13 (2025-04-29)</small>

* chore: release v0.1.10 ([c598a9e](https://github.com/kevykibbz/chatzuri/commit/c598a9e))
* chore: release v0.1.11 ([a2850bf](https://github.com/kevykibbz/chatzuri/commit/a2850bf))
* chore: release v0.1.12 ([39b7394](https://github.com/kevykibbz/chatzuri/commit/39b7394))
* chore: release v0.1.4 ([6ce20c9](https://github.com/kevykibbz/chatzuri/commit/6ce20c9))
* chore: release v0.1.5 ([6e197fc](https://github.com/kevykibbz/chatzuri/commit/6e197fc))
* chore: release v0.1.6 ([f6251fe](https://github.com/kevykibbz/chatzuri/commit/f6251fe))
* chore: release v0.1.7 ([73b9909](https://github.com/kevykibbz/chatzuri/commit/73b9909))
* chore: release v0.1.8 ([4547658](https://github.com/kevykibbz/chatzuri/commit/4547658))
* chore: release v0.1.9 ([a89bedf](https://github.com/kevykibbz/chatzuri/commit/a89bedf))
* Improves user experience by displaying changes instantly in the browser ([98ed2db](https://github.com/kevykibbz/chatzuri/commit/98ed2db))

## <small>0.1.12 (2025-04-29)</small>

* chore: release v0.1.10 ([c598a9e](https://github.com/kevykibbz/chatzuri/commit/c598a9e))
* chore: release v0.1.11 ([a2850bf](https://github.com/kevykibbz/chatzuri/commit/a2850bf))
* chore: release v0.1.4 ([6ce20c9](https://github.com/kevykibbz/chatzuri/commit/6ce20c9))
* chore: release v0.1.5 ([6e197fc](https://github.com/kevykibbz/chatzuri/commit/6e197fc))
* chore: release v0.1.6 ([f6251fe](https://github.com/kevykibbz/chatzuri/commit/f6251fe))
* chore: release v0.1.7 ([73b9909](https://github.com/kevykibbz/chatzuri/commit/73b9909))
* chore: release v0.1.8 ([4547658](https://github.com/kevykibbz/chatzuri/commit/4547658))
* chore: release v0.1.9 ([a89bedf](https://github.com/kevykibbz/chatzuri/commit/a89bedf))
* Improves user experience by displaying changes instantly in the browser ([98ed2db](https://github.com/kevykibbz/chatzuri/commit/98ed2db))

## <small>0.1.11 (2025-04-29)</small>

* chore: release v0.1.10 ([c598a9e](https://github.com/kevykibbz/chatzuri/commit/c598a9e))
* chore: release v0.1.4 ([6ce20c9](https://github.com/kevykibbz/chatzuri/commit/6ce20c9))
* chore: release v0.1.5 ([6e197fc](https://github.com/kevykibbz/chatzuri/commit/6e197fc))
* chore: release v0.1.6 ([f6251fe](https://github.com/kevykibbz/chatzuri/commit/f6251fe))
* chore: release v0.1.7 ([73b9909](https://github.com/kevykibbz/chatzuri/commit/73b9909))
* chore: release v0.1.8 ([4547658](https://github.com/kevykibbz/chatzuri/commit/4547658))
* chore: release v0.1.9 ([a89bedf](https://github.com/kevykibbz/chatzuri/commit/a89bedf))
* Improves user experience by displaying changes instantly in the browser ([98ed2db](https://github.com/kevykibbz/chatzuri/commit/98ed2db))

## <small>0.1.10 (2025-04-29)</small>

* chore: release v0.1.4 ([6ce20c9](https://github.com/kevykibbz/chatzuri/commit/6ce20c9))
* chore: release v0.1.5 ([6e197fc](https://github.com/kevykibbz/chatzuri/commit/6e197fc))
* chore: release v0.1.6 ([f6251fe](https://github.com/kevykibbz/chatzuri/commit/f6251fe))
* chore: release v0.1.7 ([73b9909](https://github.com/kevykibbz/chatzuri/commit/73b9909))
* chore: release v0.1.8 ([4547658](https://github.com/kevykibbz/chatzuri/commit/4547658))
* chore: release v0.1.9 ([a89bedf](https://github.com/kevykibbz/chatzuri/commit/a89bedf))
* Improves user experience by displaying changes instantly in the browser ([98ed2db](https://github.com/kevykibbz/chatzuri/commit/98ed2db))

## <small>0.1.9 (2025-04-29)</small>

* chore: release v0.1.4 ([6ce20c9](https://github.com/kevykibbz/chatzuri/commit/6ce20c9))
* chore: release v0.1.5 ([6e197fc](https://github.com/kevykibbz/chatzuri/commit/6e197fc))
* chore: release v0.1.6 ([f6251fe](https://github.com/kevykibbz/chatzuri/commit/f6251fe))
* chore: release v0.1.7 ([73b9909](https://github.com/kevykibbz/chatzuri/commit/73b9909))
* chore: release v0.1.8 ([4547658](https://github.com/kevykibbz/chatzuri/commit/4547658))
* Improves user experience by displaying changes instantly in the browser ([98ed2db](https://github.com/kevykibbz/chatzuri/commit/98ed2db))

## <small>0.1.8 (2025-04-29)</small>

* chore: release v0.1.4 ([6ce20c9](https://github.com/kevykibbz/chatzuri/commit/6ce20c9))
* chore: release v0.1.5 ([6e197fc](https://github.com/kevykibbz/chatzuri/commit/6e197fc))
* chore: release v0.1.6 ([f6251fe](https://github.com/kevykibbz/chatzuri/commit/f6251fe))
* chore: release v0.1.7 ([73b9909](https://github.com/kevykibbz/chatzuri/commit/73b9909))
* Improves user experience by displaying changes instantly in the browser ([98ed2db](https://github.com/kevykibbz/chatzuri/commit/98ed2db))

## <small>0.1.7 (2025-04-29)</small>

* chore: release v0.1.4 ([6ce20c9](https://github.com/kevykibbz/chatzuri/commit/6ce20c9))
* chore: release v0.1.5 ([6e197fc](https://github.com/kevykibbz/chatzuri/commit/6e197fc))
* chore: release v0.1.6 ([f6251fe](https://github.com/kevykibbz/chatzuri/commit/f6251fe))
* Improves user experience by displaying changes instantly in the browser ([98ed2db](https://github.com/kevykibbz/chatzuri/commit/98ed2db))

## <small>0.1.6 (2025-04-29)</small>

* chore: release v0.1.4 ([6ce20c9](https://github.com/kevykibbz/chatzuri/commit/6ce20c9))
* chore: release v0.1.5 ([6e197fc](https://github.com/kevykibbz/chatzuri/commit/6e197fc))
* Improves user experience by displaying changes instantly in the browser ([98ed2db](https://github.com/kevykibbz/chatzuri/commit/98ed2db))

## <small>0.1.5 (2025-04-29)</small>

* chore: release v0.1.4 ([6ce20c9](https://github.com/kevykibbz/chatzuri/commit/6ce20c9))
* Improves user experience by displaying changes instantly in the browser ([98ed2db](https://github.com/kevykibbz/chatzuri/commit/98ed2db))

## <small>0.1.4 (2025-04-29)</small>

* Improves user experience by displaying changes instantly in the browser ([98ed2db](https://github.com/kevykibbz/chatzuri/commit/98ed2db))

## <small>0.1.3 (2025-04-29)</small>

* fix:Reinstalled husky ([a1d8687](https://github.com/kevykibbz/chatzuri/commit/a1d8687))
* Updates package dependencies and adds HTML attribute for suppressing hydration warning ([961afde](https://github.com/kevykibbz/chatzuri/commit/961afde))

## <small>0.1.2 (2025-04-29)</small>

* Updates app layout metadata for Chatzuri - Custom ChatGPT for Your Business ([4292c63](https://github.com/kevykibbz/chatzuri/commit/4292c63))

## <small>0.1.1 (2025-04-29)</small>

* fix: added missing release script in package.json file ([0090066](https://github.com/kevykibbz/chatzuri/commit/0090066))
* First Commit ([229e493](https://github.com/kevykibbz/chatzuri/commit/229e493))
* Initial commit from Create Next App ([68da0d3](https://github.com/kevykibbz/chatzuri/commit/68da0d3))
