import { DefaultSession } from "next-auth";

declare module "next-auth" {
  interface Session {
    user: {
      id: string;
      name?: string | null;
      email?: string | null;
      image?: string | null;
      role: string;
      provider?: string;
      canLogin: boolean;
      lastLogin?: Date;
      lastLoginIp?: string;
      apiKey?: string;
      mobile?: string;
      balance?: number;
      isAdmin: boolean;
      isGlobalAdmin: boolean;
    } & DefaultSession["user"];
  }

  interface User {
    id: string;
    name?: string | null;
    email?: string | null;
    image?: string | null;
    role: string;
    provider?: string;
    canLogin: boolean;
    lastLogin?: Date;
    lastLoginIp?: string;
    apiKey?: string;
    mobile?: string;
    balance?: number;
    isAdmin: boolean;
    isGlobalAdmin: boolean;
  }
}

declare module "next-auth/jwt" {
  interface JWT {
    user?: {
      id: string;
      name?: string;
      email?: string;
      image?: string;
      role: string;
      provider?: string;
      canLogin: boolean;
      isAdmin: boolean;
      isGlobalAdmin: boolean;
    };
  }
}