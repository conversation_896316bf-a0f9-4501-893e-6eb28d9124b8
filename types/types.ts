import { getDefaultMetadata } from "@/utils/site/defaults";
import {
  affiliatesFormSchema,
  billingEmailSchema,
  billingFormSchema,
  cardFormSchema,
  contactFormSchema,
  createDeleteTeamSchema,
  faqSchema,
  ForgotPasswordSchema,
  LoginFormSchema,
  openAiKeySchema,
  RegisterSchema,
  taxFormSchema,
  teamFormSchema,
} from "@/validations/validations";
import Mail from "nodemailer/lib/mailer";
import { z } from "zod";
import { Types } from "mongoose";
import { IMember, PopulatedMember } from "@/models/member";
import { TeamPlanEnum } from "@/enums/enums";

export type AffiliateFormData = z.infer<typeof affiliatesFormSchema>;

export type ContactFormData = z.infer<typeof contactFormSchema>;

export type SocialIcons = {
  name: string;
  icon: string;
};

export type LoginFormData = z.infer<typeof LoginFormSchema>;

export type ForgotPasswordData = z.infer<typeof ForgotPasswordSchema>;

export type RegisterData = z.infer<typeof RegisterSchema>;

export type SiteMetadata = {
  base: {
    title: string;
    description: string;
    keywords: string[];
    author: string;
    isOffline: boolean;
    favicon: {
      icon: string;
      shortcut: string;
      apple: string;
      sizes?: {
        "16x16": string;
        "32x32": string;
        "512x512": string;
      };
    };
    image: {
      url: string;
      width?: number;
      height?: number;
      alt: string;
    };
    themeColor?: {
      light: string;
      dark: string;
    };
    metadataBase?: string;
    robots?: {
      index: boolean;
      follow: boolean;
      nocache?: boolean;
    };
  };
  urls: {
    production: string;
    development: string;
    alternates?: {
      canonical: string;
      languages: {
        "en-US": string;
      };
    };
  };
  social: {
    twitter: {
      handle: string;
      card: string;
      images: string[];
      title?: string;
      description?: string;
    };
    openGraph: {
      title: string;
      description: string;
      url: string;
      siteName: string;
      images: {
        url: string;
        width: number;
        height: number;
        alt: string;
      }[];
      locale: string;
      type: string;
    };
  };
  icons: {
    icon: (string | { sizes: string; url: string; type: string })[];
    apple: string;
    other: {
      rel: string;
      url: string;
      color: string;
    }[];
  };
  manifest: string;
  formatDetection: {
    email: boolean;
    address: boolean;
    telephone: boolean;
  };
  category?: string;
};

export type ISiteSettingsResponse = {
  metadata: ReturnType<typeof getDefaultMetadata>;
  urls: {
    production: string;
    development: string;
  };
};

export type SendEmailTypes = {
  sender: Mail.Address;
  recipients: Mail.Address[];
  subject: string;
  message: string;
  chatbotId?: string;
  replyTo?: string;
  html?: string;
};

export interface Team {
  _id: Types.ObjectId | string;
  name: string;
  url: string;
  description: string;
  uid: Types.ObjectId;
  ownerId: Types.ObjectId;
  apiKey?: string | null;
  openAiKey?: string | null;
  openAiOrg?: string | null;
  metaData?: string | null;
  isFavorite: boolean;
  membersCount: number;
  color: string;
  plan: TeamPlanEnum; 
  avatar?: string | null; 
  members?:  (IMember | PopulatedMember | TeamInvitationMember)[];
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateTeamDto {
  name: string;
  url: string;
  description: string;
  ownerId: string;
  openAiKey?: string;
  metaData?: string;
  isFavorite?: boolean;
  color?: string;
  membersCount?: number;
}

export interface UpdateTeamDto {
  name?: string;
  url?: string;
  description?: string;
  openAiKey?: string | null;
  metaData?: string | null;
  isFavorite?: boolean;
  color?: string;
  membersCount?: number;
}

export interface PaginationData {
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface TeamsApiResponse {
  success: boolean;
  data: Team | Team[];
  pagination: PaginationData;
  error?: string;
  message?: string;
}

export interface TeamsQueryResult {
  teams: Team[];
  pagination: PaginationData;
}

export interface TeamInvitationMember {
  _id: string;
  userId: null;
  teamId: Types.ObjectId;
  role: 'member';
  status: 'pending';
  name: string;
  email: string;
  invitedAt: Date;
  invitedBy: Types.ObjectId;
  type: 'invitation';
}

// Then update your response type
export interface TeamApiResponse {
  success: boolean;
  data?: {
    currentTeam: Team & {
      members?: (IMember | PopulatedMember | TeamInvitationMember)[];
    };
    relatedTeams?: Team[];
  };
  error?: string;
  message?: string;
}
export type TeamFormValues = z.infer<typeof teamFormSchema>;

export type OpenAiKeyFormValues = z.infer<typeof openAiKeySchema>;

export type BillingFormValues = z.infer<typeof billingFormSchema>;

export type TaxFormValues = z.infer<typeof taxFormSchema>;

export type BillingEmailValues = z.infer<typeof billingEmailSchema>;

export type DeleteTeamValues = z.infer<
  ReturnType<typeof createDeleteTeamSchema>
>;

export interface BillingApiResponse {
  success: boolean;
  error?: string;
  message?: string;
  data?: unknown;
}

export type CardFormValues = z.infer<typeof cardFormSchema>;

export type FAQFormData = z.infer<typeof faqSchema>;

export interface Plan {
  _id: string;
  name: string;
  description?: string;
  priceMonthly: number;
  priceYearly: number;
  discountPercentage?: number;
  maxMembers: number;
  maxChatbots: number;
  maxTrainingChars: number;
  maxTrainingLinks: number;
  monthlyMessageCredits: number;
  models: string[];
  features: {
    hasBasicAnalytics: boolean;
    hasApiAccess: boolean;
    hasCustomBranding: boolean;
    hasCustomDomains: boolean;
    hasNotifications: boolean;
    [key: string]: boolean;
  };
  isActive: boolean;
  isCurrent?: boolean;
  hasBasicAnalytics?: boolean;
  hasApiAccess?: boolean;
  hasCustomBranding?: boolean;
  hasCustomDomains?: boolean;
  isDefault: boolean;
  createdAt: string | Date;
  updatedAt: string | Date;
}


export interface Subscription {
  autoRechargeCredits: boolean;
  extraMessageCredits: number;
  extraChatbots: number;
  hasCustomDomains: boolean;
  hasCustomBranding: boolean;
}

export interface PlansSettingsProps {
  team: Team;
}


export interface Agent {
  _id: string;
  url: string;
  name: string;
  iconPath?: string;
}

export interface Team {
  url: string;
}

export interface Message {
  createdAt: string;
  content: string;
  source: string;
  role: string;
  conversationId: string;
}

export interface Lead {
  title: string;
  name: string;
  email: string;
  phone: string;
  createdAt: string;
}

export interface Thread {
  _id: string;
  lastUserMessage: string;
  lastAsistantMessage: string;
  lastSeen: string;
  source: string;
}